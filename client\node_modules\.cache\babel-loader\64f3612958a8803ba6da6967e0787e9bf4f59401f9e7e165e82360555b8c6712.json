{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\FloatingBrainwaveAI.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { TbRobot, TbX, TbSend, TbMinus, TbMaximize } from 'react-icons/tb';\nimport { chatWithChatGPT } from '../apicalls/chat';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FloatingBrainwaveAI = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const [messages, setMessages] = useState([{\n    role: 'assistant',\n    content: 'Hi! I\\'m <PERSON><PERSON>, your study assistant. How can I help you today?'\n  }]);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n    const userMessage = input.trim();\n    setInput('');\n    setMessages(prev => [...prev, {\n      role: 'user',\n      content: userMessage\n    }]);\n    setIsTyping(true);\n    try {\n      // Get AI response from ChatGPT\n      const response = await chatWithChatGPT([...messages, {\n        role: 'user',\n        content: userMessage\n      }]);\n      if (response !== null && response !== void 0 && response.success && response !== null && response !== void 0 && response.data) {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: response.data\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: \"I'm sorry, I couldn't process your request right now. Please try again in a moment.\"\n        }]);\n      }\n    } catch (error) {\n      console.error('Chat error:', error);\n      setMessages(prev => [...prev, {\n        role: 'assistant',\n        content: \"I'm experiencing some technical difficulties. Please try again later.\"\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  if (!isOpen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => setIsOpen(true),\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        width: '60px',\n        height: '60px',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        borderRadius: '50%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        cursor: 'pointer',\n        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n        zIndex: 1000,\n        transition: 'all 0.3s ease',\n        animation: 'pulse 2s infinite'\n      },\n      onMouseEnter: e => {\n        e.target.style.transform = 'scale(1.1)';\n        e.target.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.6)';\n      },\n      onMouseLeave: e => {\n        e.target.style.transform = 'scale(1)';\n        e.target.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.4)';\n      },\n      children: [/*#__PURE__*/_jsxDEV(TbRobot, {\n        style: {\n          color: 'white',\n          fontSize: '28px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n          @keyframes pulse {\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      bottom: '20px',\n      right: '20px',\n      width: isMinimized ? '300px' : '380px',\n      height: isMinimized ? '60px' : '500px',\n      background: 'rgba(255, 255, 255, 0.95)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '20px',\n      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n      border: '1px solid rgba(255, 255, 255, 0.2)',\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        padding: '16px 20px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        borderRadius: '20px 20px 0 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '32px',\n            height: '32px',\n            background: 'rgba(255, 255, 255, 0.2)',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(TbRobot, {\n            style: {\n              color: 'white',\n              fontSize: '18px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: 0,\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: '600'\n            },\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              color: 'rgba(255, 255, 255, 0.8)',\n              fontSize: '12px'\n            },\n            children: \"Always here to help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsMinimized(!isMinimized),\n          style: {\n            background: 'rgba(255, 255, 255, 0.2)',\n            border: 'none',\n            borderRadius: '6px',\n            width: '28px',\n            height: '28px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease'\n          },\n          onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.3)',\n          onMouseLeave: e => e.target.style.background = 'rgba(255, 255, 255, 0.2)',\n          children: isMinimized ? /*#__PURE__*/_jsxDEV(TbMaximize, {\n            style: {\n              color: 'white',\n              fontSize: '14px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TbMinus, {\n            style: {\n              color: 'white',\n              fontSize: '14px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsOpen(false),\n          style: {\n            background: 'rgba(255, 255, 255, 0.2)',\n            border: 'none',\n            borderRadius: '6px',\n            width: '28px',\n            height: '28px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease'\n          },\n          onMouseEnter: e => e.target.style.background = 'rgba(255, 0, 0, 0.3)',\n          onMouseLeave: e => e.target.style.background = 'rgba(255, 255, 255, 0.2)',\n          children: /*#__PURE__*/_jsxDEV(TbX, {\n            style: {\n              color: 'white',\n              fontSize: '14px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), !isMinimized && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          padding: '20px',\n          overflowY: 'auto',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '16px'\n        },\n        children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxWidth: '80%',\n              padding: '12px 16px',\n              borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',\n              background: msg.role === 'user' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f8fafc',\n              color: msg.role === 'user' ? 'white' : '#334155',\n              fontSize: '14px',\n              lineHeight: '1.4',\n              boxShadow: msg.role === 'user' ? '0 4px 12px rgba(102, 126, 234, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',\n              wordWrap: 'break-word'\n            },\n            children: msg.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 15\n        }, this)), isTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '12px 16px',\n              borderRadius: '18px 18px 18px 4px',\n              background: '#f8fafc',\n              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '4px',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.16s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.32s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px 20px 20px',\n          borderTop: '1px solid rgba(0, 0, 0, 0.05)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '8px',\n            background: '#f8fafc',\n            borderRadius: '12px',\n            padding: '8px',\n            border: '1px solid #e2e8f0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: input,\n            onChange: e => setInput(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Ask me anything...\",\n            style: {\n              flex: 1,\n              border: 'none',\n              background: 'transparent',\n              outline: 'none',\n              fontSize: '14px',\n              color: '#334155',\n              padding: '8px 12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: sendMessage,\n            disabled: !input.trim(),\n            style: {\n              background: input.trim() ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#e2e8f0',\n              border: 'none',\n              borderRadius: '8px',\n              width: '36px',\n              height: '36px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: input.trim() ? 'pointer' : 'not-allowed',\n              transition: 'all 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(TbSend, {\n              style: {\n                color: input.trim() ? 'white' : '#94a3b8',\n                fontSize: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n_s(FloatingBrainwaveAI, \"7EUt4cRSOzDQ+LemY20K0ap6qyM=\");\n_c = FloatingBrainwaveAI;\nexport default FloatingBrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"FloatingBrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "TbRobot", "TbX", "TbSend", "TbMinus", "TbMaximize", "chatWithChatGPT", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FloatingBrainwaveAI", "_s", "isOpen", "setIsOpen", "isMinimized", "setIsMinimized", "messages", "setMessages", "role", "content", "input", "setInput", "isTyping", "setIsTyping", "messagesEndRef", "current", "scrollIntoView", "behavior", "sendMessage", "trim", "userMessage", "prev", "response", "success", "data", "error", "console", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "onClick", "style", "position", "bottom", "right", "width", "height", "background", "borderRadius", "display", "alignItems", "justifyContent", "cursor", "boxShadow", "zIndex", "transition", "animation", "onMouseEnter", "target", "transform", "onMouseLeave", "children", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>ilter", "border", "flexDirection", "overflow", "padding", "gap", "margin", "fontWeight", "flex", "overflowY", "map", "msg", "index", "max<PERSON><PERSON><PERSON>", "lineHeight", "wordWrap", "ref", "borderTop", "type", "value", "onChange", "onKeyPress", "placeholder", "outline", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/FloatingBrainwaveAI.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { TbRobot, TbX, TbS<PERSON>, TbMinus, TbMaximize } from 'react-icons/tb';\nimport { chatWithChatGPT } from '../apicalls/chat';\n\nconst FloatingBrainwaveAI = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const [messages, setMessages] = useState([\n    { role: 'assistant', content: 'Hi! I\\'m <PERSON><PERSON> <PERSON>, your study assistant. How can I help you today?' }\n  ]);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n\n    const userMessage = input.trim();\n    setInput('');\n    setMessages(prev => [...prev, { role: 'user', content: userMessage }]);\n    setIsTyping(true);\n\n    try {\n      // Get AI response from ChatGPT\n      const response = await chatWithChatGPT([\n        ...messages,\n        { role: 'user', content: userMessage }\n      ]);\n\n      if (response?.success && response?.data) {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: response.data\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: \"I'm sorry, I couldn't process your request right now. Please try again in a moment.\"\n        }]);\n      }\n    } catch (error) {\n      console.error('Chat error:', error);\n      setMessages(prev => [...prev, {\n        role: 'assistant',\n        content: \"I'm experiencing some technical difficulties. Please try again later.\"\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  if (!isOpen) {\n    return (\n      <div\n        onClick={() => setIsOpen(true)}\n        style={{\n          position: 'fixed',\n          bottom: '20px',\n          right: '20px',\n          width: '60px',\n          height: '60px',\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          borderRadius: '50%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          cursor: 'pointer',\n          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n          zIndex: 1000,\n          transition: 'all 0.3s ease',\n          animation: 'pulse 2s infinite'\n        }}\n        onMouseEnter={(e) => {\n          e.target.style.transform = 'scale(1.1)';\n          e.target.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.6)';\n        }}\n        onMouseLeave={(e) => {\n          e.target.style.transform = 'scale(1)';\n          e.target.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.4)';\n        }}\n      >\n        <TbRobot style={{ color: 'white', fontSize: '28px' }} />\n        <style>{`\n          @keyframes pulse {\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n          }\n        `}</style>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        width: isMinimized ? '300px' : '380px',\n        height: isMinimized ? '60px' : '500px',\n        background: 'rgba(255, 255, 255, 0.95)',\n        backdropFilter: 'blur(20px)',\n        borderRadius: '20px',\n        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        zIndex: 1000,\n        display: 'flex',\n        flexDirection: 'column',\n        overflow: 'hidden',\n        transition: 'all 0.3s ease'\n      }}\n    >\n      {/* Header */}\n      <div\n        style={{\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          padding: '16px 20px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          borderRadius: '20px 20px 0 0'\n        }}\n      >\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div\n            style={{\n              width: '32px',\n              height: '32px',\n              background: 'rgba(255, 255, 255, 0.2)',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n          >\n            <TbRobot style={{ color: 'white', fontSize: '18px' }} />\n          </div>\n          <div>\n            <h3 style={{ margin: 0, color: 'white', fontSize: '16px', fontWeight: '600' }}>\n              Brainwave AI\n            </h3>\n            <p style={{ margin: 0, color: 'rgba(255, 255, 255, 0.8)', fontSize: '12px' }}>\n              Always here to help\n            </p>\n          </div>\n        </div>\n        \n        <div style={{ display: 'flex', gap: '8px' }}>\n          <button\n            onClick={() => setIsMinimized(!isMinimized)}\n            style={{\n              background: 'rgba(255, 255, 255, 0.2)',\n              border: 'none',\n              borderRadius: '6px',\n              width: '28px',\n              height: '28px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease'\n            }}\n            onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.3)'}\n            onMouseLeave={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.2)'}\n          >\n            {isMinimized ? (\n              <TbMaximize style={{ color: 'white', fontSize: '14px' }} />\n            ) : (\n              <TbMinus style={{ color: 'white', fontSize: '14px' }} />\n            )}\n          </button>\n          \n          <button\n            onClick={() => setIsOpen(false)}\n            style={{\n              background: 'rgba(255, 255, 255, 0.2)',\n              border: 'none',\n              borderRadius: '6px',\n              width: '28px',\n              height: '28px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease'\n            }}\n            onMouseEnter={(e) => e.target.style.background = 'rgba(255, 0, 0, 0.3)'}\n            onMouseLeave={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.2)'}\n          >\n            <TbX style={{ color: 'white', fontSize: '14px' }} />\n          </button>\n        </div>\n      </div>\n\n      {!isMinimized && (\n        <>\n          {/* Messages */}\n          <div\n            style={{\n              flex: 1,\n              padding: '20px',\n              overflowY: 'auto',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '16px'\n            }}\n          >\n            {messages.map((msg, index) => (\n              <div\n                key={index}\n                style={{\n                  display: 'flex',\n                  justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'\n                }}\n              >\n                <div\n                  style={{\n                    maxWidth: '80%',\n                    padding: '12px 16px',\n                    borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',\n                    background: msg.role === 'user' \n                      ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                      : '#f8fafc',\n                    color: msg.role === 'user' ? 'white' : '#334155',\n                    fontSize: '14px',\n                    lineHeight: '1.4',\n                    boxShadow: msg.role === 'user' \n                      ? '0 4px 12px rgba(102, 126, 234, 0.3)'\n                      : '0 2px 8px rgba(0, 0, 0, 0.1)',\n                    wordWrap: 'break-word'\n                  }}\n                >\n                  {msg.content}\n                </div>\n              </div>\n            ))}\n\n            {isTyping && (\n              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>\n                <div\n                  style={{\n                    padding: '12px 16px',\n                    borderRadius: '18px 18px 18px 4px',\n                    background: '#f8fafc',\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n                  }}\n                >\n                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>\n                    <div\n                      style={{\n                        width: '6px',\n                        height: '6px',\n                        borderRadius: '50%',\n                        background: '#94a3b8',\n                        animation: 'bounce 1.4s infinite ease-in-out'\n                      }}\n                    />\n                    <div\n                      style={{\n                        width: '6px',\n                        height: '6px',\n                        borderRadius: '50%',\n                        background: '#94a3b8',\n                        animation: 'bounce 1.4s infinite ease-in-out 0.16s'\n                      }}\n                    />\n                    <div\n                      style={{\n                        width: '6px',\n                        height: '6px',\n                        borderRadius: '50%',\n                        background: '#94a3b8',\n                        animation: 'bounce 1.4s infinite ease-in-out 0.32s'\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* Input */}\n          <div\n            style={{\n              padding: '16px 20px 20px',\n              borderTop: '1px solid rgba(0, 0, 0, 0.05)'\n            }}\n          >\n            <div\n              style={{\n                display: 'flex',\n                gap: '8px',\n                background: '#f8fafc',\n                borderRadius: '12px',\n                padding: '8px',\n                border: '1px solid #e2e8f0'\n              }}\n            >\n              <input\n                type=\"text\"\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Ask me anything...\"\n                style={{\n                  flex: 1,\n                  border: 'none',\n                  background: 'transparent',\n                  outline: 'none',\n                  fontSize: '14px',\n                  color: '#334155',\n                  padding: '8px 12px'\n                }}\n              />\n              <button\n                onClick={sendMessage}\n                disabled={!input.trim()}\n                style={{\n                  background: input.trim() \n                    ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                    : '#e2e8f0',\n                  border: 'none',\n                  borderRadius: '8px',\n                  width: '36px',\n                  height: '36px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: input.trim() ? 'pointer' : 'not-allowed',\n                  transition: 'all 0.2s ease'\n                }}\n              >\n                <TbSend\n                  style={{\n                    color: input.trim() ? 'white' : '#94a3b8',\n                    fontSize: '16px'\n                  }}\n                />\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n\n      <style>{`\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default FloatingBrainwaveAI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AAC1E,SAASC,eAAe,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEnD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,CACvC;IAAEqB,IAAI,EAAE,WAAW;IAAEC,OAAO,EAAE;EAAyE,CAAC,CACzG,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM2B,cAAc,GAAG1B,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACAC,SAAS,CAAC,MAAM;IACd,IAAIyB,cAAc,CAACC,OAAO,EAAE;MAC1BD,cAAc,CAACC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAACX,QAAQ,CAAC,CAAC;EAEd,MAAMY,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACR,KAAK,CAACS,IAAI,CAAC,CAAC,EAAE;IAEnB,MAAMC,WAAW,GAAGV,KAAK,CAACS,IAAI,CAAC,CAAC;IAChCR,QAAQ,CAAC,EAAE,CAAC;IACZJ,WAAW,CAACc,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAEb,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEW;IAAY,CAAC,CAAC,CAAC;IACtEP,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF;MACA,MAAMS,QAAQ,GAAG,MAAM3B,eAAe,CAAC,CACrC,GAAGW,QAAQ,EACX;QAAEE,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAEW;MAAY,CAAC,CACvC,CAAC;MAEF,IAAIE,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,OAAO,IAAID,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEE,IAAI,EAAE;QACvCjB,WAAW,CAACc,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5Bb,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEa,QAAQ,CAACE;QACpB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLjB,WAAW,CAACc,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5Bb,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnClB,WAAW,CAACc,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5Bb,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRI,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMc,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBb,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,IAAI,CAAChB,MAAM,EAAE;IACX,oBACEL,OAAA;MACEmC,OAAO,EAAEA,CAAA,KAAM7B,SAAS,CAAC,IAAI,CAAE;MAC/B8B,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,mDAAmD;QAC/DC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,qCAAqC;QAChDC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE;MACb,CAAE;MACFC,YAAY,EAAGrB,CAAC,IAAK;QACnBA,CAAC,CAACsB,MAAM,CAACjB,KAAK,CAACkB,SAAS,GAAG,YAAY;QACvCvB,CAAC,CAACsB,MAAM,CAACjB,KAAK,CAACY,SAAS,GAAG,sCAAsC;MACnE,CAAE;MACFO,YAAY,EAAGxB,CAAC,IAAK;QACnBA,CAAC,CAACsB,MAAM,CAACjB,KAAK,CAACkB,SAAS,GAAG,UAAU;QACrCvB,CAAC,CAACsB,MAAM,CAACjB,KAAK,CAACY,SAAS,GAAG,qCAAqC;MAClE,CAAE;MAAAQ,QAAA,gBAEFxD,OAAA,CAACP,OAAO;QAAC2C,KAAK,EAAE;UAAEqB,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxD9D,OAAA;QAAAwD,QAAA,EAAS;AACjB;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACE9D,OAAA;IACEoC,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,KAAK,EAAEjC,WAAW,GAAG,OAAO,GAAG,OAAO;MACtCkC,MAAM,EAAElC,WAAW,GAAG,MAAM,GAAG,OAAO;MACtCmC,UAAU,EAAE,2BAA2B;MACvCqB,cAAc,EAAE,YAAY;MAC5BpB,YAAY,EAAE,MAAM;MACpBK,SAAS,EAAE,iCAAiC;MAC5CgB,MAAM,EAAE,oCAAoC;MAC5Cf,MAAM,EAAE,IAAI;MACZL,OAAO,EAAE,MAAM;MACfqB,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE,QAAQ;MAClBhB,UAAU,EAAE;IACd,CAAE;IAAAM,QAAA,gBAGFxD,OAAA;MACEoC,KAAK,EAAE;QACLM,UAAU,EAAE,mDAAmD;QAC/DyB,OAAO,EAAE,WAAW;QACpBvB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BH,YAAY,EAAE;MAChB,CAAE;MAAAa,QAAA,gBAEFxD,OAAA;QAAKoC,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEuB,GAAG,EAAE;QAAO,CAAE;QAAAZ,QAAA,gBACjExD,OAAA;UACEoC,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,0BAA0B;YACtCC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAU,QAAA,eAEFxD,OAAA,CAACP,OAAO;YAAC2C,KAAK,EAAE;cAAEqB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACN9D,OAAA;UAAAwD,QAAA,gBACExD,OAAA;YAAIoC,KAAK,EAAE;cAAEiC,MAAM,EAAE,CAAC;cAAEZ,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEY,UAAU,EAAE;YAAM,CAAE;YAAAd,QAAA,EAAC;UAE/E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9D,OAAA;YAAGoC,KAAK,EAAE;cAAEiC,MAAM,EAAE,CAAC;cAAEZ,KAAK,EAAE,0BAA0B;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAE9E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9D,OAAA;QAAKoC,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEwB,GAAG,EAAE;QAAM,CAAE;QAAAZ,QAAA,gBAC1CxD,OAAA;UACEmC,OAAO,EAAEA,CAAA,KAAM3B,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5C6B,KAAK,EAAE;YACLM,UAAU,EAAE,0BAA0B;YACtCsB,MAAM,EAAE,MAAM;YACdrB,YAAY,EAAE,KAAK;YACnBH,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBG,UAAU,EAAE;UACd,CAAE;UACFE,YAAY,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACjB,KAAK,CAACM,UAAU,GAAG,0BAA2B;UAC5Ea,YAAY,EAAGxB,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACjB,KAAK,CAACM,UAAU,GAAG,0BAA2B;UAAAc,QAAA,EAE3EjD,WAAW,gBACVP,OAAA,CAACH,UAAU;YAACuC,KAAK,EAAE;cAAEqB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE3D9D,OAAA,CAACJ,OAAO;YAACwC,KAAK,EAAE;cAAEqB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACxD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAET9D,OAAA;UACEmC,OAAO,EAAEA,CAAA,KAAM7B,SAAS,CAAC,KAAK,CAAE;UAChC8B,KAAK,EAAE;YACLM,UAAU,EAAE,0BAA0B;YACtCsB,MAAM,EAAE,MAAM;YACdrB,YAAY,EAAE,KAAK;YACnBH,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBG,UAAU,EAAE;UACd,CAAE;UACFE,YAAY,EAAGrB,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACjB,KAAK,CAACM,UAAU,GAAG,sBAAuB;UACxEa,YAAY,EAAGxB,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACjB,KAAK,CAACM,UAAU,GAAG,0BAA2B;UAAAc,QAAA,eAE5ExD,OAAA,CAACN,GAAG;YAAC0C,KAAK,EAAE;cAAEqB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAACvD,WAAW,iBACXP,OAAA,CAAAE,SAAA;MAAAsD,QAAA,gBAEExD,OAAA;QACEoC,KAAK,EAAE;UACLmC,IAAI,EAAE,CAAC;UACPJ,OAAO,EAAE,MAAM;UACfK,SAAS,EAAE,MAAM;UACjB5B,OAAO,EAAE,MAAM;UACfqB,aAAa,EAAE,QAAQ;UACvBG,GAAG,EAAE;QACP,CAAE;QAAAZ,QAAA,GAED/C,QAAQ,CAACgE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvB3E,OAAA;UAEEoC,KAAK,EAAE;YACLQ,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE4B,GAAG,CAAC/D,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG;UACrD,CAAE;UAAA6C,QAAA,eAEFxD,OAAA;YACEoC,KAAK,EAAE;cACLwC,QAAQ,EAAE,KAAK;cACfT,OAAO,EAAE,WAAW;cACpBxB,YAAY,EAAE+B,GAAG,CAAC/D,IAAI,KAAK,MAAM,GAAG,oBAAoB,GAAG,oBAAoB;cAC/E+B,UAAU,EAAEgC,GAAG,CAAC/D,IAAI,KAAK,MAAM,GAC3B,mDAAmD,GACnD,SAAS;cACb8C,KAAK,EAAEiB,GAAG,CAAC/D,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,SAAS;cAChD+C,QAAQ,EAAE,MAAM;cAChBmB,UAAU,EAAE,KAAK;cACjB7B,SAAS,EAAE0B,GAAG,CAAC/D,IAAI,KAAK,MAAM,GAC1B,qCAAqC,GACrC,8BAA8B;cAClCmE,QAAQ,EAAE;YACZ,CAAE;YAAAtB,QAAA,EAEDkB,GAAG,CAAC9D;UAAO;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC,GAxBDa,KAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyBP,CACN,CAAC,EAED/C,QAAQ,iBACPf,OAAA;UAAKoC,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE;UAAa,CAAE;UAAAU,QAAA,eAC5DxD,OAAA;YACEoC,KAAK,EAAE;cACL+B,OAAO,EAAE,WAAW;cACpBxB,YAAY,EAAE,oBAAoB;cAClCD,UAAU,EAAE,SAAS;cACrBM,SAAS,EAAE;YACb,CAAE;YAAAQ,QAAA,eAEFxD,OAAA;cAAKoC,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEwB,GAAG,EAAE,KAAK;gBAAEvB,UAAU,EAAE;cAAS,CAAE;cAAAW,QAAA,gBAChExD,OAAA;gBACEoC,KAAK,EAAE;kBACLI,KAAK,EAAE,KAAK;kBACZC,MAAM,EAAE,KAAK;kBACbE,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBS,SAAS,EAAE;gBACb;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF9D,OAAA;gBACEoC,KAAK,EAAE;kBACLI,KAAK,EAAE,KAAK;kBACZC,MAAM,EAAE,KAAK;kBACbE,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBS,SAAS,EAAE;gBACb;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF9D,OAAA;gBACEoC,KAAK,EAAE;kBACLI,KAAK,EAAE,KAAK;kBACZC,MAAM,EAAE,KAAK;kBACbE,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBS,SAAS,EAAE;gBACb;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED9D,OAAA;UAAK+E,GAAG,EAAE9D;QAAe;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGN9D,OAAA;QACEoC,KAAK,EAAE;UACL+B,OAAO,EAAE,gBAAgB;UACzBa,SAAS,EAAE;QACb,CAAE;QAAAxB,QAAA,eAEFxD,OAAA;UACEoC,KAAK,EAAE;YACLQ,OAAO,EAAE,MAAM;YACfwB,GAAG,EAAE,KAAK;YACV1B,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,MAAM;YACpBwB,OAAO,EAAE,KAAK;YACdH,MAAM,EAAE;UACV,CAAE;UAAAR,QAAA,gBAEFxD,OAAA;YACEiF,IAAI,EAAC,MAAM;YACXC,KAAK,EAAErE,KAAM;YACbsE,QAAQ,EAAGpD,CAAC,IAAKjB,QAAQ,CAACiB,CAAC,CAACsB,MAAM,CAAC6B,KAAK,CAAE;YAC1CE,UAAU,EAAEtD,cAAe;YAC3BuD,WAAW,EAAC,oBAAoB;YAChCjD,KAAK,EAAE;cACLmC,IAAI,EAAE,CAAC;cACPP,MAAM,EAAE,MAAM;cACdtB,UAAU,EAAE,aAAa;cACzB4C,OAAO,EAAE,MAAM;cACf5B,QAAQ,EAAE,MAAM;cAChBD,KAAK,EAAE,SAAS;cAChBU,OAAO,EAAE;YACX;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF9D,OAAA;YACEmC,OAAO,EAAEd,WAAY;YACrBkE,QAAQ,EAAE,CAAC1E,KAAK,CAACS,IAAI,CAAC,CAAE;YACxBc,KAAK,EAAE;cACLM,UAAU,EAAE7B,KAAK,CAACS,IAAI,CAAC,CAAC,GACpB,mDAAmD,GACnD,SAAS;cACb0C,MAAM,EAAE,MAAM;cACdrB,YAAY,EAAE,KAAK;cACnBH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAElC,KAAK,CAACS,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa;cAChD4B,UAAU,EAAE;YACd,CAAE;YAAAM,QAAA,eAEFxD,OAAA,CAACL,MAAM;cACLyC,KAAK,EAAE;gBACLqB,KAAK,EAAE5C,KAAK,CAACS,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,SAAS;gBACzCoC,QAAQ,EAAE;cACZ;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CACH,eAED9D,OAAA;MAAAwD,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC1D,EAAA,CA7WID,mBAAmB;AAAAqF,EAAA,GAAnBrF,mBAAmB;AA+WzB,eAAeA,mBAAmB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}