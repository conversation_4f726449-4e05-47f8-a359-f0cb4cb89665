{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\FloatingBrainwaveAI.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { TbRobot } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\nimport ContentRenderer from './ContentRenderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FloatingBrainwaveAI = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const [isMaximized, setIsMaximized] = useState(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  const [isTablet, setIsTablet] = useState(window.innerWidth > 768 && window.innerWidth <= 1024);\n\n  // Handle responsive design\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n      setIsTablet(window.innerWidth > 768 && window.innerWidth <= 1024);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  const [messages, setMessages] = useState([{\n    role: 'assistant',\n    content: 'Hi! I am Brainwave AI, your study assistant. How can I help you today?'\n  }]);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n  const handleImageSelect = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n    setInput('');\n    removeImage();\n    setIsTyping(true);\n    try {\n      let imageUrl = null;\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        if (uploadResult !== null && uploadResult !== void 0 && uploadResult.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n      const newUserMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: userMessage\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: userMessage\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n      const response = await chatWithChatGPT({\n        messages: [...messages, newUserMessage]\n      });\n      if (response !== null && response !== void 0 && response.success && response !== null && response !== void 0 && response.data) {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: response.data\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: \"I'm sorry, I couldn't process your request right now. Please try again in a moment.\"\n        }]);\n      }\n    } catch (error) {\n      setMessages(prev => [...prev, {\n        role: 'assistant',\n        content: \"I'm experiencing some technical difficulties. Please try again later.\"\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  if (!isOpen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => setIsOpen(true),\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        width: '60px',\n        height: '60px',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        borderRadius: '50%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        cursor: 'pointer',\n        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n        zIndex: 1000,\n        transition: 'all 0.3s ease',\n        animation: 'pulse 2s infinite'\n      },\n      children: [/*#__PURE__*/_jsxDEV(TbRobot, {\n        style: {\n          color: 'white',\n          fontSize: '28px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n          @keyframes pulse {\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      bottom: isMaximized ? '10px' : '20px',\n      right: isMaximized ? '10px' : '20px',\n      top: isMaximized ? '10px' : 'auto',\n      left: isMaximized ? '10px' : 'auto',\n      width: isMinimized ? '300px' : isMaximized ? 'calc(100vw - 20px)' : '380px',\n      height: isMinimized ? '60px' : isMaximized ? 'calc(100vh - 20px)' : '500px',\n      background: 'rgba(255, 255, 255, 0.95)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: isMaximized ? '15px' : '20px',\n      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n      border: '1px solid rgba(255, 255, 255, 0.2)',\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease'\n    },\n    onWheel: e => {\n      // Only prevent background scrolling when AI chat is open and not minimized\n      if (isOpen && !isMinimized) {\n        e.stopPropagation();\n      }\n    },\n    onTouchMove: e => {\n      // Only prevent background scrolling when AI chat is open and not minimized\n      if (isOpen && !isMinimized) {\n        e.stopPropagation();\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        padding: '16px 20px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        borderRadius: isMaximized ? '15px 15px 0 0' : '20px 20px 0 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '32px',\n            height: '32px',\n            background: 'rgba(255, 255, 255, 0.2)',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(TbRobot, {\n            style: {\n              color: 'white',\n              fontSize: '18px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: 0,\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: '600'\n            },\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              color: 'rgba(255, 255, 255, 0.8)',\n              fontSize: '12px'\n            },\n            children: \"Always here to help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsMinimized(!isMinimized),\n          style: {\n            background: 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: '8px',\n            width: '32px',\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            },\n            children: isMinimized ? '⬆' : '➖'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setIsMaximized(!isMaximized);\n            if (isMinimized) setIsMinimized(false);\n          },\n          style: {\n            background: 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: '8px',\n            width: '32px',\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            },\n            children: isMaximized ? '⬇' : '⬆'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsOpen(false),\n          style: {\n            background: 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: '8px',\n            width: '32px',\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            },\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), !isMinimized && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          padding: '20px 20px 0 20px',\n          overflowY: 'auto',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '16px',\n          scrollBehavior: 'smooth',\n          scrollbarWidth: 'thin',\n          scrollbarColor: '#cbd5e1 transparent'\n        },\n        onWheel: e => {\n          const element = e.currentTarget;\n          const {\n            scrollTop,\n            scrollHeight,\n            clientHeight\n          } = element;\n\n          // Allow scrolling within the messages container\n          if (scrollTop > 0 && scrollTop + clientHeight < scrollHeight) {\n            // We're in the middle, allow normal scrolling\n            return;\n          }\n\n          // At the boundaries, prevent propagation to background\n          if (scrollTop === 0 && e.deltaY < 0 || scrollTop + clientHeight >= scrollHeight && e.deltaY > 0) {\n            e.preventDefault();\n            e.stopPropagation();\n          }\n        },\n        className: \"custom-scrollbar\",\n        children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxWidth: '85%',\n              padding: '12px 16px',\n              borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',\n              background: msg.role === 'user' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f8fafc',\n              color: msg.role === 'user' ? 'white' : '#334155',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              boxShadow: msg.role === 'user' ? '0 4px 12px rgba(102, 126, 234, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',\n              wordWrap: 'break-word'\n            },\n            children: typeof msg.content === 'string' ? msg.role === 'assistant' ? /*#__PURE__*/_jsxDEV(ContentRenderer, {\n              text: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 23\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                whiteSpace: 'pre-wrap'\n              },\n              children: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 23\n            }, this) : Array.isArray(msg.content) ? msg.content.map((item, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: item.text ? '8px' : '0'\n                },\n                children: msg.role === 'assistant' ? /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                  text: item.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 31\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    whiteSpace: 'pre-wrap'\n                  },\n                  children: item.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 27\n              }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"img\", {\n                src: item.image_url.url,\n                alt: \"User upload\",\n                style: {\n                  maxWidth: '100%',\n                  height: 'auto',\n                  borderRadius: '12px',\n                  maxHeight: '200px',\n                  objectFit: 'cover',\n                  border: '2px solid rgba(255, 255, 255, 0.2)',\n                  marginTop: '4px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 55\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 23\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Invalid message format\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 15\n        }, this)), isTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '12px 16px',\n              borderRadius: '18px 18px 18px 4px',\n              background: '#f8fafc',\n              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '4px',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.16s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.32s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'sticky',\n          bottom: 0,\n          background: 'rgba(255, 255, 255, 0.98)',\n          backdropFilter: 'blur(20px)',\n          borderTop: '1px solid rgba(0, 0, 0, 0.08)',\n          padding: '16px 20px 20px',\n          zIndex: 10\n        },\n        children: [imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            padding: '12px',\n            background: '#f1f5f9',\n            borderRadius: '12px',\n            border: '1px solid #e2e8f0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  objectFit: 'cover',\n                  borderRadius: '8px',\n                  border: '2px solid #e2e8f0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: removeImage,\n                style: {\n                  position: 'absolute',\n                  top: '-6px',\n                  right: '-6px',\n                  width: '20px',\n                  height: '20px',\n                  background: '#ef4444',\n                  color: 'white',\n                  borderRadius: '50%',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '12px',\n                  fontWeight: 'bold'\n                },\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '13px',\n                  fontWeight: '600',\n                  color: '#374151',\n                  margin: 0\n                },\n                children: \"Image attached\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '11px',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '8px',\n            background: '#f8fafc',\n            borderRadius: '16px',\n            padding: '8px',\n            border: '2px solid #e2e8f0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            style: {\n              background: '#3b82f6',\n              border: 'none',\n              borderRadius: '12px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)'\n            },\n            title: \"Attach image\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'white',\n                fontSize: '20px',\n                fontWeight: 'bold'\n              },\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: input,\n            onChange: e => setInput(e.target.value),\n            onKeyDown: handleKeyDown,\n            placeholder: \"Ask me anything...\",\n            style: {\n              flex: 1,\n              border: 'none',\n              background: 'transparent',\n              outline: 'none',\n              fontSize: '14px',\n              color: '#334155',\n              padding: '12px 16px',\n              fontFamily: 'inherit'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: sendMessage,\n            disabled: !input.trim() && !selectedImage,\n            style: {\n              background: input.trim() || selectedImage ? '#3b82f6' : '#e2e8f0',\n              border: 'none',\n              borderRadius: '12px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: input.trim() || selectedImage ? 'pointer' : 'not-allowed',\n              transition: 'all 0.2s ease',\n              boxShadow: input.trim() || selectedImage ? '0 2px 8px rgba(59, 130, 246, 0.3)' : 'none'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: input.trim() || selectedImage ? 'white' : '#94a3b8',\n                fontSize: '18px',\n                fontWeight: 'bold'\n              },\n              children: \"\\u27A4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '11px',\n            color: '#94a3b8',\n            textAlign: 'center',\n            margin: '8px 0 0 0'\n          },\n          children: \"Press Enter to send \\u2022 Attach images for analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n};\n_s(FloatingBrainwaveAI, \"f+TS1/jzSv80BRhHmSwa/UzzZqY=\");\n_c = FloatingBrainwaveAI;\nexport default FloatingBrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"FloatingBrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "TbRobot", "chatWithChatGPT", "uploadImg", "Content<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FloatingBrainwaveAI", "_s", "isOpen", "setIsOpen", "isMinimized", "setIsMinimized", "isMaximized", "setIsMaximized", "isMobile", "setIsMobile", "window", "innerWidth", "isTablet", "setIsTablet", "handleResize", "addEventListener", "removeEventListener", "messages", "setMessages", "role", "content", "input", "setInput", "isTyping", "setIsTyping", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "messagesEndRef", "fileInputRef", "current", "scrollIntoView", "behavior", "handleImageSelect", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeImage", "value", "sendMessage", "trim", "userMessage", "imageFile", "imageUrl", "formData", "FormData", "append", "uploadResult", "success", "url", "newUserMessage", "text", "image_url", "prev", "response", "data", "error", "handleKeyDown", "key", "shift<PERSON>ey", "preventDefault", "onClick", "style", "position", "bottom", "right", "width", "height", "background", "borderRadius", "display", "alignItems", "justifyContent", "cursor", "boxShadow", "zIndex", "transition", "animation", "children", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "left", "<PERSON><PERSON>ilter", "border", "flexDirection", "overflow", "onWheel", "stopPropagation", "onTouchMove", "padding", "gap", "margin", "fontWeight", "flex", "overflowY", "scroll<PERSON>eh<PERSON>or", "scrollbarWidth", "scrollbarColor", "element", "currentTarget", "scrollTop", "scrollHeight", "clientHeight", "deltaY", "className", "map", "msg", "index", "max<PERSON><PERSON><PERSON>", "lineHeight", "wordWrap", "whiteSpace", "Array", "isArray", "item", "idx", "marginBottom", "src", "alt", "maxHeight", "objectFit", "marginTop", "ref", "borderTop", "name", "_fileInputRef$current", "click", "title", "onChange", "onKeyDown", "placeholder", "outline", "fontFamily", "disabled", "accept", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/FloatingBrainwaveAI.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { TbRobot } from 'react-icons/tb';\r\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\r\nimport ContentRenderer from './ContentRenderer';\r\n\r\nconst FloatingBrainwaveAI = () => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [isMinimized, setIsMinimized] = useState(false);\r\n  const [isMaximized, setIsMaximized] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n  const [isTablet, setIsTablet] = useState(window.innerWidth > 768 && window.innerWidth <= 1024);\r\n\r\n  // Handle responsive design\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n      setIsTablet(window.innerWidth > 768 && window.innerWidth <= 1024);\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n  const [messages, setMessages] = useState([\r\n    { role: 'assistant', content: 'Hi! I am Brainwave AI, your study assistant. How can I help you today?' }\r\n  ]);\r\n  const [input, setInput] = useState('');\r\n  const [isTyping, setIsTyping] = useState(false);\r\n  const [selectedImage, setSelectedImage] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const messagesEndRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    if (messagesEndRef.current) {\r\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  }, [messages]);\r\n\r\n  const handleImageSelect = (event) => {\r\n    const file = event.target.files[0];\r\n    if (file && file.type.startsWith('image/')) {\r\n      setSelectedImage(file);\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImagePreview(e.target.result);\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const removeImage = () => {\r\n    setSelectedImage(null);\r\n    setImagePreview(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  const sendMessage = async () => {\r\n    if (!input.trim() && !selectedImage) return;\r\n\r\n    const userMessage = input.trim();\r\n    const imageFile = selectedImage;\r\n\r\n    setInput('');\r\n    removeImage();\r\n    setIsTyping(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n        const uploadResult = await uploadImg(formData);\r\n\r\n        if (uploadResult?.success) {\r\n          imageUrl = uploadResult.url;\r\n        }\r\n      }\r\n\r\n      const newUserMessage = imageUrl\r\n        ? {\r\n            role: \"user\",\r\n            content: [\r\n              { type: \"text\", text: userMessage },\r\n              { type: \"image_url\", image_url: { url: imageUrl } }\r\n            ]\r\n          }\r\n        : { role: \"user\", content: userMessage };\r\n\r\n      setMessages(prev => [...prev, newUserMessage]);\r\n\r\n      const response = await chatWithChatGPT({\r\n        messages: [...messages, newUserMessage]\r\n      });\r\n\r\n      if (response?.success && response?.data) {\r\n        setMessages(prev => [...prev, {\r\n          role: 'assistant',\r\n          content: response.data\r\n        }]);\r\n      } else {\r\n        setMessages(prev => [...prev, {\r\n          role: 'assistant',\r\n          content: \"I'm sorry, I couldn't process your request right now. Please try again in a moment.\"\r\n        }]);\r\n      }\r\n    } catch (error) {\r\n      setMessages(prev => [...prev, {\r\n        role: 'assistant',\r\n        content: \"I'm experiencing some technical difficulties. Please try again later.\"\r\n      }]);\r\n    } finally {\r\n      setIsTyping(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      sendMessage();\r\n    }\r\n  };\r\n\r\n  if (!isOpen) {\r\n    return (\r\n      <div\r\n        onClick={() => setIsOpen(true)}\r\n        style={{\r\n          position: 'fixed',\r\n          bottom: '20px',\r\n          right: '20px',\r\n          width: '60px',\r\n          height: '60px',\r\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n          borderRadius: '50%',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          cursor: 'pointer',\r\n          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\r\n          zIndex: 1000,\r\n          transition: 'all 0.3s ease',\r\n          animation: 'pulse 2s infinite'\r\n        }}\r\n      >\r\n        <TbRobot style={{ color: 'white', fontSize: '28px' }} />\r\n        <style>{`\r\n          @keyframes pulse {\r\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\r\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\r\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\r\n          }\r\n        `}</style>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        position: 'fixed',\r\n        bottom: isMaximized ? '10px' : '20px',\r\n        right: isMaximized ? '10px' : '20px',\r\n        top: isMaximized ? '10px' : 'auto',\r\n        left: isMaximized ? '10px' : 'auto',\r\n        width: isMinimized ? '300px' : isMaximized ? 'calc(100vw - 20px)' : '380px',\r\n        height: isMinimized ? '60px' : isMaximized ? 'calc(100vh - 20px)' : '500px',\r\n        background: 'rgba(255, 255, 255, 0.95)',\r\n        backdropFilter: 'blur(20px)',\r\n        borderRadius: isMaximized ? '15px' : '20px',\r\n        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\r\n        border: '1px solid rgba(255, 255, 255, 0.2)',\r\n        zIndex: 1000,\r\n        display: 'flex',\r\n        flexDirection: 'column',\r\n        overflow: 'hidden',\r\n        transition: 'all 0.3s ease'\r\n      }}\r\n      onWheel={(e) => {\r\n        // Only prevent background scrolling when AI chat is open and not minimized\r\n        if (isOpen && !isMinimized) {\r\n          e.stopPropagation();\r\n        }\r\n      }}\r\n      onTouchMove={(e) => {\r\n        // Only prevent background scrolling when AI chat is open and not minimized\r\n        if (isOpen && !isMinimized) {\r\n          e.stopPropagation();\r\n        }\r\n      }}\r\n    >\r\n      <div\r\n        style={{\r\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n          padding: '16px 20px',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'space-between',\r\n          borderRadius: isMaximized ? '15px 15px 0 0' : '20px 20px 0 0'\r\n        }}\r\n      >\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\r\n          <div style={{ width: '32px', height: '32px', background: 'rgba(255, 255, 255, 0.2)', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\r\n            <TbRobot style={{ color: 'white', fontSize: '18px' }} />\r\n          </div>\r\n          <div>\r\n            <h3 style={{ margin: 0, color: 'white', fontSize: '16px', fontWeight: '600' }}>Brainwave AI</h3>\r\n            <p style={{ margin: 0, color: 'rgba(255, 255, 255, 0.8)', fontSize: '12px' }}>Always here to help</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div style={{ display: 'flex', gap: '8px' }}>\r\n          <button onClick={() => setIsMinimized(!isMinimized)} style={{ background: 'rgba(255, 255, 255, 0.25)', border: 'none', borderRadius: '8px', width: '32px', height: '32px', display: 'flex', alignItems: 'center', justifyContent: 'center', cursor: 'pointer', transition: 'all 0.2s ease', backdropFilter: 'blur(10px)' }}>\r\n            <span style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>{isMinimized ? '⬆' : '➖'}</span>\r\n          </button>\r\n\r\n          <button onClick={() => { setIsMaximized(!isMaximized); if (isMinimized) setIsMinimized(false); }} style={{ background: 'rgba(255, 255, 255, 0.25)', border: 'none', borderRadius: '8px', width: '32px', height: '32px', display: 'flex', alignItems: 'center', justifyContent: 'center', cursor: 'pointer', transition: 'all 0.2s ease', backdropFilter: 'blur(10px)' }}>\r\n            <span style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>{isMaximized ? '⬇' : '⬆'}</span>\r\n          </button>\r\n\r\n          <button onClick={() => setIsOpen(false)} style={{ background: 'rgba(255, 255, 255, 0.25)', border: 'none', borderRadius: '8px', width: '32px', height: '32px', display: 'flex', alignItems: 'center', justifyContent: 'center', cursor: 'pointer', transition: 'all 0.2s ease', backdropFilter: 'blur(10px)' }}>\r\n            <span style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>✕</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {!isMinimized && (\r\n        <>\r\n          <div\r\n            style={{\r\n              flex: 1,\r\n              padding: '20px 20px 0 20px',\r\n              overflowY: 'auto',\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              gap: '16px',\r\n              scrollBehavior: 'smooth',\r\n              scrollbarWidth: 'thin',\r\n              scrollbarColor: '#cbd5e1 transparent'\r\n            }}\r\n            onWheel={(e) => {\r\n              const element = e.currentTarget;\r\n              const { scrollTop, scrollHeight, clientHeight } = element;\r\n\r\n              // Allow scrolling within the messages container\r\n              if (scrollTop > 0 && scrollTop + clientHeight < scrollHeight) {\r\n                // We're in the middle, allow normal scrolling\r\n                return;\r\n              }\r\n\r\n              // At the boundaries, prevent propagation to background\r\n              if ((scrollTop === 0 && e.deltaY < 0) ||\r\n                  (scrollTop + clientHeight >= scrollHeight && e.deltaY > 0)) {\r\n                e.preventDefault();\r\n                e.stopPropagation();\r\n              }\r\n            }}\r\n            className=\"custom-scrollbar\"\r\n          >\r\n            {messages.map((msg, index) => (\r\n              <div key={index} style={{ display: 'flex', justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start' }}>\r\n                <div style={{ maxWidth: '85%', padding: '12px 16px', borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px', background: msg.role === 'user' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f8fafc', color: msg.role === 'user' ? 'white' : '#334155', fontSize: '14px', lineHeight: '1.5', boxShadow: msg.role === 'user' ? '0 4px 12px rgba(102, 126, 234, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)', wordWrap: 'break-word' }}>\r\n                  {typeof msg.content === 'string' ? (\r\n                    msg.role === 'assistant' ? (\r\n                      <ContentRenderer text={msg.content} />\r\n                    ) : (\r\n                      <div style={{ whiteSpace: 'pre-wrap' }}>{msg.content}</div>\r\n                    )\r\n                  ) : Array.isArray(msg.content) ? (\r\n                    msg.content.map((item, idx) => (\r\n                      <div key={idx}>\r\n                        {item.type === 'text' && (\r\n                          <div style={{ marginBottom: item.text ? '8px' : '0' }}>\r\n                            {msg.role === 'assistant' ? (\r\n                              <ContentRenderer text={item.text} />\r\n                            ) : (\r\n                              <div style={{ whiteSpace: 'pre-wrap' }}>{item.text}</div>\r\n                            )}\r\n                          </div>\r\n                        )}\r\n                        {item.type === 'image_url' && <img src={item.image_url.url} alt=\"User upload\" style={{ maxWidth: '100%', height: 'auto', borderRadius: '12px', maxHeight: '200px', objectFit: 'cover', border: '2px solid rgba(255, 255, 255, 0.2)', marginTop: '4px' }} />}\r\n                      </div>\r\n                    ))\r\n                  ) : (\r\n                    <div>Invalid message format</div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            ))}\r\n\r\n            {isTyping && (\r\n              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>\r\n                <div style={{ padding: '12px 16px', borderRadius: '18px 18px 18px 4px', background: '#f8fafc', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)' }}>\r\n                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>\r\n                    <div style={{ width: '6px', height: '6px', borderRadius: '50%', background: '#94a3b8', animation: 'bounce 1.4s infinite ease-in-out' }} />\r\n                    <div style={{ width: '6px', height: '6px', borderRadius: '50%', background: '#94a3b8', animation: 'bounce 1.4s infinite ease-in-out 0.16s' }} />\r\n                    <div style={{ width: '6px', height: '6px', borderRadius: '50%', background: '#94a3b8', animation: 'bounce 1.4s infinite ease-in-out 0.32s' }} />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div ref={messagesEndRef} />\r\n          </div>\r\n\r\n          <div style={{ position: 'sticky', bottom: 0, background: 'rgba(255, 255, 255, 0.98)', backdropFilter: 'blur(20px)', borderTop: '1px solid rgba(0, 0, 0, 0.08)', padding: '16px 20px 20px', zIndex: 10 }}>\r\n            {imagePreview && (\r\n              <div style={{ marginBottom: '12px', padding: '12px', background: '#f1f5f9', borderRadius: '12px', border: '1px solid #e2e8f0' }}>\r\n                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\r\n                  <div style={{ position: 'relative' }}>\r\n                    <img src={imagePreview} alt=\"Preview\" style={{ width: '60px', height: '60px', objectFit: 'cover', borderRadius: '8px', border: '2px solid #e2e8f0' }} />\r\n                    <button onClick={removeImage} style={{ position: 'absolute', top: '-6px', right: '-6px', width: '20px', height: '20px', background: '#ef4444', color: 'white', borderRadius: '50%', border: 'none', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px', fontWeight: 'bold' }}>×</button>\r\n                  </div>\r\n                  <div style={{ flex: 1 }}>\r\n                    <p style={{ fontSize: '13px', fontWeight: '600', color: '#374151', margin: 0 }}>Image attached</p>\r\n                    <p style={{ fontSize: '11px', color: '#6b7280', margin: 0 }}>{selectedImage?.name}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div style={{ display: 'flex', gap: '8px', background: '#f8fafc', borderRadius: '16px', padding: '8px', border: '2px solid #e2e8f0' }}>\r\n              <button onClick={() => fileInputRef.current?.click()} style={{ background: '#3b82f6', border: 'none', borderRadius: '12px', width: '40px', height: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center', cursor: 'pointer', transition: 'all 0.2s ease', boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)' }} title=\"Attach image\">\r\n                <span style={{ color: 'white', fontSize: '20px', fontWeight: 'bold' }}>+</span>\r\n              </button>\r\n\r\n              <input type=\"text\" value={input} onChange={(e) => setInput(e.target.value)} onKeyDown={handleKeyDown} placeholder=\"Ask me anything...\" style={{ flex: 1, border: 'none', background: 'transparent', outline: 'none', fontSize: '14px', color: '#334155', padding: '12px 16px', fontFamily: 'inherit' }} />\r\n\r\n              <button onClick={sendMessage} disabled={!input.trim() && !selectedImage} style={{ background: (input.trim() || selectedImage) ? '#3b82f6' : '#e2e8f0', border: 'none', borderRadius: '12px', width: '40px', height: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center', cursor: (input.trim() || selectedImage) ? 'pointer' : 'not-allowed', transition: 'all 0.2s ease', boxShadow: (input.trim() || selectedImage) ? '0 2px 8px rgba(59, 130, 246, 0.3)' : 'none' }}>\r\n                <span style={{ color: (input.trim() || selectedImage) ? 'white' : '#94a3b8', fontSize: '18px', fontWeight: 'bold' }}>➤</span>\r\n              </button>\r\n            </div>\r\n\r\n            <input ref={fileInputRef} type=\"file\" accept=\"image/*\" onChange={handleImageSelect} style={{ display: 'none' }} />\r\n\r\n            <p style={{ fontSize: '11px', color: '#94a3b8', textAlign: 'center', margin: '8px 0 0 0' }}>Press Enter to send • Attach images for analysis</p>\r\n          </div>\r\n        </>\r\n      )}\r\n\r\n      <style>{`\r\n        @keyframes bounce {\r\n          0%, 80%, 100% { transform: scale(0); }\r\n          40% { transform: scale(1); }\r\n        }\r\n\r\n        .custom-scrollbar::-webkit-scrollbar {\r\n          width: 6px;\r\n        }\r\n\r\n        .custom-scrollbar::-webkit-scrollbar-track {\r\n          background: transparent;\r\n        }\r\n\r\n        .custom-scrollbar::-webkit-scrollbar-thumb {\r\n          background: #cbd5e1;\r\n          border-radius: 3px;\r\n        }\r\n\r\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n          background: #94a3b8;\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FloatingBrainwaveAI;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,eAAe,EAAEC,SAAS,QAAQ,kBAAkB;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAACqB,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAClE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAACqB,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAAI,CAAC;;EAE9F;EACApB,SAAS,CAAC,MAAM;IACd,MAAMuB,YAAY,GAAGA,CAAA,KAAM;MACzBL,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;MACrCE,WAAW,CAACH,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAAI,CAAC;IACnE,CAAC;IAEDD,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMJ,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,CACvC;IAAE8B,IAAI,EAAE,WAAW;IAAEC,OAAO,EAAE;EAAyE,CAAC,CACzG,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMwC,cAAc,GAAGvC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMwC,YAAY,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAEjCC,SAAS,CAAC,MAAM;IACd,IAAIsC,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;EAEd,MAAMiB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1Cd,gBAAgB,CAACU,IAAI,CAAC;MACtB,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKhB,eAAe,CAACgB,CAAC,CAACP,MAAM,CAACQ,MAAM,CAAC;MACvDJ,MAAM,CAACK,aAAa,CAACV,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBrB,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIE,YAAY,CAACC,OAAO,EAAE;MACxBD,YAAY,CAACC,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC5B,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAACzB,aAAa,EAAE;IAErC,MAAM0B,WAAW,GAAG9B,KAAK,CAAC6B,IAAI,CAAC,CAAC;IAChC,MAAME,SAAS,GAAG3B,aAAa;IAE/BH,QAAQ,CAAC,EAAE,CAAC;IACZyB,WAAW,CAAC,CAAC;IACbvB,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,IAAI6B,QAAQ,GAAG,IAAI;MAEnB,IAAID,SAAS,EAAE;QACb,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,SAAS,CAAC;QACnC,MAAMK,YAAY,GAAG,MAAM/D,SAAS,CAAC4D,QAAQ,CAAC;QAE9C,IAAIG,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,OAAO,EAAE;UACzBL,QAAQ,GAAGI,YAAY,CAACE,GAAG;QAC7B;MACF;MAEA,MAAMC,cAAc,GAAGP,QAAQ,GAC3B;QACElC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEmB,IAAI,EAAE,MAAM;UAAEsB,IAAI,EAAEV;QAAY,CAAC,EACnC;UAAEZ,IAAI,EAAE,WAAW;UAAEuB,SAAS,EAAE;YAAEH,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACD;QAAElC,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAE+B;MAAY,CAAC;MAE1CjC,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,cAAc,CAAC,CAAC;MAE9C,MAAMI,QAAQ,GAAG,MAAMvE,eAAe,CAAC;QACrCwB,QAAQ,EAAE,CAAC,GAAGA,QAAQ,EAAE2C,cAAc;MACxC,CAAC,CAAC;MAEF,IAAII,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEN,OAAO,IAAIM,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,IAAI,EAAE;QACvC/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5B5C,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE4C,QAAQ,CAACC;QACpB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5B5C,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdhD,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5B5C,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRI,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM2C,aAAa,GAAIvB,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACwB,GAAG,KAAK,OAAO,IAAI,CAACxB,CAAC,CAACyB,QAAQ,EAAE;MACpCzB,CAAC,CAAC0B,cAAc,CAAC,CAAC;MAClBrB,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,IAAI,CAAC/C,MAAM,EAAE;IACX,oBACEL,OAAA;MACE0E,OAAO,EAAEA,CAAA,KAAMpE,SAAS,CAAC,IAAI,CAAE;MAC/BqE,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,mDAAmD;QAC/DC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,qCAAqC;QAChDC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,gBAEF3F,OAAA,CAACL,OAAO;QAACgF,KAAK,EAAE;UAAEiB,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxDjG,OAAA;QAAA2F,QAAA,EAAS;AACjB;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACEjG,OAAA;IACE2E,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAEpE,WAAW,GAAG,MAAM,GAAG,MAAM;MACrCqE,KAAK,EAAErE,WAAW,GAAG,MAAM,GAAG,MAAM;MACpCyF,GAAG,EAAEzF,WAAW,GAAG,MAAM,GAAG,MAAM;MAClC0F,IAAI,EAAE1F,WAAW,GAAG,MAAM,GAAG,MAAM;MACnCsE,KAAK,EAAExE,WAAW,GAAG,OAAO,GAAGE,WAAW,GAAG,oBAAoB,GAAG,OAAO;MAC3EuE,MAAM,EAAEzE,WAAW,GAAG,MAAM,GAAGE,WAAW,GAAG,oBAAoB,GAAG,OAAO;MAC3EwE,UAAU,EAAE,2BAA2B;MACvCmB,cAAc,EAAE,YAAY;MAC5BlB,YAAY,EAAEzE,WAAW,GAAG,MAAM,GAAG,MAAM;MAC3C8E,SAAS,EAAE,iCAAiC;MAC5Cc,MAAM,EAAE,oCAAoC;MAC5Cb,MAAM,EAAE,IAAI;MACZL,OAAO,EAAE,MAAM;MACfmB,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE,QAAQ;MAClBd,UAAU,EAAE;IACd,CAAE;IACFe,OAAO,EAAGzD,CAAC,IAAK;MACd;MACA,IAAI1C,MAAM,IAAI,CAACE,WAAW,EAAE;QAC1BwC,CAAC,CAAC0D,eAAe,CAAC,CAAC;MACrB;IACF,CAAE;IACFC,WAAW,EAAG3D,CAAC,IAAK;MAClB;MACA,IAAI1C,MAAM,IAAI,CAACE,WAAW,EAAE;QAC1BwC,CAAC,CAAC0D,eAAe,CAAC,CAAC;MACrB;IACF,CAAE;IAAAd,QAAA,gBAEF3F,OAAA;MACE2E,KAAK,EAAE;QACLM,UAAU,EAAE,mDAAmD;QAC/D0B,OAAO,EAAE,WAAW;QACpBxB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BH,YAAY,EAAEzE,WAAW,GAAG,eAAe,GAAG;MAChD,CAAE;MAAAkF,QAAA,gBAEF3F,OAAA;QAAK2E,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEwB,GAAG,EAAE;QAAO,CAAE;QAAAjB,QAAA,gBACjE3F,OAAA;UAAK2E,KAAK,EAAE;YAAEI,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,UAAU,EAAE,0BAA0B;YAAEC,YAAY,EAAE,KAAK;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAM,QAAA,eAC1K3F,OAAA,CAACL,OAAO;YAACgF,KAAK,EAAE;cAAEiB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNjG,OAAA;UAAA2F,QAAA,gBACE3F,OAAA;YAAI2E,KAAK,EAAE;cAAEkC,MAAM,EAAE,CAAC;cAAEjB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE;YAAM,CAAE;YAAAnB,QAAA,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChGjG,OAAA;YAAG2E,KAAK,EAAE;cAAEkC,MAAM,EAAE,CAAC;cAAEjB,KAAK,EAAE,0BAA0B;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjG,OAAA;QAAK2E,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEyB,GAAG,EAAE;QAAM,CAAE;QAAAjB,QAAA,gBAC1C3F,OAAA;UAAQ0E,OAAO,EAAEA,CAAA,KAAMlE,cAAc,CAAC,CAACD,WAAW,CAAE;UAACoE,KAAK,EAAE;YAAEM,UAAU,EAAE,2BAA2B;YAAEoB,MAAM,EAAE,MAAM;YAAEnB,YAAY,EAAE,KAAK;YAAEH,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE,QAAQ;YAAEC,MAAM,EAAE,SAAS;YAAEG,UAAU,EAAE,eAAe;YAAEW,cAAc,EAAE;UAAa,CAAE;UAAAT,QAAA,eACzT3F,OAAA;YAAM2E,KAAK,EAAE;cAAEiB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE;YAAO,CAAE;YAAAnB,QAAA,EAAEpF,WAAW,GAAG,GAAG,GAAG;UAAG;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC,eAETjG,OAAA;UAAQ0E,OAAO,EAAEA,CAAA,KAAM;YAAEhE,cAAc,CAAC,CAACD,WAAW,CAAC;YAAE,IAAIF,WAAW,EAAEC,cAAc,CAAC,KAAK,CAAC;UAAE,CAAE;UAACmE,KAAK,EAAE;YAAEM,UAAU,EAAE,2BAA2B;YAAEoB,MAAM,EAAE,MAAM;YAAEnB,YAAY,EAAE,KAAK;YAAEH,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE,QAAQ;YAAEC,MAAM,EAAE,SAAS;YAAEG,UAAU,EAAE,eAAe;YAAEW,cAAc,EAAE;UAAa,CAAE;UAAAT,QAAA,eACtW3F,OAAA;YAAM2E,KAAK,EAAE;cAAEiB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE;YAAO,CAAE;YAAAnB,QAAA,EAAElF,WAAW,GAAG,GAAG,GAAG;UAAG;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC,eAETjG,OAAA;UAAQ0E,OAAO,EAAEA,CAAA,KAAMpE,SAAS,CAAC,KAAK,CAAE;UAACqE,KAAK,EAAE;YAAEM,UAAU,EAAE,2BAA2B;YAAEoB,MAAM,EAAE,MAAM;YAAEnB,YAAY,EAAE,KAAK;YAAEH,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEG,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE,QAAQ;YAAEC,MAAM,EAAE,SAAS;YAAEG,UAAU,EAAE,eAAe;YAAEW,cAAc,EAAE;UAAa,CAAE;UAAAT,QAAA,eAC7S3F,OAAA;YAAM2E,KAAK,EAAE;cAAEiB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE;YAAO,CAAE;YAAAnB,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAAC1F,WAAW,iBACXP,OAAA,CAAAE,SAAA;MAAAyF,QAAA,gBACE3F,OAAA;QACE2E,KAAK,EAAE;UACLoC,IAAI,EAAE,CAAC;UACPJ,OAAO,EAAE,kBAAkB;UAC3BK,SAAS,EAAE,MAAM;UACjB7B,OAAO,EAAE,MAAM;UACfmB,aAAa,EAAE,QAAQ;UACvBM,GAAG,EAAE,MAAM;UACXK,cAAc,EAAE,QAAQ;UACxBC,cAAc,EAAE,MAAM;UACtBC,cAAc,EAAE;QAClB,CAAE;QACFX,OAAO,EAAGzD,CAAC,IAAK;UACd,MAAMqE,OAAO,GAAGrE,CAAC,CAACsE,aAAa;UAC/B,MAAM;YAAEC,SAAS;YAAEC,YAAY;YAAEC;UAAa,CAAC,GAAGJ,OAAO;;UAEzD;UACA,IAAIE,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAGE,YAAY,GAAGD,YAAY,EAAE;YAC5D;YACA;UACF;;UAEA;UACA,IAAKD,SAAS,KAAK,CAAC,IAAIvE,CAAC,CAAC0E,MAAM,GAAG,CAAC,IAC/BH,SAAS,GAAGE,YAAY,IAAID,YAAY,IAAIxE,CAAC,CAAC0E,MAAM,GAAG,CAAE,EAAE;YAC9D1E,CAAC,CAAC0B,cAAc,CAAC,CAAC;YAClB1B,CAAC,CAAC0D,eAAe,CAAC,CAAC;UACrB;QACF,CAAE;QACFiB,SAAS,EAAC,kBAAkB;QAAA/B,QAAA,GAE3BvE,QAAQ,CAACuG,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvB7H,OAAA;UAAiB2E,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAEuC,GAAG,CAACtG,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG;UAAa,CAAE;UAAAqE,QAAA,eAC3G3F,OAAA;YAAK2E,KAAK,EAAE;cAAEmD,QAAQ,EAAE,KAAK;cAAEnB,OAAO,EAAE,WAAW;cAAEzB,YAAY,EAAE0C,GAAG,CAACtG,IAAI,KAAK,MAAM,GAAG,oBAAoB,GAAG,oBAAoB;cAAE2D,UAAU,EAAE2C,GAAG,CAACtG,IAAI,KAAK,MAAM,GAAG,mDAAmD,GAAG,SAAS;cAAEsE,KAAK,EAAEgC,GAAG,CAACtG,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,SAAS;cAAEuE,QAAQ,EAAE,MAAM;cAAEkC,UAAU,EAAE,KAAK;cAAExC,SAAS,EAAEqC,GAAG,CAACtG,IAAI,KAAK,MAAM,GAAG,qCAAqC,GAAG,8BAA8B;cAAE0G,QAAQ,EAAE;YAAa,CAAE;YAAArC,QAAA,EAC/b,OAAOiC,GAAG,CAACrG,OAAO,KAAK,QAAQ,GAC9BqG,GAAG,CAACtG,IAAI,KAAK,WAAW,gBACtBtB,OAAA,CAACF,eAAe;cAACkE,IAAI,EAAE4D,GAAG,CAACrG;YAAQ;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEtCjG,OAAA;cAAK2E,KAAK,EAAE;gBAAEsD,UAAU,EAAE;cAAW,CAAE;cAAAtC,QAAA,EAAEiC,GAAG,CAACrG;YAAO;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAC3D,GACCiC,KAAK,CAACC,OAAO,CAACP,GAAG,CAACrG,OAAO,CAAC,GAC5BqG,GAAG,CAACrG,OAAO,CAACoG,GAAG,CAAC,CAACS,IAAI,EAAEC,GAAG,kBACxBrI,OAAA;cAAA2F,QAAA,GACGyC,IAAI,CAAC1F,IAAI,KAAK,MAAM,iBACnB1C,OAAA;gBAAK2E,KAAK,EAAE;kBAAE2D,YAAY,EAAEF,IAAI,CAACpE,IAAI,GAAG,KAAK,GAAG;gBAAI,CAAE;gBAAA2B,QAAA,EACnDiC,GAAG,CAACtG,IAAI,KAAK,WAAW,gBACvBtB,OAAA,CAACF,eAAe;kBAACkE,IAAI,EAAEoE,IAAI,CAACpE;gBAAK;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEpCjG,OAAA;kBAAK2E,KAAK,EAAE;oBAAEsD,UAAU,EAAE;kBAAW,CAAE;kBAAAtC,QAAA,EAAEyC,IAAI,CAACpE;gBAAI;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACzD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EACAmC,IAAI,CAAC1F,IAAI,KAAK,WAAW,iBAAI1C,OAAA;gBAAKuI,GAAG,EAAEH,IAAI,CAACnE,SAAS,CAACH,GAAI;gBAAC0E,GAAG,EAAC,aAAa;gBAAC7D,KAAK,EAAE;kBAAEmD,QAAQ,EAAE,MAAM;kBAAE9C,MAAM,EAAE,MAAM;kBAAEE,YAAY,EAAE,MAAM;kBAAEuD,SAAS,EAAE,OAAO;kBAAEC,SAAS,EAAE,OAAO;kBAAErC,MAAM,EAAE,oCAAoC;kBAAEsC,SAAS,EAAE;gBAAM;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,GAVnPoC,GAAG;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWR,CACN,CAAC,gBAEFjG,OAAA;cAAA2F,QAAA,EAAK;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACjC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA1BE4B,KAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BV,CACN,CAAC,EAEDvE,QAAQ,iBACP1B,OAAA;UAAK2E,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE;UAAa,CAAE;UAAAM,QAAA,eAC5D3F,OAAA;YAAK2E,KAAK,EAAE;cAAEgC,OAAO,EAAE,WAAW;cAAEzB,YAAY,EAAE,oBAAoB;cAAED,UAAU,EAAE,SAAS;cAAEM,SAAS,EAAE;YAA+B,CAAE;YAAAI,QAAA,eACzI3F,OAAA;cAAK2E,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAEyB,GAAG,EAAE,KAAK;gBAAExB,UAAU,EAAE;cAAS,CAAE;cAAAO,QAAA,gBAChE3F,OAAA;gBAAK2E,KAAK,EAAE;kBAAEI,KAAK,EAAE,KAAK;kBAAEC,MAAM,EAAE,KAAK;kBAAEE,YAAY,EAAE,KAAK;kBAAED,UAAU,EAAE,SAAS;kBAAES,SAAS,EAAE;gBAAmC;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1IjG,OAAA;gBAAK2E,KAAK,EAAE;kBAAEI,KAAK,EAAE,KAAK;kBAAEC,MAAM,EAAE,KAAK;kBAAEE,YAAY,EAAE,KAAK;kBAAED,UAAU,EAAE,SAAS;kBAAES,SAAS,EAAE;gBAAyC;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChJjG,OAAA;gBAAK2E,KAAK,EAAE;kBAAEI,KAAK,EAAE,KAAK;kBAAEC,MAAM,EAAE,KAAK;kBAAEE,YAAY,EAAE,KAAK;kBAAED,UAAU,EAAE,SAAS;kBAAES,SAAS,EAAE;gBAAyC;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDjG,OAAA;UAAK4I,GAAG,EAAE5G;QAAe;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAENjG,OAAA;QAAK2E,KAAK,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE,CAAC;UAAEI,UAAU,EAAE,2BAA2B;UAAEmB,cAAc,EAAE,YAAY;UAAEyC,SAAS,EAAE,+BAA+B;UAAElC,OAAO,EAAE,gBAAgB;UAAEnB,MAAM,EAAE;QAAG,CAAE;QAAAG,QAAA,GACrM7D,YAAY,iBACX9B,OAAA;UAAK2E,KAAK,EAAE;YAAE2D,YAAY,EAAE,MAAM;YAAE3B,OAAO,EAAE,MAAM;YAAE1B,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE,MAAM;YAAEmB,MAAM,EAAE;UAAoB,CAAE;UAAAV,QAAA,eAC9H3F,OAAA;YAAK2E,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEwB,GAAG,EAAE;YAAO,CAAE;YAAAjB,QAAA,gBACjE3F,OAAA;cAAK2E,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAe,QAAA,gBACnC3F,OAAA;gBAAKuI,GAAG,EAAEzG,YAAa;gBAAC0G,GAAG,EAAC,SAAS;gBAAC7D,KAAK,EAAE;kBAAEI,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAE0D,SAAS,EAAE,OAAO;kBAAExD,YAAY,EAAE,KAAK;kBAAEmB,MAAM,EAAE;gBAAoB;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxJjG,OAAA;gBAAQ0E,OAAO,EAAExB,WAAY;gBAACyB,KAAK,EAAE;kBAAEC,QAAQ,EAAE,UAAU;kBAAEsB,GAAG,EAAE,MAAM;kBAAEpB,KAAK,EAAE,MAAM;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,UAAU,EAAE,SAAS;kBAAEW,KAAK,EAAE,OAAO;kBAAEV,YAAY,EAAE,KAAK;kBAAEmB,MAAM,EAAE,MAAM;kBAAEf,MAAM,EAAE,SAAS;kBAAEH,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,cAAc,EAAE,QAAQ;kBAAEQ,QAAQ,EAAE,MAAM;kBAAEiB,UAAU,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvU,CAAC,eACNjG,OAAA;cAAK2E,KAAK,EAAE;gBAAEoC,IAAI,EAAE;cAAE,CAAE;cAAApB,QAAA,gBACtB3F,OAAA;gBAAG2E,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEiB,UAAU,EAAE,KAAK;kBAAElB,KAAK,EAAE,SAAS;kBAAEiB,MAAM,EAAE;gBAAE,CAAE;gBAAAlB,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClGjG,OAAA;gBAAG2E,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAED,KAAK,EAAE,SAAS;kBAAEiB,MAAM,EAAE;gBAAE,CAAE;gBAAAlB,QAAA,EAAE/D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkH;cAAI;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDjG,OAAA;UAAK2E,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEyB,GAAG,EAAE,KAAK;YAAE3B,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE,MAAM;YAAEyB,OAAO,EAAE,KAAK;YAAEN,MAAM,EAAE;UAAoB,CAAE;UAAAV,QAAA,gBACpI3F,OAAA;YAAQ0E,OAAO,EAAEA,CAAA;cAAA,IAAAqE,qBAAA;cAAA,QAAAA,qBAAA,GAAM9G,YAAY,CAACC,OAAO,cAAA6G,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAACrE,KAAK,EAAE;cAAEM,UAAU,EAAE,SAAS;cAAEoB,MAAM,EAAE,MAAM;cAAEnB,YAAY,EAAE,MAAM;cAAEH,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE,MAAM;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,cAAc,EAAE,QAAQ;cAAEC,MAAM,EAAE,SAAS;cAAEG,UAAU,EAAE,eAAe;cAAEF,SAAS,EAAE;YAAoC,CAAE;YAAC0D,KAAK,EAAC,cAAc;YAAAtD,QAAA,eAChV3F,OAAA;cAAM2E,KAAK,EAAE;gBAAEiB,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE,MAAM;gBAAEiB,UAAU,EAAE;cAAO,CAAE;cAAAnB,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAETjG,OAAA;YAAO0C,IAAI,EAAC,MAAM;YAACS,KAAK,EAAE3B,KAAM;YAAC0H,QAAQ,EAAGnG,CAAC,IAAKtB,QAAQ,CAACsB,CAAC,CAACP,MAAM,CAACW,KAAK,CAAE;YAACgG,SAAS,EAAE7E,aAAc;YAAC8E,WAAW,EAAC,oBAAoB;YAACzE,KAAK,EAAE;cAAEoC,IAAI,EAAE,CAAC;cAAEV,MAAM,EAAE,MAAM;cAAEpB,UAAU,EAAE,aAAa;cAAEoE,OAAO,EAAE,MAAM;cAAExD,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE,SAAS;cAAEe,OAAO,EAAE,WAAW;cAAE2C,UAAU,EAAE;YAAU;UAAE;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1SjG,OAAA;YAAQ0E,OAAO,EAAEtB,WAAY;YAACmG,QAAQ,EAAE,CAAC/H,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAACzB,aAAc;YAAC+C,KAAK,EAAE;cAAEM,UAAU,EAAGzD,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GAAI,SAAS,GAAG,SAAS;cAAEyE,MAAM,EAAE,MAAM;cAAEnB,YAAY,EAAE,MAAM;cAAEH,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE,MAAM;cAAEG,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,cAAc,EAAE,QAAQ;cAAEC,MAAM,EAAG9D,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GAAI,SAAS,GAAG,aAAa;cAAE6D,UAAU,EAAE,eAAe;cAAEF,SAAS,EAAG/D,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GAAI,mCAAmC,GAAG;YAAO,CAAE;YAAA+D,QAAA,eACzd3F,OAAA;cAAM2E,KAAK,EAAE;gBAAEiB,KAAK,EAAGpE,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GAAI,OAAO,GAAG,SAAS;gBAAEiE,QAAQ,EAAE,MAAM;gBAAEiB,UAAU,EAAE;cAAO,CAAE;cAAAnB,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENjG,OAAA;UAAO4I,GAAG,EAAE3G,YAAa;UAACS,IAAI,EAAC,MAAM;UAAC8G,MAAM,EAAC,SAAS;UAACN,QAAQ,EAAE7G,iBAAkB;UAACsC,KAAK,EAAE;YAAEQ,OAAO,EAAE;UAAO;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAElHjG,OAAA;UAAG2E,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,SAAS;YAAE6D,SAAS,EAAE,QAAQ;YAAE5C,MAAM,EAAE;UAAY,CAAE;UAAAlB,QAAA,EAAC;QAAgD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7I,CAAC;IAAA,eACN,CACH,eAEDjG,OAAA;MAAA2F,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7F,EAAA,CAxWID,mBAAmB;AAAAuJ,EAAA,GAAnBvJ,mBAAmB;AA0WzB,eAAeA,mBAAmB;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}