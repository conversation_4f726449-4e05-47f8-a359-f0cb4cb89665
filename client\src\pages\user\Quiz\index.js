import React, { useState, useEffect, useCallback, startTransition } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { message } from 'antd';
import {
  Tb<PERSON>earch,
  Tb<PERSON><PERSON><PERSON>,
  TbClock,
  TbQuestionMark,
  TbTrophy,
  TbPlayerPlay,
  TbBrain,
  TbTarget,
  TbCheck,
  TbX,
  TbStar,
  TbHome,
  TbBolt,
  TbRefresh
} from 'react-icons/tb';
import { getAllExams } from '../../../apicalls/exams';
import { getAllReportsByUser } from '../../../apicalls/reports';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import './animations.css';

const Quiz = () => {
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [userResults, setUserResults] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  const getUserResults = useCallback(async () => {
    try {
      if (!user?._id) return;

      const response = await getAllReportsByUser({ userId: user._id });

      if (response.success) {
        const resultsMap = {};
        response.data.forEach(report => {
          const examId = report.exam?._id;
          if (!examId || !report.result) return;

          // Extract data from the result object
          const result = report.result;

          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {
            resultsMap[examId] = {
              verdict: result.verdict,
              percentage: result.percentage,
              correctAnswers: result.correctAnswers,
              wrongAnswers: result.wrongAnswers,
              totalQuestions: result.totalQuestions,
              obtainedMarks: result.obtainedMarks,
              totalMarks: result.totalMarks,
              score: result.score,
              points: result.points,
              xpEarned: result.xpEarned || result.points || result.xpGained || 0,
              timeTaken: report.timeTaken,
              completedAt: report.createdAt,
            };
          }
        });
        setUserResults(resultsMap);
      }
    } catch (error) {
      console.error('Error fetching user results:', error);
    }
  }, [user?._id]);

  // Define getExams function outside useEffect so it can be called from other functions
  const getExams = useCallback(async (isRefresh = false) => {
      try {
        // Safety check: ensure user exists before proceeding
        if (!user) {
          console.log("User not loaded yet, skipping exam fetch");
          return;
        }

        // Check cache first (unless refreshing)
        if (!isRefresh) {
          const cachedExams = localStorage.getItem('user_exams_cache');
          const cacheTime = localStorage.getItem('user_exams_cache_time');
          const now = Date.now();

          // Use cache if less than 3 minutes old
          if (cachedExams && cacheTime && (now - parseInt(cacheTime)) < 180000) {
            const cached = JSON.parse(cachedExams);
            setExams(cached);
            setLastRefresh(new Date(parseInt(cacheTime)));
            if (user?.class) {
              setSelectedClass(String(user.class));
            }
            setLoading(false);
            return;
          }
        }

        if (isRefresh) {
          setRefreshing(true);
        } else {
          dispatch(ShowLoading());
        }

        const response = await getAllExams();

        if (isRefresh) {
          setRefreshing(false);
        } else {
          dispatch(HideLoading());
        }

        if (response.success) {
          console.log('Raw exams from API:', response.data.length);
          console.log('User level:', user?.level);

          // Filter exams by user's level with proper null checks
          const userLevelExams = response.data.filter(exam => {
            if (!exam.level || !user || !user.level) return false;
            return exam.level.toLowerCase() === user.level.toLowerCase();
          });

          console.log('User level exams after filtering:', userLevelExams.length);
          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
          setExams(sortedExams);
          setLastRefresh(new Date());

          // Cache the exams data
          localStorage.setItem('user_exams_cache', JSON.stringify(sortedExams));
          localStorage.setItem('user_exams_cache_time', Date.now().toString());

          // Set default class filter to user's class
          if (user?.class) {
            setSelectedClass(String(user.class));
          }
        } else {
          message.error(response.message);
        }
      } catch (error) {
        if (isRefresh) {
          setRefreshing(false);
        } else {
          dispatch(HideLoading());
        }
        message.error(error.message);
      } finally {
        setLoading(false);
      }
  }, [dispatch, user]);

  useEffect(() => {
    getExams(false); // Initial load
    getUserResults();
  }, [getExams, getUserResults]);

  // Real-time updates for quiz completion and new exams
  useEffect(() => {
    // Listen for real-time updates from quiz completion
    const handleRankingUpdate = () => {
      console.log('🔄 Quiz listing - refreshing data after quiz completion...');
      getUserResults(); // Refresh user results to show updated XP
    };

    // Listen for new exam creation events
    const handleNewExam = () => {
      console.log('🆕 New exam created - refreshing exam list...');
      if (user) {
        getExams(true); // Use refresh mode
        getUserResults();
      }
    };

    // Listen for window focus to refresh data when returning from quiz
    const handleWindowFocus = () => {
      console.log('🎯 Quiz listing - window focused, refreshing data...');
      getUserResults();
      // Also refresh exams list to show newly generated exams
      if (user) {
        console.log('🔄 Refreshing exams list for new exams...');
        getExams(true); // Use refresh mode
      }
    };

    window.addEventListener('rankingUpdate', handleRankingUpdate);
    window.addEventListener('focus', handleWindowFocus);
    window.addEventListener('newExamCreated', handleNewExam);

    return () => {
      window.removeEventListener('rankingUpdate', handleRankingUpdate);
      window.removeEventListener('focus', handleWindowFocus);
      window.removeEventListener('newExamCreated', handleNewExam);
    };
  }, []);

  // Periodic refresh to ensure quiz list stays up to date
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      if (user && !loading && !refreshing) {
        console.log('🔄 Periodic refresh of quiz list...');
        getExams(true); // Use refresh mode
      }
    }, 5 * 60 * 1000); // Refresh every 5 minutes

    return () => clearInterval(refreshInterval);
  }, [user, loading, refreshing]);

  useEffect(() => {
    console.log('Filtering exams:', { exams: exams.length, searchTerm, selectedClass });
    let filtered = exams;
    if (searchTerm) {
      filtered = filtered.filter(exam =>
        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    if (selectedClass) {
      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));
    }
    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    console.log('Filtered exams result:', filtered.length);
    setFilteredExams(filtered);
  }, [exams, searchTerm, selectedClass]);

  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();

  const handleQuizStart = (quiz) => {
    if (!quiz || !quiz._id) {
      message.error('Invalid quiz selected. Please try again.');
      return;
    }

    // Validate MongoDB ObjectId format (24 character hex string)
    const objectIdRegex = /^[0-9a-fA-F]{24}$/;
    if (!objectIdRegex.test(quiz._id)) {
      message.error('Invalid quiz ID format. Please try again.');
      return;
    }

    startTransition(() => {
      navigate(`/quiz/${quiz._id}/play`);
    });
  };

  // Manual refresh function
  const handleRefresh = async () => {
    console.log('🔄 Manual refresh triggered...');
    if (refreshing || loading) return; // Prevent multiple simultaneous refreshes

    try {
      if (user) {
        await getExams(true); // Use refresh mode
        await getUserResults();
        message.success('Quiz list refreshed successfully!');
      }
    } catch (error) {
      message.error('Failed to refresh quiz list');
    }
  };

  const handleQuizView = (quiz) => {
    if (!quiz || !quiz._id) {
      message.error('Invalid quiz selected. Please try again.');
      return;
    }
    // Check if user has attempted this quiz
    const userResult = userResults[quiz._id];
    if (!userResult) {
      message.info('You need to attempt this quiz first to view results.');
      return;
    }
    startTransition(() => {
      navigate(`/quiz/${quiz._id}/result`);
    });
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading quizzes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8">
        {/* Hero Section */}
        <div className="text-center mb-8 sm:mb-12 opacity-100">
          <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg">
            <TbBrain className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
          </div>
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4">
            Challenge Your Brain, Beat the Rest
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4">
            Test your knowledge with our comprehensive quizzes designed for You!
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>{filteredExams.length} Available Quizzes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>Level: {user?.level || 'All Levels'}</span>
            </div>
            {lastRefresh && (
              <div className="flex items-center gap-2 text-xs text-gray-400">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span>Updated: {lastRefresh.toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        </div>

        {/* Search and Filter */}
        <div className="max-w-4xl mx-auto mb-8 sm:mb-12 opacity-100">
          <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                  <TbSearch className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search quizzes by name or subject..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base"
                />
              </div>
              <div className="sm:w-48 md:w-64">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                    <TbFilter className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  </div>
                  <select
                    value={selectedClass}
                    onChange={(e) => setSelectedClass(e.target.value)}
                    className="block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base"
                  >
                    <option value="">All Classes</option>
                    {availableClasses.map((className) => (
                      <option key={className} value={className}>Class {className}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={loading || refreshing}
                className="flex items-center justify-center px-4 py-2.5 sm:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95"
                title="Refresh quiz list"
              >
                <TbRefresh className={`h-4 w-4 sm:h-5 sm:w-5 ${(loading || refreshing) ? 'animate-spin' : ''}`} />
                <span className="ml-2 hidden sm:inline text-sm sm:text-base">
                  {refreshing ? 'Refreshing...' : 'Refresh'}
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Quiz Grid */}
        <div className="opacity-100">


          {filteredExams.length === 0 ? (
            <div className="text-center py-12 sm:py-16">
              <div className="bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto">
                <TbTarget className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">No Quizzes Found</h3>
                <p className="text-gray-600 text-sm sm:text-base">
                  {searchTerm || selectedClass
                    ? "Try adjusting your search or filter criteria."
                    : "No quizzes are available for your level at the moment."
                  }
                </p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredExams.map((quiz, index) => (
                <QuizCard
                  key={quiz._id}
                  quiz={quiz}
                  userResult={userResults[quiz._id]}
                  showResults={true}
                  onStart={handleQuizStart}
                  onView={() => handleQuizView(quiz)}
                  index={index}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Simple QuizCard component without Framer Motion
const QuizCard = ({ quiz, userResult, onStart, onView, index }) => {
  const formatTime = (seconds) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatCompletionTime = (timeInSeconds) => {
    // Handle different possible time formats
    if (!timeInSeconds && timeInSeconds !== 0) return 'N/A';

    let totalSeconds = timeInSeconds;

    // If it's a string, try to parse it
    if (typeof timeInSeconds === 'string') {
      totalSeconds = parseInt(timeInSeconds, 10);
    }

    // If it's still not a valid number, return N/A
    if (isNaN(totalSeconds) || totalSeconds < 0) return 'N/A';

    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${timeInSeconds}s`;
  };

  // Safety checks for quiz object
  if (!quiz || typeof quiz !== 'object') {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <p className="text-gray-500">Invalid quiz data</p>
      </div>
    );
  }

  return (
    <div
      className="rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-500 p-8 transform hover:scale-110 opacity-100 relative border-4"
      style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',
        borderColor: userResult
          ? (userResult.verdict === 'Pass' ? '#10b981' : '#ef4444')
          : '#3b82f6',
        boxShadow: userResult
          ? (userResult.verdict === 'Pass'
              ? '0 20px 40px rgba(16, 185, 129, 0.3), 0 0 0 1px rgba(255,255,255,0.5)'
              : '0 20px 40px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(255,255,255,0.5)')
          : '0 20px 40px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(255,255,255,0.5)',
        backdropFilter: 'blur(10px)'
      }}
    >
      {/* Quiz Title - At Top */}
      <div className="mb-6 text-center">
        <h3
          className="text-2xl font-bold mb-4 line-clamp-2"
          style={{
            color: '#1f2937',
            textShadow: '0 2px 4px rgba(0,0,0,0.1)',
            lineHeight: '1.3'
          }}
        >
          {typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'}
        </h3>
      </div>

      {/* Status Tags - Centered */}
      <div className="mb-6 text-center">
        {userResult ? (
          <div className="flex items-center justify-center gap-3">
            <div
              className="px-4 py-2 rounded-full text-sm font-bold text-white shadow-lg border-2"
              style={{
                backgroundColor: userResult.verdict === 'Pass' ? '#10b981' : '#ef4444',
                borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5'
              }}
            >
              {userResult.verdict === 'Pass' ? '✅ PASSED' : '❌ FAILED'}
            </div>
            <div
              className="px-4 py-2 rounded-full text-sm font-bold text-center shadow-lg border-2"
              style={{
                backgroundColor: '#ffffff',
                color: '#1f2937',
                borderColor: '#d1d5db'
              }}
            >
              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}% • {userResult.xpEarned || userResult.points || 0} XP
            </div>
          </div>
        ) : (
          <div
            className="px-4 py-2 rounded-full text-sm font-bold text-white shadow-lg border-2"
            style={{
              backgroundColor: '#3b82f6',
              borderColor: '#60a5fa'
            }}
          >
            🆕 NOT ATTEMPTED
          </div>
        )}
      </div>

      <div className="text-center mb-6">
        <div className="flex-1">

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div
              className="flex flex-col items-center rounded-xl py-4 px-4 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              style={{
                background: 'linear-gradient(to bottom right, #eff6ff, #e0e7ff)',
                borderColor: '#bfdbfe'
              }}
            >
              <TbQuestionMark className="w-6 h-6 mb-2" style={{ color: '#2563eb' }} />
              <span className="text-xl font-bold" style={{ color: '#1e40af' }}>
                {Array.isArray(quiz.questions) ? quiz.questions.length : 0}
              </span>
              <span className="text-sm font-semibold" style={{ color: '#1d4ed8' }}>Questions</span>
            </div>
            <div
              className="flex flex-col items-center rounded-xl py-4 px-4 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              style={{
                background: 'linear-gradient(to bottom right, #fdf4ff, #fce7f3)',
                borderColor: '#e9d5ff'
              }}
            >
              <TbClock className="w-6 h-6 mb-2" style={{ color: '#9333ea' }} />
              <span className="text-xl font-bold" style={{ color: '#7c3aed' }}>
                {typeof quiz.duration === 'number' ? Math.round(quiz.duration / 60) : 0}m
              </span>
              <span className="text-sm font-semibold" style={{ color: '#8b5cf6' }}>Duration</span>
            </div>
          </div>

          {/* Border Line Separator */}
          <div
            className="w-full mb-6"
            style={{
              height: '3px',
              background: 'linear-gradient(to right, #3b82f6, #8b5cf6, #3b82f6)',
              borderRadius: '2px',
              boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)'
            }}
          ></div>

          <div className="flex items-center justify-center gap-3 flex-wrap">
            <span
              className="inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200"
              style={{
                background: 'linear-gradient(to right, #4ade80, #3b82f6)',
                borderColor: '#86efac'
              }}
            >
              📖 Class {typeof quiz.class === 'string' || typeof quiz.class === 'number' ? quiz.class : 'N/A'}
            </span>
            <span
              className="inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200"
              style={{
                background: 'linear-gradient(to right, #667eea, #764ba2)',
                borderColor: '#a78bfa'
              }}
            >
              📚 {quiz.subject}
            </span>
            {/* Topic Tag */}
            <span
              className="inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200"
              style={{
                background: 'linear-gradient(to right, #10b981, #059669)',
                borderColor: '#86efac'
              }}
            >
              📖 {quiz.topic || 'General'}
            </span>
            {quiz.xpPoints && (
              <span
                className="inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200"
                style={{
                  background: 'linear-gradient(to right, #fbbf24, #f97316)',
                  borderColor: '#fde047'
                }}
              >
                ⭐ {quiz.xpPoints} XP
              </span>
            )}
            {quiz.passingMarks && (
              <span
                className="inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200"
                style={{
                  background: 'linear-gradient(to right, #f87171, #ec4899)',
                  borderColor: '#fca5a5'
                }}
              >
                🎯 {quiz.passingMarks}% Pass
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Border Line Separator */}
      <div
        className="w-full mb-6"
        style={{
          height: '3px',
          background: 'linear-gradient(to right, #10b981, #059669, #10b981)',
          borderRadius: '2px',
          boxShadow: '0 2px 8px rgba(16, 185, 129, 0.3)'
        }}
      ></div>

      {userResult && typeof userResult === 'object' && (
        <div
          className="mb-6 p-6 rounded-2xl border-4 shadow-2xl transform hover:scale-102 transition-all duration-300"
          style={{
            background: userResult.verdict === 'Pass'
              ? 'linear-gradient(to bottom right, #f0fdf4, #ecfdf5, #ccfbf1)'
              : 'linear-gradient(to bottom right, #fef2f2, #fdf2f8, #fce7f3)',
            borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5',
            boxShadow: userResult.verdict === 'Pass'
              ? '0 25px 50px rgba(34, 197, 94, 0.25)'
              : '0 25px 50px rgba(239, 68, 68, 0.25)'
          }}
        >
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              {userResult.verdict === 'Pass' ? (
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse"
                  style={{
                    background: 'linear-gradient(to right, #10b981, #059669)',
                    borderColor: '#86efac'
                  }}
                >
                  <TbCheck className="w-6 h-6 font-bold" style={{ color: '#ffffff' }} />
                </div>
              ) : (
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse"
                  style={{
                    background: 'linear-gradient(to right, #ef4444, #dc2626)',
                    borderColor: '#fca5a5'
                  }}
                >
                  <TbX className="w-6 h-6 font-bold" style={{ color: '#ffffff' }} />
                </div>
              )}
              <div>
                <span className="text-lg font-bold" style={{ color: '#1f2937' }}>🏆 Last Result</span>
                <div className="text-sm" style={{ color: '#6b7280' }}>
                  {new Date(userResult.completedAt || userResult.createdAt || Date.now()).toLocaleDateString()}
                </div>
              </div>
            </div>
            <span
              className="text-3xl font-bold shadow-lg"
              style={{
                color: userResult.verdict === 'Pass' ? '#059669' : '#dc2626'
              }}
            >
              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%
            </span>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div
              className="text-center rounded-xl py-4 px-3 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              style={{
                background: 'linear-gradient(to bottom right, #dbeafe, #c7d2fe)',
                borderColor: '#93c5fd'
              }}
            >
              <TbTarget className="w-8 h-8 mx-auto mb-2" style={{ color: '#2563eb' }} />
              <div className="text-2xl font-bold" style={{ color: '#1e40af' }}>
                {typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0}
              </div>
              <div className="text-sm font-semibold" style={{ color: '#1d4ed8' }}>Correct</div>
            </div>
            <div
              className="text-center rounded-xl py-4 px-3 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              style={{
                background: 'linear-gradient(to bottom right, #fef3c7, #fed7aa)',
                borderColor: '#fde047'
              }}
            >
              <span className="text-3xl mb-2 block">⭐</span>
              <div className="text-2xl font-bold" style={{ color: '#92400e' }}>
                {userResult.xpEarned || userResult.points || 0}
              </div>
              <div className="text-sm font-semibold" style={{ color: '#a16207' }}>XP Earned</div>
            </div>
            <div
              className="text-center rounded-xl py-4 px-3 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              style={{
                background: 'linear-gradient(to bottom right, #e9d5ff, #f3e8ff)',
                borderColor: '#c4b5fd'
              }}
            >
              <TbClock className="w-8 h-8 mx-auto mb-2" style={{ color: '#9333ea' }} />
              <div className="text-2xl font-bold" style={{ color: '#7c3aed' }}>
                {formatCompletionTime(userResult.timeTaken)}
              </div>
              <div className="text-sm font-semibold" style={{ color: '#8b5cf6' }}>Completed</div>
            </div>
          </div>
        </div>
      )}

      <div className="flex gap-4">
        <button
          onClick={() => onStart(quiz)}
          className="flex-1 flex items-center justify-center gap-3 px-6 py-4 rounded-xl font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-110 active:scale-95 text-white border-2"
          style={{
            background: userResult
              ? 'linear-gradient(to right, #f97316, #ef4444, #ec4899)'
              : 'linear-gradient(to right, #3b82f6, #8b5cf6, #4338ca)',
            borderColor: userResult ? '#fb923c' : '#60a5fa'
          }}
        >
          <TbPlayerPlay className="w-6 h-6" />
          {userResult ? '🔄 Retake Quiz' : '🚀 Start Quiz'}
        </button>

        {userResult && (
          <button
            onClick={() => onView(quiz)}
            className="px-6 py-4 rounded-xl transition-all duration-300 font-bold text-lg transform hover:scale-110 active:scale-95 shadow-xl hover:shadow-2xl text-white border-2"
            style={{
              background: userResult.verdict === 'Pass'
                ? 'linear-gradient(to right, #fbbf24, #f97316, #ef4444)'
                : 'linear-gradient(to right, #6b7280, #64748b, #475569)',
              borderColor: userResult.verdict === 'Pass' ? '#fde047' : '#9ca3af'
            }}
            title="View Results"
          >
            <TbTrophy
              className="w-6 h-6"
              style={{
                color: userResult.verdict === 'Pass' ? '#fef3c7' : '#e5e7eb'
              }}
            />
          </button>
        )}
      </div>
    </div>
  );
};

export default Quiz;
