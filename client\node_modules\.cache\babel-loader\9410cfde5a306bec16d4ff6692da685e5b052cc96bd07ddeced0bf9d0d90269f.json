{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BrainwaveAI = () => {\n  _s();\n  const [messages, setMessages] = useState([{\n    role: \"assistant\",\n    content: \"Hello! I'm <PERSON><PERSON> AI, your intelligent study assistant. How can I help you today?\"\n  }]);\n  const [inputText, setInputText] = useState(\"\");\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputText.trim() && !selectedImage) return;\n    const userMessage = inputText.trim();\n    const imageFile = selectedImage;\n\n    // Clear input immediately\n    setInputText(\"\");\n    removeImage();\n    setIsLoading(true);\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        if (uploadResult !== null && uploadResult !== void 0 && uploadResult.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: userMessage\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: userMessage\n      };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response\n      const response = await chatWithChatGPT([...messages, newUserMessage]);\n      if (response !== null && response !== void 0 && response.success && response !== null && response !== void 0 && response.data) {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: response.data\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: \"Sorry, I couldn't process your request. Please try again.\"\n        }]);\n      }\n    } catch (error) {\n      console.error(\"Chat error:\", error);\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: \"An error occurred. Please try again.\"\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Render message content\n  const renderMessageContent = message => {\n    if (typeof message.content === 'string') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"whitespace-pre-wrap\",\n        children: message.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 14\n      }, this);\n    }\n    if (Array.isArray(message.content)) {\n      return message.content.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"whitespace-pre-wrap mb-2\",\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 36\n        }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: item.image_url.url,\n          alt: \"User upload\",\n          className: \"max-w-full h-auto rounded-lg shadow-md\",\n          style: {\n            maxHeight: '300px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Invalid message format\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(TbRobot, {\n            className: \"w-6 h-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-xl font-bold text-gray-800\",\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Your intelligent study assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-full max-w-4xl mx-auto p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full overflow-y-auto space-y-4 pb-4\",\n          children: [messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`,\n            children: [message.role === 'assistant' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `max-w-[80%] ${message.role === 'user' ? 'order-1' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `rounded-2xl p-4 ${message.role === 'user' ? 'bg-blue-500 text-white' : 'bg-white border border-gray-200 shadow-sm text-gray-800'}`,\n                children: renderMessageContent(message)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), message.role === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(TbUser, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white border border-gray-200 rounded-2xl p-4 shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                  style: {\n                    animationDelay: '0.1s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                  style: {\n                    animationDelay: '0.2s'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-gray-50 rounded-xl border border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\",\n                className: \"w-16 h-16 object-cover rounded-lg border border-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: removeImage,\n                className: \"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Image attached\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-end gap-3 bg-white rounded-2xl border border-gray-300 p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            className: \"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors\",\n            title: \"Attach image\",\n            children: /*#__PURE__*/_jsxDEV(TbPaperclip, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: inputText,\n            onChange: e => setInputText(e.target.value),\n            placeholder: \"Ask me anything... (Enter for new line)\",\n            className: \"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 max-h-32 min-h-[24px]\",\n            rows: 1,\n            onInput: e => {\n              e.target.style.height = 'auto';\n              e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSendMessage,\n            disabled: isLoading || !inputText.trim() && !selectedImage,\n            className: `p-2 rounded-lg transition-all ${isLoading || !inputText.trim() && !selectedImage ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"}`,\n            children: /*#__PURE__*/_jsxDEV(TbSend, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 mt-2 text-center\",\n          children: \"Click send button to send \\u2022 Enter for new line \\u2022 Upload images for analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_s(BrainwaveAI, \"Z74RUj86va5BIx3BdQBV9e1IE5g=\");\n_c = BrainwaveAI;\nexport default BrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"BrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "chatWithChatGPT", "uploadImg", "jsxDEV", "_jsxDEV", "BrainwaveAI", "_s", "messages", "setMessages", "role", "content", "inputText", "setInputText", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "isLoading", "setIsLoading", "messagesEndRef", "fileInputRef", "current", "scrollIntoView", "behavior", "handleImageSelect", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeImage", "value", "handleSendMessage", "trim", "userMessage", "imageFile", "imageUrl", "formData", "FormData", "append", "uploadResult", "success", "url", "newUserMessage", "text", "image_url", "prev", "response", "data", "error", "console", "renderMessageContent", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "isArray", "map", "item", "index", "src", "alt", "style", "maxHeight", "TbRobot", "TbUser", "animationDelay", "ref", "onClick", "TbX", "name", "_fileInputRef$current", "click", "title", "TbPaperclip", "onChange", "placeholder", "rows", "onInput", "height", "Math", "min", "scrollHeight", "disabled", "TbSend", "accept", "display", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\n\nconst BrainwaveAI = () => {\n  const [messages, setMessages] = useState([\n    {\n      role: \"assistant\",\n      content: \"Hello! I'm <PERSON><PERSON> AI, your intelligent study assistant. How can I help you today?\"\n    }\n  ]);\n  const [inputText, setInputText] = useState(\"\");\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  \n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = (event) => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = (e) => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputText.trim() && !selectedImage) return;\n\n    const userMessage = inputText.trim();\n    const imageFile = selectedImage;\n\n    // Clear input immediately\n    setInputText(\"\");\n    removeImage();\n    setIsLoading(true);\n\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        \n        if (uploadResult?.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl\n        ? {\n            role: \"user\",\n            content: [\n              { type: \"text\", text: userMessage },\n              { type: \"image_url\", image_url: { url: imageUrl } }\n            ]\n          }\n        : { role: \"user\", content: userMessage };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response\n      const response = await chatWithChatGPT([...messages, newUserMessage]);\n      \n      if (response?.success && response?.data) {\n        setMessages(prev => [...prev, { role: \"assistant\", content: response.data }]);\n      } else {\n        setMessages(prev => [...prev, { role: \"assistant\", content: \"Sorry, I couldn't process your request. Please try again.\" }]);\n      }\n\n    } catch (error) {\n      console.error(\"Chat error:\", error);\n      setMessages(prev => [...prev, { role: \"assistant\", content: \"An error occurred. Please try again.\" }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Render message content\n  const renderMessageContent = (message) => {\n    if (typeof message.content === 'string') {\n      return <div className=\"whitespace-pre-wrap\">{message.content}</div>;\n    }\n    \n    if (Array.isArray(message.content)) {\n      return message.content.map((item, index) => (\n        <div key={index}>\n          {item.type === 'text' && <div className=\"whitespace-pre-wrap mb-2\">{item.text}</div>}\n          {item.type === 'image_url' && (\n            <img \n              src={item.image_url.url} \n              alt=\"User upload\" \n              className=\"max-w-full h-auto rounded-lg shadow-md\"\n              style={{ maxHeight: '300px' }}\n            />\n          )}\n        </div>\n      ));\n    }\n    \n    return <div>Invalid message format</div>;\n  };\n\n  return (\n    <div className=\"h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b p-4\">\n        <div className=\"max-w-4xl mx-auto flex items-center gap-3\">\n          <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n            <TbRobot className=\"w-6 h-6 text-white\" />\n          </div>\n          <div>\n            <h1 className=\"text-xl font-bold text-gray-800\">Brainwave AI</h1>\n            <p className=\"text-sm text-gray-600\">Your intelligent study assistant</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-hidden\">\n        <div className=\"h-full max-w-4xl mx-auto p-4\">\n          <div className=\"h-full overflow-y-auto space-y-4 pb-4\">\n            {messages.map((message, index) => (\n              <div\n                key={index}\n                className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                {message.role === 'assistant' && (\n                  <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0\">\n                    <TbRobot className=\"w-5 h-5 text-white\" />\n                  </div>\n                )}\n                \n                <div className={`max-w-[80%] ${message.role === 'user' ? 'order-1' : ''}`}>\n                  <div\n                    className={`rounded-2xl p-4 ${\n                      message.role === 'user'\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-white border border-gray-200 shadow-sm text-gray-800'\n                    }`}\n                  >\n                    {renderMessageContent(message)}\n                  </div>\n                </div>\n\n                {message.role === 'user' && (\n                  <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0\">\n                    <TbUser className=\"w-5 h-5 text-white\" />\n                  </div>\n                )}\n              </div>\n            ))}\n            \n            {isLoading && (\n              <div className=\"flex gap-3\">\n                <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                  <TbRobot className=\"w-5 h-5 text-white\" />\n                </div>\n                <div className=\"bg-white border border-gray-200 rounded-2xl p-4 shadow-sm\">\n                  <div className=\"flex gap-1\">\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n            \n            <div ref={messagesEndRef} />\n          </div>\n        </div>\n      </div>\n\n      {/* Input Section */}\n      <div className=\"bg-white border-t p-4\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Image Preview */}\n          {imagePreview && (\n            <div className=\"mb-4 p-3 bg-gray-50 rounded-xl border border-gray-200\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"relative\">\n                  <img\n                    src={imagePreview}\n                    alt=\"Preview\"\n                    className=\"w-16 h-16 object-cover rounded-lg border border-gray-300\"\n                  />\n                  <button\n                    onClick={removeImage}\n                    className=\"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\"\n                  >\n                    <TbX className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-700\">Image attached</p>\n                  <p className=\"text-xs text-gray-500\">{selectedImage?.name}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Input Area */}\n          <div className=\"flex items-end gap-3 bg-white rounded-2xl border border-gray-300 p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\">\n            {/* Attachment Button */}\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              className=\"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors\"\n              title=\"Attach image\"\n            >\n              <TbPaperclip className=\"w-5 h-5\" />\n            </button>\n\n            {/* Text Input */}\n            <textarea\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              placeholder=\"Ask me anything... (Enter for new line)\"\n              className=\"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 max-h-32 min-h-[24px]\"\n              rows={1}\n              onInput={(e) => {\n                e.target.style.height = 'auto';\n                e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';\n              }}\n            />\n\n            {/* Send Button */}\n            <button\n              onClick={handleSendMessage}\n              disabled={isLoading || (!inputText.trim() && !selectedImage)}\n              className={`p-2 rounded-lg transition-all ${\n                isLoading || (!inputText.trim() && !selectedImage)\n                  ? \"bg-gray-200 text-gray-400 cursor-not-allowed\"\n                  : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"\n              }`}\n            >\n              <TbSend className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* Hidden File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={handleImageSelect}\n            style={{ display: 'none' }}\n          />\n\n          {/* Helper Text */}\n          <p className=\"text-xs text-gray-500 mt-2 text-center\">\n            Click send button to send • Enter for new line • Upload images for analysis\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BrainwaveAI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,eAAe,EAAEC,SAAS,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,CACvC;IACEW,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE;EACX,CAAC,CACF,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMqB,cAAc,GAAGpB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMqB,YAAY,GAAGrB,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAC,SAAS,CAAC,MAAM;IACd,IAAImB,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1ChB,gBAAgB,CAACY,IAAI,CAAC;MACtB,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKlB,eAAe,CAACkB,CAAC,CAACP,MAAM,CAACQ,MAAM,CAAC;MACvDJ,MAAM,CAACK,aAAa,CAACV,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBvB,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAII,YAAY,CAACC,OAAO,EAAE;MACxBD,YAAY,CAACC,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC5B,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAa,EAAE;IAEzC,MAAM4B,WAAW,GAAG9B,SAAS,CAAC6B,IAAI,CAAC,CAAC;IACpC,MAAME,SAAS,GAAG7B,aAAa;;IAE/B;IACAD,YAAY,CAAC,EAAE,CAAC;IAChByB,WAAW,CAAC,CAAC;IACbnB,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,IAAIyB,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAID,SAAS,EAAE;QACb,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,SAAS,CAAC;QACnC,MAAMK,YAAY,GAAG,MAAM7C,SAAS,CAAC0C,QAAQ,CAAC;QAE9C,IAAIG,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,OAAO,EAAE;UACzBL,QAAQ,GAAGI,YAAY,CAACE,GAAG;QAC7B;MACF;;MAEA;MACA,MAAMC,cAAc,GAAGP,QAAQ,GAC3B;QACElC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEmB,IAAI,EAAE,MAAM;UAAEsB,IAAI,EAAEV;QAAY,CAAC,EACnC;UAAEZ,IAAI,EAAE,WAAW;UAAEuB,SAAS,EAAE;YAAEH,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACD;QAAElC,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAE+B;MAAY,CAAC;;MAE1C;MACAjC,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,cAAc,CAAC,CAAC;;MAE9C;MACA,MAAMI,QAAQ,GAAG,MAAMrD,eAAe,CAAC,CAAC,GAAGM,QAAQ,EAAE2C,cAAc,CAAC,CAAC;MAErE,IAAII,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEN,OAAO,IAAIM,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,IAAI,EAAE;QACvC/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAE5C,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAE4C,QAAQ,CAACC;QAAK,CAAC,CAAC,CAAC;MAC/E,CAAC,MAAM;QACL/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAE5C,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAE;QAA4D,CAAC,CAAC,CAAC;MAC7H;IAEF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnChD,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE5C,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAuC,CAAC,CAAC,CAAC;IACxG,CAAC,SAAS;MACRQ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMwC,oBAAoB,GAAIC,OAAO,IAAK;IACxC,IAAI,OAAOA,OAAO,CAACjD,OAAO,KAAK,QAAQ,EAAE;MACvC,oBAAON,OAAA;QAAKwD,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAEF,OAAO,CAACjD;MAAO;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACrE;IAEA,IAAIC,KAAK,CAACC,OAAO,CAACR,OAAO,CAACjD,OAAO,CAAC,EAAE;MAClC,OAAOiD,OAAO,CAACjD,OAAO,CAAC0D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrClE,OAAA;QAAAyD,QAAA,GACGQ,IAAI,CAACxC,IAAI,KAAK,MAAM,iBAAIzB,OAAA;UAAKwD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAEQ,IAAI,CAAClB;QAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACnFI,IAAI,CAACxC,IAAI,KAAK,WAAW,iBACxBzB,OAAA;UACEmE,GAAG,EAAEF,IAAI,CAACjB,SAAS,CAACH,GAAI;UACxBuB,GAAG,EAAC,aAAa;UACjBZ,SAAS,EAAC,wCAAwC;UAClDa,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAQ;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CACF;MAAA,GATOK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUV,CACN,CAAC;IACJ;IAEA,oBAAO7D,OAAA;MAAAyD,QAAA,EAAK;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1C,CAAC;EAED,oBACE7D,OAAA;IAAKwD,SAAS,EAAC,qEAAqE;IAAAC,QAAA,gBAElFzD,OAAA;MAAKwD,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eAC9CzD,OAAA;QAAKwD,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDzD,OAAA;UAAKwD,SAAS,EAAC,qEAAqE;UAAAC,QAAA,eAClFzD,OAAA,CAACuE,OAAO;YAACf,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACN7D,OAAA;UAAAyD,QAAA,gBACEzD,OAAA;YAAIwD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjE7D,OAAA;YAAGwD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKwD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCzD,OAAA;QAAKwD,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3CzD,OAAA;UAAKwD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GACnDtD,QAAQ,CAAC6D,GAAG,CAAC,CAACT,OAAO,EAAEW,KAAK,kBAC3BlE,OAAA;YAEEwD,SAAS,EAAG,cAAaD,OAAO,CAAClD,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAgB,EAAE;YAAAoD,QAAA,GAEpFF,OAAO,CAAClD,IAAI,KAAK,WAAW,iBAC3BL,OAAA;cAAKwD,SAAS,EAAC,iFAAiF;cAAAC,QAAA,eAC9FzD,OAAA,CAACuE,OAAO;gBAACf,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACN,eAED7D,OAAA;cAAKwD,SAAS,EAAG,eAAcD,OAAO,CAAClD,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,EAAG,EAAE;cAAAoD,QAAA,eACxEzD,OAAA;gBACEwD,SAAS,EAAG,mBACVD,OAAO,CAAClD,IAAI,KAAK,MAAM,GACnB,wBAAwB,GACxB,yDACL,EAAE;gBAAAoD,QAAA,EAEFH,oBAAoB,CAACC,OAAO;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELN,OAAO,CAAClD,IAAI,KAAK,MAAM,iBACtBL,OAAA;cAAKwD,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/FzD,OAAA,CAACwE,MAAM;gBAAChB,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CACN;UAAA,GAzBIK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BP,CACN,CAAC,EAEDhD,SAAS,iBACRb,OAAA;YAAKwD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzD,OAAA;cAAKwD,SAAS,EAAC,mEAAmE;cAAAC,QAAA,eAChFzD,OAAA,CAACuE,OAAO;gBAACf,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN7D,OAAA;cAAKwD,SAAS,EAAC,2DAA2D;cAAAC,QAAA,eACxEzD,OAAA;gBAAKwD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBzD,OAAA;kBAAKwD,SAAS,EAAC;gBAAiD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvE7D,OAAA;kBAAKwD,SAAS,EAAC,iDAAiD;kBAACa,KAAK,EAAE;oBAAEI,cAAc,EAAE;kBAAO;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1G7D,OAAA;kBAAKwD,SAAS,EAAC,iDAAiD;kBAACa,KAAK,EAAE;oBAAEI,cAAc,EAAE;kBAAO;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED7D,OAAA;YAAK0E,GAAG,EAAE3D;UAAe;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKwD,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCzD,OAAA;QAAKwD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAE/B9C,YAAY,iBACXX,OAAA;UAAKwD,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eACpEzD,OAAA;YAAKwD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCzD,OAAA;cAAKwD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBzD,OAAA;gBACEmE,GAAG,EAAExD,YAAa;gBAClByD,GAAG,EAAC,SAAS;gBACbZ,SAAS,EAAC;cAA0D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACF7D,OAAA;gBACE2E,OAAO,EAAE1C,WAAY;gBACrBuB,SAAS,EAAC,yIAAyI;gBAAAC,QAAA,eAEnJzD,OAAA,CAAC4E,GAAG;kBAACpB,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7D,OAAA;cAAKwD,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBzD,OAAA;gBAAGwD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnE7D,OAAA;gBAAGwD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEhD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoE;cAAI;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD7D,OAAA;UAAKwD,SAAS,EAAC,+JAA+J;UAAAC,QAAA,gBAE5KzD,OAAA;YACE2E,OAAO,EAAEA,CAAA;cAAA,IAAAG,qBAAA;cAAA,QAAAA,qBAAA,GAAM9D,YAAY,CAACC,OAAO,cAAA6D,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CvB,SAAS,EAAC,2EAA2E;YACrFwB,KAAK,EAAC,cAAc;YAAAvB,QAAA,eAEpBzD,OAAA,CAACiF,WAAW;cAACzB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAGT7D,OAAA;YACEkC,KAAK,EAAE3B,SAAU;YACjB2E,QAAQ,EAAGpD,CAAC,IAAKtB,YAAY,CAACsB,CAAC,CAACP,MAAM,CAACW,KAAK,CAAE;YAC9CiD,WAAW,EAAC,yCAAyC;YACrD3B,SAAS,EAAC,qHAAqH;YAC/H4B,IAAI,EAAE,CAAE;YACRC,OAAO,EAAGvD,CAAC,IAAK;cACdA,CAAC,CAACP,MAAM,CAAC8C,KAAK,CAACiB,MAAM,GAAG,MAAM;cAC9BxD,CAAC,CAACP,MAAM,CAAC8C,KAAK,CAACiB,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC1D,CAAC,CAACP,MAAM,CAACkE,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;YACrE;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGF7D,OAAA;YACE2E,OAAO,EAAExC,iBAAkB;YAC3BuD,QAAQ,EAAE7E,SAAS,IAAK,CAACN,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAe;YAC7D+C,SAAS,EAAG,iCACV3C,SAAS,IAAK,CAACN,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAc,GAC9C,8CAA8C,GAC9C,oEACL,EAAE;YAAAgD,QAAA,eAEHzD,OAAA,CAAC2F,MAAM;cAACnC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN7D,OAAA;UACE0E,GAAG,EAAE1D,YAAa;UAClBS,IAAI,EAAC,MAAM;UACXmE,MAAM,EAAC,SAAS;UAChBV,QAAQ,EAAE9D,iBAAkB;UAC5BiD,KAAK,EAAE;YAAEwB,OAAO,EAAE;UAAO;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF7D,OAAA;UAAGwD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3D,EAAA,CArRID,WAAW;AAAA6F,EAAA,GAAX7F,WAAW;AAuRjB,eAAeA,WAAW;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}