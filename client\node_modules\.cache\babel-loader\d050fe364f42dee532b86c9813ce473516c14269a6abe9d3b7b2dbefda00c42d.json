{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ModernSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport { TbMenu2, TbX, TbHome, TbBrain, TbBook, TbRobot, TbChartLine, TbTrophy, TbUser, TbMessageCircle, TbCreditCard, TbLogout, TbChevronRight } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModernSidebar = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user\n  } = useSelector(state => state.user);\n\n  // Handle keyboard events\n  useEffect(() => {\n    const handleKeyDown = event => {\n      if (event.key === 'Escape' && isOpen) {\n        setIsOpen(false);\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when sidebar is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n  const navigationItems = [{\n    title: 'Hub',\n    description: 'Main dashboard',\n    icon: TbHome,\n    path: '/user/hub',\n    color: 'from-blue-500 to-blue-600'\n  }, {\n    title: 'Take Quiz',\n    description: 'Test your knowledge',\n    icon: TbBrain,\n    path: '/user/quiz',\n    color: 'from-purple-500 to-purple-600'\n  }, {\n    title: 'Study Materials',\n    description: 'Books, videos & notes',\n    icon: TbBook,\n    path: '/user/study-material',\n    color: 'from-green-500 to-green-600'\n  }, {\n    title: 'Ask AI',\n    description: 'Get instant help',\n    icon: TbRobot,\n    path: '/user/chat',\n    color: 'from-orange-500 to-orange-600'\n  }, {\n    title: 'Reports',\n    description: 'Track progress',\n    icon: TbChartLine,\n    path: '/user/reports',\n    color: 'from-red-500 to-red-600'\n  }, {\n    title: 'Ranking',\n    description: 'See your position',\n    icon: TbTrophy,\n    path: '/user/ranking',\n    color: 'from-yellow-500 to-yellow-600'\n  }, {\n    title: 'Profile',\n    description: 'Manage account',\n    icon: TbUser,\n    path: '/profile',\n    color: 'from-indigo-500 to-indigo-600'\n  }, {\n    title: 'Forum',\n    description: 'Connect with peers',\n    icon: TbMessageCircle,\n    path: '/forum',\n    color: 'from-pink-500 to-pink-600'\n  }, {\n    title: 'Plans',\n    description: 'Upgrade learning',\n    icon: TbCreditCard,\n    path: '/user/plans',\n    color: 'from-emerald-500 to-emerald-600'\n  }, {\n    title: 'Logout',\n    description: 'Sign out of account',\n    icon: TbLogout,\n    path: 'logout',\n    color: 'from-red-500 to-red-600'\n  }];\n  const handleNavigation = path => {\n    if (path === 'logout') {\n      handleLogout();\n    } else {\n      navigate(path);\n    }\n    setIsOpen(false);\n  };\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    // Force page reload to clear all state\n    window.location.href = \"/\";\n  };\n  const isActivePath = path => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(!isOpen),\n      className: \"fixed z-50 bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105\",\n      style: {\n        top: window.innerWidth <= 768 ? '8px' : '16px',\n        left: window.innerWidth <= 768 ? '8px' : '16px',\n        padding: window.innerWidth <= 768 ? '8px' : '12px'\n      },\n      title: isOpen ? \"Close Menu\" : \"Open Menu\",\n      children: isOpen ? /*#__PURE__*/_jsxDEV(TbX, {\n        className: \"text-gray-700\",\n        style: {\n          width: window.innerWidth <= 768 ? '20px' : '24px',\n          height: window.innerWidth <= 768 ? '20px' : '24px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TbMenu2, {\n        className: \"text-gray-700\",\n        style: {\n          width: window.innerWidth <= 768 ? '20px' : '24px',\n          height: window.innerWidth <= 768 ? '20px' : '24px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        exit: {\n          opacity: 0\n        },\n        onClick: () => setIsOpen(false),\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: isOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          x: -400,\n          opacity: 0\n        },\n        animate: {\n          x: 0,\n          opacity: 1\n        },\n        exit: {\n          x: -400,\n          opacity: 0\n        },\n        transition: {\n          type: \"spring\",\n          damping: 25,\n          stiffness: 200\n        },\n        className: \"fixed left-0 top-0 h-full bg-white shadow-2xl z-50 flex flex-col\",\n        style: {\n          width: window.innerWidth <= 768 ? '85vw' : window.innerWidth <= 1024 ? '350px' : '380px',\n          maxWidth: window.innerWidth <= 768 ? '300px' : '400px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-blue-700 text-white relative\",\n          style: {\n            padding: window.innerWidth <= 768 ? '12px 16px' : '16px 24px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsOpen(false),\n            className: \"absolute rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200\",\n            style: {\n              top: window.innerWidth <= 768 ? '8px' : '12px',\n              right: window.innerWidth <= 768 ? '8px' : '12px',\n              padding: window.innerWidth <= 768 ? '6px' : '8px'\n            },\n            title: \"Close Menu\",\n            children: /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"text-white\",\n              style: {\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            style: {\n              paddingRight: window.innerWidth <= 768 ? '32px' : '48px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"font-bold mb-2\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '18px' : '24px'\n              },\n              children: \"Navigation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-200\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n              },\n              children: \"Choose your destination\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto\",\n          style: {\n            padding: window.innerWidth <= 768 ? '12px' : '16px',\n            gap: window.innerWidth <= 768 ? '8px' : '12px',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: navigationItems.map((item, index) => {\n            const IconComponent = item.icon;\n            const isActive = item.path !== 'logout' && isActivePath(item.path);\n            const isLogout = item.path === 'logout';\n            return /*#__PURE__*/_jsxDEV(motion.button, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                duration: 0.3,\n                delay: index * 0.05\n              },\n              onClick: () => handleNavigation(item.path),\n              className: `w-full flex items-center justify-between rounded-xl transition-all duration-200 ${isActive ? 'bg-blue-50 border-2 border-blue-200 shadow-md' : isLogout ? 'hover:bg-red-50 border-2 border-transparent' : 'hover:bg-gray-50 border-2 border-transparent'}`,\n              style: {\n                padding: window.innerWidth <= 768 ? '8px 12px' : '12px 16px',\n                marginBottom: window.innerWidth <= 768 ? '6px' : '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                style: {\n                  gap: window.innerWidth <= 768 ? '8px' : '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`,\n                  style: {\n                    width: window.innerWidth <= 768 ? '32px' : '40px',\n                    height: window.innerWidth <= 768 ? '32px' : '40px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                    className: \"text-white\",\n                    style: {\n                      width: window.innerWidth <= 768 ? '16px' : '20px',\n                      height: window.innerWidth <= 768 ? '16px' : '20px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-left flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `font-medium ${isActive ? 'text-blue-700' : isLogout ? 'text-red-700' : 'text-gray-900'}`,\n                    style: {\n                      fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                    },\n                    children: item.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `${isActive ? 'text-blue-600' : isLogout ? 'text-red-600' : 'text-gray-500'}`,\n                    style: {\n                      fontSize: window.innerWidth <= 768 ? '11px' : '14px'\n                    },\n                    children: item.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TbChevronRight, {\n                className: `w-5 h-5 ${isActive ? 'text-blue-600' : isLogout ? 'text-red-400' : 'text-gray-400'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 21\n              }, this)]\n            }, item.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ModernSidebar, \"48y0qeclxm0j0IxiXymT1cAtcjo=\", false, function () {\n  return [useNavigate, useLocation, useSelector];\n});\n_c = ModernSidebar;\nexport default ModernSidebar;\nvar _c;\n$RefreshReg$(_c, \"ModernSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "motion", "AnimatePresence", "useSelector", "TbMenu2", "TbX", "TbHome", "TbBrain", "TbBook", "TbRobot", "TbChartLine", "TbTrophy", "TbUser", "TbMessageCircle", "TbCreditCard", "TbLogout", "TbChevronRight", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModernSidebar", "_s", "isOpen", "setIsOpen", "navigate", "location", "user", "state", "handleKeyDown", "event", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "navigationItems", "title", "description", "icon", "path", "color", "handleNavigation", "handleLogout", "localStorage", "removeItem", "window", "href", "isActivePath", "pathname", "startsWith", "children", "onClick", "className", "top", "innerWidth", "left", "padding", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "animate", "exit", "x", "transition", "type", "damping", "stiffness", "max<PERSON><PERSON><PERSON>", "right", "paddingRight", "fontSize", "gap", "display", "flexDirection", "map", "item", "index", "IconComponent", "isActive", "isLogout", "button", "duration", "delay", "marginBottom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ModernSidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport {\n  TbMenu2,\n  TbX,\n  TbHome,\n  TbBrain,\n  TbBook,\n  TbRobot,\n  TbChartLine,\n  TbTrophy,\n  TbUser,\n  TbMessageCircle,\n  TbCreditCard,\n  TbLogout,\n  TbChevronRight\n} from 'react-icons/tb';\n\nconst ModernSidebar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user } = useSelector((state) => state.user);\n\n  // Handle keyboard events\n  useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === 'Escape' && isOpen) {\n        setIsOpen(false);\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n      // Prevent body scroll when sidebar is open\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  const navigationItems = [\n    {\n      title: 'Hub',\n      description: 'Main dashboard',\n      icon: TbHome,\n      path: '/user/hub',\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      title: 'Take Quiz',\n      description: 'Test your knowledge',\n      icon: TbBrain,\n      path: '/user/quiz',\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Books, videos & notes',\n      icon: TbBook,\n      path: '/user/study-material',\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      title: 'Ask AI',\n      description: 'Get instant help',\n      icon: TbRobot,\n      path: '/user/chat',\n      color: 'from-orange-500 to-orange-600'\n    },\n    {\n      title: 'Reports',\n      description: 'Track progress',\n      icon: TbChartLine,\n      path: '/user/reports',\n      color: 'from-red-500 to-red-600'\n    },\n    {\n      title: 'Ranking',\n      description: 'See your position',\n      icon: TbTrophy,\n      path: '/user/ranking',\n      color: 'from-yellow-500 to-yellow-600'\n    },\n    {\n      title: 'Profile',\n      description: 'Manage account',\n      icon: TbUser,\n      path: '/profile',\n      color: 'from-indigo-500 to-indigo-600'\n    },\n    {\n      title: 'Forum',\n      description: 'Connect with peers',\n      icon: TbMessageCircle,\n      path: '/forum',\n      color: 'from-pink-500 to-pink-600'\n    },\n    {\n      title: 'Plans',\n      description: 'Upgrade learning',\n      icon: TbCreditCard,\n      path: '/user/plans',\n      color: 'from-emerald-500 to-emerald-600'\n    },\n    {\n      title: 'Logout',\n      description: 'Sign out of account',\n      icon: TbLogout,\n      path: 'logout',\n      color: 'from-red-500 to-red-600'\n    }\n  ];\n\n  const handleNavigation = (path) => {\n    if (path === 'logout') {\n      handleLogout();\n    } else {\n      navigate(path);\n    }\n    setIsOpen(false);\n  };\n\n  const handleLogout = () => {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    // Force page reload to clear all state\n    window.location.href = \"/\";\n  };\n\n  const isActivePath = (path) => {\n    return location.pathname === path || location.pathname.startsWith(path);\n  };\n\n  return (\n    <>\n      {/* Toggle Button - Responsive */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"fixed z-50 bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 hover:scale-105\"\n        style={{\n          top: window.innerWidth <= 768 ? '8px' : '16px',\n          left: window.innerWidth <= 768 ? '8px' : '16px',\n          padding: window.innerWidth <= 768 ? '8px' : '12px'\n        }}\n        title={isOpen ? \"Close Menu\" : \"Open Menu\"}\n      >\n        {isOpen ? (\n          <TbX\n            className=\"text-gray-700\"\n            style={{\n              width: window.innerWidth <= 768 ? '20px' : '24px',\n              height: window.innerWidth <= 768 ? '20px' : '24px'\n            }}\n          />\n        ) : (\n          <TbMenu2\n            className=\"text-gray-700\"\n            style={{\n              width: window.innerWidth <= 768 ? '20px' : '24px',\n              height: window.innerWidth <= 768 ? '20px' : '24px'\n            }}\n          />\n        )}\n      </button>\n\n      {/* Backdrop */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={() => setIsOpen(false)}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40\"\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar - Responsive */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ x: -400, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            exit={{ x: -400, opacity: 0 }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 200 }}\n            className=\"fixed left-0 top-0 h-full bg-white shadow-2xl z-50 flex flex-col\"\n            style={{\n              width: window.innerWidth <= 768 ? '85vw' : window.innerWidth <= 1024 ? '350px' : '380px',\n              maxWidth: window.innerWidth <= 768 ? '300px' : '400px'\n            }}\n          >\n            {/* Header - Responsive */}\n            <div\n              className=\"bg-gradient-to-r from-blue-600 to-blue-700 text-white relative\"\n              style={{\n                padding: window.innerWidth <= 768 ? '12px 16px' : '16px 24px'\n              }}\n            >\n              {/* Close Button */}\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"absolute rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200\"\n                style={{\n                  top: window.innerWidth <= 768 ? '8px' : '12px',\n                  right: window.innerWidth <= 768 ? '8px' : '12px',\n                  padding: window.innerWidth <= 768 ? '6px' : '8px'\n                }}\n                title=\"Close Menu\"\n              >\n                <TbX\n                  className=\"text-white\"\n                  style={{\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }}\n                />\n              </button>\n\n              <div\n                className=\"text-center\"\n                style={{\n                  paddingRight: window.innerWidth <= 768 ? '32px' : '48px'\n                }}\n              >\n                <h1\n                  className=\"font-bold mb-2\"\n                  style={{\n                    fontSize: window.innerWidth <= 768 ? '18px' : '24px'\n                  }}\n                >\n                  Navigation\n                </h1>\n                <p\n                  className=\"text-blue-200\"\n                  style={{\n                    fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n                  }}\n                >\n                  Choose your destination\n                </p>\n              </div>\n            </div>\n\n            {/* Navigation - Responsive */}\n            <div\n              className=\"flex-1 overflow-y-auto\"\n              style={{\n                padding: window.innerWidth <= 768 ? '12px' : '16px',\n                gap: window.innerWidth <= 768 ? '8px' : '12px',\n                display: 'flex',\n                flexDirection: 'column'\n              }}\n            >\n              {navigationItems.map((item, index) => {\n                const IconComponent = item.icon;\n                const isActive = item.path !== 'logout' && isActivePath(item.path);\n                const isLogout = item.path === 'logout';\n\n                return (\n                  <motion.button\n                    key={item.path}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.05 }}\n                    onClick={() => handleNavigation(item.path)}\n                    className={`w-full flex items-center justify-between rounded-xl transition-all duration-200 ${\n                      isActive\n                        ? 'bg-blue-50 border-2 border-blue-200 shadow-md'\n                        : isLogout\n                        ? 'hover:bg-red-50 border-2 border-transparent'\n                        : 'hover:bg-gray-50 border-2 border-transparent'\n                    }`}\n                    style={{\n                      padding: window.innerWidth <= 768 ? '8px 12px' : '12px 16px',\n                      marginBottom: window.innerWidth <= 768 ? '6px' : '8px'\n                    }}\n                  >\n                    <div\n                      className=\"flex items-center\"\n                      style={{\n                        gap: window.innerWidth <= 768 ? '8px' : '12px'\n                      }}\n                    >\n                      <div\n                        className={`rounded-lg bg-gradient-to-r ${item.color} flex items-center justify-center`}\n                        style={{\n                          width: window.innerWidth <= 768 ? '32px' : '40px',\n                          height: window.innerWidth <= 768 ? '32px' : '40px'\n                        }}\n                      >\n                        <IconComponent\n                          className=\"text-white\"\n                          style={{\n                            width: window.innerWidth <= 768 ? '16px' : '20px',\n                            height: window.innerWidth <= 768 ? '16px' : '20px'\n                          }}\n                        />\n                      </div>\n                      <div className=\"text-left flex-1\">\n                        <p\n                          className={`font-medium ${\n                            isActive\n                              ? 'text-blue-700'\n                              : isLogout\n                              ? 'text-red-700'\n                              : 'text-gray-900'\n                          }`}\n                          style={{\n                            fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                          }}\n                        >\n                          {item.title}\n                        </p>\n                        <p\n                          className={`${\n                            isActive\n                              ? 'text-blue-600'\n                              : isLogout\n                              ? 'text-red-600'\n                              : 'text-gray-500'\n                          }`}\n                          style={{\n                            fontSize: window.innerWidth <= 768 ? '11px' : '14px'\n                          }}\n                        >\n                          {item.description}\n                        </p>\n                      </div>\n                    </div>\n                    <TbChevronRight className={`w-5 h-5 ${\n                      isActive\n                        ? 'text-blue-600'\n                        : isLogout\n                        ? 'text-red-400'\n                        : 'text-gray-400'\n                    }`} />\n                  </motion.button>\n                );\n              })}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default ModernSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,aAAa;AACzC,SACEC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,WAAW,EACXC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfC,YAAY,EACZC,QAAQ,EACRC,cAAc,QACT,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAExB,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM4B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2B;EAAK,CAAC,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAEnD;EACA7B,SAAS,CAAC,MAAM;IACd,MAAM+B,aAAa,GAAIC,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,IAAIR,MAAM,EAAE;QACpCC,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVS,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,aAAa,CAAC;MACnD;MACAG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACLJ,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;MACtDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACb,MAAM,CAAC,CAAC;EAEZ,MAAMe,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE,gBAAgB;IAC7BC,IAAI,EAAEnC,MAAM;IACZoC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAElC,OAAO;IACbmC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAEjC,MAAM;IACZkC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,kBAAkB;IAC/BC,IAAI,EAAEhC,OAAO;IACbiC,IAAI,EAAE,YAAY;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,gBAAgB;IAC7BC,IAAI,EAAE/B,WAAW;IACjBgC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,mBAAmB;IAChCC,IAAI,EAAE9B,QAAQ;IACd+B,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,gBAAgB;IAC7BC,IAAI,EAAE7B,MAAM;IACZ8B,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAE5B,eAAe;IACrB6B,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,kBAAkB;IAC/BC,IAAI,EAAE3B,YAAY;IAClB4B,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE1B,QAAQ;IACd2B,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAIF,IAAI,IAAK;IACjC,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrBG,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLpB,QAAQ,CAACiB,IAAI,CAAC;IAChB;IACAlB,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMqB,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/B;IACAC,MAAM,CAACtB,QAAQ,CAACuB,IAAI,GAAG,GAAG;EAC5B,CAAC;EAED,MAAMC,YAAY,GAAIR,IAAI,IAAK;IAC7B,OAAOhB,QAAQ,CAACyB,QAAQ,KAAKT,IAAI,IAAIhB,QAAQ,CAACyB,QAAQ,CAACC,UAAU,CAACV,IAAI,CAAC;EACzE,CAAC;EAED,oBACExB,OAAA,CAAAE,SAAA;IAAAiC,QAAA,gBAEEnC,OAAA;MACEoC,OAAO,EAAEA,CAAA,KAAM9B,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCgC,SAAS,EAAC,6HAA6H;MACvIpB,KAAK,EAAE;QACLqB,GAAG,EAAER,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;QAC9CC,IAAI,EAAEV,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;QAC/CE,OAAO,EAAEX,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;MAC9C,CAAE;MACFlB,KAAK,EAAEhB,MAAM,GAAG,YAAY,GAAG,WAAY;MAAA8B,QAAA,EAE1C9B,MAAM,gBACLL,OAAA,CAACb,GAAG;QACFkD,SAAS,EAAC,eAAe;QACzBpB,KAAK,EAAE;UACLyB,KAAK,EAAEZ,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;UACjDI,MAAM,EAAEb,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;QAC9C;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEF/C,OAAA,CAACd,OAAO;QACNmD,SAAS,EAAC,eAAe;QACzBpB,KAAK,EAAE;UACLyB,KAAK,EAAEZ,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;UACjDI,MAAM,EAAEb,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;QAC9C;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAGT/C,OAAA,CAAChB,eAAe;MAAAmD,QAAA,EACb9B,MAAM,iBACLL,OAAA,CAACjB,MAAM,CAACiE,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,IAAI,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACrBd,OAAO,EAAEA,CAAA,KAAM9B,SAAS,CAAC,KAAK,CAAE;QAChC+B,SAAS,EAAC;MAAiD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC,eAGlB/C,OAAA,CAAChB,eAAe;MAAAmD,QAAA,EACb9B,MAAM,iBACLL,OAAA,CAACjB,MAAM,CAACiE,GAAG;QACTC,OAAO,EAAE;UAAEI,CAAC,EAAE,CAAC,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QACjCC,OAAO,EAAE;UAAEE,CAAC,EAAE,CAAC;UAAEH,OAAO,EAAE;QAAE,CAAE;QAC9BE,IAAI,EAAE;UAAEC,CAAC,EAAE,CAAC,GAAG;UAAEH,OAAO,EAAE;QAAE,CAAE;QAC9BI,UAAU,EAAE;UAAEC,IAAI,EAAE,QAAQ;UAAEC,OAAO,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAI,CAAE;QAC5DpB,SAAS,EAAC,kEAAkE;QAC5EpB,KAAK,EAAE;UACLyB,KAAK,EAAEZ,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAGT,MAAM,CAACS,UAAU,IAAI,IAAI,GAAG,OAAO,GAAG,OAAO;UACxFmB,QAAQ,EAAE5B,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;QACjD,CAAE;QAAAJ,QAAA,gBAGFnC,OAAA;UACEqC,SAAS,EAAC,gEAAgE;UAC1EpB,KAAK,EAAE;YACLwB,OAAO,EAAEX,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG;UACpD,CAAE;UAAAJ,QAAA,gBAGFnC,OAAA;YACEoC,OAAO,EAAEA,CAAA,KAAM9B,SAAS,CAAC,KAAK,CAAE;YAChC+B,SAAS,EAAC,kFAAkF;YAC5FpB,KAAK,EAAE;cACLqB,GAAG,EAAER,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;cAC9CoB,KAAK,EAAE7B,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;cAChDE,OAAO,EAAEX,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;YAC9C,CAAE;YACFlB,KAAK,EAAC,YAAY;YAAAc,QAAA,eAElBnC,OAAA,CAACb,GAAG;cACFkD,SAAS,EAAC,YAAY;cACtBpB,KAAK,EAAE;gBACLyB,KAAK,EAAEZ,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACjDI,MAAM,EAAEb,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAC9C;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAET/C,OAAA;YACEqC,SAAS,EAAC,aAAa;YACvBpB,KAAK,EAAE;cACL2C,YAAY,EAAE9B,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YACpD,CAAE;YAAAJ,QAAA,gBAEFnC,OAAA;cACEqC,SAAS,EAAC,gBAAgB;cAC1BpB,KAAK,EAAE;gBACL4C,QAAQ,EAAE/B,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAChD,CAAE;cAAAJ,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/C,OAAA;cACEqC,SAAS,EAAC,eAAe;cACzBpB,KAAK,EAAE;gBACL4C,QAAQ,EAAE/B,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAChD,CAAE;cAAAJ,QAAA,EACH;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/C,OAAA;UACEqC,SAAS,EAAC,wBAAwB;UAClCpB,KAAK,EAAE;YACLwB,OAAO,EAAEX,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;YACnDuB,GAAG,EAAEhC,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;YAC9CwB,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE;UACjB,CAAE;UAAA7B,QAAA,EAEDf,eAAe,CAAC6C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;YACpC,MAAMC,aAAa,GAAGF,IAAI,CAAC3C,IAAI;YAC/B,MAAM8C,QAAQ,GAAGH,IAAI,CAAC1C,IAAI,KAAK,QAAQ,IAAIQ,YAAY,CAACkC,IAAI,CAAC1C,IAAI,CAAC;YAClE,MAAM8C,QAAQ,GAAGJ,IAAI,CAAC1C,IAAI,KAAK,QAAQ;YAEvC,oBACExB,OAAA,CAACjB,MAAM,CAACwF,MAAM;cAEZtB,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEG,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCF,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEG,CAAC,EAAE;cAAE,CAAE;cAC9BC,UAAU,EAAE;gBAAEkB,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEN,KAAK,GAAG;cAAK,CAAE;cACnD/B,OAAO,EAAEA,CAAA,KAAMV,gBAAgB,CAACwC,IAAI,CAAC1C,IAAI,CAAE;cAC3Ca,SAAS,EAAG,mFACVgC,QAAQ,GACJ,+CAA+C,GAC/CC,QAAQ,GACR,6CAA6C,GAC7C,8CACL,EAAE;cACHrD,KAAK,EAAE;gBACLwB,OAAO,EAAEX,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,UAAU,GAAG,WAAW;gBAC5DmC,YAAY,EAAE5C,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;cACnD,CAAE;cAAAJ,QAAA,gBAEFnC,OAAA;gBACEqC,SAAS,EAAC,mBAAmB;gBAC7BpB,KAAK,EAAE;kBACL6C,GAAG,EAAEhC,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;gBAC1C,CAAE;gBAAAJ,QAAA,gBAEFnC,OAAA;kBACEqC,SAAS,EAAG,+BAA8B6B,IAAI,CAACzC,KAAM,mCAAmC;kBACxFR,KAAK,EAAE;oBACLyB,KAAK,EAAEZ,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;oBACjDI,MAAM,EAAEb,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;kBAC9C,CAAE;kBAAAJ,QAAA,eAEFnC,OAAA,CAACoE,aAAa;oBACZ/B,SAAS,EAAC,YAAY;oBACtBpB,KAAK,EAAE;sBACLyB,KAAK,EAAEZ,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;sBACjDI,MAAM,EAAEb,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;oBAC9C;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN/C,OAAA;kBAAKqC,SAAS,EAAC,kBAAkB;kBAAAF,QAAA,gBAC/BnC,OAAA;oBACEqC,SAAS,EAAG,eACVgC,QAAQ,GACJ,eAAe,GACfC,QAAQ,GACR,cAAc,GACd,eACL,EAAE;oBACHrD,KAAK,EAAE;sBACL4C,QAAQ,EAAE/B,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;oBAChD,CAAE;oBAAAJ,QAAA,EAED+B,IAAI,CAAC7C;kBAAK;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACJ/C,OAAA;oBACEqC,SAAS,EAAG,GACVgC,QAAQ,GACJ,eAAe,GACfC,QAAQ,GACR,cAAc,GACd,eACL,EAAE;oBACHrD,KAAK,EAAE;sBACL4C,QAAQ,EAAE/B,MAAM,CAACS,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;oBAChD,CAAE;oBAAAJ,QAAA,EAED+B,IAAI,CAAC5C;kBAAW;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN/C,OAAA,CAACF,cAAc;gBAACuC,SAAS,EAAG,WAC1BgC,QAAQ,GACJ,eAAe,GACfC,QAAQ,GACR,cAAc,GACd,eACL;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,GA3EDmB,IAAI,CAAC1C,IAAI;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4ED,CAAC;UAEpB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACc,CAAC;EAAA,eAClB,CAAC;AAEP,CAAC;AAAC3C,EAAA,CA9UID,aAAa;EAAA,QAEAtB,WAAW,EACXC,WAAW,EACXG,WAAW;AAAA;AAAA0F,EAAA,GAJxBxE,aAAa;AAgVnB,eAAeA,aAAa;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}