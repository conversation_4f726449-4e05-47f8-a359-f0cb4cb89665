import React, { useState, useEffect } from "react";
import './index.css';
import PageTitle from "../../../components/PageTitle";
import { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty, Table, Tag } from "antd";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { getAllReportsByUser } from "../../../apicalls/reports";
import { motion } from "framer-motion";
import {
  TbTrophy,
  TbTarget,
  TbTrendingUp,
  TbCalendar,
  TbClock,
  TbAward,
  TbChartBar,
  TbDownload,
  TbFilter,
  TbEye,
  TbCheck,
  TbX,
  TbFlame
} from "react-icons/tb";
import moment from "moment";

const { Option } = Select;
const { RangePicker } = DatePicker;

function UserReports() {
  const [reportsData, setReportsData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [filterSubject, setFilterSubject] = useState('all');
  const [filterVerdict, setFilterVerdict] = useState('all');
  const [dateRange, setDateRange] = useState(null);
  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'
  const [stats, setStats] = useState({
    totalExams: 0,
    passedExams: 0,
    averageScore: 0,
    streak: 0,
    bestScore: 0
  });
  const dispatch = useDispatch();

  const calculateStats = (data) => {
    if (!data || data.length === 0) {
      setStats({
        totalExams: 0,
        passedExams: 0,
        averageScore: 0,
        streak: 0,
        bestScore: 0
      });
      return;
    }

    const totalExams = data.length;
    const passedExams = data.filter(report => report.result?.verdict === 'Pass').length;
    const scores = data.map(report => {
      const obtained = report.result?.correctAnswers?.length || 0;
      const total = report.exam?.totalMarks || 1;
      return (obtained / total) * 100;
    });

    const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalExams;
    const bestScore = Math.max(...scores);

    // Calculate streak (consecutive passes)
    let currentStreak = 0;
    let maxStreak = 0;
    for (let i = data.length - 1; i >= 0; i--) {
      if (data[i].result?.verdict === 'Pass') {
        currentStreak++;
        maxStreak = Math.max(maxStreak, currentStreak);
      } else {
        currentStreak = 0;
      }
    }

    setStats({
      totalExams,
      passedExams,
      averageScore: Math.round(averageScore),
      streak: maxStreak,
      bestScore: Math.round(bestScore)
    });
  };

  const getData = async () => {
    try {
      dispatch(ShowLoading());
      const response = await getAllReportsByUser();
      if (response.success) {
        setReportsData(response.data);
        setFilteredData(response.data);
        calculateStats(response.data);
      } else {
        message.error(response.message);
      }
      dispatch(HideLoading());
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  const applyFilters = () => {
    let filtered = [...reportsData];

    if (filterSubject !== 'all') {
      filtered = filtered.filter(report =>
        report.exam?.subject?.toLowerCase().includes(filterSubject.toLowerCase())
      );
    }

    if (filterVerdict !== 'all') {
      filtered = filtered.filter(report => report.result?.verdict === filterVerdict);
    }

    if (dateRange && dateRange.length === 2) {
      filtered = filtered.filter(report => {
        const reportDate = moment(report.createdAt);
        return reportDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');
      });
    }

    setFilteredData(filtered);
    calculateStats(filtered);
  };

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [filterSubject, filterVerdict, dateRange, reportsData]);

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-blue-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getVerdictIcon = (verdict) => {
    return verdict === 'Pass' ?
      <TbCheck className="w-5 h-5 text-green-600" /> :
      <TbX className="w-5 h-5 text-red-600" />;
  };



  const getUniqueSubjects = () => {
    const subjects = reportsData.map(report => report.exam?.subject).filter(Boolean);
    return [...new Set(subjects)];
  };

  const columns = [
    {
      title: 'Exam Name',
      dataIndex: 'examName',
      key: 'examName',
      render: (text, record) => (
        <div>
          <div className="font-semibold text-gray-900">{record.exam?.name || 'Unnamed Exam'}</div>
          <div className="text-sm text-gray-500">{record.exam?.subject || 'General'}</div>
        </div>
      ),
      width: 250,
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'date',
      render: (date) => (
        <div className="text-sm">
          <div className="font-medium">{moment(date).format("MMM DD, YYYY")}</div>
          <div className="text-gray-500">{moment(date).format("HH:mm")}</div>
        </div>
      ),
      width: 120,
    },
    {
      title: 'Score',
      dataIndex: 'score',
      key: 'score',
      render: (text, record) => {
        const obtained = record.result?.correctAnswers?.length || 0;
        const total = record.exam?.totalMarks || 1;
        const percentage = Math.round((obtained / total) * 100);

        return (
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">{obtained}/{total}</div>
            <Progress
              percent={percentage}
              size="small"
              strokeColor={percentage >= 60 ? '#10b981' : '#ef4444'}
              showInfo={false}
              className="mb-1"
            />
            <div className={`text-sm font-medium ${getScoreColor(percentage)}`}>
              {percentage}%
            </div>
          </div>
        );
      },
      width: 120,
      sorter: (a, b) => {
        const scoreA = Math.round(((a.result?.correctAnswers?.length || 0) / (a.exam?.totalMarks || 1)) * 100);
        const scoreB = Math.round(((b.result?.correctAnswers?.length || 0) / (b.exam?.totalMarks || 1)) * 100);
        return scoreA - scoreB;
      },
    },
    {
      title: 'Result',
      dataIndex: 'verdict',
      key: 'verdict',
      render: (text, record) => {
        const verdict = record.result?.verdict;
        const isPassed = verdict === 'Pass';

        return (
          <Tag
            icon={getVerdictIcon(verdict)}
            color={isPassed ? 'success' : 'error'}
            className="font-medium"
          >
            {verdict || 'N/A'}
          </Tag>
        );
      },
      width: 100,
      filters: [
        { text: 'Pass', value: 'Pass' },
        { text: 'Fail', value: 'Fail' },
      ],
      onFilter: (value, record) => record.result?.verdict === value,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (text, record) => (
        <Button
          type="primary"
          size="small"
          icon={<TbEye />}
          className="bg-blue-500 hover:bg-blue-600"
        >
          View
        </Button>
      ),
      width: 80,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <PageTitle title="Performance Reports" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg">
            <TbChartBar className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">Performance</span> Journey
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Track your progress, analyze your performance, and celebrate your achievements
          </p>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8"
        >
          <Card className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3">
                <TbTarget className="w-6 h-6 text-white" />
              </div>
              <Statistic
                title="Total Exams"
                value={stats.totalExams}
                valueStyle={{ color: '#1e40af', fontSize: '24px', fontWeight: 'bold' }}
              />
            </div>
          </Card>

          <Card className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-3">
                <TbCheck className="w-6 h-6 text-white" />
              </div>
              <Statistic
                title="Passed"
                value={stats.passedExams}
                valueStyle={{ color: '#059669', fontSize: '24px', fontWeight: 'bold' }}
              />
            </div>
          </Card>

          <Card className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3">
                <TbTrendingUp className="w-6 h-6 text-white" />
              </div>
              <Statistic
                title="Average Score"
                value={stats.averageScore}
                suffix="%"
                valueStyle={{ color: '#7c3aed', fontSize: '24px', fontWeight: 'bold' }}
              />
            </div>
          </Card>

          <Card className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-3">
                <TbTrophy className="w-6 h-6 text-white" />
              </div>
              <Statistic
                title="Best Score"
                value={stats.bestScore}
                suffix="%"
                valueStyle={{ color: '#ea580c', fontSize: '24px', fontWeight: 'bold' }}
              />
            </div>
          </Card>

          <Card className="text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100">
            <div className="flex flex-col items-center">
              <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mb-3">
                <TbFlame className="w-6 h-6 text-white" />
              </div>
              <Statistic
                title="Best Streak"
                value={stats.streak}
                valueStyle={{ color: '#db2777', fontSize: '24px', fontWeight: 'bold' }}
              />
            </div>
          </Card>


        </motion.div>

        {/* Filters Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100"
        >
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-2">
              <TbFilter className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">Filter Results</h3>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Select
                placeholder="Select Subject"
                value={filterSubject}
                onChange={setFilterSubject}
                className="w-full sm:w-48"
                size="large"
              >
                <Option value="all">All Subjects</Option>
                {getUniqueSubjects().map(subject => (
                  <Option key={subject} value={subject}>{subject}</Option>
                ))}
              </Select>

              <Select
                placeholder="Select Result"
                value={filterVerdict}
                onChange={setFilterVerdict}
                className="w-full sm:w-48"
                size="large"
              >
                <Option value="all">All Results</Option>
                <Option value="Pass">Passed</Option>
                <Option value="Fail">Failed</Option>
              </Select>

              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                className="w-full sm:w-64"
                size="large"
                placeholder={['Start Date', 'End Date']}
              />

              <Button
                onClick={() => {
                  setFilterSubject('all');
                  setFilterVerdict('all');
                  setDateRange(null);
                }}
                size="large"
                className="w-full sm:w-auto"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Exam Results Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100"
        >
          <Table
            columns={columns}
            dataSource={filteredData}
            rowKey={(record) => record._id}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} results`,
              className: "px-6 py-4"
            }}
            scroll={{ x: 800 }}
            className="modern-table"
            size="large"
            locale={{
              emptyText: (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No exam results found</h3>
                      <p className="text-gray-500">Try adjusting your filters or take some exams to see your results here.</p>
                    </div>
                  }
                />
              )
            }}
          />
        </motion.div>
      </div>
    </div>
  );
}

export default UserReports;
