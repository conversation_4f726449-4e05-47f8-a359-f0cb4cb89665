{"ast": null, "code": "import React from \"react\";\nexport default function BrainwaveAI() {\n  return /*#__PURE__*/React.createElement(\"div\", null, \"Brainwave AI Works!\");\n}\n_c = BrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"BrainwaveAI\");", "map": {"version": 3, "names": ["React", "BrainwaveAI", "createElement", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React from \"react\"; export default function BrainwaveAI() { return React.createElement(\"div\", null, \"Brainwave AI Works!\"); }\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAE,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAE,oBAAOD,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,qBAAqB,CAAC;AAAE;AAACC,EAAA,GAAjFF,WAAW;AAAA,IAAAE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}