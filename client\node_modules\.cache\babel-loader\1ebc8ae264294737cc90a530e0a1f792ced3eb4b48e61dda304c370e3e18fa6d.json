{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\FloatingBrainwaveAI.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { TbRobot, TbX, TbSend, TbMinus, TbMaximize, TbPaperclip } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FloatingBrainwaveAI = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const [messages, setMessages] = useState([{\n    role: 'assistant',\n    content: 'Hi! I\\'m <PERSON><PERSON> <PERSON>, your study assistant. How can I help you today?'\n  }]);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n    setInput('');\n    removeImage();\n    setIsTyping(true);\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        if (uploadResult !== null && uploadResult !== void 0 && uploadResult.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: userMessage\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: userMessage\n      };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response from ChatGPT\n      const response = await chatWithChatGPT([...messages, newUserMessage]);\n      if (response !== null && response !== void 0 && response.success && response !== null && response !== void 0 && response.data) {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: response.data\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: \"I'm sorry, I couldn't process your request right now. Please try again in a moment.\"\n        }]);\n      }\n    } catch (error) {\n      console.error('Chat error:', error);\n      setMessages(prev => [...prev, {\n        role: 'assistant',\n        content: \"I'm experiencing some technical difficulties. Please try again later.\"\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  if (!isOpen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => setIsOpen(true),\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        width: '60px',\n        height: '60px',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        borderRadius: '50%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        cursor: 'pointer',\n        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n        zIndex: 1000,\n        transition: 'all 0.3s ease',\n        animation: 'pulse 2s infinite'\n      },\n      onMouseEnter: e => {\n        e.target.style.transform = 'scale(1.1)';\n        e.target.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.6)';\n      },\n      onMouseLeave: e => {\n        e.target.style.transform = 'scale(1)';\n        e.target.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.4)';\n      },\n      children: [/*#__PURE__*/_jsxDEV(TbRobot, {\n        style: {\n          color: 'white',\n          fontSize: '28px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n          @keyframes pulse {\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      bottom: '20px',\n      right: '20px',\n      width: isMinimized ? '300px' : '380px',\n      height: isMinimized ? '60px' : '500px',\n      background: 'rgba(255, 255, 255, 0.95)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '20px',\n      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n      border: '1px solid rgba(255, 255, 255, 0.2)',\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease'\n    },\n    onWheel: e => {\n      // Prevent background scrolling when scrolling over the AI chat\n      e.stopPropagation();\n    },\n    onTouchMove: e => {\n      // Prevent background scrolling on mobile\n      e.stopPropagation();\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        padding: '16px 20px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        borderRadius: '20px 20px 0 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '32px',\n            height: '32px',\n            background: 'rgba(255, 255, 255, 0.2)',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(TbRobot, {\n            style: {\n              color: 'white',\n              fontSize: '18px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: 0,\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: '600'\n            },\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              color: 'rgba(255, 255, 255, 0.8)',\n              fontSize: '12px'\n            },\n            children: \"Always here to help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsMinimized(!isMinimized),\n          style: {\n            background: 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: '8px',\n            width: '32px',\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)'\n          },\n          onMouseEnter: e => {\n            e.target.style.background = 'rgba(255, 255, 255, 0.4)';\n            e.target.style.transform = 'scale(1.05)';\n          },\n          onMouseLeave: e => {\n            e.target.style.background = 'rgba(255, 255, 255, 0.25)';\n            e.target.style.transform = 'scale(1)';\n          },\n          children: isMinimized ? /*#__PURE__*/_jsxDEV(TbMaximize, {\n            style: {\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TbMinus, {\n            style: {\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsOpen(false),\n          style: {\n            background: 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: '8px',\n            width: '32px',\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)'\n          },\n          onMouseEnter: e => {\n            e.target.style.background = 'rgba(255, 60, 60, 0.4)';\n            e.target.style.transform = 'scale(1.05)';\n          },\n          onMouseLeave: e => {\n            e.target.style.background = 'rgba(255, 255, 255, 0.25)';\n            e.target.style.transform = 'scale(1)';\n          },\n          children: /*#__PURE__*/_jsxDEV(TbX, {\n            style: {\n              color: '#ffffff',\n              fontSize: '16px',\n              fontWeight: 'bold',\n              filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), !isMinimized && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          padding: '20px 20px 0 20px',\n          overflowY: 'auto',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '16px',\n          scrollBehavior: 'smooth',\n          scrollbarWidth: 'thin',\n          scrollbarColor: '#cbd5e1 transparent'\n        },\n        className: \"custom-scrollbar\",\n        onWheel: e => {\n          // Allow scrolling within the messages container\n          const element = e.currentTarget;\n          const {\n            scrollTop,\n            scrollHeight,\n            clientHeight\n          } = element;\n\n          // If at top and scrolling up, or at bottom and scrolling down, prevent propagation\n          if (scrollTop === 0 && e.deltaY < 0 || scrollTop + clientHeight >= scrollHeight && e.deltaY > 0) {\n            e.preventDefault();\n            e.stopPropagation();\n          }\n        },\n        children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxWidth: '85%',\n              padding: '12px 16px',\n              borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',\n              background: msg.role === 'user' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f8fafc',\n              color: msg.role === 'user' ? 'white' : '#334155',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              boxShadow: msg.role === 'user' ? '0 4px 12px rgba(102, 126, 234, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',\n              wordWrap: 'break-word'\n            },\n            children: typeof msg.content === 'string' ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                whiteSpace: 'pre-wrap'\n              },\n              children: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 21\n            }, this) : Array.isArray(msg.content) ? msg.content.map((item, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  whiteSpace: 'pre-wrap',\n                  marginBottom: item.text ? '8px' : '0'\n                },\n                children: item.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 27\n              }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"img\", {\n                src: item.image_url.url,\n                alt: \"User upload\",\n                style: {\n                  maxWidth: '100%',\n                  height: 'auto',\n                  borderRadius: '12px',\n                  maxHeight: '200px',\n                  objectFit: 'cover',\n                  border: '2px solid rgba(255, 255, 255, 0.2)',\n                  marginTop: '4px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 27\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 23\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Invalid message format\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 15\n        }, this)), isTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '12px 16px',\n              borderRadius: '18px 18px 18px 4px',\n              background: '#f8fafc',\n              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '4px',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.16s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.32s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'sticky',\n          bottom: 0,\n          background: 'rgba(255, 255, 255, 0.98)',\n          backdropFilter: 'blur(20px)',\n          borderTop: '1px solid rgba(0, 0, 0, 0.08)',\n          padding: '16px 20px 20px',\n          zIndex: 10\n        },\n        children: [imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            padding: '12px',\n            background: '#f1f5f9',\n            borderRadius: '12px',\n            border: '1px solid #e2e8f0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  objectFit: 'cover',\n                  borderRadius: '8px',\n                  border: '2px solid #e2e8f0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: removeImage,\n                style: {\n                  position: 'absolute',\n                  top: '-6px',\n                  right: '-6px',\n                  width: '20px',\n                  height: '20px',\n                  background: '#ef4444',\n                  color: 'white',\n                  borderRadius: '50%',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '12px',\n                  fontWeight: 'bold'\n                },\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '13px',\n                  fontWeight: '600',\n                  color: '#374151',\n                  margin: 0\n                },\n                children: \"Image attached\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '11px',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '8px',\n            background: '#f8fafc',\n            borderRadius: '16px',\n            padding: '8px',\n            border: '2px solid #e2e8f0',\n            transition: 'border-color 0.2s ease'\n          },\n          onFocus: e => e.target.style.borderColor = '#667eea',\n          onBlur: e => e.target.style.borderColor = '#e2e8f0',\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            style: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              border: 'none',\n              borderRadius: '12px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'\n            },\n            onMouseEnter: e => {\n              e.target.style.transform = 'scale(1.05)';\n              e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';\n            },\n            onMouseLeave: e => {\n              e.target.style.transform = 'scale(1)';\n              e.target.style.boxShadow = '0 2px 8px rgba(102, 126, 234, 0.3)';\n            },\n            title: \"Attach image\",\n            children: /*#__PURE__*/_jsxDEV(TbPaperclip, {\n              style: {\n                color: 'white',\n                fontSize: '18px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: input,\n            onChange: e => setInput(e.target.value),\n            onKeyDown: handleKeyDown,\n            placeholder: \"Ask me anything...\",\n            style: {\n              flex: 1,\n              border: 'none',\n              background: 'transparent',\n              outline: 'none',\n              fontSize: '14px',\n              color: '#334155',\n              padding: '12px 16px',\n              fontFamily: 'inherit'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: sendMessage,\n            disabled: !input.trim() && !selectedImage,\n            style: {\n              background: input.trim() || selectedImage ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#e2e8f0',\n              border: 'none',\n              borderRadius: '12px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: input.trim() || selectedImage ? 'pointer' : 'not-allowed',\n              transition: 'all 0.2s ease',\n              boxShadow: input.trim() || selectedImage ? '0 2px 8px rgba(102, 126, 234, 0.3)' : 'none'\n            },\n            onMouseEnter: e => {\n              if (input.trim() || selectedImage) {\n                e.target.style.transform = 'scale(1.05)';\n                e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';\n              }\n            },\n            onMouseLeave: e => {\n              e.target.style.transform = 'scale(1)';\n              e.target.style.boxShadow = input.trim() || selectedImage ? '0 2px 8px rgba(102, 126, 234, 0.3)' : 'none';\n            },\n            children: /*#__PURE__*/_jsxDEV(TbSend, {\n              style: {\n                color: input.trim() || selectedImage ? 'white' : '#94a3b8',\n                fontSize: '18px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '11px',\n            color: '#94a3b8',\n            textAlign: 'center',\n            margin: '8px 0 0 0'\n          },\n          children: \"Press Enter to send \\u2022 Attach images for analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n_s(FloatingBrainwaveAI, \"ZhOZT57spe4ri1k2EGMoAEq6kBM=\");\n_c = FloatingBrainwaveAI;\nexport default FloatingBrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"FloatingBrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "TbRobot", "TbX", "TbSend", "TbMinus", "TbMaximize", "TbPaperclip", "chatWithChatGPT", "uploadImg", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FloatingBrainwaveAI", "_s", "isOpen", "setIsOpen", "isMinimized", "setIsMinimized", "messages", "setMessages", "role", "content", "input", "setInput", "isTyping", "setIsTyping", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "messagesEndRef", "fileInputRef", "current", "scrollIntoView", "behavior", "handleImageSelect", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeImage", "value", "sendMessage", "trim", "userMessage", "imageFile", "imageUrl", "formData", "FormData", "append", "uploadResult", "success", "url", "newUserMessage", "text", "image_url", "prev", "response", "data", "error", "console", "handleKeyDown", "key", "shift<PERSON>ey", "preventDefault", "onClick", "style", "position", "bottom", "right", "width", "height", "background", "borderRadius", "display", "alignItems", "justifyContent", "cursor", "boxShadow", "zIndex", "transition", "animation", "onMouseEnter", "transform", "onMouseLeave", "children", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>ilter", "border", "flexDirection", "overflow", "onWheel", "stopPropagation", "onTouchMove", "padding", "gap", "margin", "fontWeight", "filter", "flex", "overflowY", "scroll<PERSON>eh<PERSON>or", "scrollbarWidth", "scrollbarColor", "className", "element", "currentTarget", "scrollTop", "scrollHeight", "clientHeight", "deltaY", "map", "msg", "index", "max<PERSON><PERSON><PERSON>", "lineHeight", "wordWrap", "whiteSpace", "Array", "isArray", "item", "idx", "marginBottom", "src", "alt", "maxHeight", "objectFit", "marginTop", "ref", "borderTop", "top", "name", "onFocus", "borderColor", "onBlur", "_fileInputRef$current", "click", "title", "onChange", "onKeyDown", "placeholder", "outline", "fontFamily", "disabled", "accept", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/FloatingBrainwaveAI.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { TbR<PERSON>ot, TbX, Tb<PERSON><PERSON>, TbMinus, TbMaximize, TbPaperclip } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\n\nconst FloatingBrainwaveAI = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const [messages, setMessages] = useState([\n    { role: 'assistant', content: 'Hi! I\\'m <PERSON><PERSON> AI, your study assistant. How can I help you today?' }\n  ]);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = (event) => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = (e) => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n\n    setInput('');\n    removeImage();\n    setIsTyping(true);\n\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n\n        if (uploadResult?.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl\n        ? {\n            role: \"user\",\n            content: [\n              { type: \"text\", text: userMessage },\n              { type: \"image_url\", image_url: { url: imageUrl } }\n            ]\n          }\n        : { role: \"user\", content: userMessage };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response from ChatGPT\n      const response = await chatWithChatGPT([\n        ...messages,\n        newUserMessage\n      ]);\n\n      if (response?.success && response?.data) {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: response.data\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: \"I'm sorry, I couldn't process your request right now. Please try again in a moment.\"\n        }]);\n      }\n    } catch (error) {\n      console.error('Chat error:', error);\n      setMessages(prev => [...prev, {\n        role: 'assistant',\n        content: \"I'm experiencing some technical difficulties. Please try again later.\"\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  if (!isOpen) {\n    return (\n      <div\n        onClick={() => setIsOpen(true)}\n        style={{\n          position: 'fixed',\n          bottom: '20px',\n          right: '20px',\n          width: '60px',\n          height: '60px',\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          borderRadius: '50%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          cursor: 'pointer',\n          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n          zIndex: 1000,\n          transition: 'all 0.3s ease',\n          animation: 'pulse 2s infinite'\n        }}\n        onMouseEnter={(e) => {\n          e.target.style.transform = 'scale(1.1)';\n          e.target.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.6)';\n        }}\n        onMouseLeave={(e) => {\n          e.target.style.transform = 'scale(1)';\n          e.target.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.4)';\n        }}\n      >\n        <TbRobot style={{ color: 'white', fontSize: '28px' }} />\n        <style>{`\n          @keyframes pulse {\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n          }\n        `}</style>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        width: isMinimized ? '300px' : '380px',\n        height: isMinimized ? '60px' : '500px',\n        background: 'rgba(255, 255, 255, 0.95)',\n        backdropFilter: 'blur(20px)',\n        borderRadius: '20px',\n        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        zIndex: 1000,\n        display: 'flex',\n        flexDirection: 'column',\n        overflow: 'hidden',\n        transition: 'all 0.3s ease'\n      }}\n      onWheel={(e) => {\n        // Prevent background scrolling when scrolling over the AI chat\n        e.stopPropagation();\n      }}\n      onTouchMove={(e) => {\n        // Prevent background scrolling on mobile\n        e.stopPropagation();\n      }}\n    >\n      {/* Header */}\n      <div\n        style={{\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          padding: '16px 20px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          borderRadius: '20px 20px 0 0'\n        }}\n      >\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div\n            style={{\n              width: '32px',\n              height: '32px',\n              background: 'rgba(255, 255, 255, 0.2)',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n          >\n            <TbRobot style={{ color: 'white', fontSize: '18px' }} />\n          </div>\n          <div>\n            <h3 style={{ margin: 0, color: 'white', fontSize: '16px', fontWeight: '600' }}>\n              Brainwave AI\n            </h3>\n            <p style={{ margin: 0, color: 'rgba(255, 255, 255, 0.8)', fontSize: '12px' }}>\n              Always here to help\n            </p>\n          </div>\n        </div>\n        \n        <div style={{ display: 'flex', gap: '8px' }}>\n          <button\n            onClick={() => setIsMinimized(!isMinimized)}\n            style={{\n              background: 'rgba(255, 255, 255, 0.25)',\n              border: 'none',\n              borderRadius: '8px',\n              width: '32px',\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              backdropFilter: 'blur(10px)'\n            }}\n            onMouseEnter={(e) => {\n              e.target.style.background = 'rgba(255, 255, 255, 0.4)';\n              e.target.style.transform = 'scale(1.05)';\n            }}\n            onMouseLeave={(e) => {\n              e.target.style.background = 'rgba(255, 255, 255, 0.25)';\n              e.target.style.transform = 'scale(1)';\n            }}\n          >\n            {isMinimized ? (\n              <TbMaximize style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }} />\n            ) : (\n              <TbMinus style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }} />\n            )}\n          </button>\n\n          <button\n            onClick={() => setIsOpen(false)}\n            style={{\n              background: 'rgba(255, 255, 255, 0.25)',\n              border: 'none',\n              borderRadius: '8px',\n              width: '32px',\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              backdropFilter: 'blur(10px)'\n            }}\n            onMouseEnter={(e) => {\n              e.target.style.background = 'rgba(255, 60, 60, 0.4)';\n              e.target.style.transform = 'scale(1.05)';\n            }}\n            onMouseLeave={(e) => {\n              e.target.style.background = 'rgba(255, 255, 255, 0.25)';\n              e.target.style.transform = 'scale(1)';\n            }}\n          >\n            <TbX style={{ color: '#ffffff', fontSize: '16px', fontWeight: 'bold', filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.3))' }} />\n          </button>\n        </div>\n      </div>\n\n      {!isMinimized && (\n        <>\n          {/* Messages */}\n          <div\n            style={{\n              flex: 1,\n              padding: '20px 20px 0 20px',\n              overflowY: 'auto',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '16px',\n              scrollBehavior: 'smooth',\n              scrollbarWidth: 'thin',\n              scrollbarColor: '#cbd5e1 transparent'\n            }}\n            className=\"custom-scrollbar\"\n            onWheel={(e) => {\n              // Allow scrolling within the messages container\n              const element = e.currentTarget;\n              const { scrollTop, scrollHeight, clientHeight } = element;\n\n              // If at top and scrolling up, or at bottom and scrolling down, prevent propagation\n              if ((scrollTop === 0 && e.deltaY < 0) ||\n                  (scrollTop + clientHeight >= scrollHeight && e.deltaY > 0)) {\n                e.preventDefault();\n                e.stopPropagation();\n              }\n            }}\n          >\n            {messages.map((msg, index) => (\n              <div\n                key={index}\n                style={{\n                  display: 'flex',\n                  justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'\n                }}\n              >\n                <div\n                  style={{\n                    maxWidth: '85%',\n                    padding: '12px 16px',\n                    borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',\n                    background: msg.role === 'user'\n                      ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                      : '#f8fafc',\n                    color: msg.role === 'user' ? 'white' : '#334155',\n                    fontSize: '14px',\n                    lineHeight: '1.5',\n                    boxShadow: msg.role === 'user'\n                      ? '0 4px 12px rgba(102, 126, 234, 0.3)'\n                      : '0 2px 8px rgba(0, 0, 0, 0.1)',\n                    wordWrap: 'break-word'\n                  }}\n                >\n                  {/* Handle different message content types */}\n                  {typeof msg.content === 'string' ? (\n                    <div style={{ whiteSpace: 'pre-wrap' }}>{msg.content}</div>\n                  ) : Array.isArray(msg.content) ? (\n                    msg.content.map((item, idx) => (\n                      <div key={idx}>\n                        {item.type === 'text' && (\n                          <div style={{ whiteSpace: 'pre-wrap', marginBottom: item.text ? '8px' : '0' }}>\n                            {item.text}\n                          </div>\n                        )}\n                        {item.type === 'image_url' && (\n                          <img\n                            src={item.image_url.url}\n                            alt=\"User upload\"\n                            style={{\n                              maxWidth: '100%',\n                              height: 'auto',\n                              borderRadius: '12px',\n                              maxHeight: '200px',\n                              objectFit: 'cover',\n                              border: '2px solid rgba(255, 255, 255, 0.2)',\n                              marginTop: '4px'\n                            }}\n                          />\n                        )}\n                      </div>\n                    ))\n                  ) : (\n                    <div>Invalid message format</div>\n                  )}\n                </div>\n              </div>\n            ))}\n\n            {isTyping && (\n              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>\n                <div\n                  style={{\n                    padding: '12px 16px',\n                    borderRadius: '18px 18px 18px 4px',\n                    background: '#f8fafc',\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n                  }}\n                >\n                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>\n                    <div\n                      style={{\n                        width: '6px',\n                        height: '6px',\n                        borderRadius: '50%',\n                        background: '#94a3b8',\n                        animation: 'bounce 1.4s infinite ease-in-out'\n                      }}\n                    />\n                    <div\n                      style={{\n                        width: '6px',\n                        height: '6px',\n                        borderRadius: '50%',\n                        background: '#94a3b8',\n                        animation: 'bounce 1.4s infinite ease-in-out 0.16s'\n                      }}\n                    />\n                    <div\n                      style={{\n                        width: '6px',\n                        height: '6px',\n                        borderRadius: '50%',\n                        background: '#94a3b8',\n                        animation: 'bounce 1.4s infinite ease-in-out 0.32s'\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* Input Section - Sticky */}\n          <div\n            style={{\n              position: 'sticky',\n              bottom: 0,\n              background: 'rgba(255, 255, 255, 0.98)',\n              backdropFilter: 'blur(20px)',\n              borderTop: '1px solid rgba(0, 0, 0, 0.08)',\n              padding: '16px 20px 20px',\n              zIndex: 10\n            }}\n          >\n            {/* Image Preview */}\n            {imagePreview && (\n              <div\n                style={{\n                  marginBottom: '12px',\n                  padding: '12px',\n                  background: '#f1f5f9',\n                  borderRadius: '12px',\n                  border: '1px solid #e2e8f0'\n                }}\n              >\n                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n                  <div style={{ position: 'relative' }}>\n                    <img\n                      src={imagePreview}\n                      alt=\"Preview\"\n                      style={{\n                        width: '60px',\n                        height: '60px',\n                        objectFit: 'cover',\n                        borderRadius: '8px',\n                        border: '2px solid #e2e8f0'\n                      }}\n                    />\n                    <button\n                      onClick={removeImage}\n                      style={{\n                        position: 'absolute',\n                        top: '-6px',\n                        right: '-6px',\n                        width: '20px',\n                        height: '20px',\n                        background: '#ef4444',\n                        color: 'white',\n                        borderRadius: '50%',\n                        border: 'none',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: '12px',\n                        fontWeight: 'bold'\n                      }}\n                    >\n                      ×\n                    </button>\n                  </div>\n                  <div style={{ flex: 1 }}>\n                    <p style={{ fontSize: '13px', fontWeight: '600', color: '#374151', margin: 0 }}>\n                      Image attached\n                    </p>\n                    <p style={{ fontSize: '11px', color: '#6b7280', margin: 0 }}>\n                      {selectedImage?.name}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Input Area */}\n            <div\n              style={{\n                display: 'flex',\n                gap: '8px',\n                background: '#f8fafc',\n                borderRadius: '16px',\n                padding: '8px',\n                border: '2px solid #e2e8f0',\n                transition: 'border-color 0.2s ease'\n              }}\n              onFocus={(e) => e.target.style.borderColor = '#667eea'}\n              onBlur={(e) => e.target.style.borderColor = '#e2e8f0'}\n            >\n              {/* Attachment Button */}\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                style={{\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  border: 'none',\n                  borderRadius: '12px',\n                  width: '40px',\n                  height: '40px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'\n                }}\n                onMouseEnter={(e) => {\n                  e.target.style.transform = 'scale(1.05)';\n                  e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';\n                }}\n                onMouseLeave={(e) => {\n                  e.target.style.transform = 'scale(1)';\n                  e.target.style.boxShadow = '0 2px 8px rgba(102, 126, 234, 0.3)';\n                }}\n                title=\"Attach image\"\n              >\n                <TbPaperclip style={{ color: 'white', fontSize: '18px' }} />\n              </button>\n\n              {/* Text Input */}\n              <input\n                type=\"text\"\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                onKeyDown={handleKeyDown}\n                placeholder=\"Ask me anything...\"\n                style={{\n                  flex: 1,\n                  border: 'none',\n                  background: 'transparent',\n                  outline: 'none',\n                  fontSize: '14px',\n                  color: '#334155',\n                  padding: '12px 16px',\n                  fontFamily: 'inherit'\n                }}\n              />\n\n              {/* Send Button */}\n              <button\n                onClick={sendMessage}\n                disabled={!input.trim() && !selectedImage}\n                style={{\n                  background: (input.trim() || selectedImage)\n                    ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                    : '#e2e8f0',\n                  border: 'none',\n                  borderRadius: '12px',\n                  width: '40px',\n                  height: '40px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: (input.trim() || selectedImage) ? 'pointer' : 'not-allowed',\n                  transition: 'all 0.2s ease',\n                  boxShadow: (input.trim() || selectedImage)\n                    ? '0 2px 8px rgba(102, 126, 234, 0.3)'\n                    : 'none'\n                }}\n                onMouseEnter={(e) => {\n                  if (input.trim() || selectedImage) {\n                    e.target.style.transform = 'scale(1.05)';\n                    e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  e.target.style.transform = 'scale(1)';\n                  e.target.style.boxShadow = (input.trim() || selectedImage)\n                    ? '0 2px 8px rgba(102, 126, 234, 0.3)'\n                    : 'none';\n                }}\n              >\n                <TbSend\n                  style={{\n                    color: (input.trim() || selectedImage) ? 'white' : '#94a3b8',\n                    fontSize: '18px'\n                  }}\n                />\n              </button>\n            </div>\n\n            {/* Hidden File Input */}\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleImageSelect}\n              style={{ display: 'none' }}\n            />\n\n            {/* Helper Text */}\n            <p style={{\n              fontSize: '11px',\n              color: '#94a3b8',\n              textAlign: 'center',\n              margin: '8px 0 0 0'\n            }}>\n              Press Enter to send • Attach images for analysis\n            </p>\n          </div>\n        </>\n      )}\n\n      <style>{`\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default FloatingBrainwaveAI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,WAAW,QAAQ,gBAAgB;AACvF,SAASC,eAAe,EAAEC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,CACvC;IAAEuB,IAAI,EAAE,WAAW;IAAEC,OAAO,EAAE;EAAyE,CAAC,CACzG,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMiC,cAAc,GAAGhC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMiC,YAAY,GAAGjC,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAC,SAAS,CAAC,MAAM;IACd,IAAI+B,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1Cd,gBAAgB,CAACU,IAAI,CAAC;MACtB,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKhB,eAAe,CAACgB,CAAC,CAACP,MAAM,CAACQ,MAAM,CAAC;MACvDJ,MAAM,CAACK,aAAa,CAACV,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBrB,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIE,YAAY,CAACC,OAAO,EAAE;MACxBD,YAAY,CAACC,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC5B,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAACzB,aAAa,EAAE;IAErC,MAAM0B,WAAW,GAAG9B,KAAK,CAAC6B,IAAI,CAAC,CAAC;IAChC,MAAME,SAAS,GAAG3B,aAAa;IAE/BH,QAAQ,CAAC,EAAE,CAAC;IACZyB,WAAW,CAAC,CAAC;IACbvB,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,IAAI6B,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAID,SAAS,EAAE;QACb,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,SAAS,CAAC;QACnC,MAAMK,YAAY,GAAG,MAAMnD,SAAS,CAACgD,QAAQ,CAAC;QAE9C,IAAIG,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,OAAO,EAAE;UACzBL,QAAQ,GAAGI,YAAY,CAACE,GAAG;QAC7B;MACF;;MAEA;MACA,MAAMC,cAAc,GAAGP,QAAQ,GAC3B;QACElC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEmB,IAAI,EAAE,MAAM;UAAEsB,IAAI,EAAEV;QAAY,CAAC,EACnC;UAAEZ,IAAI,EAAE,WAAW;UAAEuB,SAAS,EAAE;YAAEH,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACD;QAAElC,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAE+B;MAAY,CAAC;;MAE1C;MACAjC,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,cAAc,CAAC,CAAC;;MAE9C;MACA,MAAMI,QAAQ,GAAG,MAAM3D,eAAe,CAAC,CACrC,GAAGY,QAAQ,EACX2C,cAAc,CACf,CAAC;MAEF,IAAII,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEN,OAAO,IAAIM,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,IAAI,EAAE;QACvC/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5B5C,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE4C,QAAQ,CAACC;QACpB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5B5C,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnChD,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5B5C,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRI,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM4C,aAAa,GAAIxB,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACyB,GAAG,KAAK,OAAO,IAAI,CAACzB,CAAC,CAAC0B,QAAQ,EAAE;MACpC1B,CAAC,CAAC2B,cAAc,CAAC,CAAC;MAClBtB,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,IAAI,CAACpC,MAAM,EAAE;IACX,oBACEL,OAAA;MACEgE,OAAO,EAAEA,CAAA,KAAM1D,SAAS,CAAC,IAAI,CAAE;MAC/B2D,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,mDAAmD;QAC/DC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,qCAAqC;QAChDC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE;MACb,CAAE;MACFC,YAAY,EAAG7C,CAAC,IAAK;QACnBA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,YAAY;QACvC9C,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACY,SAAS,GAAG,sCAAsC;MACnE,CAAE;MACFM,YAAY,EAAG/C,CAAC,IAAK;QACnBA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,UAAU;QACrC9C,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACY,SAAS,GAAG,qCAAqC;MAClE,CAAE;MAAAO,QAAA,gBAEFpF,OAAA,CAACT,OAAO;QAAC0E,KAAK,EAAE;UAAEoB,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxD1F,OAAA;QAAAoF,QAAA,EAAS;AACjB;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACE1F,OAAA;IACEiE,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE9D,WAAW,GAAG,OAAO,GAAG,OAAO;MACtC+D,MAAM,EAAE/D,WAAW,GAAG,MAAM,GAAG,OAAO;MACtCgE,UAAU,EAAE,2BAA2B;MACvCoB,cAAc,EAAE,YAAY;MAC5BnB,YAAY,EAAE,MAAM;MACpBK,SAAS,EAAE,iCAAiC;MAC5Ce,MAAM,EAAE,oCAAoC;MAC5Cd,MAAM,EAAE,IAAI;MACZL,OAAO,EAAE,MAAM;MACfoB,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE,QAAQ;MAClBf,UAAU,EAAE;IACd,CAAE;IACFgB,OAAO,EAAG3D,CAAC,IAAK;MACd;MACAA,CAAC,CAAC4D,eAAe,CAAC,CAAC;IACrB,CAAE;IACFC,WAAW,EAAG7D,CAAC,IAAK;MAClB;MACAA,CAAC,CAAC4D,eAAe,CAAC,CAAC;IACrB,CAAE;IAAAZ,QAAA,gBAGFpF,OAAA;MACEiE,KAAK,EAAE;QACLM,UAAU,EAAE,mDAAmD;QAC/D2B,OAAO,EAAE,WAAW;QACpBzB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BH,YAAY,EAAE;MAChB,CAAE;MAAAY,QAAA,gBAEFpF,OAAA;QAAKiE,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEyB,GAAG,EAAE;QAAO,CAAE;QAAAf,QAAA,gBACjEpF,OAAA;UACEiE,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,0BAA0B;YACtCC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAS,QAAA,eAEFpF,OAAA,CAACT,OAAO;YAAC0E,KAAK,EAAE;cAAEoB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACN1F,OAAA;UAAAoF,QAAA,gBACEpF,OAAA;YAAIiE,KAAK,EAAE;cAAEmC,MAAM,EAAE,CAAC;cAAEf,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEe,UAAU,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAAC;UAE/E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1F,OAAA;YAAGiE,KAAK,EAAE;cAAEmC,MAAM,EAAE,CAAC;cAAEf,KAAK,EAAE,0BAA0B;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAE9E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1F,OAAA;QAAKiE,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAE0B,GAAG,EAAE;QAAM,CAAE;QAAAf,QAAA,gBAC1CpF,OAAA;UACEgE,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5C0D,KAAK,EAAE;YACLM,UAAU,EAAE,2BAA2B;YACvCqB,MAAM,EAAE,MAAM;YACdpB,YAAY,EAAE,KAAK;YACnBH,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBG,UAAU,EAAE,eAAe;YAC3BY,cAAc,EAAE;UAClB,CAAE;UACFV,YAAY,EAAG7C,CAAC,IAAK;YACnBA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACM,UAAU,GAAG,0BAA0B;YACtDnC,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,aAAa;UAC1C,CAAE;UACFC,YAAY,EAAG/C,CAAC,IAAK;YACnBA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACM,UAAU,GAAG,2BAA2B;YACvDnC,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,UAAU;UACvC,CAAE;UAAAE,QAAA,EAED7E,WAAW,gBACVP,OAAA,CAACL,UAAU;YAACsE,KAAK,EAAE;cAAEoB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEe,UAAU,EAAE;YAAO;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE/E1F,OAAA,CAACN,OAAO;YAACuE,KAAK,EAAE;cAAEoB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEe,UAAU,EAAE;YAAO;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAC5E;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAET1F,OAAA;UACEgE,OAAO,EAAEA,CAAA,KAAM1D,SAAS,CAAC,KAAK,CAAE;UAChC2D,KAAK,EAAE;YACLM,UAAU,EAAE,2BAA2B;YACvCqB,MAAM,EAAE,MAAM;YACdpB,YAAY,EAAE,KAAK;YACnBH,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBG,UAAU,EAAE,eAAe;YAC3BY,cAAc,EAAE;UAClB,CAAE;UACFV,YAAY,EAAG7C,CAAC,IAAK;YACnBA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACM,UAAU,GAAG,wBAAwB;YACpDnC,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,aAAa;UAC1C,CAAE;UACFC,YAAY,EAAG/C,CAAC,IAAK;YACnBA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACM,UAAU,GAAG,2BAA2B;YACvDnC,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,UAAU;UACvC,CAAE;UAAAE,QAAA,eAEFpF,OAAA,CAACR,GAAG;YAACyE,KAAK,EAAE;cAAEoB,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE,MAAM;cAAEe,UAAU,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAyC;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAACnF,WAAW,iBACXP,OAAA,CAAAE,SAAA;MAAAkF,QAAA,gBAEEpF,OAAA;QACEiE,KAAK,EAAE;UACLsC,IAAI,EAAE,CAAC;UACPL,OAAO,EAAE,kBAAkB;UAC3BM,SAAS,EAAE,MAAM;UACjB/B,OAAO,EAAE,MAAM;UACfoB,aAAa,EAAE,QAAQ;UACvBM,GAAG,EAAE,MAAM;UACXM,cAAc,EAAE,QAAQ;UACxBC,cAAc,EAAE,MAAM;UACtBC,cAAc,EAAE;QAClB,CAAE;QACFC,SAAS,EAAC,kBAAkB;QAC5Bb,OAAO,EAAG3D,CAAC,IAAK;UACd;UACA,MAAMyE,OAAO,GAAGzE,CAAC,CAAC0E,aAAa;UAC/B,MAAM;YAAEC,SAAS;YAAEC,YAAY;YAAEC;UAAa,CAAC,GAAGJ,OAAO;;UAEzD;UACA,IAAKE,SAAS,KAAK,CAAC,IAAI3E,CAAC,CAAC8E,MAAM,GAAG,CAAC,IAC/BH,SAAS,GAAGE,YAAY,IAAID,YAAY,IAAI5E,CAAC,CAAC8E,MAAM,GAAG,CAAE,EAAE;YAC9D9E,CAAC,CAAC2B,cAAc,CAAC,CAAC;YAClB3B,CAAC,CAAC4D,eAAe,CAAC,CAAC;UACrB;QACF,CAAE;QAAAZ,QAAA,GAED3E,QAAQ,CAAC0G,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBrH,OAAA;UAEEiE,KAAK,EAAE;YACLQ,OAAO,EAAE,MAAM;YACfE,cAAc,EAAEyC,GAAG,CAACzG,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG;UACrD,CAAE;UAAAyE,QAAA,eAEFpF,OAAA;YACEiE,KAAK,EAAE;cACLqD,QAAQ,EAAE,KAAK;cACfpB,OAAO,EAAE,WAAW;cACpB1B,YAAY,EAAE4C,GAAG,CAACzG,IAAI,KAAK,MAAM,GAAG,oBAAoB,GAAG,oBAAoB;cAC/E4D,UAAU,EAAE6C,GAAG,CAACzG,IAAI,KAAK,MAAM,GAC3B,mDAAmD,GACnD,SAAS;cACb0E,KAAK,EAAE+B,GAAG,CAACzG,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,SAAS;cAChD2E,QAAQ,EAAE,MAAM;cAChBiC,UAAU,EAAE,KAAK;cACjB1C,SAAS,EAAEuC,GAAG,CAACzG,IAAI,KAAK,MAAM,GAC1B,qCAAqC,GACrC,8BAA8B;cAClC6G,QAAQ,EAAE;YACZ,CAAE;YAAApC,QAAA,EAGD,OAAOgC,GAAG,CAACxG,OAAO,KAAK,QAAQ,gBAC9BZ,OAAA;cAAKiE,KAAK,EAAE;gBAAEwD,UAAU,EAAE;cAAW,CAAE;cAAArC,QAAA,EAAEgC,GAAG,CAACxG;YAAO;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,GACzDgC,KAAK,CAACC,OAAO,CAACP,GAAG,CAACxG,OAAO,CAAC,GAC5BwG,GAAG,CAACxG,OAAO,CAACuG,GAAG,CAAC,CAACS,IAAI,EAAEC,GAAG,kBACxB7H,OAAA;cAAAoF,QAAA,GACGwC,IAAI,CAAC7F,IAAI,KAAK,MAAM,iBACnB/B,OAAA;gBAAKiE,KAAK,EAAE;kBAAEwD,UAAU,EAAE,UAAU;kBAAEK,YAAY,EAAEF,IAAI,CAACvE,IAAI,GAAG,KAAK,GAAG;gBAAI,CAAE;gBAAA+B,QAAA,EAC3EwC,IAAI,CAACvE;cAAI;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACN,EACAkC,IAAI,CAAC7F,IAAI,KAAK,WAAW,iBACxB/B,OAAA;gBACE+H,GAAG,EAAEH,IAAI,CAACtE,SAAS,CAACH,GAAI;gBACxB6E,GAAG,EAAC,aAAa;gBACjB/D,KAAK,EAAE;kBACLqD,QAAQ,EAAE,MAAM;kBAChBhD,MAAM,EAAE,MAAM;kBACdE,YAAY,EAAE,MAAM;kBACpByD,SAAS,EAAE,OAAO;kBAClBC,SAAS,EAAE,OAAO;kBAClBtC,MAAM,EAAE,oCAAoC;kBAC5CuC,SAAS,EAAE;gBACb;cAAE;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA,GApBOmC,GAAG;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBR,CACN,CAAC,gBAEF1F,OAAA;cAAAoF,QAAA,EAAK;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACjC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GAtDD2B,KAAK;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuDP,CACN,CAAC,EAED3E,QAAQ,iBACPf,OAAA;UAAKiE,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE;UAAa,CAAE;UAAAS,QAAA,eAC5DpF,OAAA;YACEiE,KAAK,EAAE;cACLiC,OAAO,EAAE,WAAW;cACpB1B,YAAY,EAAE,oBAAoB;cAClCD,UAAU,EAAE,SAAS;cACrBM,SAAS,EAAE;YACb,CAAE;YAAAO,QAAA,eAEFpF,OAAA;cAAKiE,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE0B,GAAG,EAAE,KAAK;gBAAEzB,UAAU,EAAE;cAAS,CAAE;cAAAU,QAAA,gBAChEpF,OAAA;gBACEiE,KAAK,EAAE;kBACLI,KAAK,EAAE,KAAK;kBACZC,MAAM,EAAE,KAAK;kBACbE,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBS,SAAS,EAAE;gBACb;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF1F,OAAA;gBACEiE,KAAK,EAAE;kBACLI,KAAK,EAAE,KAAK;kBACZC,MAAM,EAAE,KAAK;kBACbE,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBS,SAAS,EAAE;gBACb;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF1F,OAAA;gBACEiE,KAAK,EAAE;kBACLI,KAAK,EAAE,KAAK;kBACZC,MAAM,EAAE,KAAK;kBACbE,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBS,SAAS,EAAE;gBACb;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED1F,OAAA;UAAKoI,GAAG,EAAE/G;QAAe;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGN1F,OAAA;QACEiE,KAAK,EAAE;UACLC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE,CAAC;UACTI,UAAU,EAAE,2BAA2B;UACvCoB,cAAc,EAAE,YAAY;UAC5B0C,SAAS,EAAE,+BAA+B;UAC1CnC,OAAO,EAAE,gBAAgB;UACzBpB,MAAM,EAAE;QACV,CAAE;QAAAM,QAAA,GAGDjE,YAAY,iBACXnB,OAAA;UACEiE,KAAK,EAAE;YACL6D,YAAY,EAAE,MAAM;YACpB5B,OAAO,EAAE,MAAM;YACf3B,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,MAAM;YACpBoB,MAAM,EAAE;UACV,CAAE;UAAAR,QAAA,eAEFpF,OAAA;YAAKiE,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEyB,GAAG,EAAE;YAAO,CAAE;YAAAf,QAAA,gBACjEpF,OAAA;cAAKiE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAkB,QAAA,gBACnCpF,OAAA;gBACE+H,GAAG,EAAE5G,YAAa;gBAClB6G,GAAG,EAAC,SAAS;gBACb/D,KAAK,EAAE;kBACLI,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACd4D,SAAS,EAAE,OAAO;kBAClB1D,YAAY,EAAE,KAAK;kBACnBoB,MAAM,EAAE;gBACV;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF1F,OAAA;gBACEgE,OAAO,EAAEzB,WAAY;gBACrB0B,KAAK,EAAE;kBACLC,QAAQ,EAAE,UAAU;kBACpBoE,GAAG,EAAE,MAAM;kBACXlE,KAAK,EAAE,MAAM;kBACbC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdC,UAAU,EAAE,SAAS;kBACrBc,KAAK,EAAE,OAAO;kBACdb,YAAY,EAAE,KAAK;kBACnBoB,MAAM,EAAE,MAAM;kBACdhB,MAAM,EAAE,SAAS;kBACjBH,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBW,QAAQ,EAAE,MAAM;kBAChBe,UAAU,EAAE;gBACd,CAAE;gBAAAjB,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN1F,OAAA;cAAKiE,KAAK,EAAE;gBAAEsC,IAAI,EAAE;cAAE,CAAE;cAAAnB,QAAA,gBACtBpF,OAAA;gBAAGiE,KAAK,EAAE;kBAAEqB,QAAQ,EAAE,MAAM;kBAAEe,UAAU,EAAE,KAAK;kBAAEhB,KAAK,EAAE,SAAS;kBAAEe,MAAM,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,EAAC;cAEhF;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ1F,OAAA;gBAAGiE,KAAK,EAAE;kBAAEqB,QAAQ,EAAE,MAAM;kBAAED,KAAK,EAAE,SAAS;kBAAEe,MAAM,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,EACzDnE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsH;cAAI;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD1F,OAAA;UACEiE,KAAK,EAAE;YACLQ,OAAO,EAAE,MAAM;YACf0B,GAAG,EAAE,KAAK;YACV5B,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,MAAM;YACpB0B,OAAO,EAAE,KAAK;YACdN,MAAM,EAAE,mBAAmB;YAC3Bb,UAAU,EAAE;UACd,CAAE;UACFyD,OAAO,EAAGpG,CAAC,IAAKA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACwE,WAAW,GAAG,SAAU;UACvDC,MAAM,EAAGtG,CAAC,IAAKA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACwE,WAAW,GAAG,SAAU;UAAArD,QAAA,gBAGtDpF,OAAA;YACEgE,OAAO,EAAEA,CAAA;cAAA,IAAA2E,qBAAA;cAAA,QAAAA,qBAAA,GAAMrH,YAAY,CAACC,OAAO,cAAAoH,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7C3E,KAAK,EAAE;cACLM,UAAU,EAAE,mDAAmD;cAC/DqB,MAAM,EAAE,MAAM;cACdpB,YAAY,EAAE,MAAM;cACpBH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBG,UAAU,EAAE,eAAe;cAC3BF,SAAS,EAAE;YACb,CAAE;YACFI,YAAY,EAAG7C,CAAC,IAAK;cACnBA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,aAAa;cACxC9C,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACY,SAAS,GAAG,qCAAqC;YAClE,CAAE;YACFM,YAAY,EAAG/C,CAAC,IAAK;cACnBA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,UAAU;cACrC9C,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACY,SAAS,GAAG,oCAAoC;YACjE,CAAE;YACFgE,KAAK,EAAC,cAAc;YAAAzD,QAAA,eAEpBpF,OAAA,CAACJ,WAAW;cAACqE,KAAK,EAAE;gBAAEoB,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE;cAAO;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eAGT1F,OAAA;YACE+B,IAAI,EAAC,MAAM;YACXS,KAAK,EAAE3B,KAAM;YACbiI,QAAQ,EAAG1G,CAAC,IAAKtB,QAAQ,CAACsB,CAAC,CAACP,MAAM,CAACW,KAAK,CAAE;YAC1CuG,SAAS,EAAEnF,aAAc;YACzBoF,WAAW,EAAC,oBAAoB;YAChC/E,KAAK,EAAE;cACLsC,IAAI,EAAE,CAAC;cACPX,MAAM,EAAE,MAAM;cACdrB,UAAU,EAAE,aAAa;cACzB0E,OAAO,EAAE,MAAM;cACf3D,QAAQ,EAAE,MAAM;cAChBD,KAAK,EAAE,SAAS;cAChBa,OAAO,EAAE,WAAW;cACpBgD,UAAU,EAAE;YACd;UAAE;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGF1F,OAAA;YACEgE,OAAO,EAAEvB,WAAY;YACrB0G,QAAQ,EAAE,CAACtI,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAACzB,aAAc;YAC1CgD,KAAK,EAAE;cACLM,UAAU,EAAG1D,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GACtC,mDAAmD,GACnD,SAAS;cACb2E,MAAM,EAAE,MAAM;cACdpB,YAAY,EAAE,MAAM;cACpBH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAG/D,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GAAI,SAAS,GAAG,aAAa;cACnE8D,UAAU,EAAE,eAAe;cAC3BF,SAAS,EAAGhE,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GACrC,oCAAoC,GACpC;YACN,CAAE;YACFgE,YAAY,EAAG7C,CAAC,IAAK;cACnB,IAAIvB,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,EAAE;gBACjCmB,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,aAAa;gBACxC9C,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACY,SAAS,GAAG,qCAAqC;cAClE;YACF,CAAE;YACFM,YAAY,EAAG/C,CAAC,IAAK;cACnBA,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACiB,SAAS,GAAG,UAAU;cACrC9C,CAAC,CAACP,MAAM,CAACoC,KAAK,CAACY,SAAS,GAAIhE,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GACrD,oCAAoC,GACpC,MAAM;YACZ,CAAE;YAAAmE,QAAA,eAEFpF,OAAA,CAACP,MAAM;cACLwE,KAAK,EAAE;gBACLoB,KAAK,EAAGxE,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GAAI,OAAO,GAAG,SAAS;gBAC5DqE,QAAQ,EAAE;cACZ;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN1F,OAAA;UACEoI,GAAG,EAAE9G,YAAa;UAClBS,IAAI,EAAC,MAAM;UACXqH,MAAM,EAAC,SAAS;UAChBN,QAAQ,EAAEpH,iBAAkB;UAC5BuC,KAAK,EAAE;YAAEQ,OAAO,EAAE;UAAO;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF1F,OAAA;UAAGiE,KAAK,EAAE;YACRqB,QAAQ,EAAE,MAAM;YAChBD,KAAK,EAAE,SAAS;YAChBgE,SAAS,EAAE,QAAQ;YACnBjD,MAAM,EAAE;UACV,CAAE;UAAAhB,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA,eACN,CACH,eAED1F,OAAA;MAAAoF,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACtF,EAAA,CA7nBID,mBAAmB;AAAAmJ,EAAA,GAAnBnJ,mBAAmB;AA+nBzB,eAAeA,mBAAmB;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}