{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    id\n  } = useParams();\n  const {\n    user\n  } = useSelector(state => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n  const [isFlashing, setIsFlashing] = useState(false);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n    // Play enhanced sound effects and trigger flash animation\n    const playSound = () => {\n      // Trigger premium flash animation\n      setIsFlashing(true);\n      setTimeout(() => setIsFlashing(false), 3200); // Flash for 3.2 seconds (2 cycles of 0.8s each)\n\n      try {\n        if (isPassed) {\n          // Success sound with clapping\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration, type = 'sine') => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = type;\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Create clapping sound effect\n          const createClap = startTime => {\n            const noise = audioContext.createBufferSource();\n            const buffer = audioContext.createBuffer(1, audioContext.sampleRate * 0.1, audioContext.sampleRate);\n            const data = buffer.getChannelData(0);\n\n            // Generate white noise for clap\n            for (let i = 0; i < data.length; i++) {\n              data[i] = Math.random() * 2 - 1;\n            }\n            noise.buffer = buffer;\n            const filter = audioContext.createBiquadFilter();\n            filter.type = 'highpass';\n            filter.frequency.setValueAtTime(1000, startTime);\n            const gainNode = audioContext.createGain();\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);\n            noise.connect(filter);\n            filter.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            noise.start(startTime);\n            noise.stop(startTime + 0.1);\n          };\n          const now = audioContext.currentTime;\n\n          // Play success melody: C-E-G-C (major chord progression)\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n          // Add clapping sounds\n          createClap(now + 0.5);\n          createClap(now + 0.7);\n          createClap(now + 0.9);\n          createClap(now + 1.1);\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate premium animations based on pass/fail\n    if (isPassed) {\n      // Premium confetti explosion\n      const premiumConfetti = [];\n\n      // Main confetti burst (200 pieces)\n      for (let i = 0; i < 200; i++) {\n        const colors = ['#FFD700', '#FFA500', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FF69B4', '#32CD32', '#FF4500', '#9370DB', '#00CED1', '#FF1493', '#00FF7F', '#FF8C00', '#DA70D6'];\n        premiumConfetti.push({\n          id: `confetti_${i}`,\n          left: 20 + Math.random() * 60,\n          // More centered\n          delay: Math.random() * 2,\n          duration: 4 + Math.random() * 3,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],\n          size: 3 + Math.random() * 6,\n          type: 'confetti',\n          randomX: (Math.random() - 0.5) * 200 // For burst effect\n        });\n      }\n\n      // Premium sparkles (50 pieces)\n      for (let i = 0; i < 50; i++) {\n        premiumConfetti.push({\n          id: `sparkle_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 2 + Math.random() * 2,\n          color: ['#FFD700', '#FFFFFF', '#FFF700', '#FFFF00'][Math.floor(Math.random() * 4)],\n          size: 1 + Math.random() * 3,\n          type: 'sparkle'\n        });\n      }\n\n      // Burst confetti (100 pieces from center)\n      for (let i = 0; i < 100; i++) {\n        premiumConfetti.push({\n          id: `burst_${i}`,\n          left: 45 + Math.random() * 10,\n          // Center burst\n          delay: Math.random() * 0.5,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#FF69B4'][Math.floor(Math.random() * 4)],\n          shape: 'circle',\n          size: 2 + Math.random() * 4,\n          type: 'burst',\n          randomX: (Math.random() - 0.5) * 300\n        });\n      }\n      setConfetti(premiumConfetti);\n\n      // Remove all animations after 10 seconds\n      setTimeout(() => setConfetti([]), 10000);\n    } else {\n      // Premium motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡', '⭐', '✨', '🔥', '💎'];\n      for (let i = 0; i < 30; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 5 + Math.random() * 3,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true,\n          size: 2 + Math.random() * 2\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    }\n    playSound();\n  }, [isPassed]);\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n    try {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: true\n      }));\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: false\n      }));\n    }\n  };\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen flex items-center justify-center p-4 relative overflow-hidden ${isPassed ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100' : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'} ${isFlashing ? isPassed ? 'flash-green' : 'flash-red' : ''}`,\n    children: [confetti.map(piece => {\n      if (piece.isMotivational) {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute opacity-90\",\n          style: {\n            left: `${piece.left}%`,\n            top: `${20 + Math.random() * 60}%`,\n            fontSize: `${piece.size || 2}rem`,\n            animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n            zIndex: 100\n          },\n          children: piece.emoji\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 13\n        }, this);\n      }\n      if (piece.type === 'sparkle') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute\",\n          style: {\n            left: `${piece.left}%`,\n            width: `${piece.size}px`,\n            height: `${piece.size}px`,\n            background: `radial-gradient(circle, ${piece.color}, transparent)`,\n            borderRadius: '50%',\n            animation: `premium-sparkle ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n            top: `${Math.random() * 100}%`,\n            boxShadow: `0 0 ${piece.size * 2}px ${piece.color}`,\n            zIndex: 100\n          }\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this);\n      }\n      if (piece.type === 'burst') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute opacity-90\",\n          style: {\n            left: `${piece.left}%`,\n            width: `${piece.size}px`,\n            height: `${piece.size}px`,\n            backgroundColor: piece.color,\n            borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n            clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n            animation: `confetti-burst ${piece.duration}s ease-out ${piece.delay}s forwards`,\n            top: '40%',\n            '--random-x': `${piece.randomX}px`,\n            boxShadow: `0 0 ${piece.size}px ${piece.color}40`,\n            zIndex: 100\n          }\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this);\n      }\n\n      // Regular premium confetti\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute opacity-90\",\n        style: {\n          left: `${piece.left}%`,\n          width: `${piece.size}px`,\n          height: `${piece.size}px`,\n          backgroundColor: piece.color,\n          borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n          clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n          animation: `confetti-fall ${piece.duration}s ease-out ${piece.delay}s forwards`,\n          top: '-20px',\n          boxShadow: `0 0 ${piece.size}px ${piece.color}60`,\n          border: `1px solid ${piece.color}`,\n          background: `linear-gradient(45deg, ${piece.color}, ${piece.color}80)`,\n          zIndex: 100\n        }\n      }, piece.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-20px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n            transform: translateY(0px) rotate(36deg) scale(1);\n          }\n          100% {\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\n            opacity: 0;\n          }\n        }\n\n        @keyframes confetti-burst {\n          0% {\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          15% {\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\n            opacity: 0;\n          }\n        }\n\n        @keyframes premium-sparkle {\n          0%, 100% {\n            transform: scale(0) rotate(0deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.5) rotate(180deg);\n            opacity: 1;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes text-bounce {\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n          40% { transform: translateY(-20px) scale(1.1); }\n          60% { transform: translateY(-10px) scale(1.05); }\n        }\n\n        @keyframes text-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\n            transform: scale(1);\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\n            transform: scale(1.05);\n          }\n        }\n\n        @keyframes rainbow-text {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D;\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F;\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF;\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6;\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4;\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n        }\n\n        @keyframes shake-celebrate {\n          0%, 100% { transform: translateX(0) rotate(0deg); }\n          10% { transform: translateX(-5px) rotate(-1deg); }\n          20% { transform: translateX(5px) rotate(1deg); }\n          30% { transform: translateX(-5px) rotate(-1deg); }\n          40% { transform: translateX(5px) rotate(1deg); }\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\n          60% { transform: translateX(3px) rotate(0.5deg); }\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\n          80% { transform: translateX(2px) rotate(0.5deg); }\n          90% { transform: translateX(-1px) rotate(0deg); }\n        }\n\n        @keyframes zoom-in-out {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.2); }\n        }\n\n        .animate-text-bounce {\n          animation: text-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-text-glow {\n          animation: text-glow 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-shake-celebrate {\n          animation: shake-celebrate 1s ease-in-out infinite;\n        }\n\n        .animate-zoom {\n          animation: zoom-in-out 2s ease-in-out infinite;\n        }\n\n        @keyframes elegant-scale {\n          0%, 100% {\n            transform: scale(1) rotateY(0deg);\n            text-shadow: 0 0 20px currentColor;\n          }\n          50% {\n            transform: scale(1.1) rotateY(5deg);\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\n          }\n        }\n\n        @keyframes premium-pulse {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 1;\n          }\n          50% {\n            transform: scale(1.05);\n            opacity: 0.9;\n          }\n        }\n\n        @keyframes smooth-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;\n            filter: brightness(1);\n          }\n          50% {\n            text-shadow: 0 0 30px currentColor, 0 0 50px currentColor, 0 0 70px currentColor;\n            filter: brightness(1.2);\n          }\n        }\n\n        @keyframes celebration-bounce {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0) scale(1);\n          }\n          40% {\n            transform: translateY(-15px) scale(1.05);\n          }\n          60% {\n            transform: translateY(-8px) scale(1.02);\n          }\n        }\n\n        .animate-elegant {\n          animation: elegant-scale 3s ease-in-out infinite;\n        }\n\n        .animate-premium-pulse {\n          animation: premium-pulse 2s ease-in-out infinite;\n        }\n\n        .animate-smooth-glow {\n          animation: smooth-glow 2.5s ease-in-out infinite;\n        }\n\n        .animate-celebration {\n          animation: celebration-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        @keyframes premium-green-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        @keyframes premium-red-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        .flash-green {\n          animation: premium-green-flash 0.8s ease-in-out 2;\n        }\n\n        .flash-red {\n          animation: premium-red-flash 0.8s ease-in-out 2;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), isFlashing && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 pointer-events-none\",\n      style: {\n        background: isPassed ? 'radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)' : 'radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)',\n        animation: isPassed ? 'premium-green-flash 0.8s ease-in-out infinite' : 'premium-red-flash 0.8s ease-in-out infinite',\n        zIndex: 5\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white rounded-2xl shadow-2xl border-2 p-8 max-w-2xl w-full relative ${isPassed ? 'border-green-400 shadow-green-200' : 'border-red-400 shadow-red-200'} ${isFlashing ? 'shadow-3xl' : ''}`,\n      style: {\n        background: isFlashing ? isPassed ? 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))' : 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))' : 'white',\n        boxShadow: isFlashing ? isPassed ? '0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)' : '0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)' : '0 25px 50px rgba(0,0,0,0.15)',\n        zIndex: 10\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'}`,\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: `w-12 h-12 ${isPassed ? 'text-green-600' : 'text-red-600'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `text-6xl font-bold mb-4 ${isPassed ? 'text-green-600 animate-elegant animate-smooth-glow' : 'text-red-600 animate-premium-pulse animate-smooth-glow'}`,\n          children: isPassed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-celebration text-7xl\",\n              children: \"\\uD83C\\uDF89\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-rainbow animate-elegant\",\n              children: \"Congratulations!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-celebration text-7xl\",\n              children: \"\\uD83C\\uDF89\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-premium-pulse text-7xl\",\n              children: \"\\uD83D\\uDCAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-smooth-glow\",\n              children: \"Keep Going!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-premium-pulse text-7xl\",\n              children: \"\\uD83D\\uDCAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-3xl font-bold mb-4 ${isPassed ? 'text-green-700 animate-celebration animate-rainbow' : 'text-red-700 animate-premium-pulse animate-smooth-glow'}`,\n          children: isPassed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-elegant\",\n            children: \"\\u2728 You Passed! \\u2728\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-smooth-glow\",\n            children: \"\\uD83C\\uDF1F You Can Do It! \\uD83C\\uDF1F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2 text-lg\",\n          children: [\"\\uD83D\\uDCDA \", quizName, \" - \", quizSubject]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-block px-8 py-4 rounded-2xl ${isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-5xl font-bold mb-2 ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-600\",\n            children: \"Your Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-green-600\",\n            children: correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-green-600 font-medium\",\n            children: \"\\u2705 Correct\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-red-600\",\n            children: totalQuestions - correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-600 font-medium\",\n            children: \"\\u274C Wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\uD83D\\uDCCA Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-blue-600\",\n            children: [Math.floor(timeTaken / 60), \":\", (timeTaken % 60).toString().padStart(2, '0')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\u23F1\\uFE0F Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 9\n      }, this), xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-purple-600 font-bold text-xl\",\n                children: [\"+\", xpData.xpAwarded || 0, \" XP\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Earned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (((user === null || user === void 0 ? void 0 : user.totalXP) || 0) + (xpData.xpAwarded || 0)).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", (user === null || user === void 0 ? void 0 : user.currentLevel) || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 11\n      }, this), !xpData && user && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Your Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (user.totalXP || 0).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", user.currentLevel || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCDA Learning Summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 818,\n        columnNumber: 9\n      }, this), resultDetails && resultDetails.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCCB Question by Question Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 836,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 max-h-96 overflow-y-auto\",\n          children: resultDetails.map((detail, index) => {\n            // Debug: Log question data to see what's available\n            console.log(`Question ${index + 1} data:`, detail);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl ${detail.isCorrect ? 'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300' : 'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'}`,\n              style: {\n                boxShadow: detail.isCorrect ? '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)' : '0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 ${detail.isCorrect ? 'bg-green-300 border-b-4 border-green-500' : 'bg-red-300 border-b-4 border-red-500'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-10 h-10 rounded-full flex items-center justify-center font-bold ${detail.isCorrect ? 'bg-green-500 text-white shadow-lg' : 'bg-red-500 text-white shadow-lg'}`,\n                    children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 45\n                    }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 79\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 864,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-bold text-gray-900 text-lg\",\n                      children: [\"Question \", index + 1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-3 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 877,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 876,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 bg-white p-3 rounded-lg border\",\n                    children: detail.questionText || detail.questionName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 21\n                }, this), (detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-lg border-2 ${detail.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-semibold text-gray-700\",\n                        children: \"\\uD83D\\uDCF7 Question Image:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 906,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 907,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 905,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-2 rounded-lg border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: detail.questionImage || detail.image || detail.imageUrl,\n                        alt: \"Question Image\",\n                        className: \"max-w-full h-auto rounded-lg shadow-sm mx-auto block\",\n                        style: {\n                          maxHeight: '300px'\n                        },\n                        onError: e => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'block';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 916,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\",\n                        style: {\n                          display: 'none'\n                        },\n                        children: \"\\uD83D\\uDCF7 Image could not be loaded\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 926,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 915,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 900,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-4 rounded-lg border-4 ${detail.isCorrect ? 'bg-green-50 border-green-500' : 'bg-red-50 border-red-500'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-6 h-6 rounded-full flex items-center justify-center ${detail.isCorrect ? 'bg-green-500' : 'bg-red-500'}`,\n                        children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 949,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 951,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 945,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Your Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 954,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 944,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `p-3 rounded-lg font-bold text-lg ${detail.isCorrect ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'}`,\n                      children: detail.userAnswer || 'No answer provided'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 956,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 939,\n                    columnNumber: 23\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-50 p-4 rounded-lg border-4 border-green-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 969,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 968,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Correct Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 971,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 967,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\",\n                      children: detail.correctAnswer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 973,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 25\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${loadingExplanations[`question_${index}`] ? 'bg-gray-400 cursor-not-allowed' : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'}`,\n                      onClick: () => fetchExplanation(index, detail),\n                      disabled: loadingExplanations[`question_${index}`],\n                      children: loadingExplanations[`question_${index}`] ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 993,\n                          columnNumber: 33\n                        }, this), \"Getting Explanation...\"]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 998,\n                          columnNumber: 33\n                        }, this), \"Get Explanation\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 982,\n                      columnNumber: 27\n                    }, this), explanations[`question_${index}`] && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-2 mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5 text-blue-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1008,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-bold text-blue-800\",\n                          children: \"\\uD83D\\uDCA1 Explanation:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1009,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1007,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-blue-700 leading-relaxed whitespace-pre-wrap\",\n                        children: explanations[`question_${index}`]\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1011,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1006,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 938,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 19\n              }, this)]\n            }, detail.questionId || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 More Quizzes button clicked!');\n            handleBackToQuizzes();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbHome, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 13\n          }, this), \"More Quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1031,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 Retake Quiz button clicked!');\n            handleRetakeQuiz();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 13\n          }, this), \"Retake Quiz\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1030,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 666,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 305,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"Ts/BUI5rSPpEvy+1LV+0+/PKtNw=\", false, function () {\n  return [useNavigate, useLocation, useParams, useSelector];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "startTransition", "useEffect", "useState", "useNavigate", "useLocation", "useParams", "useSelector", "TbTrophy", "TbCheck", "TbX", "TbHome", "TbStar", "TbBrain", "TbChartBar", "chatWithChatGPTToExplainAns", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "navigate", "location", "id", "user", "state", "resultData", "percentage", "correctAnswers", "totalQuestions", "timeTaken", "resultDetails", "xpData", "quizName", "quizSubject", "passingPercentage", "verdict", "isPassed", "confetti", "set<PERSON>on<PERSON>tti", "explanations", "setExplanations", "loadingExplanations", "setLoadingExplanations", "isFlashing", "setIsFlashing", "playSound", "setTimeout", "audioContext", "window", "AudioContext", "webkitAudioContext", "playTone", "frequency", "startTime", "duration", "type", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "createClap", "noise", "createBufferSource", "buffer", "createBuffer", "sampleRate", "data", "getChannelData", "i", "length", "Math", "random", "filter", "createBiquadFilter", "now", "currentTime", "error", "console", "log", "premiumConfetti", "colors", "push", "left", "delay", "color", "floor", "shape", "size", "randomX", "motivationalElements", "motivationalEmojis", "emoji", "isMotivational", "fetchExplanation", "questionIndex", "detail", "<PERSON><PERSON><PERSON>", "prev", "response", "question", "questionText", "questionName", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "userAnswer", "imageUrl", "questionImage", "image", "success", "explanation", "handleBackToQuizzes", "handleRetakeQuiz", "className", "children", "map", "piece", "style", "top", "fontSize", "animation", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "background", "borderRadius", "boxShadow", "backgroundColor", "clipPath", "border", "jsx", "toString", "padStart", "xpAwarded", "totalXP", "toLocaleString", "currentLevel", "index", "isCorrect", "questionType", "src", "alt", "maxHeight", "onError", "e", "target", "display", "nextS<PERSON>ling", "onClick", "disabled", "questionId", "preventDefault", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\n\nconst QuizResult = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { id } = useParams();\n  const { user } = useSelector((state) => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n  const [isFlashing, setIsFlashing] = useState(false);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n\n    // Play enhanced sound effects and trigger flash animation\n    const playSound = () => {\n      // Trigger premium flash animation\n      setIsFlashing(true);\n      setTimeout(() => setIsFlashing(false), 3200); // Flash for 3.2 seconds (2 cycles of 0.8s each)\n\n      try {\n        if (isPassed) {\n          // Success sound with clapping\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration, type = 'sine') => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = type;\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Create clapping sound effect\n          const createClap = (startTime) => {\n            const noise = audioContext.createBufferSource();\n            const buffer = audioContext.createBuffer(1, audioContext.sampleRate * 0.1, audioContext.sampleRate);\n            const data = buffer.getChannelData(0);\n\n            // Generate white noise for clap\n            for (let i = 0; i < data.length; i++) {\n              data[i] = Math.random() * 2 - 1;\n            }\n\n            noise.buffer = buffer;\n\n            const filter = audioContext.createBiquadFilter();\n            filter.type = 'highpass';\n            filter.frequency.setValueAtTime(1000, startTime);\n\n            const gainNode = audioContext.createGain();\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);\n\n            noise.connect(filter);\n            filter.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            noise.start(startTime);\n            noise.stop(startTime + 0.1);\n          };\n\n          const now = audioContext.currentTime;\n\n          // Play success melody: C-E-G-C (major chord progression)\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n          // Add clapping sounds\n          createClap(now + 0.5);\n          createClap(now + 0.7);\n          createClap(now + 0.9);\n          createClap(now + 1.1);\n\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate premium animations based on pass/fail\n    if (isPassed) {\n      // Premium confetti explosion\n      const premiumConfetti = [];\n\n      // Main confetti burst (200 pieces)\n      for (let i = 0; i < 200; i++) {\n        const colors = [\n          '#FFD700', '#FFA500', '#FF6B6B', '#4ECDC4', '#45B7D1',\n          '#96CEB4', '#FF69B4', '#32CD32', '#FF4500', '#9370DB',\n          '#00CED1', '#FF1493', '#00FF7F', '#FF8C00', '#DA70D6'\n        ];\n\n        premiumConfetti.push({\n          id: `confetti_${i}`,\n          left: 20 + Math.random() * 60, // More centered\n          delay: Math.random() * 2,\n          duration: 4 + Math.random() * 3,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],\n          size: 3 + Math.random() * 6,\n          type: 'confetti',\n          randomX: (Math.random() - 0.5) * 200 // For burst effect\n        });\n      }\n\n      // Premium sparkles (50 pieces)\n      for (let i = 0; i < 50; i++) {\n        premiumConfetti.push({\n          id: `sparkle_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 2 + Math.random() * 2,\n          color: ['#FFD700', '#FFFFFF', '#FFF700', '#FFFF00'][Math.floor(Math.random() * 4)],\n          size: 1 + Math.random() * 3,\n          type: 'sparkle'\n        });\n      }\n\n      // Burst confetti (100 pieces from center)\n      for (let i = 0; i < 100; i++) {\n        premiumConfetti.push({\n          id: `burst_${i}`,\n          left: 45 + Math.random() * 10, // Center burst\n          delay: Math.random() * 0.5,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#FF69B4'][Math.floor(Math.random() * 4)],\n          shape: 'circle',\n          size: 2 + Math.random() * 4,\n          type: 'burst',\n          randomX: (Math.random() - 0.5) * 300\n        });\n      }\n\n      setConfetti(premiumConfetti);\n\n      // Remove all animations after 10 seconds\n      setTimeout(() => setConfetti([]), 10000);\n    } else {\n      // Premium motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡', '⭐', '✨', '🔥', '💎'];\n\n      for (let i = 0; i < 30; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 5 + Math.random() * 3,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true,\n          size: 2 + Math.random() * 2\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    }\n\n    playSound();\n  }, [isPassed]);\n\n\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n\n    try {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: true }));\n\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: false }));\n    }\n  };\n\n\n\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center p-4 relative overflow-hidden ${\n      isPassed\n        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100'\n        : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'\n    } ${isFlashing ? (isPassed ? 'flash-green' : 'flash-red') : ''}`}>\n\n      {/* Premium Confetti System */}\n      {confetti.map((piece) => {\n        if (piece.isMotivational) {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                top: `${20 + Math.random() * 60}%`,\n                fontSize: `${piece.size || 2}rem`,\n                animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                zIndex: 100\n              }}\n            >\n              {piece.emoji}\n            </div>\n          );\n        }\n\n        if (piece.type === 'sparkle') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                background: `radial-gradient(circle, ${piece.color}, transparent)`,\n                borderRadius: '50%',\n                animation: `premium-sparkle ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                top: `${Math.random() * 100}%`,\n                boxShadow: `0 0 ${piece.size * 2}px ${piece.color}`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        if (piece.type === 'burst') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                backgroundColor: piece.color,\n                borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n                clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n                animation: `confetti-burst ${piece.duration}s ease-out ${piece.delay}s forwards`,\n                top: '40%',\n                '--random-x': `${piece.randomX}px`,\n                boxShadow: `0 0 ${piece.size}px ${piece.color}40`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        // Regular premium confetti\n        return (\n          <div\n            key={piece.id}\n            className=\"absolute opacity-90\"\n            style={{\n              left: `${piece.left}%`,\n              width: `${piece.size}px`,\n              height: `${piece.size}px`,\n              backgroundColor: piece.color,\n              borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n              clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n              animation: `confetti-fall ${piece.duration}s ease-out ${piece.delay}s forwards`,\n              top: '-20px',\n              boxShadow: `0 0 ${piece.size}px ${piece.color}60`,\n              border: `1px solid ${piece.color}`,\n              background: `linear-gradient(45deg, ${piece.color}, ${piece.color}80)`,\n              zIndex: 100\n            }}\n          />\n        );\n      })}\n\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-20px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n            transform: translateY(0px) rotate(36deg) scale(1);\n          }\n          100% {\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\n            opacity: 0;\n          }\n        }\n\n        @keyframes confetti-burst {\n          0% {\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          15% {\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\n            opacity: 0;\n          }\n        }\n\n        @keyframes premium-sparkle {\n          0%, 100% {\n            transform: scale(0) rotate(0deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.5) rotate(180deg);\n            opacity: 1;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes text-bounce {\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n          40% { transform: translateY(-20px) scale(1.1); }\n          60% { transform: translateY(-10px) scale(1.05); }\n        }\n\n        @keyframes text-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\n            transform: scale(1);\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\n            transform: scale(1.05);\n          }\n        }\n\n        @keyframes rainbow-text {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D;\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F;\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF;\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6;\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4;\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n        }\n\n        @keyframes shake-celebrate {\n          0%, 100% { transform: translateX(0) rotate(0deg); }\n          10% { transform: translateX(-5px) rotate(-1deg); }\n          20% { transform: translateX(5px) rotate(1deg); }\n          30% { transform: translateX(-5px) rotate(-1deg); }\n          40% { transform: translateX(5px) rotate(1deg); }\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\n          60% { transform: translateX(3px) rotate(0.5deg); }\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\n          80% { transform: translateX(2px) rotate(0.5deg); }\n          90% { transform: translateX(-1px) rotate(0deg); }\n        }\n\n        @keyframes zoom-in-out {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.2); }\n        }\n\n        .animate-text-bounce {\n          animation: text-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-text-glow {\n          animation: text-glow 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-shake-celebrate {\n          animation: shake-celebrate 1s ease-in-out infinite;\n        }\n\n        .animate-zoom {\n          animation: zoom-in-out 2s ease-in-out infinite;\n        }\n\n        @keyframes elegant-scale {\n          0%, 100% {\n            transform: scale(1) rotateY(0deg);\n            text-shadow: 0 0 20px currentColor;\n          }\n          50% {\n            transform: scale(1.1) rotateY(5deg);\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\n          }\n        }\n\n        @keyframes premium-pulse {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 1;\n          }\n          50% {\n            transform: scale(1.05);\n            opacity: 0.9;\n          }\n        }\n\n        @keyframes smooth-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;\n            filter: brightness(1);\n          }\n          50% {\n            text-shadow: 0 0 30px currentColor, 0 0 50px currentColor, 0 0 70px currentColor;\n            filter: brightness(1.2);\n          }\n        }\n\n        @keyframes celebration-bounce {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0) scale(1);\n          }\n          40% {\n            transform: translateY(-15px) scale(1.05);\n          }\n          60% {\n            transform: translateY(-8px) scale(1.02);\n          }\n        }\n\n        .animate-elegant {\n          animation: elegant-scale 3s ease-in-out infinite;\n        }\n\n        .animate-premium-pulse {\n          animation: premium-pulse 2s ease-in-out infinite;\n        }\n\n        .animate-smooth-glow {\n          animation: smooth-glow 2.5s ease-in-out infinite;\n        }\n\n        .animate-celebration {\n          animation: celebration-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        @keyframes premium-green-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        @keyframes premium-red-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        .flash-green {\n          animation: premium-green-flash 0.8s ease-in-out 2;\n        }\n\n        .flash-red {\n          animation: premium-red-flash 0.8s ease-in-out 2;\n        }\n      `}</style>\n\n      {/* Premium Overlay Effect */}\n      {isFlashing && (\n        <div\n          className=\"fixed inset-0 pointer-events-none\"\n          style={{\n            background: isPassed\n              ? 'radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)'\n              : 'radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)',\n            animation: isPassed ? 'premium-green-flash 0.8s ease-in-out infinite' : 'premium-red-flash 0.8s ease-in-out infinite',\n            zIndex: 5\n          }}\n        />\n      )}\n\n      <div className={`bg-white rounded-2xl shadow-2xl border-2 p-8 max-w-2xl w-full relative ${\n        isPassed ? 'border-green-400 shadow-green-200' : 'border-red-400 shadow-red-200'\n      } ${isFlashing ? 'shadow-3xl' : ''}`}\n      style={{\n        background: isFlashing\n          ? (isPassed\n              ? 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))'\n              : 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))')\n          : 'white',\n        boxShadow: isFlashing\n          ? (isPassed\n              ? '0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)'\n              : '0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)')\n          : '0 25px 50px rgba(0,0,0,0.15)',\n        zIndex: 10\n      }}>\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${\n            isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'\n          }`}>\n\n            <TbTrophy className={`w-12 h-12 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`} />\n          </div>\n          \n          <h1 className={`text-6xl font-bold mb-4 ${\n            isPassed\n              ? 'text-green-600 animate-elegant animate-smooth-glow'\n              : 'text-red-600 animate-premium-pulse animate-smooth-glow'\n          }`}>\n            {isPassed ? (\n              <span className=\"flex items-center justify-center gap-4\">\n                <span className=\"animate-celebration text-7xl\">🎉</span>\n                <span className=\"animate-rainbow animate-elegant\">Congratulations!</span>\n                <span className=\"animate-celebration text-7xl\">🎉</span>\n              </span>\n            ) : (\n              <span className=\"flex items-center justify-center gap-4\">\n                <span className=\"animate-premium-pulse text-7xl\">💪</span>\n                <span className=\"animate-smooth-glow\">Keep Going!</span>\n                <span className=\"animate-premium-pulse text-7xl\">💪</span>\n              </span>\n            )}\n          </h1>\n\n          <div className={`text-3xl font-bold mb-4 ${\n            isPassed\n              ? 'text-green-700 animate-celebration animate-rainbow'\n              : 'text-red-700 animate-premium-pulse animate-smooth-glow'\n          }`}>\n            {isPassed ? (\n              <span className=\"animate-elegant\">✨ You Passed! ✨</span>\n            ) : (\n              <span className=\"animate-smooth-glow\">🌟 You Can Do It! 🌟</span>\n            )}\n          </div>\n\n          <p className=\"text-gray-600 mt-2 text-lg\">\n            📚 {quizName} - {quizSubject}\n          </p>\n        </div>\n\n        {/* Score Display */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-block px-8 py-4 rounded-2xl ${\n            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'\n          }`}>\n            <div className={`text-5xl font-bold mb-2 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {percentage}%\n            </div>\n            <div className=\"text-gray-600\">\n              Your Score\n            </div>\n          </div>\n        </div>\n\n\n\n        {/* Streamlined Results Cards */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-green-600\">{correctAnswers}</div>\n            <div className=\"text-sm text-green-600 font-medium\">✅ Correct</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-red-600\">{totalQuestions - correctAnswers}</div>\n            <div className=\"text-sm text-red-600 font-medium\">❌ Wrong</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className={`text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`}>{percentage}%</div>\n            <div className=\"text-sm text-gray-600 font-medium\">📊 Score</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-blue-600\">\n              {Math.floor(timeTaken / 60)}:{(timeTaken % 60).toString().padStart(2, '0')}\n            </div>\n            <div className=\"text-sm text-gray-600 font-medium\">⏱️ Time</div>\n          </div>\n        </div>\n\n        {/* Streamlined XP Section */}\n        {xpData && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-purple-600 font-bold text-xl\">+{xpData.xpAwarded || 0} XP</div>\n                  <div className=\"text-sm text-gray-600\">Earned</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {((user?.totalXP || 0) + (xpData.xpAwarded || 0)).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user?.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Streamlined XP Section (for users without XP data) */}\n        {!xpData && user && (\n          <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-sm text-gray-600\">Your Progress</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {(user.totalXP || 0).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Summary Section */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n              <TbBrain className=\"w-6 h-6 text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">📚 Learning Summary</h3>\n          </div>\n\n          {/* Simplified Learning Summary - Only Question Review */}\n        </div>\n\n        {/* Detailed Question Breakdown */}\n        {resultDetails && resultDetails.length > 0 && (\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\">\n                <TbChartBar className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">📋 Question by Question Review</h3>\n            </div>\n\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              {resultDetails.map((detail, index) => {\n                // Debug: Log question data to see what's available\n                console.log(`Question ${index + 1} data:`, detail);\n                return (\n                <div\n                  key={detail.questionId || index}\n                  className={`rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl ${\n                    detail.isCorrect\n                      ? 'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300'\n                      : 'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'\n                  }`}\n                  style={{\n                    boxShadow: detail.isCorrect\n                      ? '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)'\n                      : '0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'\n                  }}\n                >\n                  {/* Question Header */}\n                  <div className={`p-4 ${\n                    detail.isCorrect\n                      ? 'bg-green-300 border-b-4 border-green-500'\n                      : 'bg-red-300 border-b-4 border-red-500'\n                  }`}>\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${\n                        detail.isCorrect\n                          ? 'bg-green-500 text-white shadow-lg'\n                          : 'bg-red-500 text-white shadow-lg'\n                      }`}>\n                        {detail.isCorrect ? <TbCheck className=\"w-5 h-5\" /> : <TbX className=\"w-5 h-5\" />}\n                      </div>\n\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-bold text-gray-900 text-lg\">\n                          Question {index + 1}\n                        </h4>\n                        <div className=\"flex items-center gap-2 mt-1\">\n                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${\n                            detail.isCorrect\n                              ? 'bg-green-500 text-white'\n                              : 'bg-red-500 text-white'\n                          }`}>\n                            {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4\">\n                    <div className=\"mb-4\">\n                      <p className=\"text-gray-700 bg-white p-3 rounded-lg border\">\n                        {detail.questionText || detail.questionName}\n                      </p>\n                    </div>\n\n                    {/* Show Image for image questions or any question with an image */}\n                    {(detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && (\n                      <div className=\"mb-4\">\n                        <div className={`p-3 rounded-lg border-2 ${\n                          detail.isCorrect\n                            ? 'bg-green-50 border-green-200'\n                            : 'bg-red-50 border-red-200'\n                        }`}>\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <span className=\"text-sm font-semibold text-gray-700\">📷 Question Image:</span>\n                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${\n                              detail.isCorrect\n                                ? 'bg-green-500 text-white'\n                                : 'bg-red-500 text-white'\n                            }`}>\n                              {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                            </span>\n                          </div>\n                          <div className=\"bg-white p-2 rounded-lg border\">\n                            <img\n                              src={detail.questionImage || detail.image || detail.imageUrl}\n                              alt=\"Question Image\"\n                              className=\"max-w-full h-auto rounded-lg shadow-sm mx-auto block\"\n                              style={{ maxHeight: '300px' }}\n                              onError={(e) => {\n                                e.target.style.display = 'none';\n                                e.target.nextSibling.style.display = 'block';\n                              }}\n                            />\n                            <div\n                              className=\"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\"\n                              style={{ display: 'none' }}\n                            >\n                              📷 Image could not be loaded\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Enhanced Answer Section with Color Indicators */}\n                    <div className=\"space-y-3\">\n                      <div className={`p-4 rounded-lg border-4 ${\n                        detail.isCorrect\n                          ? 'bg-green-50 border-green-500'\n                          : 'bg-red-50 border-red-500'\n                      }`}>\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${\n                            detail.isCorrect ? 'bg-green-500' : 'bg-red-500'\n                          }`}>\n                            {detail.isCorrect ? (\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            ) : (\n                              <TbX className=\"w-4 h-4 text-white\" />\n                            )}\n                          </div>\n                          <span className=\"font-semibold text-gray-700\">Your Answer:</span>\n                        </div>\n                        <div className={`p-3 rounded-lg font-bold text-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-100 text-green-700 border border-green-200'\n                            : 'bg-red-100 text-red-700 border border-red-200'\n                        }`}>\n                          {detail.userAnswer || 'No answer provided'}\n                        </div>\n                      </div>\n\n                      {!detail.isCorrect && (\n                        <div className=\"bg-green-50 p-4 rounded-lg border-4 border-green-500\">\n                          <div className=\"flex items-center gap-3 mb-2\">\n                            <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\">\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            </div>\n                            <span className=\"font-semibold text-gray-700\">Correct Answer:</span>\n                          </div>\n                          <div className=\"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\">\n                            {detail.correctAnswer}\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Explanation Button for Wrong Answers */}\n                      {!detail.isCorrect && (\n                        <div className=\"mt-3\">\n                          <button\n                            className={`w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${\n                              loadingExplanations[`question_${index}`]\n                                ? 'bg-gray-400 cursor-not-allowed'\n                                : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'\n                            }`}\n                            onClick={() => fetchExplanation(index, detail)}\n                            disabled={loadingExplanations[`question_${index}`]}\n                          >\n                            {loadingExplanations[`question_${index}`] ? (\n                              <>\n                                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                                Getting Explanation...\n                              </>\n                            ) : (\n                              <>\n                                <TbBrain className=\"w-5 h-5\" />\n                                Get Explanation\n                              </>\n                            )}\n                          </button>\n\n                          {/* Explanation Display */}\n                          {explanations[`question_${index}`] && (\n                            <div className=\"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\">\n                              <div className=\"flex items-center gap-2 mb-3\">\n                                <TbBrain className=\"w-5 h-5 text-blue-600\" />\n                                <h6 className=\"font-bold text-blue-800\">💡 Explanation:</h6>\n                              </div>\n                              <div className=\"text-blue-700 leading-relaxed whitespace-pre-wrap\">\n                                {explanations[`question_${index}`]}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                );\n              })}\n            </div>\n\n\n          </div>\n        )}\n\n        {/* Actions */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 More Quizzes button clicked!');\n              handleBackToQuizzes();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbHome className=\"w-5 h-5\" />\n            More Quizzes\n          </button>\n\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 Retake Quiz button clicked!');\n              handleRetakeQuiz();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbTrophy className=\"w-5 h-5\" />\n            Retake Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACnE,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AAC5F,SAASC,2BAA2B,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB;EAAG,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEmB;EAAK,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD;EACA,MAAME,UAAU,GAAGJ,QAAQ,CAACG,KAAK,IAAI;IACnCE,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,EAAE;IACjBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,SAAS;IACtBC,iBAAiB,EAAE,EAAE;IACrBC,OAAO,EAAE;EACX,CAAC;EAED,MAAM;IACJT,UAAU;IACVC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTE,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,iBAAiB;IACjBC,OAAO;IACPL;EACF,CAAC,GAAGL,UAAU;EACd,MAAMW,QAAQ,GAAGD,OAAO,KAAK,MAAM,IAAIT,UAAU,KAAKQ,iBAAiB,IAAI,EAAE,CAAC;EAE9E,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACwC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClE,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAD,SAAS,CAAC,MAAM;IAEd;IACA,MAAM6C,SAAS,GAAGA,CAAA,KAAM;MACtB;MACAD,aAAa,CAAC,IAAI,CAAC;MACnBE,UAAU,CAAC,MAAMF,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAE9C,IAAI;QACF,IAAIR,QAAQ,EAAE;UACZ;UACA,MAAMW,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;;UAE7E;UACA,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,GAAG,MAAM,KAAK;YAClE,MAAMC,UAAU,GAAGT,YAAY,CAACU,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGX,YAAY,CAACY,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;YAE1CL,UAAU,CAACJ,SAAS,CAACU,cAAc,CAACV,SAAS,EAAEC,SAAS,CAAC;YACzDG,UAAU,CAACD,IAAI,GAAGA,IAAI;YAEtBG,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAET,SAAS,CAAC;YAC1CK,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC5DK,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEE,UAAU,CAACU,KAAK,CAACb,SAAS,CAAC;YAC3BG,UAAU,CAACW,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAMc,UAAU,GAAIf,SAAS,IAAK;YAChC,MAAMgB,KAAK,GAAGtB,YAAY,CAACuB,kBAAkB,CAAC,CAAC;YAC/C,MAAMC,MAAM,GAAGxB,YAAY,CAACyB,YAAY,CAAC,CAAC,EAAEzB,YAAY,CAAC0B,UAAU,GAAG,GAAG,EAAE1B,YAAY,CAAC0B,UAAU,CAAC;YACnG,MAAMC,IAAI,GAAGH,MAAM,CAACI,cAAc,CAAC,CAAC,CAAC;;YAErC;YACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;cACpCF,IAAI,CAACE,CAAC,CAAC,GAAGE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;YACjC;YAEAV,KAAK,CAACE,MAAM,GAAGA,MAAM;YAErB,MAAMS,MAAM,GAAGjC,YAAY,CAACkC,kBAAkB,CAAC,CAAC;YAChDD,MAAM,CAACzB,IAAI,GAAG,UAAU;YACxByB,MAAM,CAAC5B,SAAS,CAACU,cAAc,CAAC,IAAI,EAAET,SAAS,CAAC;YAEhD,MAAMK,QAAQ,GAAGX,YAAY,CAACY,UAAU,CAAC,CAAC;YAC1CD,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAET,SAAS,CAAC;YAC1CK,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC5DK,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAG,GAAG,CAAC;YAEjEgB,KAAK,CAACT,OAAO,CAACoB,MAAM,CAAC;YACrBA,MAAM,CAACpB,OAAO,CAACF,QAAQ,CAAC;YACxBA,QAAQ,CAACE,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;YAE1CQ,KAAK,CAACH,KAAK,CAACb,SAAS,CAAC;YACtBgB,KAAK,CAACF,IAAI,CAACd,SAAS,GAAG,GAAG,CAAC;UAC7B,CAAC;UAED,MAAM6B,GAAG,GAAGnC,YAAY,CAACoC,WAAW;;UAEpC;UACAhC,QAAQ,CAAC,MAAM,EAAE+B,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAC5B/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClC/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClC/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;UAElC;UACAd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;UACrBd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;UACrBd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;UACrBd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;QAEvB,CAAC,MAAM;UACL;UACA,MAAMnC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;UAE7E,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,KAAK;YACnD,MAAME,UAAU,GAAGT,YAAY,CAACU,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGX,YAAY,CAACY,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;YAE1CL,UAAU,CAACJ,SAAS,CAACU,cAAc,CAACV,SAAS,EAAEC,SAAS,CAAC;YACzDG,UAAU,CAACD,IAAI,GAAG,MAAM;YAExBG,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAET,SAAS,CAAC;YAC1CK,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,IAAI,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC7DK,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEE,UAAU,CAACU,KAAK,CAACb,SAAS,CAAC;YAC3BG,UAAU,CAACW,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAM4B,GAAG,GAAGnC,YAAY,CAACoC,WAAW;UACpChC,QAAQ,CAAC,GAAG,EAAE+B,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UACzB/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACpC;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACpC;IACF,CAAC;;IAED;IACA,IAAIlD,QAAQ,EAAE;MACZ;MACA,MAAMmD,eAAe,GAAG,EAAE;;MAE1B;MACA,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5B,MAAMY,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CACtD;QAEDD,eAAe,CAACE,IAAI,CAAC;UACnBnE,EAAE,EAAG,YAAWsD,CAAE,EAAC;UACnBc,IAAI,EAAE,EAAE,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UAAE;UAC/BY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Ba,KAAK,EAAEJ,MAAM,CAACV,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGS,MAAM,CAACX,MAAM,CAAC,CAAC;UACxDiB,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAChB,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACtEgB,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC3BxB,IAAI,EAAE,UAAU;UAChByC,OAAO,EAAE,CAAClB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;QACvC,CAAC,CAAC;MACJ;;MAEA;MACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BW,eAAe,CAACE,IAAI,CAAC;UACnBnE,EAAE,EAAG,WAAUsD,CAAE,EAAC;UAClBc,IAAI,EAAEZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Ba,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACd,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClFgB,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC3BxB,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;;MAEA;MACA,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5BW,eAAe,CAACE,IAAI,CAAC;UACnBnE,EAAE,EAAG,SAAQsD,CAAE,EAAC;UAChBc,IAAI,EAAE,EAAE,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UAAE;UAC/BY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC1BzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Ba,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACd,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClFe,KAAK,EAAE,QAAQ;UACfC,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC3BxB,IAAI,EAAE,OAAO;UACbyC,OAAO,EAAE,CAAClB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;QACnC,CAAC,CAAC;MACJ;MAEAzC,WAAW,CAACiD,eAAe,CAAC;;MAE5B;MACAzC,UAAU,CAAC,MAAMR,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;IAC1C,CAAC,MAAM;MACL;MACA,MAAM2D,oBAAoB,GAAG,EAAE;MAC/B,MAAMC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;MAErF,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BqB,oBAAoB,CAACR,IAAI,CAAC;UACxBnE,EAAE,EAAG,YAAWsD,CAAE,EAAC;UACnBc,IAAI,EAAEZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BoB,KAAK,EAAED,kBAAkB,CAACpB,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGmB,kBAAkB,CAACrB,MAAM,CAAC,CAAC;UAChFuB,cAAc,EAAE,IAAI;UACpBL,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAC5B,CAAC,CAAC;MACJ;MACAzC,WAAW,CAAC2D,oBAAoB,CAAC;;MAEjC;MACAnD,UAAU,CAAC,MAAMR,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACzC;IAEAO,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAId;EACA,MAAMiE,gBAAgB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,MAAM,KAAK;IACxD,MAAMC,WAAW,GAAI,YAAWF,aAAc,EAAC;;IAE/C;IACA,IAAI7D,mBAAmB,CAAC+D,WAAW,CAAC,IAAIjE,YAAY,CAACiE,WAAW,CAAC,EAAE;MACjE;IACF;IAEA,IAAI;MACF9D,sBAAsB,CAAC+D,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,WAAW,GAAG;MAAK,CAAC,CAAC,CAAC;MAElE,MAAME,QAAQ,GAAG,MAAM7F,2BAA2B,CAAC;QACjD8F,QAAQ,EAAEJ,MAAM,CAACK,YAAY,IAAIL,MAAM,CAACM,YAAY;QACpDC,cAAc,EAAEP,MAAM,CAACQ,aAAa;QACpCC,UAAU,EAAET,MAAM,CAACS,UAAU;QAC7BC,QAAQ,EAAEV,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAQ,IAAI;MACvE,CAAC,CAAC;MAEF,IAAIP,QAAQ,CAACU,OAAO,EAAE;QACpB5E,eAAe,CAACiE,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACD,WAAW,GAAGE,QAAQ,CAACW;QAC1B,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLhC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEsB,QAAQ,CAACtB,KAAK,CAAC;QAC7D5C,eAAe,CAACiE,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACD,WAAW,GAAG;QACjB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD5C,eAAe,CAACiE,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACD,WAAW,GAAG;MACjB,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACR9D,sBAAsB,CAAC+D,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,WAAW,GAAG;MAAM,CAAC,CAAC,CAAC;IACrE;EACF,CAAC;EAID,MAAMc,mBAAmB,GAAGA,CAAA,KAAM;IAChCjC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CvF,eAAe,CAAC,MAAM;MACpBqB,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhE,EAAE,CAAC;IAC5C,IAAIA,EAAE,EAAE;MACNvB,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAE,SAAQE,EAAG,OAAM,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL+D,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DvF,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEL,OAAA;IAAKyG,SAAS,EAAG,8EACfpF,QAAQ,GACJ,4DAA4D,GAC5D,yDACL,IAAGO,UAAU,GAAIP,QAAQ,GAAG,aAAa,GAAG,WAAW,GAAI,EAAG,EAAE;IAAAqF,QAAA,GAG9DpF,QAAQ,CAACqF,GAAG,CAAEC,KAAK,IAAK;MACvB,IAAIA,KAAK,CAACvB,cAAc,EAAE;QACxB,oBACErF,OAAA;UAEEyG,SAAS,EAAC,qBAAqB;UAC/BI,KAAK,EAAE;YACLlC,IAAI,EAAG,GAAEiC,KAAK,CAACjC,IAAK,GAAE;YACtBmC,GAAG,EAAG,GAAE,EAAE,GAAG/C,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAG,GAAE;YAClC+C,QAAQ,EAAG,GAAEH,KAAK,CAAC5B,IAAI,IAAI,CAAE,KAAI;YACjCgC,SAAS,EAAG,cAAaJ,KAAK,CAACrE,QAAS,iBAAgBqE,KAAK,CAAChC,KAAM,YAAW;YAC/EqC,MAAM,EAAE;UACV,CAAE;UAAAP,QAAA,EAEDE,KAAK,CAACxB;QAAK,GAVPwB,KAAK,CAACrG,EAAE;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWV,CAAC;MAEV;MAEA,IAAIT,KAAK,CAACpE,IAAI,KAAK,SAAS,EAAE;QAC5B,oBACExC,OAAA;UAEEyG,SAAS,EAAC,UAAU;UACpBI,KAAK,EAAE;YACLlC,IAAI,EAAG,GAAEiC,KAAK,CAACjC,IAAK,GAAE;YACtB2C,KAAK,EAAG,GAAEV,KAAK,CAAC5B,IAAK,IAAG;YACxBuC,MAAM,EAAG,GAAEX,KAAK,CAAC5B,IAAK,IAAG;YACzBwC,UAAU,EAAG,2BAA0BZ,KAAK,CAAC/B,KAAM,gBAAe;YAClE4C,YAAY,EAAE,KAAK;YACnBT,SAAS,EAAG,mBAAkBJ,KAAK,CAACrE,QAAS,iBAAgBqE,KAAK,CAAChC,KAAM,YAAW;YACpFkC,GAAG,EAAG,GAAE/C,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC9B0D,SAAS,EAAG,OAAMd,KAAK,CAAC5B,IAAI,GAAG,CAAE,MAAK4B,KAAK,CAAC/B,KAAM,EAAC;YACnDoC,MAAM,EAAE;UACV;QAAE,GAZGL,KAAK,CAACrG,EAAE;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAad,CAAC;MAEN;MAEA,IAAIT,KAAK,CAACpE,IAAI,KAAK,OAAO,EAAE;QAC1B,oBACExC,OAAA;UAEEyG,SAAS,EAAC,qBAAqB;UAC/BI,KAAK,EAAE;YACLlC,IAAI,EAAG,GAAEiC,KAAK,CAACjC,IAAK,GAAE;YACtB2C,KAAK,EAAG,GAAEV,KAAK,CAAC5B,IAAK,IAAG;YACxBuC,MAAM,EAAG,GAAEX,KAAK,CAAC5B,IAAK,IAAG;YACzB2C,eAAe,EAAEf,KAAK,CAAC/B,KAAK;YAC5B4C,YAAY,EAAEb,KAAK,CAAC7B,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG6B,KAAK,CAAC7B,KAAK,KAAK,UAAU,GAAG,GAAG,GAAG,IAAI;YACxF6C,QAAQ,EAAEhB,KAAK,CAAC7B,KAAK,KAAK,UAAU,GAAG,qCAAqC,GAAG,MAAM;YACrFiC,SAAS,EAAG,kBAAiBJ,KAAK,CAACrE,QAAS,cAAaqE,KAAK,CAAChC,KAAM,YAAW;YAChFkC,GAAG,EAAE,KAAK;YACV,YAAY,EAAG,GAAEF,KAAK,CAAC3B,OAAQ,IAAG;YAClCyC,SAAS,EAAG,OAAMd,KAAK,CAAC5B,IAAK,MAAK4B,KAAK,CAAC/B,KAAM,IAAG;YACjDoC,MAAM,EAAE;UACV;QAAE,GAdGL,KAAK,CAACrG,EAAE;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAed,CAAC;MAEN;;MAEA;MACA,oBACErH,OAAA;QAEEyG,SAAS,EAAC,qBAAqB;QAC/BI,KAAK,EAAE;UACLlC,IAAI,EAAG,GAAEiC,KAAK,CAACjC,IAAK,GAAE;UACtB2C,KAAK,EAAG,GAAEV,KAAK,CAAC5B,IAAK,IAAG;UACxBuC,MAAM,EAAG,GAAEX,KAAK,CAAC5B,IAAK,IAAG;UACzB2C,eAAe,EAAEf,KAAK,CAAC/B,KAAK;UAC5B4C,YAAY,EAAEb,KAAK,CAAC7B,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG6B,KAAK,CAAC7B,KAAK,KAAK,UAAU,GAAG,GAAG,GAAG,IAAI;UACxF6C,QAAQ,EAAEhB,KAAK,CAAC7B,KAAK,KAAK,UAAU,GAAG,qCAAqC,GAAG,MAAM;UACrFiC,SAAS,EAAG,iBAAgBJ,KAAK,CAACrE,QAAS,cAAaqE,KAAK,CAAChC,KAAM,YAAW;UAC/EkC,GAAG,EAAE,OAAO;UACZY,SAAS,EAAG,OAAMd,KAAK,CAAC5B,IAAK,MAAK4B,KAAK,CAAC/B,KAAM,IAAG;UACjDgD,MAAM,EAAG,aAAYjB,KAAK,CAAC/B,KAAM,EAAC;UAClC2C,UAAU,EAAG,0BAAyBZ,KAAK,CAAC/B,KAAM,KAAI+B,KAAK,CAAC/B,KAAM,KAAI;UACtEoC,MAAM,EAAE;QACV;MAAE,GAfGL,KAAK,CAACrG,EAAE;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBd,CAAC;IAEN,CAAC,CAAC,eAGFrH,OAAA;MAAO8H,GAAG;MAAApB,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAGTzF,UAAU,iBACT5B,OAAA;MACEyG,SAAS,EAAC,mCAAmC;MAC7CI,KAAK,EAAE;QACLW,UAAU,EAAEnG,QAAQ,GAChB,6GAA6G,GAC7G,6GAA6G;QACjH2F,SAAS,EAAE3F,QAAQ,GAAG,+CAA+C,GAAG,6CAA6C;QACrH4F,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,eAEDrH,OAAA;MAAKyG,SAAS,EAAG,0EACfpF,QAAQ,GAAG,mCAAmC,GAAG,+BAClD,IAAGO,UAAU,GAAG,YAAY,GAAG,EAAG,EAAE;MACrCiF,KAAK,EAAE;QACLW,UAAU,EAAE5F,UAAU,GACjBP,QAAQ,GACL,0EAA0E,GAC1E,0EAA0E,GAC9E,OAAO;QACXqG,SAAS,EAAE9F,UAAU,GAChBP,QAAQ,GACL,sEAAsE,GACtE,sEAAsE,GAC1E,8BAA8B;QAClC4F,MAAM,EAAE;MACV,CAAE;MAAAP,QAAA,gBAEA1G,OAAA;QAAKyG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B1G,OAAA;UAAKyG,SAAS,EAAG,gFACfpF,QAAQ,GAAG,iDAAiD,GAAG,4CAChE,EAAE;UAAAqF,QAAA,eAED1G,OAAA,CAACT,QAAQ;YAACkH,SAAS,EAAG,aACpBpF,QAAQ,GAAG,gBAAgB,GAAG,cAC/B;UAAE;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrH,OAAA;UAAIyG,SAAS,EAAG,2BACdpF,QAAQ,GACJ,oDAAoD,GACpD,wDACL,EAAE;UAAAqF,QAAA,EACArF,QAAQ,gBACPrB,OAAA;YAAMyG,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACtD1G,OAAA;cAAMyG,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDrH,OAAA;cAAMyG,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAgB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzErH,OAAA;cAAMyG,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,gBAEPrH,OAAA;YAAMyG,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACtD1G,OAAA;cAAMyG,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DrH,OAAA;cAAMyG,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDrH,OAAA;cAAMyG,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAELrH,OAAA;UAAKyG,SAAS,EAAG,2BACfpF,QAAQ,GACJ,oDAAoD,GACpD,wDACL,EAAE;UAAAqF,QAAA,EACArF,QAAQ,gBACPrB,OAAA;YAAMyG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAExDrH,OAAA;YAAMyG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAoB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACjE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENrH,OAAA;UAAGyG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,eACrC,EAACzF,QAAQ,EAAC,KAAG,EAACC,WAAW;QAAA;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNrH,OAAA;QAAKyG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B1G,OAAA;UAAKyG,SAAS,EAAG,sCACfpF,QAAQ,GAAG,uCAAuC,GAAG,mCACtD,EAAE;UAAAqF,QAAA,gBACD1G,OAAA;YAAKyG,SAAS,EAAG,2BACfpF,QAAQ,GAAG,gBAAgB,GAAG,cAC/B,EAAE;YAAAqF,QAAA,GACA/F,UAAU,EAAC,GACd;UAAA;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNrH,OAAA;YAAKyG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAKNrH,OAAA;QAAKyG,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzD1G,OAAA;UAAKyG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnF1G,OAAA;YAAKyG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAE9F;UAAc;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzErH,OAAA;YAAKyG,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACNrH,OAAA;UAAKyG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnF1G,OAAA;YAAKyG,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAE7F,cAAc,GAAGD;UAAc;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxFrH,OAAA;YAAKyG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eACNrH,OAAA;UAAKyG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnF1G,OAAA;YAAKyG,SAAS,EAAG,sBAAqBpF,QAAQ,GAAG,gBAAgB,GAAG,cAAe,EAAE;YAAAqF,QAAA,GAAE/F,UAAU,EAAC,GAAC;UAAA;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzGrH,OAAA;YAAKyG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNrH,OAAA;UAAKyG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnF1G,OAAA;YAAKyG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAC9C3C,IAAI,CAACe,KAAK,CAAChE,SAAS,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC,CAACA,SAAS,GAAG,EAAE,EAAEiH,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNrH,OAAA;YAAKyG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLrG,MAAM,iBACLhB,OAAA;QAAKyG,SAAS,EAAC,2FAA2F;QAAAC,QAAA,eACxG1G,OAAA;UAAKyG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1G,OAAA;YAAKyG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC1G,OAAA;cAAKyG,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eACpF1G,OAAA,CAACL,MAAM;gBAAC8G,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNrH,OAAA;cAAA0G,QAAA,gBACE1G,OAAA;gBAAKyG,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,GAAC,EAAC1F,MAAM,CAACiH,SAAS,IAAI,CAAC,EAAC,KAAG;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpFrH,OAAA;gBAAKyG,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrH,OAAA;YAAKyG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1G,OAAA;cAAKyG,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD,CAAC,CAAC,CAAAlG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0H,OAAO,KAAI,CAAC,KAAKlH,MAAM,CAACiH,SAAS,IAAI,CAAC,CAAC,EAAEE,cAAc,CAAC;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNrH,OAAA;cAAKyG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBAAiB,EAAC,CAAAlG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4H,YAAY,KAAI,CAAC;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACrG,MAAM,IAAIR,IAAI,iBACdR,OAAA;QAAKyG,SAAS,EAAC,2FAA2F;QAAAC,QAAA,eACxG1G,OAAA;UAAKyG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1G,OAAA;YAAKyG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC1G,OAAA;cAAKyG,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eACpF1G,OAAA,CAACL,MAAM;gBAAC8G,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNrH,OAAA;cAAA0G,QAAA,eACE1G,OAAA;gBAAKyG,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrH,OAAA;YAAKyG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB1G,OAAA;cAAKyG,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD,CAAClG,IAAI,CAAC0H,OAAO,IAAI,CAAC,EAAEC,cAAc,CAAC;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNrH,OAAA;cAAKyG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBAAiB,EAAClG,IAAI,CAAC4H,YAAY,IAAI,CAAC;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDrH,OAAA;QAAKyG,SAAS,EAAC,qFAAqF;QAAAC,QAAA,eAClG1G,OAAA;UAAKyG,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C1G,OAAA;YAAKyG,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClF1G,OAAA,CAACJ,OAAO;cAAC6G,SAAS,EAAC;YAAoB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNrH,OAAA;YAAIyG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAmB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CAAC,EAGLtG,aAAa,IAAIA,aAAa,CAAC+C,MAAM,GAAG,CAAC,iBACxC9D,OAAA;QAAKyG,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBACnG1G,OAAA;UAAKyG,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3C1G,OAAA;YAAKyG,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClF1G,OAAA,CAACH,UAAU;cAAC4G,SAAS,EAAC;YAAoB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNrH,OAAA;YAAIyG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAA8B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAENrH,OAAA;UAAKyG,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChD3F,aAAa,CAAC4F,GAAG,CAAC,CAACnB,MAAM,EAAE6C,KAAK,KAAK;YACpC;YACA/D,OAAO,CAACC,GAAG,CAAE,YAAW8D,KAAK,GAAG,CAAE,QAAO,EAAE7C,MAAM,CAAC;YAClD,oBACAxF,OAAA;cAEEyG,SAAS,EAAG,mFACVjB,MAAM,CAAC8C,SAAS,GACZ,4FAA4F,GAC5F,mFACL,EAAE;cACHzB,KAAK,EAAE;gBACLa,SAAS,EAAElC,MAAM,CAAC8C,SAAS,GACvB,sEAAsE,GACtE;cACN,CAAE;cAAA5B,QAAA,gBAGF1G,OAAA;gBAAKyG,SAAS,EAAG,OACfjB,MAAM,CAAC8C,SAAS,GACZ,0CAA0C,GAC1C,sCACL,EAAE;gBAAA5B,QAAA,eACD1G,OAAA;kBAAKyG,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtC1G,OAAA;oBAAKyG,SAAS,EAAG,qEACfjB,MAAM,CAAC8C,SAAS,GACZ,mCAAmC,GACnC,iCACL,EAAE;oBAAA5B,QAAA,EACAlB,MAAM,CAAC8C,SAAS,gBAAGtI,OAAA,CAACR,OAAO;sBAACiH,SAAS,EAAC;oBAAS;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGrH,OAAA,CAACP,GAAG;sBAACgH,SAAS,EAAC;oBAAS;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eAENrH,OAAA;oBAAKyG,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrB1G,OAAA;sBAAIyG,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,GAAC,WACrC,EAAC2B,KAAK,GAAG,CAAC;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACLrH,OAAA;sBAAKyG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,eAC3C1G,OAAA;wBAAMyG,SAAS,EAAG,4CAChBjB,MAAM,CAAC8C,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAA5B,QAAA,EACAlB,MAAM,CAAC8C,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNrH,OAAA;gBAAKyG,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB1G,OAAA;kBAAKyG,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB1G,OAAA;oBAAGyG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EACxDlB,MAAM,CAACK,YAAY,IAAIL,MAAM,CAACM;kBAAY;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EAGL,CAAC7B,MAAM,CAAC+C,YAAY,KAAK,OAAO,IAAI/C,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAQ,MAAMV,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAQ,CAAC,iBACxJlG,OAAA;kBAAKyG,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB1G,OAAA;oBAAKyG,SAAS,EAAG,2BACfjB,MAAM,CAAC8C,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;oBAAA5B,QAAA,gBACD1G,OAAA;sBAAKyG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3C1G,OAAA;wBAAMyG,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/ErH,OAAA;wBAAMyG,SAAS,EAAG,4CAChBjB,MAAM,CAAC8C,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAA5B,QAAA,EACAlB,MAAM,CAAC8C,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNrH,OAAA;sBAAKyG,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBAC7C1G,OAAA;wBACEwI,GAAG,EAAEhD,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAS;wBAC7DuC,GAAG,EAAC,gBAAgB;wBACpBhC,SAAS,EAAC,sDAAsD;wBAChEI,KAAK,EAAE;0BAAE6B,SAAS,EAAE;wBAAQ,CAAE;wBAC9BC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,MAAM,CAAChC,KAAK,CAACiC,OAAO,GAAG,MAAM;0BAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAAClC,KAAK,CAACiC,OAAO,GAAG,OAAO;wBAC9C;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrH,OAAA;wBACEyG,SAAS,EAAC,8DAA8D;wBACxEI,KAAK,EAAE;0BAAEiC,OAAO,EAAE;wBAAO,CAAE;wBAAApC,QAAA,EAC5B;sBAED;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGDrH,OAAA;kBAAKyG,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB1G,OAAA;oBAAKyG,SAAS,EAAG,2BACfjB,MAAM,CAAC8C,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;oBAAA5B,QAAA,gBACD1G,OAAA;sBAAKyG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3C1G,OAAA;wBAAKyG,SAAS,EAAG,yDACfjB,MAAM,CAAC8C,SAAS,GAAG,cAAc,GAAG,YACrC,EAAE;wBAAA5B,QAAA,EACAlB,MAAM,CAAC8C,SAAS,gBACftI,OAAA,CAACR,OAAO;0BAACiH,SAAS,EAAC;wBAAoB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAE1CrH,OAAA,CAACP,GAAG;0BAACgH,SAAS,EAAC;wBAAoB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBACtC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNrH,OAAA;wBAAMyG,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAY;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNrH,OAAA;sBAAKyG,SAAS,EAAG,oCACfjB,MAAM,CAAC8C,SAAS,GACZ,qDAAqD,GACrD,+CACL,EAAE;sBAAA5B,QAAA,EACAlB,MAAM,CAACS,UAAU,IAAI;oBAAoB;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,CAAC7B,MAAM,CAAC8C,SAAS,iBAChBtI,OAAA;oBAAKyG,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,gBACnE1G,OAAA;sBAAKyG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3C1G,OAAA;wBAAKyG,SAAS,EAAC,oEAAoE;wBAAAC,QAAA,eACjF1G,OAAA,CAACR,OAAO;0BAACiH,SAAS,EAAC;wBAAoB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNrH,OAAA;wBAAMyG,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAe;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNrH,OAAA;sBAAKyG,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,EACpGlB,MAAM,CAACQ;oBAAa;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA,CAAC7B,MAAM,CAAC8C,SAAS,iBAChBtI,OAAA;oBAAKyG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB1G,OAAA;sBACEyG,SAAS,EAAG,gHACV/E,mBAAmB,CAAE,YAAW2G,KAAM,EAAC,CAAC,GACpC,gCAAgC,GAChC,2HACL,EAAE;sBACHW,OAAO,EAAEA,CAAA,KAAM1D,gBAAgB,CAAC+C,KAAK,EAAE7C,MAAM,CAAE;sBAC/CyD,QAAQ,EAAEvH,mBAAmB,CAAE,YAAW2G,KAAM,EAAC,CAAE;sBAAA3B,QAAA,EAElDhF,mBAAmB,CAAE,YAAW2G,KAAM,EAAC,CAAC,gBACvCrI,OAAA,CAAAE,SAAA;wBAAAwG,QAAA,gBACE1G,OAAA;0BAAKyG,SAAS,EAAC;wBAA8E;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,0BAEtG;sBAAA,eAAE,CAAC,gBAEHrH,OAAA,CAAAE,SAAA;wBAAAwG,QAAA,gBACE1G,OAAA,CAACJ,OAAO;0BAAC6G,SAAS,EAAC;wBAAS;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,mBAEjC;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC,EAGR7F,YAAY,CAAE,YAAW6G,KAAM,EAAC,CAAC,iBAChCrI,OAAA;sBAAKyG,SAAS,EAAC,yFAAyF;sBAAAC,QAAA,gBACtG1G,OAAA;wBAAKyG,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,gBAC3C1G,OAAA,CAACJ,OAAO;0BAAC6G,SAAS,EAAC;wBAAuB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CrH,OAAA;0BAAIyG,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,EAAC;wBAAe;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eACNrH,OAAA;wBAAKyG,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAC/DlF,YAAY,CAAE,YAAW6G,KAAM,EAAC;sBAAC;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA9KD7B,MAAM,CAAC0D,UAAU,IAAIb,KAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+K5B,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CACN,eAGDrH,OAAA;QAAKyG,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9C1G,OAAA;UACEgJ,OAAO,EAAGJ,CAAC,IAAK;YACdA,CAAC,CAACO,cAAc,CAAC,CAAC;YAClB7E,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9CgC,mBAAmB,CAAC,CAAC;UACvB,CAAE;UACFE,SAAS,EAAC,+NAA+N;UACzOjE,IAAI,EAAC,QAAQ;UAAAkE,QAAA,gBAEb1G,OAAA,CAACN,MAAM;YAAC+G,SAAS,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETrH,OAAA;UACEgJ,OAAO,EAAGJ,CAAC,IAAK;YACdA,CAAC,CAACO,cAAc,CAAC,CAAC;YAClB7E,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7CiC,gBAAgB,CAAC,CAAC;UACpB,CAAE;UACFC,SAAS,EAAC,kOAAkO;UAC5OjE,IAAI,EAAC,QAAQ;UAAAkE,QAAA,gBAEb1G,OAAA,CAACT,QAAQ;YAACkH,SAAS,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjH,EAAA,CA7hCID,UAAU;EAAA,QACGhB,WAAW,EACXC,WAAW,EACbC,SAAS,EACPC,WAAW;AAAA;AAAA8J,EAAA,GAJxBjJ,UAAU;AA+hChB,eAAeA,UAAU;AAAC,IAAAiJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}