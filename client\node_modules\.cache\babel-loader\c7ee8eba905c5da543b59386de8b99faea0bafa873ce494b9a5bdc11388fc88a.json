{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { message, Button, Input, Form, Card, Badge, Tooltip } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport { getAllQuestions, deleteQuestion, updateReplyStatus } from \"../../../apicalls/forum\";\nimport { FaCheck, FaTimes, FaEye, FaTrash } from \"react-icons/fa\";\nimport { MdMessage, MdVerified, MdPending } from \"react-icons/md\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminForum = () => {\n  _s();\n  const [questions, setQuestions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [expandedQuestions, setExpandedQuestions] = useState({});\n  const [stats, setStats] = useState({\n    totalQuestions: 0,\n    totalReplies: 0,\n    pendingReplies: 0,\n    verifiedReplies: 0\n  });\n  const dispatch = useDispatch();\n  const fetchQuestions = async () => {\n    setLoading(true);\n    dispatch(ShowLoading());\n    try {\n      const response = await getAllQuestions();\n      if (response.success) {\n        setQuestions(response.data);\n        calculateStats(response.data);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const calculateStats = questionsData => {\n    let totalReplies = 0;\n    let pendingReplies = 0;\n    let verifiedReplies = 0;\n    questionsData.forEach(question => {\n      totalReplies += question.replies.length;\n      question.replies.forEach(reply => {\n        if (!reply.user.isAdmin) {\n          if (reply.isVerified) {\n            verifiedReplies++;\n          } else {\n            pendingReplies++;\n          }\n        }\n      });\n    });\n    setStats({\n      totalQuestions: questionsData.length,\n      totalReplies,\n      pendingReplies,\n      verifiedReplies\n    });\n  };\n  const handleUpdateStatus = async (questionId, replyId, status) => {\n    try {\n      const response = await updateReplyStatus({\n        questionId,\n        replyId,\n        isVerified: status\n      });\n      if (response.success) {\n        message.success(status ? \"Reply approved successfully\" : \"Reply disapproved successfully\");\n        fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleDeleteQuestion = async questionId => {\n    try {\n      const response = await deleteQuestion({\n        questionId\n      });\n      if (response.success) {\n        message.success(\"Question deleted successfully\");\n        fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const toggleQuestion = questionId => {\n    setExpandedQuestions(prev => ({\n      ...prev,\n      [questionId]: !prev[questionId]\n    }));\n  };\n  useEffect(() => {\n    fetchQuestions();\n  }, []);\n  const StatCard = ({\n    title,\n    value,\n    icon: Icon,\n    color,\n    bgColor\n  }) => /*#__PURE__*/_jsxDEV(Card, {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `inline-flex items-center justify-center w-12 h-12 ${bgColor} rounded-lg mb-3`,\n      children: /*#__PURE__*/_jsxDEV(Icon, {\n        className: `w-6 h-6 ${color}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-2xl font-bold text-gray-900\",\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      title: \"Forum Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-2\",\n          children: \"Forum Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage community questions and verify user replies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Questions\",\n          value: stats.totalQuestions,\n          icon: MdMessage,\n          color: \"text-blue-600\",\n          bgColor: \"bg-blue-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Replies\",\n          value: stats.totalReplies,\n          icon: MdMessage,\n          color: \"text-green-600\",\n          bgColor: \"bg-green-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Pending Approval\",\n          value: stats.pendingReplies,\n          icon: MdPending,\n          color: \"text-orange-600\",\n          bgColor: \"bg-orange-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Verified Replies\",\n          value: stats.verifiedReplies,\n          icon: MdVerified,\n          color: \"text-green-600\",\n          bgColor: \"bg-green-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: questions.map(question => /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                  user: question.user,\n                  size: \"sm\",\n                  showOnlineStatus: false\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: question.user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: new Date(question.createdAt).toLocaleDateString('en-US', {\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric',\n                      hour: '2-digit',\n                      minute: '2-digit'\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Badge, {\n                  count: question.replies.length,\n                  showZero: true,\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    icon: /*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 31\n                    }, this),\n                    onClick: () => toggleQuestion(question._id),\n                    type: expandedQuestions[question._id] ? \"primary\" : \"default\",\n                    children: [expandedQuestions[question._id] ? \"Hide\" : \"View\", \" Replies\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 29\n                  }, this),\n                  danger: true,\n                  onClick: () => handleDeleteQuestion(question._id),\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-2\",\n              children: question.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 mb-4\",\n              children: question.body\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), expandedQuestions[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 space-y-4 bg-gray-50 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                children: [\"Replies (\", question.replies.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this), question.replies.map(reply => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `bg-white rounded-lg p-4 border-l-4 ${reply.user.isAdmin ? \"border-purple-500 bg-purple-50\" : reply.isVerified ? \"border-green-500 bg-green-50\" : \"border-orange-500 bg-orange-50\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                      user: reply.user,\n                      size: \"xs\",\n                      showOnlineStatus: false\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                          className: \"font-semibold text-gray-900\",\n                          children: reply.user.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 245,\n                          columnNumber: 33\n                        }, this), reply.user.isAdmin && /*#__PURE__*/_jsxDEV(Badge, {\n                          color: \"purple\",\n                          text: \"Admin\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 247,\n                          columnNumber: 35\n                        }, this), reply.isVerified && !reply.user.isAdmin && /*#__PURE__*/_jsxDEV(Badge, {\n                          color: \"green\",\n                          text: \"Verified\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 250,\n                          columnNumber: 35\n                        }, this), !reply.isVerified && !reply.user.isAdmin && /*#__PURE__*/_jsxDEV(Badge, {\n                          color: \"orange\",\n                          text: \"Pending\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 253,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 244,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: `text-sm mb-2 ${reply.isVerified && !reply.user.isAdmin ? 'text-green-800 font-medium' : reply.user.isAdmin ? 'text-purple-800 font-medium' : 'text-gray-700'}`,\n                        children: reply.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 256,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: (() => {\n                          try {\n                            const date = new Date(reply.createdAt);\n                            if (isNaN(date.getTime())) {\n                              return 'Invalid date';\n                            }\n                            return date.toLocaleDateString('en-US', {\n                              month: \"short\",\n                              day: \"numeric\",\n                              hour: \"2-digit\",\n                              minute: \"2-digit\"\n                            });\n                          } catch (error) {\n                            return 'Invalid date';\n                          }\n                        })()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 265,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 27\n                  }, this), !reply.user.isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: reply.isVerified ? \"Disapprove Reply\" : \"Approve Reply\",\n                      children: /*#__PURE__*/_jsxDEV(Button, {\n                        size: \"small\",\n                        type: reply.isVerified ? \"danger\" : \"primary\",\n                        icon: reply.isVerified ? /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 293,\n                          columnNumber: 60\n                        }, this) : /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 293,\n                          columnNumber: 74\n                        }, this),\n                        onClick: () => handleUpdateStatus(question._id, reply._id, !reply.isVerified),\n                        children: reply.isVerified ? \"Disapprove\" : \"Approve\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 290,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 25\n                }, this)\n              }, reply._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)\n        }, question._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), questions.length === 0 && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(MdMessage, {\n          className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"No questions found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Questions will appear here when users post them.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminForum, \"jhA7wJISoOnKC/gi6ofjmSDCPmY=\", false, function () {\n  return [useDispatch];\n});\n_c = AdminForum;\nexport default AdminForum;\nvar _c;\n$RefreshReg$(_c, \"AdminForum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "message", "<PERSON><PERSON>", "Input", "Form", "Card", "Badge", "<PERSON><PERSON><PERSON>", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "ProfilePicture", "getAllQuestions", "deleteQuestion", "updateReplyStatus", "FaCheck", "FaTimes", "FaEye", "FaTrash", "MdMessage", "MdVerified", "MdPending", "jsxDEV", "_jsxDEV", "AdminForum", "_s", "questions", "setQuestions", "loading", "setLoading", "expandedQuestions", "setExpandedQuestions", "stats", "setStats", "totalQuestions", "totalReplies", "pendingReplies", "verifiedReplies", "dispatch", "fetchQuestions", "response", "success", "data", "calculateStats", "error", "questionsData", "for<PERSON>ach", "question", "replies", "length", "reply", "user", "isAdmin", "isVerified", "handleUpdateStatus", "questionId", "replyId", "status", "handleDeleteQuestion", "toggleQuestion", "prev", "StatCard", "title", "value", "icon", "Icon", "color", "bgColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "size", "showOnlineStatus", "name", "Date", "createdAt", "toLocaleDateString", "year", "month", "day", "hour", "minute", "count", "showZero", "onClick", "_id", "type", "danger", "body", "text", "date", "isNaN", "getTime", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { message, Button, Input, Form, Card, Badge, Tooltip } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport {\n  getAllQuestions,\n  deleteQuestion,\n  updateReplyStatus,\n} from \"../../../apicalls/forum\";\nimport { FaCheck, FaTimes, FaEye, FaTrash } from \"react-icons/fa\";\nimport { MdMessage, MdVerified, MdPending } from \"react-icons/md\";\n\nconst AdminForum = () => {\n  const [questions, setQuestions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [expandedQuestions, setExpandedQuestions] = useState({});\n  const [stats, setStats] = useState({\n    totalQuestions: 0,\n    totalReplies: 0,\n    pendingReplies: 0,\n    verifiedReplies: 0\n  });\n  const dispatch = useDispatch();\n\n  const fetchQuestions = async () => {\n    setLoading(true);\n    dispatch(ShowLoading());\n    try {\n      const response = await getAllQuestions();\n      if (response.success) {\n        setQuestions(response.data);\n        calculateStats(response.data);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  const calculateStats = (questionsData) => {\n    let totalReplies = 0;\n    let pendingReplies = 0;\n    let verifiedReplies = 0;\n\n    questionsData.forEach(question => {\n      totalReplies += question.replies.length;\n      question.replies.forEach(reply => {\n        if (!reply.user.isAdmin) {\n          if (reply.isVerified) {\n            verifiedReplies++;\n          } else {\n            pendingReplies++;\n          }\n        }\n      });\n    });\n\n    setStats({\n      totalQuestions: questionsData.length,\n      totalReplies,\n      pendingReplies,\n      verifiedReplies\n    });\n  };\n\n  const handleUpdateStatus = async (questionId, replyId, status) => {\n    try {\n      const response = await updateReplyStatus({\n        questionId,\n        replyId,\n        isVerified: status,\n      });\n      if (response.success) {\n        message.success(status ? \"Reply approved successfully\" : \"Reply disapproved successfully\");\n        fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n\n  const handleDeleteQuestion = async (questionId) => {\n    try {\n      const response = await deleteQuestion({ questionId });\n      if (response.success) {\n        message.success(\"Question deleted successfully\");\n        fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n\n  const toggleQuestion = (questionId) => {\n    setExpandedQuestions(prev => ({\n      ...prev,\n      [questionId]: !prev[questionId]\n    }));\n  };\n\n  useEffect(() => {\n    fetchQuestions();\n  }, []);\n\n  const StatCard = ({ title, value, icon: Icon, color, bgColor }) => (\n    <Card className=\"text-center\">\n      <div className={`inline-flex items-center justify-center w-12 h-12 ${bgColor} rounded-lg mb-3`}>\n        <Icon className={`w-6 h-6 ${color}`} />\n      </div>\n      <h3 className=\"text-2xl font-bold text-gray-900\">{value}</h3>\n      <p className=\"text-gray-600\">{title}</p>\n    </Card>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\n      <PageTitle title=\"Forum Management\" />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Forum Management</h1>\n          <p className=\"text-gray-600\">Manage community questions and verify user replies</p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <StatCard\n            title=\"Total Questions\"\n            value={stats.totalQuestions}\n            icon={MdMessage}\n            color=\"text-blue-600\"\n            bgColor=\"bg-blue-100\"\n          />\n          <StatCard\n            title=\"Total Replies\"\n            value={stats.totalReplies}\n            icon={MdMessage}\n            color=\"text-green-600\"\n            bgColor=\"bg-green-100\"\n          />\n          <StatCard\n            title=\"Pending Approval\"\n            value={stats.pendingReplies}\n            icon={MdPending}\n            color=\"text-orange-600\"\n            bgColor=\"bg-orange-100\"\n          />\n          <StatCard\n            title=\"Verified Replies\"\n            value={stats.verifiedReplies}\n            icon={MdVerified}\n            color=\"text-green-600\"\n            bgColor=\"bg-green-100\"\n          />\n        </div>\n\n        {/* Questions List */}\n        <div className=\"space-y-6\">\n          {questions.map((question) => (\n            <Card key={question._id} className=\"shadow-lg\">\n              <div className=\"p-6\">\n                {/* Question Header */}\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"flex items-center space-x-4\">\n                    <ProfilePicture\n                      user={question.user}\n                      size=\"sm\"\n                      showOnlineStatus={false}\n                    />\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900\">{question.user.name}</h4>\n                      <p className=\"text-sm text-gray-500\">\n                        {new Date(question.createdAt).toLocaleDateString('en-US', {\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric',\n                          hour: '2-digit',\n                          minute: '2-digit'\n                        })}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2\">\n                    <Badge count={question.replies.length} showZero>\n                      <Button\n                        icon={<FaEye />}\n                        onClick={() => toggleQuestion(question._id)}\n                        type={expandedQuestions[question._id] ? \"primary\" : \"default\"}\n                      >\n                        {expandedQuestions[question._id] ? \"Hide\" : \"View\"} Replies\n                      </Button>\n                    </Badge>\n                    <Button\n                      icon={<FaTrash />}\n                      danger\n                      onClick={() => handleDeleteQuestion(question._id)}\n                    >\n                      Delete\n                    </Button>\n                  </div>\n                </div>\n\n                {/* Question Content */}\n                <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{question.title}</h3>\n                <p className=\"text-gray-700 mb-4\">{question.body}</p>\n\n                {/* Replies Section */}\n                {expandedQuestions[question._id] && (\n                  <div className=\"mt-6 space-y-4 bg-gray-50 rounded-lg p-4\">\n                    <h4 className=\"text-lg font-semibold text-gray-800 mb-4\">\n                      Replies ({question.replies.length})\n                    </h4>\n                    {question.replies.map((reply) => (\n                      <div\n                        key={reply._id}\n                        className={`bg-white rounded-lg p-4 border-l-4 ${\n                          reply.user.isAdmin\n                            ? \"border-purple-500 bg-purple-50\"\n                            : reply.isVerified\n                            ? \"border-green-500 bg-green-50\"\n                            : \"border-orange-500 bg-orange-50\"\n                        }`}\n                      >\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex items-start space-x-3\">\n                            <ProfilePicture\n                              user={reply.user}\n                              size=\"xs\"\n                              showOnlineStatus={false}\n                            />\n                            <div className=\"flex-1\">\n                              <div className=\"flex items-center space-x-2 mb-2\">\n                                <h5 className=\"font-semibold text-gray-900\">{reply.user.name}</h5>\n                                {reply.user.isAdmin && (\n                                  <Badge color=\"purple\" text=\"Admin\" />\n                                )}\n                                {reply.isVerified && !reply.user.isAdmin && (\n                                  <Badge color=\"green\" text=\"Verified\" />\n                                )}\n                                {!reply.isVerified && !reply.user.isAdmin && (\n                                  <Badge color=\"orange\" text=\"Pending\" />\n                                )}\n                              </div>\n                              <p className={`text-sm mb-2 ${\n                                reply.isVerified && !reply.user.isAdmin \n                                  ? 'text-green-800 font-medium' \n                                  : reply.user.isAdmin \n                                  ? 'text-purple-800 font-medium'\n                                  : 'text-gray-700'\n                              }`}>\n                                {reply.text}\n                              </p>\n                              <p className=\"text-xs text-gray-500\">\n                                {(() => {\n                                  try {\n                                    const date = new Date(reply.createdAt);\n                                    if (isNaN(date.getTime())) {\n                                      return 'Invalid date';\n                                    }\n                                    return date.toLocaleDateString('en-US', {\n                                      month: \"short\",\n                                      day: \"numeric\",\n                                      hour: \"2-digit\",\n                                      minute: \"2-digit\"\n                                    });\n                                  } catch (error) {\n                                    return 'Invalid date';\n                                  }\n                                })()}\n                              </p>\n                            </div>\n                          </div>\n                          \n                          {/* Admin Actions */}\n                          {!reply.user.isAdmin && (\n                            <div className=\"flex space-x-2\">\n                              <Tooltip title={reply.isVerified ? \"Disapprove Reply\" : \"Approve Reply\"}>\n                                <Button\n                                  size=\"small\"\n                                  type={reply.isVerified ? \"danger\" : \"primary\"}\n                                  icon={reply.isVerified ? <FaTimes /> : <FaCheck />}\n                                  onClick={() =>\n                                    handleUpdateStatus(\n                                      question._id,\n                                      reply._id,\n                                      !reply.isVerified\n                                    )\n                                  }\n                                >\n                                  {reply.isVerified ? \"Disapprove\" : \"Approve\"}\n                                </Button>\n                              </Tooltip>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            </Card>\n          ))}\n        </div>\n\n        {questions.length === 0 && !loading && (\n          <div className=\"text-center py-12\">\n            <MdMessage className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No questions found</h3>\n            <p className=\"text-gray-500\">Questions will appear here when users post them.</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminForum;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AACzE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SACEC,eAAe,EACfC,cAAc,EACdC,iBAAiB,QACZ,yBAAyB;AAChC,SAASC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAQ,gBAAgB;AACjE,SAASC,SAAS,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC;IACjCoC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAE9B,MAAM+B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCV,UAAU,CAAC,IAAI,CAAC;IAChBS,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM8B,QAAQ,GAAG,MAAM5B,eAAe,CAAC,CAAC;MACxC,IAAI4B,QAAQ,CAACC,OAAO,EAAE;QACpBd,YAAY,CAACa,QAAQ,CAACE,IAAI,CAAC;QAC3BC,cAAc,CAACH,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,MAAM;QACL1C,OAAO,CAAC4C,KAAK,CAACJ,QAAQ,CAACxC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO4C,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAACA,KAAK,CAAC5C,OAAO,CAAC;IAC9B,CAAC,SAAS;MACR6B,UAAU,CAAC,KAAK,CAAC;MACjBS,QAAQ,CAAC7B,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMkC,cAAc,GAAIE,aAAa,IAAK;IACxC,IAAIV,YAAY,GAAG,CAAC;IACpB,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAIC,eAAe,GAAG,CAAC;IAEvBQ,aAAa,CAACC,OAAO,CAACC,QAAQ,IAAI;MAChCZ,YAAY,IAAIY,QAAQ,CAACC,OAAO,CAACC,MAAM;MACvCF,QAAQ,CAACC,OAAO,CAACF,OAAO,CAACI,KAAK,IAAI;QAChC,IAAI,CAACA,KAAK,CAACC,IAAI,CAACC,OAAO,EAAE;UACvB,IAAIF,KAAK,CAACG,UAAU,EAAE;YACpBhB,eAAe,EAAE;UACnB,CAAC,MAAM;YACLD,cAAc,EAAE;UAClB;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFH,QAAQ,CAAC;MACPC,cAAc,EAAEW,aAAa,CAACI,MAAM;MACpCd,YAAY;MACZC,cAAc;MACdC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,kBAAkB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,OAAO,EAAEC,MAAM,KAAK;IAChE,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAM1B,iBAAiB,CAAC;QACvCyC,UAAU;QACVC,OAAO;QACPH,UAAU,EAAEI;MACd,CAAC,CAAC;MACF,IAAIjB,QAAQ,CAACC,OAAO,EAAE;QACpBzC,OAAO,CAACyC,OAAO,CAACgB,MAAM,GAAG,6BAA6B,GAAG,gCAAgC,CAAC;QAC1FlB,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACLvC,OAAO,CAAC4C,KAAK,CAACJ,QAAQ,CAACxC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO4C,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAACA,KAAK,CAAC5C,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM0D,oBAAoB,GAAG,MAAOH,UAAU,IAAK;IACjD,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAM3B,cAAc,CAAC;QAAE0C;MAAW,CAAC,CAAC;MACrD,IAAIf,QAAQ,CAACC,OAAO,EAAE;QACpBzC,OAAO,CAACyC,OAAO,CAAC,+BAA+B,CAAC;QAChDF,cAAc,CAAC,CAAC;MAClB,CAAC,MAAM;QACLvC,OAAO,CAAC4C,KAAK,CAACJ,QAAQ,CAACxC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO4C,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAACA,KAAK,CAAC5C,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM2D,cAAc,GAAIJ,UAAU,IAAK;IACrCxB,oBAAoB,CAAC6B,IAAI,KAAK;MAC5B,GAAGA,IAAI;MACP,CAACL,UAAU,GAAG,CAACK,IAAI,CAACL,UAAU;IAChC,CAAC,CAAC,CAAC;EACL,CAAC;EAEDxD,SAAS,CAAC,MAAM;IACdwC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMsB,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI,EAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAQ,CAAC,kBAC5D5C,OAAA,CAACnB,IAAI;IAACgE,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC3B9C,OAAA;MAAK6C,SAAS,EAAG,qDAAoDD,OAAQ,kBAAkB;MAAAE,QAAA,eAC7F9C,OAAA,CAAC0C,IAAI;QAACG,SAAS,EAAG,WAAUF,KAAM;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eACNlD,OAAA;MAAI6C,SAAS,EAAC,kCAAkC;MAAAC,QAAA,EAAEN;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC7DlD,OAAA;MAAG6C,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEP;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpC,CACP;EAED,oBACElD,OAAA;IAAK6C,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBACjF9C,OAAA,CAAChB,SAAS;MAACuD,KAAK,EAAC;IAAkB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEtClD,OAAA;MAAK6C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1D9C,OAAA;QAAK6C,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB9C,OAAA;UAAI6C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ElD,OAAA;UAAG6C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxE9C,OAAA,CAACsC,QAAQ;UACPC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAE/B,KAAK,CAACE,cAAe;UAC5B8B,IAAI,EAAE7C,SAAU;UAChB+C,KAAK,EAAC,eAAe;UACrBC,OAAO,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACFlD,OAAA,CAACsC,QAAQ;UACPC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAE/B,KAAK,CAACG,YAAa;UAC1B6B,IAAI,EAAE7C,SAAU;UAChB+C,KAAK,EAAC,gBAAgB;UACtBC,OAAO,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACFlD,OAAA,CAACsC,QAAQ;UACPC,KAAK,EAAC,kBAAkB;UACxBC,KAAK,EAAE/B,KAAK,CAACI,cAAe;UAC5B4B,IAAI,EAAE3C,SAAU;UAChB6C,KAAK,EAAC,iBAAiB;UACvBC,OAAO,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACFlD,OAAA,CAACsC,QAAQ;UACPC,KAAK,EAAC,kBAAkB;UACxBC,KAAK,EAAE/B,KAAK,CAACK,eAAgB;UAC7B2B,IAAI,EAAE5C,UAAW;UACjB8C,KAAK,EAAC,gBAAgB;UACtBC,OAAO,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB3C,SAAS,CAACgD,GAAG,CAAE3B,QAAQ,iBACtBxB,OAAA,CAACnB,IAAI;UAAoBgE,SAAS,EAAC,WAAW;UAAAC,QAAA,eAC5C9C,OAAA;YAAK6C,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAElB9C,OAAA;cAAK6C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD9C,OAAA;gBAAK6C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C9C,OAAA,CAACZ,cAAc;kBACbwC,IAAI,EAAEJ,QAAQ,CAACI,IAAK;kBACpBwB,IAAI,EAAC,IAAI;kBACTC,gBAAgB,EAAE;gBAAM;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFlD,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAI6C,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAEtB,QAAQ,CAACI,IAAI,CAAC0B;kBAAI;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrElD,OAAA;oBAAG6C,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjC,IAAIS,IAAI,CAAC/B,QAAQ,CAACgC,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;sBACxDC,IAAI,EAAE,SAAS;sBACfC,KAAK,EAAE,MAAM;sBACbC,GAAG,EAAE,SAAS;sBACdC,IAAI,EAAE,SAAS;sBACfC,MAAM,EAAE;oBACV,CAAC;kBAAC;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlD,OAAA;gBAAK6C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C9C,OAAA,CAAClB,KAAK;kBAACiF,KAAK,EAAEvC,QAAQ,CAACC,OAAO,CAACC,MAAO;kBAACsC,QAAQ;kBAAAlB,QAAA,eAC7C9C,OAAA,CAACtB,MAAM;oBACL+D,IAAI,eAAEzC,OAAA,CAACN,KAAK;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAChBe,OAAO,EAAEA,CAAA,KAAM7B,cAAc,CAACZ,QAAQ,CAAC0C,GAAG,CAAE;oBAC5CC,IAAI,EAAE5D,iBAAiB,CAACiB,QAAQ,CAAC0C,GAAG,CAAC,GAAG,SAAS,GAAG,SAAU;oBAAApB,QAAA,GAE7DvC,iBAAiB,CAACiB,QAAQ,CAAC0C,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM,EAAC,UACrD;kBAAA;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACRlD,OAAA,CAACtB,MAAM;kBACL+D,IAAI,eAAEzC,OAAA,CAACL,OAAO;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAClBkB,MAAM;kBACNH,OAAO,EAAEA,CAAA,KAAM9B,oBAAoB,CAACX,QAAQ,CAAC0C,GAAG,CAAE;kBAAApB,QAAA,EACnD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlD,OAAA;cAAI6C,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAEtB,QAAQ,CAACe;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1ElD,OAAA;cAAG6C,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEtB,QAAQ,CAAC6C;YAAI;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAGpD3C,iBAAiB,CAACiB,QAAQ,CAAC0C,GAAG,CAAC,iBAC9BlE,OAAA;cAAK6C,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACvD9C,OAAA;gBAAI6C,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,GAAC,WAC9C,EAACtB,QAAQ,CAACC,OAAO,CAACC,MAAM,EAAC,GACpC;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACJ1B,QAAQ,CAACC,OAAO,CAAC0B,GAAG,CAAExB,KAAK,iBAC1B3B,OAAA;gBAEE6C,SAAS,EAAG,sCACVlB,KAAK,CAACC,IAAI,CAACC,OAAO,GACd,gCAAgC,GAChCF,KAAK,CAACG,UAAU,GAChB,8BAA8B,GAC9B,gCACL,EAAE;gBAAAgB,QAAA,eAEH9C,OAAA;kBAAK6C,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/C9C,OAAA;oBAAK6C,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACzC9C,OAAA,CAACZ,cAAc;sBACbwC,IAAI,EAAED,KAAK,CAACC,IAAK;sBACjBwB,IAAI,EAAC,IAAI;sBACTC,gBAAgB,EAAE;oBAAM;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACFlD,OAAA;sBAAK6C,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACrB9C,OAAA;wBAAK6C,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/C9C,OAAA;0BAAI6C,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,EAAEnB,KAAK,CAACC,IAAI,CAAC0B;wBAAI;0BAAAP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,EACjEvB,KAAK,CAACC,IAAI,CAACC,OAAO,iBACjB7B,OAAA,CAAClB,KAAK;0BAAC6D,KAAK,EAAC,QAAQ;0BAAC2B,IAAI,EAAC;wBAAO;0BAAAvB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CACrC,EACAvB,KAAK,CAACG,UAAU,IAAI,CAACH,KAAK,CAACC,IAAI,CAACC,OAAO,iBACtC7B,OAAA,CAAClB,KAAK;0BAAC6D,KAAK,EAAC,OAAO;0BAAC2B,IAAI,EAAC;wBAAU;0BAAAvB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CACvC,EACA,CAACvB,KAAK,CAACG,UAAU,IAAI,CAACH,KAAK,CAACC,IAAI,CAACC,OAAO,iBACvC7B,OAAA,CAAClB,KAAK;0BAAC6D,KAAK,EAAC,QAAQ;0BAAC2B,IAAI,EAAC;wBAAS;0BAAAvB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CACvC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNlD,OAAA;wBAAG6C,SAAS,EAAG,gBACblB,KAAK,CAACG,UAAU,IAAI,CAACH,KAAK,CAACC,IAAI,CAACC,OAAO,GACnC,4BAA4B,GAC5BF,KAAK,CAACC,IAAI,CAACC,OAAO,GAClB,6BAA6B,GAC7B,eACL,EAAE;wBAAAiB,QAAA,EACAnB,KAAK,CAAC2C;sBAAI;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACJlD,OAAA;wBAAG6C,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACjC,CAAC,MAAM;0BACN,IAAI;4BACF,MAAMyB,IAAI,GAAG,IAAIhB,IAAI,CAAC5B,KAAK,CAAC6B,SAAS,CAAC;4BACtC,IAAIgB,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;8BACzB,OAAO,cAAc;4BACvB;4BACA,OAAOF,IAAI,CAACd,kBAAkB,CAAC,OAAO,EAAE;8BACtCE,KAAK,EAAE,OAAO;8BACdC,GAAG,EAAE,SAAS;8BACdC,IAAI,EAAE,SAAS;8BACfC,MAAM,EAAE;4BACV,CAAC,CAAC;0BACJ,CAAC,CAAC,OAAOzC,KAAK,EAAE;4BACd,OAAO,cAAc;0BACvB;wBACF,CAAC,EAAE;sBAAC;wBAAA0B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAGL,CAACvB,KAAK,CAACC,IAAI,CAACC,OAAO,iBAClB7B,OAAA;oBAAK6C,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,eAC7B9C,OAAA,CAACjB,OAAO;sBAACwD,KAAK,EAAEZ,KAAK,CAACG,UAAU,GAAG,kBAAkB,GAAG,eAAgB;sBAAAgB,QAAA,eACtE9C,OAAA,CAACtB,MAAM;wBACL0E,IAAI,EAAC,OAAO;wBACZe,IAAI,EAAExC,KAAK,CAACG,UAAU,GAAG,QAAQ,GAAG,SAAU;wBAC9CW,IAAI,EAAEd,KAAK,CAACG,UAAU,gBAAG9B,OAAA,CAACP,OAAO;0BAAAsD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACR,OAAO;0BAAAuD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACnDe,OAAO,EAAEA,CAAA,KACPlC,kBAAkB,CAChBP,QAAQ,CAAC0C,GAAG,EACZvC,KAAK,CAACuC,GAAG,EACT,CAACvC,KAAK,CAACG,UACT,CACD;wBAAAgB,QAAA,EAEAnB,KAAK,CAACG,UAAU,GAAG,YAAY,GAAG;sBAAS;wBAAAiB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC,GAhFDvB,KAAK,CAACuC,GAAG;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiFX,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA7IG1B,QAAQ,CAAC0C,GAAG;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8IjB,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAEL/C,SAAS,CAACuB,MAAM,KAAK,CAAC,IAAI,CAACrB,OAAO,iBACjCL,OAAA;QAAK6C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC9C,OAAA,CAACJ,SAAS;UAACiD,SAAS,EAAC;QAAsC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DlD,OAAA;UAAI6C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9ElD,OAAA;UAAG6C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChD,EAAA,CAxTID,UAAU;EAAA,QAUGhB,WAAW;AAAA;AAAAyF,EAAA,GAVxBzE,UAAU;AA0ThB,eAAeA,UAAU;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}