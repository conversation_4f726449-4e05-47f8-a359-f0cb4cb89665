{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbSend, TbPaperclip, TbX, TbRobot, Tb<PERSON>ser, TbPhoto, TbLoader } from \"react-icons/tb\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ChatGPTIntegration() {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [prompt, setPrompt] = useState(\"\");\n  const [imageFile, setImageFile] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const textareaRef = useRef(null);\n\n  // Simple content renderer for AI responses\n  const renderContent = text => {\n    if (!text || typeof text !== 'string') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 14\n      }, this);\n    }\n\n    // Split by lines and render with proper formatting\n    return text.split('\\n').map((line, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-1\",\n      children: line.trim() === '' ? /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 31\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"whitespace-pre-wrap\",\n        children: line.split(/(\\*\\*.*?\\*\\*)/).map((part, i) => part.startsWith('**') && part.endsWith('**') ? /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: part.slice(2, -2)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          children: part\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this)\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this));\n  };\n\n  // Initialize chat with welcome message\n  useEffect(() => {\n    try {\n      // Load cached messages\n      const cachedMessages = localStorage.getItem('chat_messages');\n      if (cachedMessages) {\n        const parsedMessages = JSON.parse(cachedMessages);\n        setMessages(parsedMessages);\n      } else {\n        // Set welcome message\n        setMessages([{\n          role: \"assistant\",\n          content: \"Hello! I'm Brainwave AI, your intelligent study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\n        }]);\n      }\n      setIsInitialized(true);\n    } catch (error) {\n      console.error('Error initializing chat:', error);\n      // Fallback to welcome message\n      setMessages([{\n        role: \"assistant\",\n        content: \"Hello! I'm Brainwave AI, your intelligent study assistant. How can I help you today?\"\n      }]);\n      setIsInitialized(true);\n    }\n  }, []);\n\n  // Save messages to cache\n  useEffect(() => {\n    if (isInitialized && messages.length > 0) {\n      try {\n        localStorage.setItem('chat_messages', JSON.stringify(messages));\n      } catch (error) {\n        console.error('Error saving messages:', error);\n      }\n    }\n  }, [messages, isInitialized]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [messages]);\n\n  // Handle image file selection\n  const handleImageSelect = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setImageFile(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setImageFile(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle Enter key press - Enter for new line, send only via button\n  const handleKeyPress = e => {\n    // Enter always creates new line, no auto-send\n    if (e.key === 'Enter') {\n      // Allow default behavior (new line)\n      return;\n    }\n  };\n  const handleChat = async () => {\n    if (!prompt.trim() && !imageFile) return;\n    setIsLoading(true);\n    setIsTyping(true);\n    try {\n      let imageUrl = null;\n\n      // Step 1: Upload the image to the server (if an image is selected)\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const data = await uploadImg(formData);\n        if (data !== null && data !== void 0 && data.success) {\n          imageUrl = data.url; // Extract the S3 URL\n          console.log(\"Image URL: \", imageUrl);\n        } else {\n          throw new Error(\"Image upload failed\");\n        }\n      }\n\n      // Step 2: Construct the ChatGPT message payload\n      const userMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: prompt\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: prompt\n      };\n      const updatedMessages = [...messages, userMessage];\n      setMessages(updatedMessages);\n      setPrompt(\"\");\n      removeImage();\n\n      // Step 3: Send the payload to ChatGPT\n      const chatPayload = {\n        messages: updatedMessages\n      };\n      const chatRes = await chatWithChatGPT(chatPayload);\n      const apiResponse = chatRes === null || chatRes === void 0 ? void 0 : chatRes.data;\n      console.log(\"API Response: \", apiResponse);\n\n      // Step 4: Append the assistant's response to the conversation\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: apiResponse\n      }]);\n    } catch (error) {\n      console.error(\"Error during chat:\", error);\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: \"Sorry, I encountered an error. Please try again.\"\n      }]);\n    } finally {\n      setIsLoading(false);\n      setIsTyping(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .chat-scroll::-webkit-scrollbar {\n          width: 6px;\n        }\n        .chat-scroll::-webkit-scrollbar-track {\n          background: #f1f5f9;\n          border-radius: 3px;\n        }\n        .chat-scroll::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n        .chat-scroll::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 shadow-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbRobot, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg sm:text-xl font-bold text-gray-800\",\n              children: \"Brainwave AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs sm:text-sm text-gray-600\",\n              children: \"Ask questions, upload images, get instant help\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-hidden relative\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chat-scroll h-full overflow-y-auto space-y-3 sm:space-y-6 pb-4 scroll-smooth\",\n            children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: messages.map((msg, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: `flex gap-2 sm:gap-4 ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                children: [msg.role === \"assistant\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                    className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `max-w-[85%] sm:max-w-[80%] ${msg.role === \"user\" ? \"order-1\" : \"\"}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `rounded-xl sm:rounded-2xl p-3 sm:p-4 ${msg.role === \"user\" ? \"bg-blue-500 text-white ml-auto\" : \"bg-white border border-gray-200 shadow-sm\"}`,\n                    children: msg.role === \"assistant\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-gray-800\",\n                      children: msg !== null && msg !== void 0 && msg.content ? renderContent(msg.content) : /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-red-500\",\n                        children: \"Unable to get a response from AI\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: typeof msg.content === \"string\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"whitespace-pre-wrap\",\n                        children: msg.content\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 29\n                      }, this) : msg.content.map((item, idx) => item.type === \"text\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"whitespace-pre-wrap mb-2\",\n                        children: item.text\n                      }, idx, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 262,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: item.image_url.url,\n                          alt: \"User uploaded content\",\n                          className: \"rounded-lg max-w-full h-auto shadow-md\",\n                          style: {\n                            maxHeight: window.innerWidth <= 768 ? \"200px\" : \"300px\"\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 265,\n                          columnNumber: 35\n                        }, this)\n                      }, idx, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 264,\n                        columnNumber: 33\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this), msg.role === \"user\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(TbUser, {\n                    className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -20\n                },\n                className: \"flex gap-2 sm:gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                    className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white border border-gray-200 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                      animate: {\n                        scale: [1, 1.2, 1]\n                      },\n                      transition: {\n                        duration: 0.6,\n                        repeat: Infinity,\n                        delay: 0\n                      },\n                      className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                      animate: {\n                        scale: [1, 1.2, 1]\n                      },\n                      transition: {\n                        duration: 0.6,\n                        repeat: Infinity,\n                        delay: 0.2\n                      },\n                      className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                      animate: {\n                        scale: [1, 1.2, 1]\n                      },\n                      transition: {\n                        duration: 0.6,\n                        repeat: Infinity,\n                        delay: 0.4\n                      },\n                      className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: messagesEndRef\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sticky bottom-0 bg-white/95 backdrop-blur-lg border-t border-gray-200 p-2 sm:p-4 shadow-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: imagePreview && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                height: 0\n              },\n              animate: {\n                opacity: 1,\n                height: \"auto\"\n              },\n              exit: {\n                opacity: 0,\n                height: 0\n              },\n              className: \"mb-3 sm:mb-4 p-2 sm:p-3 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 sm:gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: imagePreview,\n                    alt: \"Preview\",\n                    className: \"w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg border border-gray-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: removeImage,\n                    className: \"absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\",\n                    children: /*#__PURE__*/_jsxDEV(TbX, {\n                      className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs sm:text-sm font-medium text-gray-700\",\n                    children: \"Image attached\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 truncate\",\n                    children: imageFile === null || imageFile === void 0 ? void 0 : imageFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-end gap-2 sm:gap-3 bg-white rounded-xl sm:rounded-2xl border border-gray-300 p-2 sm:p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  var _fileInputRef$current;\n                  return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n                },\n                className: \"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors shadow-sm\",\n                title: \"Attach image\",\n                children: /*#__PURE__*/_jsxDEV(TbPaperclip, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                ref: textareaRef,\n                value: prompt,\n                onChange: e => setPrompt(e.target.value),\n                onKeyPress: handleKeyPress,\n                placeholder: window.innerWidth <= 768 ? \"Ask me anything...\" : \"Ask me anything... (Enter for new line)\",\n                className: \"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 text-sm sm:text-base\",\n                rows: 1,\n                style: {\n                  height: 'auto',\n                  minHeight: window.innerWidth <= 768 ? '20px' : '24px',\n                  maxHeight: window.innerWidth <= 768 ? '80px' : '128px'\n                },\n                onInput: e => {\n                  e.target.style.height = 'auto';\n                  const maxHeight = window.innerWidth <= 768 ? 80 : 128;\n                  e.target.style.height = Math.min(e.target.scrollHeight, maxHeight) + 'px';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: handleChat,\n                disabled: isLoading || !prompt.trim() && !imageFile,\n                className: `p-2 rounded-lg transition-all ${isLoading || !prompt.trim() && !imageFile ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"}`,\n                children: isLoading ? /*#__PURE__*/_jsxDEV(TbLoader, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this) : /*#__PURE__*/_jsxDEV(TbSend, {\n                  className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ref: fileInputRef,\n              type: \"file\",\n              accept: \"image/*\",\n              onChange: handleImageSelect,\n              className: \"hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 mt-2 text-center px-2\",\n            children: window.innerWidth <= 768 ? \"Click send button • Upload images for analysis\" : \"Click send button to send • Enter for new line • Upload images for analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(ChatGPTIntegration, \"VSMuF9EUH1ObLJ2Bkv+9tWC7cOk=\");\n_c = ChatGPTIntegration;\nexport default ChatGPTIntegration;\nvar _c;\n$RefreshReg$(_c, \"ChatGPTIntegration\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "TbSend", "TbPaperclip", "TbX", "TbRobot", "TbUser", "TbPhoto", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatWithChatGPT", "uploadImg", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ChatGPTIntegration", "_s", "messages", "setMessages", "prompt", "setPrompt", "imageFile", "setImageFile", "imagePreview", "setImagePreview", "isLoading", "setIsLoading", "isInitialized", "setIsInitialized", "isTyping", "setIsTyping", "messagesEndRef", "fileInputRef", "textareaRef", "renderContent", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "split", "map", "line", "index", "className", "children", "trim", "part", "i", "startsWith", "endsWith", "slice", "cachedMessages", "localStorage", "getItem", "parsedMessages", "JSON", "parse", "role", "content", "error", "console", "length", "setItem", "stringify", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleImageSelect", "e", "file", "target", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "removeImage", "value", "handleKeyPress", "key", "handleChat", "imageUrl", "formData", "FormData", "append", "data", "success", "url", "log", "Error", "userMessage", "type", "image_url", "updatedMessages", "chatPayload", "chatRes", "apiResponse", "prev", "jsx", "div", "initial", "opacity", "y", "animate", "msg", "item", "idx", "src", "alt", "style", "maxHeight", "window", "innerWidth", "exit", "scale", "transition", "duration", "repeat", "Infinity", "delay", "ref", "height", "onClick", "name", "_fileInputRef$current", "click", "title", "onChange", "onKeyPress", "placeholder", "rows", "minHeight", "onInput", "Math", "min", "scrollHeight", "button", "whileHover", "whileTap", "disabled", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { TbSend, Tb<PERSON><PERSON>clip, TbX, Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON>b<PERSON>oa<PERSON> } from \"react-icons/tb\";\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\n\r\nfunction ChatGPTIntegration() {\r\n  const [messages, setMessages] = useState([]);\r\n  const [prompt, setPrompt] = useState(\"\");\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [isTyping, setIsTyping] = useState(false);\r\n\r\n  const messagesEndRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const textareaRef = useRef(null);\r\n\r\n  // Simple content renderer for AI responses\r\n  const renderContent = (text) => {\r\n    if (!text || typeof text !== 'string') {\r\n      return <div></div>;\r\n    }\r\n\r\n    // Split by lines and render with proper formatting\r\n    return text.split('\\n').map((line, index) => (\r\n      <div key={index} className=\"mb-1\">\r\n        {line.trim() === '' ? <br /> : (\r\n          <span className=\"whitespace-pre-wrap\">\r\n            {line.split(/(\\*\\*.*?\\*\\*)/).map((part, i) =>\r\n              part.startsWith('**') && part.endsWith('**') ? (\r\n                <strong key={i}>{part.slice(2, -2)}</strong>\r\n              ) : (\r\n                <span key={i}>{part}</span>\r\n              )\r\n            )}\r\n          </span>\r\n        )}\r\n      </div>\r\n    ));\r\n  };\r\n\r\n  // Initialize chat with welcome message\r\n  useEffect(() => {\r\n    try {\r\n      // Load cached messages\r\n      const cachedMessages = localStorage.getItem('chat_messages');\r\n      if (cachedMessages) {\r\n        const parsedMessages = JSON.parse(cachedMessages);\r\n        setMessages(parsedMessages);\r\n      } else {\r\n        // Set welcome message\r\n        setMessages([{\r\n          role: \"assistant\",\r\n          content: \"Hello! I'm Brainwave AI, your intelligent study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\r\n        }]);\r\n      }\r\n      setIsInitialized(true);\r\n    } catch (error) {\r\n      console.error('Error initializing chat:', error);\r\n      // Fallback to welcome message\r\n      setMessages([{\r\n        role: \"assistant\",\r\n        content: \"Hello! I'm Brainwave AI, your intelligent study assistant. How can I help you today?\"\r\n      }]);\r\n      setIsInitialized(true);\r\n    }\r\n  }, []);\r\n\r\n  // Save messages to cache\r\n  useEffect(() => {\r\n    if (isInitialized && messages.length > 0) {\r\n      try {\r\n        localStorage.setItem('chat_messages', JSON.stringify(messages));\r\n      } catch (error) {\r\n        console.error('Error saving messages:', error);\r\n      }\r\n    }\r\n  }, [messages, isInitialized]);\r\n\r\n  // Auto-scroll to bottom when new messages arrive\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [messages]);\r\n\r\n  // Handle image file selection\r\n  const handleImageSelect = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setImageFile(file);\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImagePreview(e.target.result);\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  // Remove selected image\r\n  const removeImage = () => {\r\n    setImageFile(null);\r\n    setImagePreview(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Handle Enter key press - Enter for new line, send only via button\r\n  const handleKeyPress = (e) => {\r\n    // Enter always creates new line, no auto-send\r\n    if (e.key === 'Enter') {\r\n      // Allow default behavior (new line)\r\n      return;\r\n    }\r\n  };\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n    setIsTyping(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: prompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: prompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      setMessages(updatedMessages);\r\n      setPrompt(\"\");\r\n      removeImage();\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: apiResponse },\r\n      ]);\r\n\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: \"Sorry, I encountered an error. Please try again.\" },\r\n      ]);\r\n    } finally {\r\n      setIsLoading(false);\r\n      setIsTyping(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Professional Scrollbar Styles */}\r\n      <style jsx>{`\r\n        .chat-scroll::-webkit-scrollbar {\r\n          width: 6px;\r\n        }\r\n        .chat-scroll::-webkit-scrollbar-track {\r\n          background: #f1f5f9;\r\n          border-radius: 3px;\r\n        }\r\n        .chat-scroll::-webkit-scrollbar-thumb {\r\n          background: #cbd5e1;\r\n          border-radius: 3px;\r\n        }\r\n        .chat-scroll::-webkit-scrollbar-thumb:hover {\r\n          background: #94a3b8;\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col overflow-hidden\">\r\n      {/* Header */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 shadow-sm\"\r\n      >\r\n        <div className=\"max-w-4xl mx-auto flex items-center gap-3\">\r\n          <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n            <TbRobot className=\"w-6 h-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h1 className=\"text-lg sm:text-xl font-bold text-gray-800\">Brainwave AI</h1>\r\n            <p className=\"text-xs sm:text-sm text-gray-600\">Ask questions, upload images, get instant help</p>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Messages Container - Professional Scrolling */}\r\n      <div className=\"flex-1 overflow-hidden relative\">\r\n        <div className=\"h-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4\">\r\n          <div\r\n            className=\"chat-scroll h-full overflow-y-auto space-y-3 sm:space-y-6 pb-4 scroll-smooth\"\r\n          >\r\n            <AnimatePresence>\r\n              {messages.map((msg, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  className={`flex gap-2 sm:gap-4 ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`}\r\n                >\r\n                  {msg.role === \"assistant\" && (\r\n                    <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                      <TbRobot className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className={`max-w-[85%] sm:max-w-[80%] ${msg.role === \"user\" ? \"order-1\" : \"\"}`}>\r\n                    <div\r\n                      className={`rounded-xl sm:rounded-2xl p-3 sm:p-4 ${\r\n                        msg.role === \"user\"\r\n                          ? \"bg-blue-500 text-white ml-auto\"\r\n                          : \"bg-white border border-gray-200 shadow-sm\"\r\n                      }`}\r\n                    >\r\n                      {msg.role === \"assistant\" ? (\r\n                        <div className=\"text-gray-800\">\r\n                          {msg?.content ? (\r\n                            renderContent(msg.content)\r\n                          ) : (\r\n                            <p className=\"text-red-500\">Unable to get a response from AI</p>\r\n                          )}\r\n                        </div>\r\n                      ) : (\r\n                        <div>\r\n                          {typeof msg.content === \"string\" ? (\r\n                            <p className=\"whitespace-pre-wrap\">{msg.content}</p>\r\n                          ) : (\r\n                            msg.content.map((item, idx) =>\r\n                              item.type === \"text\" ? (\r\n                                <p key={idx} className=\"whitespace-pre-wrap mb-2\">{item.text}</p>\r\n                              ) : (\r\n                                <div key={idx} className=\"mt-2\">\r\n                                  <img\r\n                                    src={item.image_url.url}\r\n                                    alt=\"User uploaded content\"\r\n                                    className=\"rounded-lg max-w-full h-auto shadow-md\"\r\n                                    style={{ maxHeight: window.innerWidth <= 768 ? \"200px\" : \"300px\" }}\r\n                                  />\r\n                                </div>\r\n                              )\r\n                            )\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {msg.role === \"user\" && (\r\n                    <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                      <TbUser className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                    </div>\r\n                  )}\r\n                </motion.div>\r\n              ))}\r\n            </AnimatePresence>\r\n\r\n            {/* Typing Indicator */}\r\n            <AnimatePresence>\r\n              {isTyping && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  exit={{ opacity: 0, y: -20 }}\r\n                  className=\"flex gap-2 sm:gap-4\"\r\n                >\r\n                  <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n                    <TbRobot className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                  </div>\r\n                  <div className=\"bg-white border border-gray-200 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-sm\">\r\n                    <div className=\"flex gap-1\">\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </AnimatePresence>\r\n\r\n            <div ref={messagesEndRef} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Input Section - Pinned at Bottom */}\r\n      <div className=\"sticky bottom-0 bg-white/95 backdrop-blur-lg border-t border-gray-200 p-2 sm:p-4 shadow-lg\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Image Preview */}\r\n          <AnimatePresence>\r\n            {imagePreview && (\r\n              <motion.div\r\n                initial={{ opacity: 0, height: 0 }}\r\n                animate={{ opacity: 1, height: \"auto\" }}\r\n                exit={{ opacity: 0, height: 0 }}\r\n                className=\"mb-3 sm:mb-4 p-2 sm:p-3 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\"\r\n              >\r\n                <div className=\"flex items-center gap-2 sm:gap-3\">\r\n                  <div className=\"relative\">\r\n                    <img\r\n                      src={imagePreview}\r\n                      alt=\"Preview\"\r\n                      className=\"w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg border border-gray-300\"\r\n                    />\r\n                    <button\r\n                      onClick={removeImage}\r\n                      className=\"absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\"\r\n                    >\r\n                      <TbX className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-xs sm:text-sm font-medium text-gray-700\">Image attached</p>\r\n                    <p className=\"text-xs text-gray-500 truncate\">{imageFile?.name}</p>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n\r\n          {/* Input Area - Responsive */}\r\n          <div className=\"relative\">\r\n            <div className=\"flex items-end gap-2 sm:gap-3 bg-white rounded-xl sm:rounded-2xl border border-gray-300 p-2 sm:p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\">\r\n              {/* Attachment Button - Blue and White with Visible Pins */}\r\n              <button\r\n                onClick={() => fileInputRef.current?.click()}\r\n                className=\"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors shadow-sm\"\r\n                title=\"Attach image\"\r\n              >\r\n                <TbPaperclip className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n              </button>\r\n\r\n              {/* Text Input - Responsive */}\r\n              <textarea\r\n                ref={textareaRef}\r\n                value={prompt}\r\n                onChange={(e) => setPrompt(e.target.value)}\r\n                onKeyPress={handleKeyPress}\r\n                placeholder={window.innerWidth <= 768 ? \"Ask me anything...\" : \"Ask me anything... (Enter for new line)\"}\r\n                className=\"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 text-sm sm:text-base\"\r\n                rows={1}\r\n                style={{\r\n                  height: 'auto',\r\n                  minHeight: window.innerWidth <= 768 ? '20px' : '24px',\r\n                  maxHeight: window.innerWidth <= 768 ? '80px' : '128px'\r\n                }}\r\n                onInput={(e) => {\r\n                  e.target.style.height = 'auto';\r\n                  const maxHeight = window.innerWidth <= 768 ? 80 : 128;\r\n                  e.target.style.height = Math.min(e.target.scrollHeight, maxHeight) + 'px';\r\n                }}\r\n              />\r\n\r\n              {/* Send Button - Responsive */}\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={handleChat}\r\n                disabled={isLoading || (!prompt.trim() && !imageFile)}\r\n                className={`p-2 rounded-lg transition-all ${\r\n                  isLoading || (!prompt.trim() && !imageFile)\r\n                    ? \"bg-gray-200 text-gray-400 cursor-not-allowed\"\r\n                    : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"\r\n                }`}\r\n              >\r\n                {isLoading ? (\r\n                  <TbLoader className=\"w-4 h-4 sm:w-5 sm:h-5 animate-spin\" />\r\n                ) : (\r\n                  <TbSend className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                )}\r\n              </motion.button>\r\n            </div>\r\n\r\n            {/* Hidden File Input */}\r\n            <input\r\n              ref={fileInputRef}\r\n              type=\"file\"\r\n              accept=\"image/*\"\r\n              onChange={handleImageSelect}\r\n              className=\"hidden\"\r\n            />\r\n          </div>\r\n\r\n          {/* Helper Text - Updated for New Behavior */}\r\n          <p className=\"text-xs text-gray-500 mt-2 text-center px-2\">\r\n            {window.innerWidth <= 768\r\n              ? \"Click send button • Upload images for analysis\"\r\n              : \"Click send button to send • Enter for new line • Upload images for analysis\"\r\n            }\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,MAAM,EAAEC,WAAW,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC7F,SAASC,eAAe,EAAEC,SAAS,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEpE,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMkC,cAAc,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMkC,YAAY,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMmC,WAAW,GAAGnC,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMoC,aAAa,GAAIC,IAAI,IAAK;IAC9B,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACrC,oBAAOvB,OAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IACpB;;IAEA;IACA,OAAOJ,IAAI,CAACK,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACtC/B,OAAA;MAAiBgC,SAAS,EAAC,MAAM;MAAAC,QAAA,EAC9BH,IAAI,CAACI,IAAI,CAAC,CAAC,KAAK,EAAE,gBAAGlC,OAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAC1B3B,OAAA;QAAMgC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAClCH,IAAI,CAACF,KAAK,CAAC,eAAe,CAAC,CAACC,GAAG,CAAC,CAACM,IAAI,EAAEC,CAAC,KACvCD,IAAI,CAACE,UAAU,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,gBAC1CtC,OAAA;UAAAiC,QAAA,EAAiBE,IAAI,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAAC,GAArBH,CAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA6B,CAAC,gBAE5C3B,OAAA;UAAAiC,QAAA,EAAeE;QAAI,GAARC,CAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAE9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IACP,GAXOI,KAAK;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAYV,CACN,CAAC;EACJ,CAAC;;EAED;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI;MACF;MACA,MAAMqD,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAC5D,IAAIF,cAAc,EAAE;QAClB,MAAMG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;QACjDlC,WAAW,CAACqC,cAAc,CAAC;MAC7B,CAAC,MAAM;QACL;QACArC,WAAW,CAAC,CAAC;UACXwC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;MACL;MACA/B,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACA1C,WAAW,CAAC,CAAC;QACXwC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;MACH/B,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7B,SAAS,CAAC,MAAM;IACd,IAAI4B,aAAa,IAAIV,QAAQ,CAAC6C,MAAM,GAAG,CAAC,EAAE;MACxC,IAAI;QACFT,YAAY,CAACU,OAAO,CAAC,eAAe,EAAEP,IAAI,CAACQ,SAAS,CAAC/C,QAAQ,CAAC,CAAC;MACjE,CAAC,CAAC,OAAO2C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;IACF;EACF,CAAC,EAAE,CAAC3C,QAAQ,EAAEU,aAAa,CAAC,CAAC;;EAE7B;EACA5B,SAAS,CAAC,MAAM;IAAA,IAAAkE,qBAAA;IACd,CAAAA,qBAAA,GAAAlC,cAAc,CAACmC,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACnD,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMoD,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRjD,YAAY,CAACiD,IAAI,CAAC;MAClB,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIN,CAAC,IAAK9C,eAAe,CAAC8C,CAAC,CAACE,MAAM,CAACK,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxBzD,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIQ,YAAY,CAACkC,OAAO,EAAE;MACxBlC,YAAY,CAACkC,OAAO,CAACc,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIX,CAAC,IAAK;IAC5B;IACA,IAAIA,CAAC,CAACY,GAAG,KAAK,OAAO,EAAE;MACrB;MACA;IACF;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAChE,MAAM,CAAC2B,IAAI,CAAC,CAAC,IAAI,CAACzB,SAAS,EAAE;IAElCK,YAAY,CAAC,IAAI,CAAC;IAClBI,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,IAAIsD,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAI/D,SAAS,EAAE;QACb,MAAMgE,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAElE,SAAS,CAAC;QAEnC,MAAMmE,IAAI,GAAG,MAAM9E,SAAS,CAAC2E,QAAQ,CAAC;QAEtC,IAAIG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,OAAO,EAAE;UACjBL,QAAQ,GAAGI,IAAI,CAACE,GAAG,CAAC,CAAC;UACrB7B,OAAO,CAAC8B,GAAG,CAAC,aAAa,EAAEP,QAAQ,CAAC;QACtC,CAAC,MAAM;UACL,MAAM,IAAIQ,KAAK,CAAC,qBAAqB,CAAC;QACxC;MACF;;MAEA;MACA,MAAMC,WAAW,GAAGT,QAAQ,GACxB;QACA1B,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEmC,IAAI,EAAE,MAAM;UAAE3D,IAAI,EAAEhB;QAAO,CAAC,EAC9B;UAAE2E,IAAI,EAAE,WAAW;UAAEC,SAAS,EAAE;YAAEL,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACC;QAAE1B,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAExC;MAAO,CAAC;MAErC,MAAM6E,eAAe,GAAG,CAAC,GAAG/E,QAAQ,EAAE4E,WAAW,CAAC;MAClD3E,WAAW,CAAC8E,eAAe,CAAC;MAC5B5E,SAAS,CAAC,EAAE,CAAC;MACb2D,WAAW,CAAC,CAAC;;MAEb;MACA,MAAMkB,WAAW,GAAG;QAAEhF,QAAQ,EAAE+E;MAAgB,CAAC;MAEjD,MAAME,OAAO,GAAG,MAAMzF,eAAe,CAACwF,WAAW,CAAC;MAElD,MAAME,WAAW,GAAGD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEV,IAAI;MACjC3B,OAAO,CAAC8B,GAAG,CAAC,gBAAgB,EAAEQ,WAAW,CAAC;;MAE1C;MACAjF,WAAW,CAAEkF,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAE1C,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAEwC;MAAY,CAAC,CAC5C,CAAC;IAEJ,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C1C,WAAW,CAAEkF,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAE1C,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAmD,CAAC,CACnF,CAAC;IACJ,CAAC,SAAS;MACRjC,YAAY,CAAC,KAAK,CAAC;MACnBI,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,oBACElB,OAAA,CAAAE,SAAA;IAAA+B,QAAA,gBAEEjC,OAAA;MAAOyF,GAAG;MAAAxD,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEV3B,OAAA;MAAKgC,SAAS,EAAC,mFAAmF;MAAAC,QAAA,gBAElGjC,OAAA,CAACZ,MAAM,CAACsG,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9B7D,SAAS,EAAC,qEAAqE;QAAAC,QAAA,eAE/EjC,OAAA;UAAKgC,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDjC,OAAA;YAAKgC,SAAS,EAAC,sGAAsG;YAAAC,QAAA,eACnHjC,OAAA,CAACP,OAAO;cAACuC,SAAS,EAAC;YAAoB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACN3B,OAAA;YAAAiC,QAAA,gBACEjC,OAAA;cAAIgC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EAAC;YAAY;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5E3B,OAAA;cAAGgC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAA8C;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb3B,OAAA;QAAKgC,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC9CjC,OAAA;UAAKgC,SAAS,EAAC,oDAAoD;UAAAC,QAAA,eACjEjC,OAAA;YACEgC,SAAS,EAAC,8EAA8E;YAAAC,QAAA,gBAExFjC,OAAA,CAACX,eAAe;cAAA4C,QAAA,EACb5B,QAAQ,CAACwB,GAAG,CAAC,CAACkE,GAAG,EAAEhE,KAAK,kBACvB/B,OAAA,CAACZ,MAAM,CAACsG,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9B7D,SAAS,EAAG,uBAAsB+D,GAAG,CAACjD,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAgB,EAAE;gBAAAb,QAAA,GAEzF8D,GAAG,CAACjD,IAAI,KAAK,WAAW,iBACvB9C,OAAA;kBAAKgC,SAAS,EAAC,gIAAgI;kBAAAC,QAAA,eAC7IjC,OAAA,CAACP,OAAO;oBAACuC,SAAS,EAAC;kBAAkC;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CACN,eAED3B,OAAA;kBAAKgC,SAAS,EAAG,8BAA6B+D,GAAG,CAACjD,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,EAAG,EAAE;kBAAAb,QAAA,eACnFjC,OAAA;oBACEgC,SAAS,EAAG,wCACV+D,GAAG,CAACjD,IAAI,KAAK,MAAM,GACf,gCAAgC,GAChC,2CACL,EAAE;oBAAAb,QAAA,EAEF8D,GAAG,CAACjD,IAAI,KAAK,WAAW,gBACvB9C,OAAA;sBAAKgC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAC3B8D,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEhD,OAAO,GACXzB,aAAa,CAACyE,GAAG,CAAChD,OAAO,CAAC,gBAE1B/C,OAAA;wBAAGgC,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAC;sBAAgC;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAChE;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,gBAEN3B,OAAA;sBAAAiC,QAAA,EACG,OAAO8D,GAAG,CAAChD,OAAO,KAAK,QAAQ,gBAC9B/C,OAAA;wBAAGgC,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAAE8D,GAAG,CAAChD;sBAAO;wBAAAvB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,GAEpDoE,GAAG,CAAChD,OAAO,CAAClB,GAAG,CAAC,CAACmE,IAAI,EAAEC,GAAG,KACxBD,IAAI,CAACd,IAAI,KAAK,MAAM,gBAClBlF,OAAA;wBAAagC,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,EAAE+D,IAAI,CAACzE;sBAAI,GAApD0E,GAAG;wBAAAzE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAqD,CAAC,gBAEjE3B,OAAA;wBAAegC,SAAS,EAAC,MAAM;wBAAAC,QAAA,eAC7BjC,OAAA;0BACEkG,GAAG,EAAEF,IAAI,CAACb,SAAS,CAACL,GAAI;0BACxBqB,GAAG,EAAC,uBAAuB;0BAC3BnE,SAAS,EAAC,wCAAwC;0BAClDoE,KAAK,EAAE;4BAAEC,SAAS,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;0BAAQ;wBAAE;0BAAA/E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpE;sBAAC,GANMsE,GAAG;wBAAAzE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOR,CAET;oBACD;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELoE,GAAG,CAACjD,IAAI,KAAK,MAAM,iBAClB9C,OAAA;kBAAKgC,SAAS,EAAC,+HAA+H;kBAAAC,QAAA,eAC5IjC,OAAA,CAACN,MAAM;oBAACsC,SAAS,EAAC;kBAAkC;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CACN;cAAA,GAxDII,KAAK;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyDA,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa,CAAC,eAGlB3B,OAAA,CAACX,eAAe;cAAA4C,QAAA,EACbhB,QAAQ,iBACPjB,OAAA,CAACZ,MAAM,CAACsG,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/BC,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBAC9BW,IAAI,EAAE;kBAAEZ,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7B7D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAE/BjC,OAAA;kBAAKgC,SAAS,EAAC,kHAAkH;kBAAAC,QAAA,eAC/HjC,OAAA,CAACP,OAAO;oBAACuC,SAAS,EAAC;kBAAkC;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACN3B,OAAA;kBAAKgC,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,eAC7FjC,OAAA;oBAAKgC,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBjC,OAAA,CAACZ,MAAM,CAACsG,GAAG;sBACTI,OAAO,EAAE;wBAAEW,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;sBAAE,CAAE;sBAChCC,UAAU,EAAE;wBAAEC,QAAQ,EAAE,GAAG;wBAAEC,MAAM,EAAEC,QAAQ;wBAAEC,KAAK,EAAE;sBAAE,CAAE;sBAC1D9E,SAAS,EAAC;oBAAkC;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACF3B,OAAA,CAACZ,MAAM,CAACsG,GAAG;sBACTI,OAAO,EAAE;wBAAEW,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;sBAAE,CAAE;sBAChCC,UAAU,EAAE;wBAAEC,QAAQ,EAAE,GAAG;wBAAEC,MAAM,EAAEC,QAAQ;wBAAEC,KAAK,EAAE;sBAAI,CAAE;sBAC5D9E,SAAS,EAAC;oBAAkC;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACF3B,OAAA,CAACZ,MAAM,CAACsG,GAAG;sBACTI,OAAO,EAAE;wBAAEW,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;sBAAE,CAAE;sBAChCC,UAAU,EAAE;wBAAEC,QAAQ,EAAE,GAAG;wBAAEC,MAAM,EAAEC,QAAQ;wBAAEC,KAAK,EAAE;sBAAI,CAAE;sBAC5D9E,SAAS,EAAC;oBAAkC;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC,eAElB3B,OAAA;cAAK+G,GAAG,EAAE5F;YAAe;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3B,OAAA;QAAKgC,SAAS,EAAC,4FAA4F;QAAAC,QAAA,eACzGjC,OAAA;UAAKgC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAEhCjC,OAAA,CAACX,eAAe;YAAA4C,QAAA,EACbtB,YAAY,iBACXX,OAAA,CAACZ,MAAM,CAACsG,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEoB,MAAM,EAAE;cAAE,CAAE;cACnClB,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEoB,MAAM,EAAE;cAAO,CAAE;cACxCR,IAAI,EAAE;gBAAEZ,OAAO,EAAE,CAAC;gBAAEoB,MAAM,EAAE;cAAE,CAAE;cAChChF,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eAE9FjC,OAAA;gBAAKgC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CjC,OAAA;kBAAKgC,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBjC,OAAA;oBACEkG,GAAG,EAAEvF,YAAa;oBAClBwF,GAAG,EAAC,SAAS;oBACbnE,SAAS,EAAC;kBAA0E;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC,eACF3B,OAAA;oBACEiH,OAAO,EAAE9C,WAAY;oBACrBnC,SAAS,EAAC,uJAAuJ;oBAAAC,QAAA,eAEjKjC,OAAA,CAACR,GAAG;sBAACwC,SAAS,EAAC;oBAAuB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN3B,OAAA;kBAAKgC,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBjC,OAAA;oBAAGgC,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC9E3B,OAAA;oBAAGgC,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAExB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyG;kBAAI;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC,eAGlB3B,OAAA;YAAKgC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBjC,OAAA;cAAKgC,SAAS,EAAC,6LAA6L;cAAAC,QAAA,gBAE1MjC,OAAA;gBACEiH,OAAO,EAAEA,CAAA;kBAAA,IAAAE,qBAAA;kBAAA,QAAAA,qBAAA,GAAM/F,YAAY,CAACkC,OAAO,cAAA6D,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;gBAAA,CAAC;gBAC7CpF,SAAS,EAAC,qFAAqF;gBAC/FqF,KAAK,EAAC,cAAc;gBAAApF,QAAA,eAEpBjC,OAAA,CAACT,WAAW;kBAACyC,SAAS,EAAC;gBAAuB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eAGT3B,OAAA;gBACE+G,GAAG,EAAE1F,WAAY;gBACjB+C,KAAK,EAAE7D,MAAO;gBACd+G,QAAQ,EAAG5D,CAAC,IAAKlD,SAAS,CAACkD,CAAC,CAACE,MAAM,CAACQ,KAAK,CAAE;gBAC3CmD,UAAU,EAAElD,cAAe;gBAC3BmD,WAAW,EAAElB,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,oBAAoB,GAAG,yCAA0C;gBACzGvE,SAAS,EAAC,oHAAoH;gBAC9HyF,IAAI,EAAE,CAAE;gBACRrB,KAAK,EAAE;kBACLY,MAAM,EAAE,MAAM;kBACdU,SAAS,EAAEpB,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACrDF,SAAS,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBACjD,CAAE;gBACFoB,OAAO,EAAGjE,CAAC,IAAK;kBACdA,CAAC,CAACE,MAAM,CAACwC,KAAK,CAACY,MAAM,GAAG,MAAM;kBAC9B,MAAMX,SAAS,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,EAAE,GAAG,GAAG;kBACrD7C,CAAC,CAACE,MAAM,CAACwC,KAAK,CAACY,MAAM,GAAGY,IAAI,CAACC,GAAG,CAACnE,CAAC,CAACE,MAAM,CAACkE,YAAY,EAAEzB,SAAS,CAAC,GAAG,IAAI;gBAC3E;cAAE;gBAAA7E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGF3B,OAAA,CAACZ,MAAM,CAAC2I,MAAM;gBACZC,UAAU,EAAE;kBAAEvB,KAAK,EAAE;gBAAK,CAAE;gBAC5BwB,QAAQ,EAAE;kBAAExB,KAAK,EAAE;gBAAK,CAAE;gBAC1BQ,OAAO,EAAE1C,UAAW;gBACpB2D,QAAQ,EAAErH,SAAS,IAAK,CAACN,MAAM,CAAC2B,IAAI,CAAC,CAAC,IAAI,CAACzB,SAAW;gBACtDuB,SAAS,EAAG,iCACVnB,SAAS,IAAK,CAACN,MAAM,CAAC2B,IAAI,CAAC,CAAC,IAAI,CAACzB,SAAU,GACvC,8CAA8C,GAC9C,oEACL,EAAE;gBAAAwB,QAAA,EAEFpB,SAAS,gBACRb,OAAA,CAACJ,QAAQ;kBAACoC,SAAS,EAAC;gBAAoC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE3D3B,OAAA,CAACV,MAAM;kBAAC0C,SAAS,EAAC;gBAAuB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC5C;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAGN3B,OAAA;cACE+G,GAAG,EAAE3F,YAAa;cAClB8D,IAAI,EAAC,MAAM;cACXiD,MAAM,EAAC,SAAS;cAChBb,QAAQ,EAAE7D,iBAAkB;cAC5BzB,SAAS,EAAC;YAAQ;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN3B,OAAA;YAAGgC,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EACvDqE,MAAM,CAACC,UAAU,IAAI,GAAG,GACrB,gDAAgD,GAChD;UAA6E;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP;AAACvB,EAAA,CAjbQD,kBAAkB;AAAAiI,EAAA,GAAlBjI,kBAAkB;AAmb3B,eAAeA,kBAAkB;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}