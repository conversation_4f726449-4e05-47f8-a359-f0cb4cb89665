/* Simple CSS animations to replace Framer Motion */

/* Responsive Quiz Grid */
.quiz-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(2, 1fr); /* Mobile: 2 cards */
}

/* Tablets: 4 cards */
@media (min-width: 768px) {
  .quiz-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Laptops: 5 cards */
@media (min-width: 1024px) {
  .quiz-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Large screens: 6 cards */
@media (min-width: 1280px) {
  .quiz-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDelay {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInStagger {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-delay {
  animation: fadeInDelay 0.6s ease-out 0.1s forwards;
}

.animate-fade-in-delay-2 {
  animation: fadeInDelay 0.6s ease-out 0.2s forwards;
}

.animate-fade-in-stagger {
  animation: fadeInStagger 0.6s ease-out forwards;
}

/* Hover effects */
.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale:active {
  transform: scale(0.95);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive grid */
.quiz-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .quiz-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Focus states */
button:focus,
input:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
