{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\FloatingBrainwaveAI.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { TbRobot, TbX, TbSend, TbMinus, TbMaximize, TbPaperclip } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FloatingBrainwaveAI = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const [messages, setMessages] = useState([{\n    role: 'assistant',\n    content: 'Hi! I\\'m <PERSON><PERSON> <PERSON>, your study assistant. How can I help you today?'\n  }]);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n    setInput('');\n    removeImage();\n    setIsTyping(true);\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        console.log('Uploading image:', imageFile.name);\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        console.log('Upload result:', uploadResult);\n        if (uploadResult !== null && uploadResult !== void 0 && uploadResult.success) {\n          imageUrl = uploadResult.url;\n          console.log('Image uploaded successfully:', imageUrl);\n        } else {\n          console.error('Image upload failed:', uploadResult);\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: userMessage\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: userMessage\n      };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response from ChatGPT\n      console.log('Sending to ChatGPT:', [...messages, newUserMessage]);\n      const response = await chatWithChatGPT({\n        messages: [...messages, newUserMessage]\n      });\n      console.log('ChatGPT response:', response);\n      if (response !== null && response !== void 0 && response.success && response !== null && response !== void 0 && response.data) {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: response.data\n        }]);\n      } else {\n        console.error('ChatGPT error:', response);\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: `I'm sorry, I couldn't process your request right now. Error: ${(response === null || response === void 0 ? void 0 : response.error) || 'Unknown error'}. Please try again in a moment.`\n        }]);\n      }\n    } catch (error) {\n      console.error('Chat error:', error);\n      setMessages(prev => [...prev, {\n        role: 'assistant',\n        content: \"I'm experiencing some technical difficulties. Please try again later.\"\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  if (!isOpen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => setIsOpen(true),\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        width: '60px',\n        height: '60px',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        borderRadius: '50%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        cursor: 'pointer',\n        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n        zIndex: 1000,\n        transition: 'all 0.3s ease',\n        animation: 'pulse 2s infinite'\n      },\n      onMouseEnter: e => {\n        e.target.style.transform = 'scale(1.1)';\n        e.target.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.6)';\n      },\n      onMouseLeave: e => {\n        e.target.style.transform = 'scale(1)';\n        e.target.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.4)';\n      },\n      children: [/*#__PURE__*/_jsxDEV(TbRobot, {\n        style: {\n          color: 'white',\n          fontSize: '28px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n          @keyframes pulse {\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      bottom: '20px',\n      right: '20px',\n      width: isMinimized ? '300px' : '380px',\n      height: isMinimized ? '60px' : '500px',\n      background: 'rgba(255, 255, 255, 0.95)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '20px',\n      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n      border: '1px solid rgba(255, 255, 255, 0.2)',\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease'\n    },\n    onWheel: e => {\n      // Prevent background scrolling when scrolling over the AI chat\n      e.stopPropagation();\n    },\n    onTouchMove: e => {\n      // Prevent background scrolling on mobile\n      e.stopPropagation();\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        padding: '16px 20px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        borderRadius: '20px 20px 0 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '32px',\n            height: '32px',\n            background: 'rgba(255, 255, 255, 0.2)',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(TbRobot, {\n            style: {\n              color: 'white',\n              fontSize: '18px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: 0,\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: '600'\n            },\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              color: 'rgba(255, 255, 255, 0.8)',\n              fontSize: '12px'\n            },\n            children: \"Always here to help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsMinimized(!isMinimized),\n          style: {\n            background: 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: '8px',\n            width: '32px',\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)'\n          },\n          onMouseEnter: e => {\n            e.target.style.background = 'rgba(255, 255, 255, 0.4)';\n            e.target.style.transform = 'scale(1.05)';\n          },\n          onMouseLeave: e => {\n            e.target.style.background = 'rgba(255, 255, 255, 0.25)';\n            e.target.style.transform = 'scale(1)';\n          },\n          children: isMinimized ? /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            },\n            children: \"\\u2B1C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            },\n            children: \"\\u2796\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsOpen(false),\n          style: {\n            background: 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: '8px',\n            width: '32px',\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)'\n          },\n          onMouseEnter: e => {\n            e.target.style.background = 'rgba(255, 60, 60, 0.4)';\n            e.target.style.transform = 'scale(1.05)';\n          },\n          onMouseLeave: e => {\n            e.target.style.background = 'rgba(255, 255, 255, 0.25)';\n            e.target.style.transform = 'scale(1)';\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'white',\n              fontSize: '16px',\n              fontWeight: 'bold'\n            },\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), !isMinimized && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          padding: '20px 20px 0 20px',\n          overflowY: 'auto',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '16px',\n          scrollBehavior: 'smooth',\n          scrollbarWidth: 'thin',\n          scrollbarColor: '#cbd5e1 transparent'\n        },\n        className: \"custom-scrollbar\",\n        onWheel: e => {\n          // Allow scrolling within the messages container\n          const element = e.currentTarget;\n          const {\n            scrollTop,\n            scrollHeight,\n            clientHeight\n          } = element;\n\n          // If at top and scrolling up, or at bottom and scrolling down, prevent propagation\n          if (scrollTop === 0 && e.deltaY < 0 || scrollTop + clientHeight >= scrollHeight && e.deltaY > 0) {\n            e.preventDefault();\n            e.stopPropagation();\n          }\n        },\n        children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxWidth: '85%',\n              padding: '12px 16px',\n              borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',\n              background: msg.role === 'user' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f8fafc',\n              color: msg.role === 'user' ? 'white' : '#334155',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              boxShadow: msg.role === 'user' ? '0 4px 12px rgba(102, 126, 234, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',\n              wordWrap: 'break-word'\n            },\n            children: typeof msg.content === 'string' ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                whiteSpace: 'pre-wrap'\n              },\n              children: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 21\n            }, this) : Array.isArray(msg.content) ? msg.content.map((item, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  whiteSpace: 'pre-wrap',\n                  marginBottom: item.text ? '8px' : '0'\n                },\n                children: item.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 27\n              }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"img\", {\n                src: item.image_url.url,\n                alt: \"User upload\",\n                style: {\n                  maxWidth: '100%',\n                  height: 'auto',\n                  borderRadius: '12px',\n                  maxHeight: '200px',\n                  objectFit: 'cover',\n                  border: '2px solid rgba(255, 255, 255, 0.2)',\n                  marginTop: '4px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 27\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 23\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Invalid message format\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 15\n        }, this)), isTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '12px 16px',\n              borderRadius: '18px 18px 18px 4px',\n              background: '#f8fafc',\n              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '4px',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.16s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.32s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'sticky',\n          bottom: 0,\n          background: 'rgba(255, 255, 255, 0.98)',\n          backdropFilter: 'blur(20px)',\n          borderTop: '1px solid rgba(0, 0, 0, 0.08)',\n          padding: '16px 20px 20px',\n          zIndex: 10\n        },\n        children: [imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            padding: '12px',\n            background: '#f1f5f9',\n            borderRadius: '12px',\n            border: '1px solid #e2e8f0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  objectFit: 'cover',\n                  borderRadius: '8px',\n                  border: '2px solid #e2e8f0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: removeImage,\n                style: {\n                  position: 'absolute',\n                  top: '-6px',\n                  right: '-6px',\n                  width: '20px',\n                  height: '20px',\n                  background: '#ef4444',\n                  color: 'white',\n                  borderRadius: '50%',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '12px',\n                  fontWeight: 'bold'\n                },\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '13px',\n                  fontWeight: '600',\n                  color: '#374151',\n                  margin: 0\n                },\n                children: \"Image attached\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '11px',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '8px',\n            background: '#f8fafc',\n            borderRadius: '16px',\n            padding: '8px',\n            border: '2px solid #e2e8f0',\n            transition: 'border-color 0.2s ease'\n          },\n          onFocus: e => e.target.style.borderColor = '#667eea',\n          onBlur: e => e.target.style.borderColor = '#e2e8f0',\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            style: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              border: 'none',\n              borderRadius: '12px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'\n            },\n            onMouseEnter: e => {\n              e.target.style.transform = 'scale(1.05)';\n              e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';\n            },\n            onMouseLeave: e => {\n              e.target.style.transform = 'scale(1)';\n              e.target.style.boxShadow = '0 2px 8px rgba(102, 126, 234, 0.3)';\n            },\n            title: \"Attach image\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'white',\n                fontSize: '18px',\n                fontWeight: 'bold'\n              },\n              children: \"\\uD83D\\uDCCE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: input,\n            onChange: e => setInput(e.target.value),\n            onKeyDown: handleKeyDown,\n            placeholder: \"Ask me anything...\",\n            style: {\n              flex: 1,\n              border: 'none',\n              background: 'transparent',\n              outline: 'none',\n              fontSize: '14px',\n              color: '#334155',\n              padding: '12px 16px',\n              fontFamily: 'inherit'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: sendMessage,\n            disabled: !input.trim() && !selectedImage,\n            style: {\n              background: input.trim() || selectedImage ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#e2e8f0',\n              border: 'none',\n              borderRadius: '12px',\n              width: '40px',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: input.trim() || selectedImage ? 'pointer' : 'not-allowed',\n              transition: 'all 0.2s ease',\n              boxShadow: input.trim() || selectedImage ? '0 2px 8px rgba(102, 126, 234, 0.3)' : 'none'\n            },\n            onMouseEnter: e => {\n              if (input.trim() || selectedImage) {\n                e.target.style.transform = 'scale(1.05)';\n                e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';\n              }\n            },\n            onMouseLeave: e => {\n              e.target.style.transform = 'scale(1)';\n              e.target.style.boxShadow = input.trim() || selectedImage ? '0 2px 8px rgba(102, 126, 234, 0.3)' : 'none';\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: input.trim() || selectedImage ? 'white' : '#94a3b8',\n                fontSize: '18px',\n                fontWeight: 'bold'\n              },\n              children: \"\\u27A4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '11px',\n            color: '#94a3b8',\n            textAlign: 'center',\n            margin: '8px 0 0 0'\n          },\n          children: \"Press Enter to send \\u2022 Attach images for analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n\n        /* Force white icons in floating AI */\n        .floating-ai-white-icon {\n          color: #FFFFFF !important;\n          fill: #FFFFFF !important;\n        }\n\n        .floating-ai-white-icon svg {\n          color: #FFFFFF !important;\n          fill: #FFFFFF !important;\n        }\n\n        .floating-ai-white-icon path {\n          fill: #FFFFFF !important;\n          stroke: #FFFFFF !important;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n};\n_s(FloatingBrainwaveAI, \"ZhOZT57spe4ri1k2EGMoAEq6kBM=\");\n_c = FloatingBrainwaveAI;\nexport default FloatingBrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"FloatingBrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "TbRobot", "TbX", "TbSend", "TbMinus", "TbMaximize", "TbPaperclip", "chatWithChatGPT", "uploadImg", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FloatingBrainwaveAI", "_s", "isOpen", "setIsOpen", "isMinimized", "setIsMinimized", "messages", "setMessages", "role", "content", "input", "setInput", "isTyping", "setIsTyping", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "messagesEndRef", "fileInputRef", "current", "scrollIntoView", "behavior", "handleImageSelect", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeImage", "value", "sendMessage", "trim", "userMessage", "imageFile", "imageUrl", "console", "log", "name", "formData", "FormData", "append", "uploadResult", "success", "url", "error", "newUserMessage", "text", "image_url", "prev", "response", "data", "handleKeyDown", "key", "shift<PERSON>ey", "preventDefault", "onClick", "style", "position", "bottom", "right", "width", "height", "background", "borderRadius", "display", "alignItems", "justifyContent", "cursor", "boxShadow", "zIndex", "transition", "animation", "onMouseEnter", "transform", "onMouseLeave", "children", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>ilter", "border", "flexDirection", "overflow", "onWheel", "stopPropagation", "onTouchMove", "padding", "gap", "margin", "fontWeight", "flex", "overflowY", "scroll<PERSON>eh<PERSON>or", "scrollbarWidth", "scrollbarColor", "className", "element", "currentTarget", "scrollTop", "scrollHeight", "clientHeight", "deltaY", "map", "msg", "index", "max<PERSON><PERSON><PERSON>", "lineHeight", "wordWrap", "whiteSpace", "Array", "isArray", "item", "idx", "marginBottom", "src", "alt", "maxHeight", "objectFit", "marginTop", "ref", "borderTop", "top", "onFocus", "borderColor", "onBlur", "_fileInputRef$current", "click", "title", "onChange", "onKeyDown", "placeholder", "outline", "fontFamily", "disabled", "accept", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/FloatingBrainwaveAI.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { TbR<PERSON>ot, TbX, Tb<PERSON><PERSON>, TbMinus, TbMaximize, TbPaperclip } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\n\nconst FloatingBrainwaveAI = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const [messages, setMessages] = useState([\n    { role: 'assistant', content: 'Hi! I\\'m <PERSON><PERSON> AI, your study assistant. How can I help you today?' }\n  ]);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = (event) => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = (e) => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n\n    setInput('');\n    removeImage();\n    setIsTyping(true);\n\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        console.log('Uploading image:', imageFile.name);\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        console.log('Upload result:', uploadResult);\n\n        if (uploadResult?.success) {\n          imageUrl = uploadResult.url;\n          console.log('Image uploaded successfully:', imageUrl);\n        } else {\n          console.error('Image upload failed:', uploadResult);\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl\n        ? {\n            role: \"user\",\n            content: [\n              { type: \"text\", text: userMessage },\n              { type: \"image_url\", image_url: { url: imageUrl } }\n            ]\n          }\n        : { role: \"user\", content: userMessage };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response from ChatGPT\n      console.log('Sending to ChatGPT:', [...messages, newUserMessage]);\n      const response = await chatWithChatGPT({\n        messages: [...messages, newUserMessage]\n      });\n      console.log('ChatGPT response:', response);\n\n      if (response?.success && response?.data) {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: response.data\n        }]);\n      } else {\n        console.error('ChatGPT error:', response);\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: `I'm sorry, I couldn't process your request right now. Error: ${response?.error || 'Unknown error'}. Please try again in a moment.`\n        }]);\n      }\n    } catch (error) {\n      console.error('Chat error:', error);\n      setMessages(prev => [...prev, {\n        role: 'assistant',\n        content: \"I'm experiencing some technical difficulties. Please try again later.\"\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  if (!isOpen) {\n    return (\n      <div\n        onClick={() => setIsOpen(true)}\n        style={{\n          position: 'fixed',\n          bottom: '20px',\n          right: '20px',\n          width: '60px',\n          height: '60px',\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          borderRadius: '50%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          cursor: 'pointer',\n          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n          zIndex: 1000,\n          transition: 'all 0.3s ease',\n          animation: 'pulse 2s infinite'\n        }}\n        onMouseEnter={(e) => {\n          e.target.style.transform = 'scale(1.1)';\n          e.target.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.6)';\n        }}\n        onMouseLeave={(e) => {\n          e.target.style.transform = 'scale(1)';\n          e.target.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.4)';\n        }}\n      >\n        <TbRobot style={{ color: 'white', fontSize: '28px' }} />\n        <style>{`\n          @keyframes pulse {\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n          }\n        `}</style>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        width: isMinimized ? '300px' : '380px',\n        height: isMinimized ? '60px' : '500px',\n        background: 'rgba(255, 255, 255, 0.95)',\n        backdropFilter: 'blur(20px)',\n        borderRadius: '20px',\n        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n        border: '1px solid rgba(255, 255, 255, 0.2)',\n        zIndex: 1000,\n        display: 'flex',\n        flexDirection: 'column',\n        overflow: 'hidden',\n        transition: 'all 0.3s ease'\n      }}\n      onWheel={(e) => {\n        // Prevent background scrolling when scrolling over the AI chat\n        e.stopPropagation();\n      }}\n      onTouchMove={(e) => {\n        // Prevent background scrolling on mobile\n        e.stopPropagation();\n      }}\n    >\n      {/* Header */}\n      <div\n        style={{\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          padding: '16px 20px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          borderRadius: '20px 20px 0 0'\n        }}\n      >\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div\n            style={{\n              width: '32px',\n              height: '32px',\n              background: 'rgba(255, 255, 255, 0.2)',\n              borderRadius: '50%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n          >\n            <TbRobot style={{ color: 'white', fontSize: '18px' }} />\n          </div>\n          <div>\n            <h3 style={{ margin: 0, color: 'white', fontSize: '16px', fontWeight: '600' }}>\n              Brainwave AI\n            </h3>\n            <p style={{ margin: 0, color: 'rgba(255, 255, 255, 0.8)', fontSize: '12px' }}>\n              Always here to help\n            </p>\n          </div>\n        </div>\n        \n        <div style={{ display: 'flex', gap: '8px' }}>\n          <button\n            onClick={() => setIsMinimized(!isMinimized)}\n            style={{\n              background: 'rgba(255, 255, 255, 0.25)',\n              border: 'none',\n              borderRadius: '8px',\n              width: '32px',\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              backdropFilter: 'blur(10px)'\n            }}\n            onMouseEnter={(e) => {\n              e.target.style.background = 'rgba(255, 255, 255, 0.4)';\n              e.target.style.transform = 'scale(1.05)';\n            }}\n            onMouseLeave={(e) => {\n              e.target.style.background = 'rgba(255, 255, 255, 0.25)';\n              e.target.style.transform = 'scale(1)';\n            }}\n          >\n            {isMinimized ? (\n              <span style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>⬜</span>\n            ) : (\n              <span style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>➖</span>\n            )}\n          </button>\n\n          <button\n            onClick={() => setIsOpen(false)}\n            style={{\n              background: 'rgba(255, 255, 255, 0.25)',\n              border: 'none',\n              borderRadius: '8px',\n              width: '32px',\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              backdropFilter: 'blur(10px)'\n            }}\n            onMouseEnter={(e) => {\n              e.target.style.background = 'rgba(255, 60, 60, 0.4)';\n              e.target.style.transform = 'scale(1.05)';\n            }}\n            onMouseLeave={(e) => {\n              e.target.style.background = 'rgba(255, 255, 255, 0.25)';\n              e.target.style.transform = 'scale(1)';\n            }}\n          >\n            <span style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>✕</span>\n          </button>\n        </div>\n      </div>\n\n      {!isMinimized && (\n        <>\n          {/* Messages */}\n          <div\n            style={{\n              flex: 1,\n              padding: '20px 20px 0 20px',\n              overflowY: 'auto',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '16px',\n              scrollBehavior: 'smooth',\n              scrollbarWidth: 'thin',\n              scrollbarColor: '#cbd5e1 transparent'\n            }}\n            className=\"custom-scrollbar\"\n            onWheel={(e) => {\n              // Allow scrolling within the messages container\n              const element = e.currentTarget;\n              const { scrollTop, scrollHeight, clientHeight } = element;\n\n              // If at top and scrolling up, or at bottom and scrolling down, prevent propagation\n              if ((scrollTop === 0 && e.deltaY < 0) ||\n                  (scrollTop + clientHeight >= scrollHeight && e.deltaY > 0)) {\n                e.preventDefault();\n                e.stopPropagation();\n              }\n            }}\n          >\n            {messages.map((msg, index) => (\n              <div\n                key={index}\n                style={{\n                  display: 'flex',\n                  justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'\n                }}\n              >\n                <div\n                  style={{\n                    maxWidth: '85%',\n                    padding: '12px 16px',\n                    borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',\n                    background: msg.role === 'user'\n                      ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                      : '#f8fafc',\n                    color: msg.role === 'user' ? 'white' : '#334155',\n                    fontSize: '14px',\n                    lineHeight: '1.5',\n                    boxShadow: msg.role === 'user'\n                      ? '0 4px 12px rgba(102, 126, 234, 0.3)'\n                      : '0 2px 8px rgba(0, 0, 0, 0.1)',\n                    wordWrap: 'break-word'\n                  }}\n                >\n                  {/* Handle different message content types */}\n                  {typeof msg.content === 'string' ? (\n                    <div style={{ whiteSpace: 'pre-wrap' }}>{msg.content}</div>\n                  ) : Array.isArray(msg.content) ? (\n                    msg.content.map((item, idx) => (\n                      <div key={idx}>\n                        {item.type === 'text' && (\n                          <div style={{ whiteSpace: 'pre-wrap', marginBottom: item.text ? '8px' : '0' }}>\n                            {item.text}\n                          </div>\n                        )}\n                        {item.type === 'image_url' && (\n                          <img\n                            src={item.image_url.url}\n                            alt=\"User upload\"\n                            style={{\n                              maxWidth: '100%',\n                              height: 'auto',\n                              borderRadius: '12px',\n                              maxHeight: '200px',\n                              objectFit: 'cover',\n                              border: '2px solid rgba(255, 255, 255, 0.2)',\n                              marginTop: '4px'\n                            }}\n                          />\n                        )}\n                      </div>\n                    ))\n                  ) : (\n                    <div>Invalid message format</div>\n                  )}\n                </div>\n              </div>\n            ))}\n\n            {isTyping && (\n              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>\n                <div\n                  style={{\n                    padding: '12px 16px',\n                    borderRadius: '18px 18px 18px 4px',\n                    background: '#f8fafc',\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n                  }}\n                >\n                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>\n                    <div\n                      style={{\n                        width: '6px',\n                        height: '6px',\n                        borderRadius: '50%',\n                        background: '#94a3b8',\n                        animation: 'bounce 1.4s infinite ease-in-out'\n                      }}\n                    />\n                    <div\n                      style={{\n                        width: '6px',\n                        height: '6px',\n                        borderRadius: '50%',\n                        background: '#94a3b8',\n                        animation: 'bounce 1.4s infinite ease-in-out 0.16s'\n                      }}\n                    />\n                    <div\n                      style={{\n                        width: '6px',\n                        height: '6px',\n                        borderRadius: '50%',\n                        background: '#94a3b8',\n                        animation: 'bounce 1.4s infinite ease-in-out 0.32s'\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* Input Section - Sticky */}\n          <div\n            style={{\n              position: 'sticky',\n              bottom: 0,\n              background: 'rgba(255, 255, 255, 0.98)',\n              backdropFilter: 'blur(20px)',\n              borderTop: '1px solid rgba(0, 0, 0, 0.08)',\n              padding: '16px 20px 20px',\n              zIndex: 10\n            }}\n          >\n            {/* Image Preview */}\n            {imagePreview && (\n              <div\n                style={{\n                  marginBottom: '12px',\n                  padding: '12px',\n                  background: '#f1f5f9',\n                  borderRadius: '12px',\n                  border: '1px solid #e2e8f0'\n                }}\n              >\n                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n                  <div style={{ position: 'relative' }}>\n                    <img\n                      src={imagePreview}\n                      alt=\"Preview\"\n                      style={{\n                        width: '60px',\n                        height: '60px',\n                        objectFit: 'cover',\n                        borderRadius: '8px',\n                        border: '2px solid #e2e8f0'\n                      }}\n                    />\n                    <button\n                      onClick={removeImage}\n                      style={{\n                        position: 'absolute',\n                        top: '-6px',\n                        right: '-6px',\n                        width: '20px',\n                        height: '20px',\n                        background: '#ef4444',\n                        color: 'white',\n                        borderRadius: '50%',\n                        border: 'none',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        fontSize: '12px',\n                        fontWeight: 'bold'\n                      }}\n                    >\n                      ×\n                    </button>\n                  </div>\n                  <div style={{ flex: 1 }}>\n                    <p style={{ fontSize: '13px', fontWeight: '600', color: '#374151', margin: 0 }}>\n                      Image attached\n                    </p>\n                    <p style={{ fontSize: '11px', color: '#6b7280', margin: 0 }}>\n                      {selectedImage?.name}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Input Area */}\n            <div\n              style={{\n                display: 'flex',\n                gap: '8px',\n                background: '#f8fafc',\n                borderRadius: '16px',\n                padding: '8px',\n                border: '2px solid #e2e8f0',\n                transition: 'border-color 0.2s ease'\n              }}\n              onFocus={(e) => e.target.style.borderColor = '#667eea'}\n              onBlur={(e) => e.target.style.borderColor = '#e2e8f0'}\n            >\n              {/* Attachment Button */}\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                style={{\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  border: 'none',\n                  borderRadius: '12px',\n                  width: '40px',\n                  height: '40px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'\n                }}\n                onMouseEnter={(e) => {\n                  e.target.style.transform = 'scale(1.05)';\n                  e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';\n                }}\n                onMouseLeave={(e) => {\n                  e.target.style.transform = 'scale(1)';\n                  e.target.style.boxShadow = '0 2px 8px rgba(102, 126, 234, 0.3)';\n                }}\n                title=\"Attach image\"\n              >\n                <span style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>📎</span>\n              </button>\n\n              {/* Text Input */}\n              <input\n                type=\"text\"\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                onKeyDown={handleKeyDown}\n                placeholder=\"Ask me anything...\"\n                style={{\n                  flex: 1,\n                  border: 'none',\n                  background: 'transparent',\n                  outline: 'none',\n                  fontSize: '14px',\n                  color: '#334155',\n                  padding: '12px 16px',\n                  fontFamily: 'inherit'\n                }}\n              />\n\n              {/* Send Button */}\n              <button\n                onClick={sendMessage}\n                disabled={!input.trim() && !selectedImage}\n                style={{\n                  background: (input.trim() || selectedImage)\n                    ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                    : '#e2e8f0',\n                  border: 'none',\n                  borderRadius: '12px',\n                  width: '40px',\n                  height: '40px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  cursor: (input.trim() || selectedImage) ? 'pointer' : 'not-allowed',\n                  transition: 'all 0.2s ease',\n                  boxShadow: (input.trim() || selectedImage)\n                    ? '0 2px 8px rgba(102, 126, 234, 0.3)'\n                    : 'none'\n                }}\n                onMouseEnter={(e) => {\n                  if (input.trim() || selectedImage) {\n                    e.target.style.transform = 'scale(1.05)';\n                    e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';\n                  }\n                }}\n                onMouseLeave={(e) => {\n                  e.target.style.transform = 'scale(1)';\n                  e.target.style.boxShadow = (input.trim() || selectedImage)\n                    ? '0 2px 8px rgba(102, 126, 234, 0.3)'\n                    : 'none';\n                }}\n              >\n                <span style={{\n                  color: (input.trim() || selectedImage) ? 'white' : '#94a3b8',\n                  fontSize: '18px',\n                  fontWeight: 'bold'\n                }}>\n                  ➤\n                </span>\n              </button>\n            </div>\n\n            {/* Hidden File Input */}\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleImageSelect}\n              style={{ display: 'none' }}\n            />\n\n            {/* Helper Text */}\n            <p style={{\n              fontSize: '11px',\n              color: '#94a3b8',\n              textAlign: 'center',\n              margin: '8px 0 0 0'\n            }}>\n              Press Enter to send • Attach images for analysis\n            </p>\n          </div>\n        </>\n      )}\n\n      <style>{`\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n\n        /* Force white icons in floating AI */\n        .floating-ai-white-icon {\n          color: #FFFFFF !important;\n          fill: #FFFFFF !important;\n        }\n\n        .floating-ai-white-icon svg {\n          color: #FFFFFF !important;\n          fill: #FFFFFF !important;\n        }\n\n        .floating-ai-white-icon path {\n          fill: #FFFFFF !important;\n          stroke: #FFFFFF !important;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default FloatingBrainwaveAI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,WAAW,QAAQ,gBAAgB;AACvF,SAASC,eAAe,EAAEC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,CACvC;IAAEuB,IAAI,EAAE,WAAW;IAAEC,OAAO,EAAE;EAAyE,CAAC,CACzG,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMiC,cAAc,GAAGhC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMiC,YAAY,GAAGjC,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAC,SAAS,CAAC,MAAM;IACd,IAAI+B,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1Cd,gBAAgB,CAACU,IAAI,CAAC;MACtB,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKhB,eAAe,CAACgB,CAAC,CAACP,MAAM,CAACQ,MAAM,CAAC;MACvDJ,MAAM,CAACK,aAAa,CAACV,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBrB,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIE,YAAY,CAACC,OAAO,EAAE;MACxBD,YAAY,CAACC,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;EAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC5B,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAACzB,aAAa,EAAE;IAErC,MAAM0B,WAAW,GAAG9B,KAAK,CAAC6B,IAAI,CAAC,CAAC;IAChC,MAAME,SAAS,GAAG3B,aAAa;IAE/BH,QAAQ,CAAC,EAAE,CAAC;IACZyB,WAAW,CAAC,CAAC;IACbvB,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,IAAI6B,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAID,SAAS,EAAE;QACbE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,SAAS,CAACI,IAAI,CAAC;QAC/C,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEP,SAAS,CAAC;QACnC,MAAMQ,YAAY,GAAG,MAAMtD,SAAS,CAACmD,QAAQ,CAAC;QAC9CH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEK,YAAY,CAAC;QAE3C,IAAIA,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,OAAO,EAAE;UACzBR,QAAQ,GAAGO,YAAY,CAACE,GAAG;UAC3BR,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,QAAQ,CAAC;QACvD,CAAC,MAAM;UACLC,OAAO,CAACS,KAAK,CAAC,sBAAsB,EAAEH,YAAY,CAAC;QACrD;MACF;;MAEA;MACA,MAAMI,cAAc,GAAGX,QAAQ,GAC3B;QACElC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEmB,IAAI,EAAE,MAAM;UAAE0B,IAAI,EAAEd;QAAY,CAAC,EACnC;UAAEZ,IAAI,EAAE,WAAW;UAAE2B,SAAS,EAAE;YAAEJ,GAAG,EAAET;UAAS;QAAE,CAAC;MAEvD,CAAC,GACD;QAAElC,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAE+B;MAAY,CAAC;;MAE1C;MACAjC,WAAW,CAACiD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,cAAc,CAAC,CAAC;;MAE9C;MACAV,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,CAAC,GAAGtC,QAAQ,EAAE+C,cAAc,CAAC,CAAC;MACjE,MAAMI,QAAQ,GAAG,MAAM/D,eAAe,CAAC;QACrCY,QAAQ,EAAE,CAAC,GAAGA,QAAQ,EAAE+C,cAAc;MACxC,CAAC,CAAC;MACFV,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEa,QAAQ,CAAC;MAE1C,IAAIA,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEP,OAAO,IAAIO,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,IAAI,EAAE;QACvCnD,WAAW,CAACiD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5BhD,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEgD,QAAQ,CAACC;QACpB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLf,OAAO,CAACS,KAAK,CAAC,gBAAgB,EAAEK,QAAQ,CAAC;QACzClD,WAAW,CAACiD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5BhD,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAG,gEAA+D,CAAAgD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEL,KAAK,KAAI,eAAgB;QAC9G,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC7C,WAAW,CAACiD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5BhD,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRI,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM8C,aAAa,GAAI1B,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAAC2B,GAAG,KAAK,OAAO,IAAI,CAAC3B,CAAC,CAAC4B,QAAQ,EAAE;MACpC5B,CAAC,CAAC6B,cAAc,CAAC,CAAC;MAClBxB,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,IAAI,CAACpC,MAAM,EAAE;IACX,oBACEL,OAAA;MACEkE,OAAO,EAAEA,CAAA,KAAM5D,SAAS,CAAC,IAAI,CAAE;MAC/B6D,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,mDAAmD;QAC/DC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,qCAAqC;QAChDC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE;MACb,CAAE;MACFC,YAAY,EAAG/C,CAAC,IAAK;QACnBA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,YAAY;QACvChD,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACY,SAAS,GAAG,sCAAsC;MACnE,CAAE;MACFM,YAAY,EAAGjD,CAAC,IAAK;QACnBA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,UAAU;QACrChD,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACY,SAAS,GAAG,qCAAqC;MAClE,CAAE;MAAAO,QAAA,gBAEFtF,OAAA,CAACT,OAAO;QAAC4E,KAAK,EAAE;UAAEoB,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAO;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxD5F,OAAA;QAAAsF,QAAA,EAAS;AACjB;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACE5F,OAAA;IACEmE,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE,MAAM;MACbC,KAAK,EAAEhE,WAAW,GAAG,OAAO,GAAG,OAAO;MACtCiE,MAAM,EAAEjE,WAAW,GAAG,MAAM,GAAG,OAAO;MACtCkE,UAAU,EAAE,2BAA2B;MACvCoB,cAAc,EAAE,YAAY;MAC5BnB,YAAY,EAAE,MAAM;MACpBK,SAAS,EAAE,iCAAiC;MAC5Ce,MAAM,EAAE,oCAAoC;MAC5Cd,MAAM,EAAE,IAAI;MACZL,OAAO,EAAE,MAAM;MACfoB,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE,QAAQ;MAClBf,UAAU,EAAE;IACd,CAAE;IACFgB,OAAO,EAAG7D,CAAC,IAAK;MACd;MACAA,CAAC,CAAC8D,eAAe,CAAC,CAAC;IACrB,CAAE;IACFC,WAAW,EAAG/D,CAAC,IAAK;MAClB;MACAA,CAAC,CAAC8D,eAAe,CAAC,CAAC;IACrB,CAAE;IAAAZ,QAAA,gBAGFtF,OAAA;MACEmE,KAAK,EAAE;QACLM,UAAU,EAAE,mDAAmD;QAC/D2B,OAAO,EAAE,WAAW;QACpBzB,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BH,YAAY,EAAE;MAChB,CAAE;MAAAY,QAAA,gBAEFtF,OAAA;QAAKmE,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEyB,GAAG,EAAE;QAAO,CAAE;QAAAf,QAAA,gBACjEtF,OAAA;UACEmE,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE,0BAA0B;YACtCC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAS,QAAA,eAEFtF,OAAA,CAACT,OAAO;YAAC4E,KAAK,EAAE;cAAEoB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACN5F,OAAA;UAAAsF,QAAA,gBACEtF,OAAA;YAAImE,KAAK,EAAE;cAAEmC,MAAM,EAAE,CAAC;cAAEf,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEe,UAAU,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAAC;UAE/E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5F,OAAA;YAAGmE,KAAK,EAAE;cAAEmC,MAAM,EAAE,CAAC;cAAEf,KAAK,EAAE,0BAA0B;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAE9E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5F,OAAA;QAAKmE,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAE0B,GAAG,EAAE;QAAM,CAAE;QAAAf,QAAA,gBAC1CtF,OAAA;UACEkE,OAAO,EAAEA,CAAA,KAAM1D,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5C4D,KAAK,EAAE;YACLM,UAAU,EAAE,2BAA2B;YACvCqB,MAAM,EAAE,MAAM;YACdpB,YAAY,EAAE,KAAK;YACnBH,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBG,UAAU,EAAE,eAAe;YAC3BY,cAAc,EAAE;UAClB,CAAE;UACFV,YAAY,EAAG/C,CAAC,IAAK;YACnBA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACM,UAAU,GAAG,0BAA0B;YACtDrC,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,aAAa;UAC1C,CAAE;UACFC,YAAY,EAAGjD,CAAC,IAAK;YACnBA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACM,UAAU,GAAG,2BAA2B;YACvDrC,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,UAAU;UACvC,CAAE;UAAAE,QAAA,EAED/E,WAAW,gBACVP,OAAA;YAAMmE,KAAK,EAAE;cAAEoB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEe,UAAU,EAAE;YAAO,CAAE;YAAAjB,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAE/E5F,OAAA;YAAMmE,KAAK,EAAE;cAAEoB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEe,UAAU,EAAE;YAAO,CAAE;YAAAjB,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAC/E;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAET5F,OAAA;UACEkE,OAAO,EAAEA,CAAA,KAAM5D,SAAS,CAAC,KAAK,CAAE;UAChC6D,KAAK,EAAE;YACLM,UAAU,EAAE,2BAA2B;YACvCqB,MAAM,EAAE,MAAM;YACdpB,YAAY,EAAE,KAAK;YACnBH,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBG,UAAU,EAAE,eAAe;YAC3BY,cAAc,EAAE;UAClB,CAAE;UACFV,YAAY,EAAG/C,CAAC,IAAK;YACnBA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACM,UAAU,GAAG,wBAAwB;YACpDrC,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,aAAa;UAC1C,CAAE;UACFC,YAAY,EAAGjD,CAAC,IAAK;YACnBA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACM,UAAU,GAAG,2BAA2B;YACvDrC,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,UAAU;UACvC,CAAE;UAAAE,QAAA,eAEFtF,OAAA;YAAMmE,KAAK,EAAE;cAAEoB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEe,UAAU,EAAE;YAAO,CAAE;YAAAjB,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAACrF,WAAW,iBACXP,OAAA,CAAAE,SAAA;MAAAoF,QAAA,gBAEEtF,OAAA;QACEmE,KAAK,EAAE;UACLqC,IAAI,EAAE,CAAC;UACPJ,OAAO,EAAE,kBAAkB;UAC3BK,SAAS,EAAE,MAAM;UACjB9B,OAAO,EAAE,MAAM;UACfoB,aAAa,EAAE,QAAQ;UACvBM,GAAG,EAAE,MAAM;UACXK,cAAc,EAAE,QAAQ;UACxBC,cAAc,EAAE,MAAM;UACtBC,cAAc,EAAE;QAClB,CAAE;QACFC,SAAS,EAAC,kBAAkB;QAC5BZ,OAAO,EAAG7D,CAAC,IAAK;UACd;UACA,MAAM0E,OAAO,GAAG1E,CAAC,CAAC2E,aAAa;UAC/B,MAAM;YAAEC,SAAS;YAAEC,YAAY;YAAEC;UAAa,CAAC,GAAGJ,OAAO;;UAEzD;UACA,IAAKE,SAAS,KAAK,CAAC,IAAI5E,CAAC,CAAC+E,MAAM,GAAG,CAAC,IAC/BH,SAAS,GAAGE,YAAY,IAAID,YAAY,IAAI7E,CAAC,CAAC+E,MAAM,GAAG,CAAE,EAAE;YAC9D/E,CAAC,CAAC6B,cAAc,CAAC,CAAC;YAClB7B,CAAC,CAAC8D,eAAe,CAAC,CAAC;UACrB;QACF,CAAE;QAAAZ,QAAA,GAED7E,QAAQ,CAAC2G,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBtH,OAAA;UAEEmE,KAAK,EAAE;YACLQ,OAAO,EAAE,MAAM;YACfE,cAAc,EAAEwC,GAAG,CAAC1G,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG;UACrD,CAAE;UAAA2E,QAAA,eAEFtF,OAAA;YACEmE,KAAK,EAAE;cACLoD,QAAQ,EAAE,KAAK;cACfnB,OAAO,EAAE,WAAW;cACpB1B,YAAY,EAAE2C,GAAG,CAAC1G,IAAI,KAAK,MAAM,GAAG,oBAAoB,GAAG,oBAAoB;cAC/E8D,UAAU,EAAE4C,GAAG,CAAC1G,IAAI,KAAK,MAAM,GAC3B,mDAAmD,GACnD,SAAS;cACb4E,KAAK,EAAE8B,GAAG,CAAC1G,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,SAAS;cAChD6E,QAAQ,EAAE,MAAM;cAChBgC,UAAU,EAAE,KAAK;cACjBzC,SAAS,EAAEsC,GAAG,CAAC1G,IAAI,KAAK,MAAM,GAC1B,qCAAqC,GACrC,8BAA8B;cAClC8G,QAAQ,EAAE;YACZ,CAAE;YAAAnC,QAAA,EAGD,OAAO+B,GAAG,CAACzG,OAAO,KAAK,QAAQ,gBAC9BZ,OAAA;cAAKmE,KAAK,EAAE;gBAAEuD,UAAU,EAAE;cAAW,CAAE;cAAApC,QAAA,EAAE+B,GAAG,CAACzG;YAAO;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,GACzD+B,KAAK,CAACC,OAAO,CAACP,GAAG,CAACzG,OAAO,CAAC,GAC5ByG,GAAG,CAACzG,OAAO,CAACwG,GAAG,CAAC,CAACS,IAAI,EAAEC,GAAG,kBACxB9H,OAAA;cAAAsF,QAAA,GACGuC,IAAI,CAAC9F,IAAI,KAAK,MAAM,iBACnB/B,OAAA;gBAAKmE,KAAK,EAAE;kBAAEuD,UAAU,EAAE,UAAU;kBAAEK,YAAY,EAAEF,IAAI,CAACpE,IAAI,GAAG,KAAK,GAAG;gBAAI,CAAE;gBAAA6B,QAAA,EAC3EuC,IAAI,CAACpE;cAAI;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACN,EACAiC,IAAI,CAAC9F,IAAI,KAAK,WAAW,iBACxB/B,OAAA;gBACEgI,GAAG,EAAEH,IAAI,CAACnE,SAAS,CAACJ,GAAI;gBACxB2E,GAAG,EAAC,aAAa;gBACjB9D,KAAK,EAAE;kBACLoD,QAAQ,EAAE,MAAM;kBAChB/C,MAAM,EAAE,MAAM;kBACdE,YAAY,EAAE,MAAM;kBACpBwD,SAAS,EAAE,OAAO;kBAClBC,SAAS,EAAE,OAAO;kBAClBrC,MAAM,EAAE,oCAAoC;kBAC5CsC,SAAS,EAAE;gBACb;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA,GApBOkC,GAAG;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBR,CACN,CAAC,gBAEF5F,OAAA;cAAAsF,QAAA,EAAK;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACjC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GAtDD0B,KAAK;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuDP,CACN,CAAC,EAED7E,QAAQ,iBACPf,OAAA;UAAKmE,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE;UAAa,CAAE;UAAAS,QAAA,eAC5DtF,OAAA;YACEmE,KAAK,EAAE;cACLiC,OAAO,EAAE,WAAW;cACpB1B,YAAY,EAAE,oBAAoB;cAClCD,UAAU,EAAE,SAAS;cACrBM,SAAS,EAAE;YACb,CAAE;YAAAO,QAAA,eAEFtF,OAAA;cAAKmE,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE0B,GAAG,EAAE,KAAK;gBAAEzB,UAAU,EAAE;cAAS,CAAE;cAAAU,QAAA,gBAChEtF,OAAA;gBACEmE,KAAK,EAAE;kBACLI,KAAK,EAAE,KAAK;kBACZC,MAAM,EAAE,KAAK;kBACbE,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBS,SAAS,EAAE;gBACb;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5F,OAAA;gBACEmE,KAAK,EAAE;kBACLI,KAAK,EAAE,KAAK;kBACZC,MAAM,EAAE,KAAK;kBACbE,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBS,SAAS,EAAE;gBACb;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5F,OAAA;gBACEmE,KAAK,EAAE;kBACLI,KAAK,EAAE,KAAK;kBACZC,MAAM,EAAE,KAAK;kBACbE,YAAY,EAAE,KAAK;kBACnBD,UAAU,EAAE,SAAS;kBACrBS,SAAS,EAAE;gBACb;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED5F,OAAA;UAAKqI,GAAG,EAAEhH;QAAe;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGN5F,OAAA;QACEmE,KAAK,EAAE;UACLC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE,CAAC;UACTI,UAAU,EAAE,2BAA2B;UACvCoB,cAAc,EAAE,YAAY;UAC5ByC,SAAS,EAAE,+BAA+B;UAC1ClC,OAAO,EAAE,gBAAgB;UACzBpB,MAAM,EAAE;QACV,CAAE;QAAAM,QAAA,GAGDnE,YAAY,iBACXnB,OAAA;UACEmE,KAAK,EAAE;YACL4D,YAAY,EAAE,MAAM;YACpB3B,OAAO,EAAE,MAAM;YACf3B,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,MAAM;YACpBoB,MAAM,EAAE;UACV,CAAE;UAAAR,QAAA,eAEFtF,OAAA;YAAKmE,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEyB,GAAG,EAAE;YAAO,CAAE;YAAAf,QAAA,gBACjEtF,OAAA;cAAKmE,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAkB,QAAA,gBACnCtF,OAAA;gBACEgI,GAAG,EAAE7G,YAAa;gBAClB8G,GAAG,EAAC,SAAS;gBACb9D,KAAK,EAAE;kBACLI,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACd2D,SAAS,EAAE,OAAO;kBAClBzD,YAAY,EAAE,KAAK;kBACnBoB,MAAM,EAAE;gBACV;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF5F,OAAA;gBACEkE,OAAO,EAAE3B,WAAY;gBACrB4B,KAAK,EAAE;kBACLC,QAAQ,EAAE,UAAU;kBACpBmE,GAAG,EAAE,MAAM;kBACXjE,KAAK,EAAE,MAAM;kBACbC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdC,UAAU,EAAE,SAAS;kBACrBc,KAAK,EAAE,OAAO;kBACdb,YAAY,EAAE,KAAK;kBACnBoB,MAAM,EAAE,MAAM;kBACdhB,MAAM,EAAE,SAAS;kBACjBH,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBW,QAAQ,EAAE,MAAM;kBAChBe,UAAU,EAAE;gBACd,CAAE;gBAAAjB,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN5F,OAAA;cAAKmE,KAAK,EAAE;gBAAEqC,IAAI,EAAE;cAAE,CAAE;cAAAlB,QAAA,gBACtBtF,OAAA;gBAAGmE,KAAK,EAAE;kBAAEqB,QAAQ,EAAE,MAAM;kBAAEe,UAAU,EAAE,KAAK;kBAAEhB,KAAK,EAAE,SAAS;kBAAEe,MAAM,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,EAAC;cAEhF;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ5F,OAAA;gBAAGmE,KAAK,EAAE;kBAAEqB,QAAQ,EAAE,MAAM;kBAAED,KAAK,EAAE,SAAS;kBAAEe,MAAM,EAAE;gBAAE,CAAE;gBAAAhB,QAAA,EACzDrE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+B;cAAI;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD5F,OAAA;UACEmE,KAAK,EAAE;YACLQ,OAAO,EAAE,MAAM;YACf0B,GAAG,EAAE,KAAK;YACV5B,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,MAAM;YACpB0B,OAAO,EAAE,KAAK;YACdN,MAAM,EAAE,mBAAmB;YAC3Bb,UAAU,EAAE;UACd,CAAE;UACFuD,OAAO,EAAGpG,CAAC,IAAKA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACsE,WAAW,GAAG,SAAU;UACvDC,MAAM,EAAGtG,CAAC,IAAKA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACsE,WAAW,GAAG,SAAU;UAAAnD,QAAA,gBAGtDtF,OAAA;YACEkE,OAAO,EAAEA,CAAA;cAAA,IAAAyE,qBAAA;cAAA,QAAAA,qBAAA,GAAMrH,YAAY,CAACC,OAAO,cAAAoH,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CzE,KAAK,EAAE;cACLM,UAAU,EAAE,mDAAmD;cAC/DqB,MAAM,EAAE,MAAM;cACdpB,YAAY,EAAE,MAAM;cACpBH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBG,UAAU,EAAE,eAAe;cAC3BF,SAAS,EAAE;YACb,CAAE;YACFI,YAAY,EAAG/C,CAAC,IAAK;cACnBA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,aAAa;cACxChD,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACY,SAAS,GAAG,qCAAqC;YAClE,CAAE;YACFM,YAAY,EAAGjD,CAAC,IAAK;cACnBA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,UAAU;cACrChD,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACY,SAAS,GAAG,oCAAoC;YACjE,CAAE;YACF8D,KAAK,EAAC,cAAc;YAAAvD,QAAA,eAEpBtF,OAAA;cAAMmE,KAAK,EAAE;gBAAEoB,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE,MAAM;gBAAEe,UAAU,EAAE;cAAO,CAAE;cAAAjB,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eAGT5F,OAAA;YACE+B,IAAI,EAAC,MAAM;YACXS,KAAK,EAAE3B,KAAM;YACbiI,QAAQ,EAAG1G,CAAC,IAAKtB,QAAQ,CAACsB,CAAC,CAACP,MAAM,CAACW,KAAK,CAAE;YAC1CuG,SAAS,EAAEjF,aAAc;YACzBkF,WAAW,EAAC,oBAAoB;YAChC7E,KAAK,EAAE;cACLqC,IAAI,EAAE,CAAC;cACPV,MAAM,EAAE,MAAM;cACdrB,UAAU,EAAE,aAAa;cACzBwE,OAAO,EAAE,MAAM;cACfzD,QAAQ,EAAE,MAAM;cAChBD,KAAK,EAAE,SAAS;cAChBa,OAAO,EAAE,WAAW;cACpB8C,UAAU,EAAE;YACd;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGF5F,OAAA;YACEkE,OAAO,EAAEzB,WAAY;YACrB0G,QAAQ,EAAE,CAACtI,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAACzB,aAAc;YAC1CkD,KAAK,EAAE;cACLM,UAAU,EAAG5D,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GACtC,mDAAmD,GACnD,SAAS;cACb6E,MAAM,EAAE,MAAM;cACdpB,YAAY,EAAE,MAAM;cACpBH,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAGjE,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GAAI,SAAS,GAAG,aAAa;cACnEgE,UAAU,EAAE,eAAe;cAC3BF,SAAS,EAAGlE,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GACrC,oCAAoC,GACpC;YACN,CAAE;YACFkE,YAAY,EAAG/C,CAAC,IAAK;cACnB,IAAIvB,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,EAAE;gBACjCmB,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,aAAa;gBACxChD,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACY,SAAS,GAAG,qCAAqC;cAClE;YACF,CAAE;YACFM,YAAY,EAAGjD,CAAC,IAAK;cACnBA,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACiB,SAAS,GAAG,UAAU;cACrChD,CAAC,CAACP,MAAM,CAACsC,KAAK,CAACY,SAAS,GAAIlE,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GACrD,oCAAoC,GACpC,MAAM;YACZ,CAAE;YAAAqE,QAAA,eAEFtF,OAAA;cAAMmE,KAAK,EAAE;gBACXoB,KAAK,EAAG1E,KAAK,CAAC6B,IAAI,CAAC,CAAC,IAAIzB,aAAa,GAAI,OAAO,GAAG,SAAS;gBAC5DuE,QAAQ,EAAE,MAAM;gBAChBe,UAAU,EAAE;cACd,CAAE;cAAAjB,QAAA,EAAC;YAEH;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5F,OAAA;UACEqI,GAAG,EAAE/G,YAAa;UAClBS,IAAI,EAAC,MAAM;UACXqH,MAAM,EAAC,SAAS;UAChBN,QAAQ,EAAEpH,iBAAkB;UAC5ByC,KAAK,EAAE;YAAEQ,OAAO,EAAE;UAAO;QAAE;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF5F,OAAA;UAAGmE,KAAK,EAAE;YACRqB,QAAQ,EAAE,MAAM;YAChBD,KAAK,EAAE,SAAS;YAChB8D,SAAS,EAAE,QAAQ;YACnB/C,MAAM,EAAE;UACV,CAAE;UAAAhB,QAAA,EAAC;QAEH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA,eACN,CACH,eAED5F,OAAA;MAAAsF,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACxF,EAAA,CArpBID,mBAAmB;AAAAmJ,EAAA,GAAnBnJ,mBAAmB;AAupBzB,eAAeA,mBAAmB;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}