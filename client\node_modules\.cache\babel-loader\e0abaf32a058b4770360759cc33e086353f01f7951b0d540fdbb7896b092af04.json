{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbSend, TbPaperclip, TbX, TbRobot, Tb<PERSON>ser, TbPhoto, TbLoader } from \"react-icons/tb\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\nimport ContentRenderer from \"../../../components/ContentRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ChatGPTIntegration() {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [prompt, setPrompt] = useState(\"\");\n  const [imageFile, setImageFile] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const textareaRef = useRef(null);\n\n  // Initialize chat with welcome message\n  React.useEffect(() => {\n    // Load cached messages\n    const cachedMessages = localStorage.getItem('chat_messages');\n    if (cachedMessages) {\n      setMessages(JSON.parse(cachedMessages));\n    } else {\n      // Set welcome message\n      setMessages([{\n        role: \"assistant\",\n        content: \"Hello! I'm your AI study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\n      }]);\n    }\n    setIsInitialized(true);\n  }, []);\n\n  // Save messages to cache\n  React.useEffect(() => {\n    if (isInitialized && messages.length > 0) {\n      localStorage.setItem('chat_messages', JSON.stringify(messages));\n    }\n  }, [messages, isInitialized]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [messages]);\n\n  // Handle image file selection\n  const handleImageSelect = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setImageFile(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setImageFile(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle Enter key press - Enter for new line, send only via button\n  const handleKeyPress = e => {\n    // Enter always creates new line, no auto-send\n    if (e.key === 'Enter') {\n      // Allow default behavior (new line)\n      return;\n    }\n  };\n  const handleChat = async () => {\n    if (!prompt.trim() && !imageFile) return;\n    setIsLoading(true);\n    setIsTyping(true);\n    try {\n      let imageUrl = null;\n\n      // Step 1: Upload the image to the server (if an image is selected)\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const data = await uploadImg(formData);\n        if (data !== null && data !== void 0 && data.success) {\n          imageUrl = data.url; // Extract the S3 URL\n          console.log(\"Image URL: \", imageUrl);\n        } else {\n          throw new Error(\"Image upload failed\");\n        }\n      }\n\n      // Step 2: Construct the ChatGPT message payload\n      const userMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: prompt\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: prompt\n      };\n      const updatedMessages = [...messages, userMessage];\n      setMessages(updatedMessages);\n      setPrompt(\"\");\n      removeImage();\n\n      // Step 3: Send the payload to ChatGPT\n      const chatPayload = {\n        messages: updatedMessages\n      };\n      const chatRes = await chatWithChatGPT(chatPayload);\n      const apiResponse = chatRes === null || chatRes === void 0 ? void 0 : chatRes.data;\n      console.log(\"API Response: \", apiResponse);\n\n      // Step 4: Append the assistant's response to the conversation\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: apiResponse\n      }]);\n    } catch (error) {\n      console.error(\"Error during chat:\", error);\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: \"Sorry, I encountered an error. Please try again.\"\n      }]);\n    } finally {\n      setIsLoading(false);\n      setIsTyping(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(TbRobot, {\n            className: \"w-6 h-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg sm:text-xl font-bold text-gray-800\",\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs sm:text-sm text-gray-600\",\n            children: \"Ask questions, upload images, get instant help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-hidden relative\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full overflow-y-auto space-y-3 sm:space-y-6 pb-4 scroll-smooth\",\n          style: {\n            scrollbarWidth: 'thin',\n            scrollbarColor: '#cbd5e1 #f1f5f9'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: messages.map((msg, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: `flex gap-2 sm:gap-4 ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n              children: [msg.role === \"assistant\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                  className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `max-w-[85%] sm:max-w-[80%] ${msg.role === \"user\" ? \"order-1\" : \"\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-xl sm:rounded-2xl p-3 sm:p-4 ${msg.role === \"user\" ? \"bg-blue-500 text-white ml-auto\" : \"bg-white border border-gray-200 shadow-sm\"}`,\n                  children: msg.role === \"assistant\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-gray-800\",\n                    children: msg !== null && msg !== void 0 && msg.content ? /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                      text: msg.content\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-red-500\",\n                      children: \"Unable to get a response from AI\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: typeof msg.content === \"string\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"whitespace-pre-wrap\",\n                      children: msg.content\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 29\n                    }, this) : msg.content.map((item, idx) => item.type === \"text\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"whitespace-pre-wrap mb-2\",\n                      children: item.text\n                    }, idx, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 33\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: item.image_url.url,\n                        alt: \"User uploaded content\",\n                        className: \"rounded-lg max-w-full h-auto shadow-md\",\n                        style: {\n                          maxHeight: window.innerWidth <= 768 ? \"200px\" : \"300px\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 212,\n                        columnNumber: 35\n                      }, this)\n                    }, idx, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 33\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), msg.role === \"user\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(TbUser, {\n                  className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              className: \"flex gap-2 sm:gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                  className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white border border-gray-200 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      scale: [1, 1.2, 1]\n                    },\n                    transition: {\n                      duration: 0.6,\n                      repeat: Infinity,\n                      delay: 0\n                    },\n                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      scale: [1, 1.2, 1]\n                    },\n                    transition: {\n                      duration: 0.6,\n                      repeat: Infinity,\n                      delay: 0.2\n                    },\n                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      scale: [1, 1.2, 1]\n                    },\n                    transition: {\n                      duration: 0.6,\n                      repeat: Infinity,\n                      delay: 0.4\n                    },\n                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sticky bottom-0 bg-white/95 backdrop-blur-lg border-t border-gray-200 p-2 sm:p-4 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: imagePreview && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              height: 0\n            },\n            animate: {\n              opacity: 1,\n              height: \"auto\"\n            },\n            exit: {\n              opacity: 0,\n              height: 0\n            },\n            className: \"mb-3 sm:mb-4 p-2 sm:p-3 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 sm:gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: imagePreview,\n                  alt: \"Preview\",\n                  className: \"w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg border border-gray-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: removeImage,\n                  className: \"absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(TbX, {\n                    className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs sm:text-sm font-medium text-gray-700\",\n                  children: \"Image attached\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 truncate\",\n                  children: imageFile === null || imageFile === void 0 ? void 0 : imageFile.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end gap-2 sm:gap-3 bg-white rounded-xl sm:rounded-2xl border border-gray-300 p-2 sm:p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _fileInputRef$current;\n                return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n              },\n              className: \"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors shadow-sm\",\n              title: \"Attach image\",\n              children: /*#__PURE__*/_jsxDEV(TbPaperclip, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              ref: textareaRef,\n              value: prompt,\n              onChange: e => setPrompt(e.target.value),\n              onKeyPress: handleKeyPress,\n              placeholder: window.innerWidth <= 768 ? \"Ask me anything...\" : \"Ask me anything... (Enter for new line)\",\n              className: \"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 text-sm sm:text-base\",\n              rows: 1,\n              style: {\n                height: 'auto',\n                minHeight: window.innerWidth <= 768 ? '20px' : '24px',\n                maxHeight: window.innerWidth <= 768 ? '80px' : '128px'\n              },\n              onInput: e => {\n                e.target.style.height = 'auto';\n                const maxHeight = window.innerWidth <= 768 ? 80 : 128;\n                e.target.style.height = Math.min(e.target.scrollHeight, maxHeight) + 'px';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: handleChat,\n              disabled: isLoading || !prompt.trim() && !imageFile,\n              className: `p-2 rounded-lg transition-all ${isLoading || !prompt.trim() && !imageFile ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"}`,\n              children: isLoading ? /*#__PURE__*/_jsxDEV(TbLoader, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(TbSend, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            ref: fileInputRef,\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: handleImageSelect,\n            className: \"hidden\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 mt-2 text-center px-2\",\n          children: window.innerWidth <= 768 ? \"Tap to send • Upload images for analysis\" : \"Press Enter to send • Shift+Enter for new line • Upload images for analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatGPTIntegration, \"VSMuF9EUH1ObLJ2Bkv+9tWC7cOk=\");\n_c = ChatGPTIntegration;\nexport default ChatGPTIntegration;\nvar _c;\n$RefreshReg$(_c, \"ChatGPTIntegration\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "TbSend", "TbPaperclip", "TbX", "TbRobot", "TbUser", "TbPhoto", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatWithChatGPT", "uploadImg", "Content<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ChatGPTIntegration", "_s", "messages", "setMessages", "prompt", "setPrompt", "imageFile", "setImageFile", "imagePreview", "setImagePreview", "isLoading", "setIsLoading", "isInitialized", "setIsInitialized", "isTyping", "setIsTyping", "messagesEndRef", "fileInputRef", "textareaRef", "cachedMessages", "localStorage", "getItem", "JSON", "parse", "role", "content", "length", "setItem", "stringify", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleImageSelect", "e", "file", "target", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "removeImage", "value", "handleKeyPress", "key", "handleChat", "trim", "imageUrl", "formData", "FormData", "append", "data", "success", "url", "console", "log", "Error", "userMessage", "type", "text", "image_url", "updatedMessages", "chatPayload", "chatRes", "apiResponse", "prev", "error", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "scrollbarWidth", "scrollbarColor", "map", "msg", "index", "item", "idx", "src", "alt", "maxHeight", "window", "innerWidth", "exit", "scale", "transition", "duration", "repeat", "Infinity", "delay", "ref", "height", "onClick", "name", "_fileInputRef$current", "click", "title", "onChange", "onKeyPress", "placeholder", "rows", "minHeight", "onInput", "Math", "min", "scrollHeight", "button", "whileHover", "whileTap", "disabled", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { TbSend, TbPaperclip, TbX, Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON>b<PERSON>oa<PERSON> } from \"react-icons/tb\";\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nfunction ChatGPTIntegration() {\r\n  const [messages, setMessages] = useState([]);\r\n  const [prompt, setPrompt] = useState(\"\");\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [isTyping, setIsTyping] = useState(false);\r\n\r\n  const messagesEndRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const textareaRef = useRef(null);\r\n\r\n  // Initialize chat with welcome message\r\n  React.useEffect(() => {\r\n    // Load cached messages\r\n    const cachedMessages = localStorage.getItem('chat_messages');\r\n    if (cachedMessages) {\r\n      setMessages(JSON.parse(cachedMessages));\r\n    } else {\r\n      // Set welcome message\r\n      setMessages([{\r\n        role: \"assistant\",\r\n        content: \"Hello! I'm your AI study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\r\n      }]);\r\n    }\r\n    setIsInitialized(true);\r\n  }, []);\r\n\r\n  // Save messages to cache\r\n  React.useEffect(() => {\r\n    if (isInitialized && messages.length > 0) {\r\n      localStorage.setItem('chat_messages', JSON.stringify(messages));\r\n    }\r\n  }, [messages, isInitialized]);\r\n\r\n  // Auto-scroll to bottom when new messages arrive\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [messages]);\r\n\r\n  // Handle image file selection\r\n  const handleImageSelect = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setImageFile(file);\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImagePreview(e.target.result);\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  // Remove selected image\r\n  const removeImage = () => {\r\n    setImageFile(null);\r\n    setImagePreview(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Handle Enter key press - Enter for new line, send only via button\r\n  const handleKeyPress = (e) => {\r\n    // Enter always creates new line, no auto-send\r\n    if (e.key === 'Enter') {\r\n      // Allow default behavior (new line)\r\n      return;\r\n    }\r\n  };\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n    setIsTyping(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: prompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: prompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      setMessages(updatedMessages);\r\n      setPrompt(\"\");\r\n      removeImage();\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: apiResponse },\r\n      ]);\r\n\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: \"Sorry, I encountered an error. Please try again.\" },\r\n      ]);\r\n    } finally {\r\n      setIsLoading(false);\r\n      setIsTyping(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col overflow-hidden\">\r\n      {/* Header */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 shadow-sm\"\r\n      >\r\n        <div className=\"max-w-4xl mx-auto flex items-center gap-3\">\r\n          <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n            <TbRobot className=\"w-6 h-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h1 className=\"text-lg sm:text-xl font-bold text-gray-800\">Brainwave AI</h1>\r\n            <p className=\"text-xs sm:text-sm text-gray-600\">Ask questions, upload images, get instant help</p>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Messages Container - Professional Scrolling */}\r\n      <div className=\"flex-1 overflow-hidden relative\">\r\n        <div className=\"h-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4\">\r\n          <div\r\n            className=\"h-full overflow-y-auto space-y-3 sm:space-y-6 pb-4 scroll-smooth\"\r\n            style={{\r\n              scrollbarWidth: 'thin',\r\n              scrollbarColor: '#cbd5e1 #f1f5f9'\r\n            }}\r\n          >\r\n            <AnimatePresence>\r\n              {messages.map((msg, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  className={`flex gap-2 sm:gap-4 ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`}\r\n                >\r\n                  {msg.role === \"assistant\" && (\r\n                    <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                      <TbRobot className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className={`max-w-[85%] sm:max-w-[80%] ${msg.role === \"user\" ? \"order-1\" : \"\"}`}>\r\n                    <div\r\n                      className={`rounded-xl sm:rounded-2xl p-3 sm:p-4 ${\r\n                        msg.role === \"user\"\r\n                          ? \"bg-blue-500 text-white ml-auto\"\r\n                          : \"bg-white border border-gray-200 shadow-sm\"\r\n                      }`}\r\n                    >\r\n                      {msg.role === \"assistant\" ? (\r\n                        <div className=\"text-gray-800\">\r\n                          {msg?.content ? (\r\n                            <ContentRenderer text={msg.content} />\r\n                          ) : (\r\n                            <p className=\"text-red-500\">Unable to get a response from AI</p>\r\n                          )}\r\n                        </div>\r\n                      ) : (\r\n                        <div>\r\n                          {typeof msg.content === \"string\" ? (\r\n                            <p className=\"whitespace-pre-wrap\">{msg.content}</p>\r\n                          ) : (\r\n                            msg.content.map((item, idx) =>\r\n                              item.type === \"text\" ? (\r\n                                <p key={idx} className=\"whitespace-pre-wrap mb-2\">{item.text}</p>\r\n                              ) : (\r\n                                <div key={idx} className=\"mt-2\">\r\n                                  <img\r\n                                    src={item.image_url.url}\r\n                                    alt=\"User uploaded content\"\r\n                                    className=\"rounded-lg max-w-full h-auto shadow-md\"\r\n                                    style={{ maxHeight: window.innerWidth <= 768 ? \"200px\" : \"300px\" }}\r\n                                  />\r\n                                </div>\r\n                              )\r\n                            )\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {msg.role === \"user\" && (\r\n                    <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                      <TbUser className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                    </div>\r\n                  )}\r\n                </motion.div>\r\n              ))}\r\n            </AnimatePresence>\r\n\r\n            {/* Typing Indicator */}\r\n            <AnimatePresence>\r\n              {isTyping && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  exit={{ opacity: 0, y: -20 }}\r\n                  className=\"flex gap-2 sm:gap-4\"\r\n                >\r\n                  <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n                    <TbRobot className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                  </div>\r\n                  <div className=\"bg-white border border-gray-200 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-sm\">\r\n                    <div className=\"flex gap-1\">\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </AnimatePresence>\r\n\r\n            <div ref={messagesEndRef} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Input Section - Pinned at Bottom */}\r\n      <div className=\"sticky bottom-0 bg-white/95 backdrop-blur-lg border-t border-gray-200 p-2 sm:p-4 shadow-lg\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Image Preview */}\r\n          <AnimatePresence>\r\n            {imagePreview && (\r\n              <motion.div\r\n                initial={{ opacity: 0, height: 0 }}\r\n                animate={{ opacity: 1, height: \"auto\" }}\r\n                exit={{ opacity: 0, height: 0 }}\r\n                className=\"mb-3 sm:mb-4 p-2 sm:p-3 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\"\r\n              >\r\n                <div className=\"flex items-center gap-2 sm:gap-3\">\r\n                  <div className=\"relative\">\r\n                    <img\r\n                      src={imagePreview}\r\n                      alt=\"Preview\"\r\n                      className=\"w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg border border-gray-300\"\r\n                    />\r\n                    <button\r\n                      onClick={removeImage}\r\n                      className=\"absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\"\r\n                    >\r\n                      <TbX className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-xs sm:text-sm font-medium text-gray-700\">Image attached</p>\r\n                    <p className=\"text-xs text-gray-500 truncate\">{imageFile?.name}</p>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n\r\n          {/* Input Area - Responsive */}\r\n          <div className=\"relative\">\r\n            <div className=\"flex items-end gap-2 sm:gap-3 bg-white rounded-xl sm:rounded-2xl border border-gray-300 p-2 sm:p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\">\r\n              {/* Attachment Button - Blue and White with Visible Pins */}\r\n              <button\r\n                onClick={() => fileInputRef.current?.click()}\r\n                className=\"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors shadow-sm\"\r\n                title=\"Attach image\"\r\n              >\r\n                <TbPaperclip className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n              </button>\r\n\r\n              {/* Text Input - Responsive */}\r\n              <textarea\r\n                ref={textareaRef}\r\n                value={prompt}\r\n                onChange={(e) => setPrompt(e.target.value)}\r\n                onKeyPress={handleKeyPress}\r\n                placeholder={window.innerWidth <= 768 ? \"Ask me anything...\" : \"Ask me anything... (Enter for new line)\"}\r\n                className=\"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 text-sm sm:text-base\"\r\n                rows={1}\r\n                style={{\r\n                  height: 'auto',\r\n                  minHeight: window.innerWidth <= 768 ? '20px' : '24px',\r\n                  maxHeight: window.innerWidth <= 768 ? '80px' : '128px'\r\n                }}\r\n                onInput={(e) => {\r\n                  e.target.style.height = 'auto';\r\n                  const maxHeight = window.innerWidth <= 768 ? 80 : 128;\r\n                  e.target.style.height = Math.min(e.target.scrollHeight, maxHeight) + 'px';\r\n                }}\r\n              />\r\n\r\n              {/* Send Button - Responsive */}\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={handleChat}\r\n                disabled={isLoading || (!prompt.trim() && !imageFile)}\r\n                className={`p-2 rounded-lg transition-all ${\r\n                  isLoading || (!prompt.trim() && !imageFile)\r\n                    ? \"bg-gray-200 text-gray-400 cursor-not-allowed\"\r\n                    : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"\r\n                }`}\r\n              >\r\n                {isLoading ? (\r\n                  <TbLoader className=\"w-4 h-4 sm:w-5 sm:h-5 animate-spin\" />\r\n                ) : (\r\n                  <TbSend className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                )}\r\n              </motion.button>\r\n            </div>\r\n\r\n            {/* Hidden File Input */}\r\n            <input\r\n              ref={fileInputRef}\r\n              type=\"file\"\r\n              accept=\"image/*\"\r\n              onChange={handleImageSelect}\r\n              className=\"hidden\"\r\n            />\r\n          </div>\r\n\r\n          {/* Helper Text - Responsive */}\r\n          <p className=\"text-xs text-gray-500 mt-2 text-center px-2\">\r\n            {window.innerWidth <= 768\r\n              ? \"Tap to send • Upload images for analysis\"\r\n              : \"Press Enter to send • Shift+Enter for new line • Upload images for analysis\"\r\n            }\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,MAAM,EAAEC,WAAW,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC7F,SAASC,eAAe,EAAEC,SAAS,QAAQ,wBAAwB;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMiC,cAAc,GAAGhC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMiC,YAAY,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkC,WAAW,GAAGlC,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACAF,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB;IACA,MAAMkC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC5D,IAAIF,cAAc,EAAE;MAClBhB,WAAW,CAACmB,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC,CAAC;IACzC,CAAC,MAAM;MACL;MACAhB,WAAW,CAAC,CAAC;QACXqB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL;IACAZ,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/B,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,IAAI2B,aAAa,IAAIV,QAAQ,CAACwB,MAAM,GAAG,CAAC,EAAE;MACxCN,YAAY,CAACO,OAAO,CAAC,eAAe,EAAEL,IAAI,CAACM,SAAS,CAAC1B,QAAQ,CAAC,CAAC;IACjE;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEU,aAAa,CAAC,CAAC;;EAE7B;EACA3B,SAAS,CAAC,MAAM;IAAA,IAAA4C,qBAAA;IACd,CAAAA,qBAAA,GAAAb,cAAc,CAACc,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAAC9B,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM+B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR5B,YAAY,CAAC4B,IAAI,CAAC;MAClB,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIN,CAAC,IAAKzB,eAAe,CAACyB,CAAC,CAACE,MAAM,CAACK,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxBpC,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIQ,YAAY,CAACa,OAAO,EAAE;MACxBb,YAAY,CAACa,OAAO,CAACc,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIX,CAAC,IAAK;IAC5B;IACA,IAAIA,CAAC,CAACY,GAAG,KAAK,OAAO,EAAE;MACrB;MACA;IACF;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC3C,MAAM,CAAC4C,IAAI,CAAC,CAAC,IAAI,CAAC1C,SAAS,EAAE;IAElCK,YAAY,CAAC,IAAI,CAAC;IAClBI,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,IAAIkC,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAI3C,SAAS,EAAE;QACb,MAAM4C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE9C,SAAS,CAAC;QAEnC,MAAM+C,IAAI,GAAG,MAAMzD,SAAS,CAACsD,QAAQ,CAAC;QAEtC,IAAIG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,OAAO,EAAE;UACjBL,QAAQ,GAAGI,IAAI,CAACE,GAAG,CAAC,CAAC;UACrBC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAER,QAAQ,CAAC;QACtC,CAAC,MAAM;UACL,MAAM,IAAIS,KAAK,CAAC,qBAAqB,CAAC;QACxC;MACF;;MAEA;MACA,MAAMC,WAAW,GAAGV,QAAQ,GACxB;QACAzB,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEmC,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAEzD;QAAO,CAAC,EAC9B;UAAEwD,IAAI,EAAE,WAAW;UAAEE,SAAS,EAAE;YAAEP,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACC;QAAEzB,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAErB;MAAO,CAAC;MAErC,MAAM2D,eAAe,GAAG,CAAC,GAAG7D,QAAQ,EAAEyD,WAAW,CAAC;MAClDxD,WAAW,CAAC4D,eAAe,CAAC;MAC5B1D,SAAS,CAAC,EAAE,CAAC;MACbsC,WAAW,CAAC,CAAC;;MAEb;MACA,MAAMqB,WAAW,GAAG;QAAE9D,QAAQ,EAAE6D;MAAgB,CAAC;MAEjD,MAAME,OAAO,GAAG,MAAMtE,eAAe,CAACqE,WAAW,CAAC;MAElD,MAAME,WAAW,GAAGD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEZ,IAAI;MACjCG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAES,WAAW,CAAC;;MAE1C;MACA/D,WAAW,CAAEgE,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAE3C,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAEyC;MAAY,CAAC,CAC5C,CAAC;IAEJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CjE,WAAW,CAAEgE,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAE3C,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAmD,CAAC,CACnF,CAAC;IACJ,CAAC,SAAS;MACRd,YAAY,CAAC,KAAK,CAAC;MACnBI,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKsE,SAAS,EAAC,mFAAmF;IAAAC,QAAA,gBAEhGvE,OAAA,CAACb,MAAM,CAACqF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAE/EvE,OAAA;QAAKsE,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDvE,OAAA;UAAKsE,SAAS,EAAC,sGAAsG;UAAAC,QAAA,eACnHvE,OAAA,CAACR,OAAO;YAAC8E,SAAS,EAAC;UAAoB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNhF,OAAA;UAAAuE,QAAA,gBACEvE,OAAA;YAAIsE,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EhF,OAAA;YAAGsE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAA8C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbhF,OAAA;MAAKsE,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eAC9CvE,OAAA;QAAKsE,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eACjEvE,OAAA;UACEsE,SAAS,EAAC,kEAAkE;UAC5EW,KAAK,EAAE;YACLC,cAAc,EAAE,MAAM;YACtBC,cAAc,EAAE;UAClB,CAAE;UAAAZ,QAAA,gBAEFvE,OAAA,CAACZ,eAAe;YAAAmF,QAAA,EACbpE,QAAQ,CAACiF,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBtF,OAAA,CAACb,MAAM,CAACqF,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BL,SAAS,EAAG,uBAAsBe,GAAG,CAAC5D,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAgB,EAAE;cAAA8C,QAAA,GAEzFc,GAAG,CAAC5D,IAAI,KAAK,WAAW,iBACvBzB,OAAA;gBAAKsE,SAAS,EAAC,gIAAgI;gBAAAC,QAAA,eAC7IvE,OAAA,CAACR,OAAO;kBAAC8E,SAAS,EAAC;gBAAkC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CACN,eAEDhF,OAAA;gBAAKsE,SAAS,EAAG,8BAA6Be,GAAG,CAAC5D,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,EAAG,EAAE;gBAAA8C,QAAA,eACnFvE,OAAA;kBACEsE,SAAS,EAAG,wCACVe,GAAG,CAAC5D,IAAI,KAAK,MAAM,GACf,gCAAgC,GAChC,2CACL,EAAE;kBAAA8C,QAAA,EAEFc,GAAG,CAAC5D,IAAI,KAAK,WAAW,gBACvBzB,OAAA;oBAAKsE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC3Bc,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAE3D,OAAO,gBACX1B,OAAA,CAACF,eAAe;sBAACgE,IAAI,EAAEuB,GAAG,CAAC3D;oBAAQ;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEtChF,OAAA;sBAAGsE,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAgC;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAChE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAENhF,OAAA;oBAAAuE,QAAA,EACG,OAAOc,GAAG,CAAC3D,OAAO,KAAK,QAAQ,gBAC9B1B,OAAA;sBAAGsE,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAEc,GAAG,CAAC3D;oBAAO;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,GAEpDK,GAAG,CAAC3D,OAAO,CAAC0D,GAAG,CAAC,CAACG,IAAI,EAAEC,GAAG,KACxBD,IAAI,CAAC1B,IAAI,KAAK,MAAM,gBAClB7D,OAAA;sBAAasE,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAEgB,IAAI,CAACzB;oBAAI,GAApD0B,GAAG;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAqD,CAAC,gBAEjEhF,OAAA;sBAAesE,SAAS,EAAC,MAAM;sBAAAC,QAAA,eAC7BvE,OAAA;wBACEyF,GAAG,EAAEF,IAAI,CAACxB,SAAS,CAACP,GAAI;wBACxBkC,GAAG,EAAC,uBAAuB;wBAC3BpB,SAAS,EAAC,wCAAwC;wBAClDW,KAAK,EAAE;0BAAEU,SAAS,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;wBAAQ;sBAAE;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE;oBAAC,GANMQ,GAAG;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAOR,CAET;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELK,GAAG,CAAC5D,IAAI,KAAK,MAAM,iBAClBzB,OAAA;gBAAKsE,SAAS,EAAC,+HAA+H;gBAAAC,QAAA,eAC5IvE,OAAA,CAACP,MAAM;kBAAC6E,SAAS,EAAC;gBAAkC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CACN;YAAA,GAxDIM,KAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyDA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa,CAAC,eAGlBhF,OAAA,CAACZ,eAAe;YAAAmF,QAAA,EACbxD,QAAQ,iBACPf,OAAA,CAACb,MAAM,CAACqF,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BmB,IAAI,EAAE;gBAAEpB,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BL,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAE/BvE,OAAA;gBAAKsE,SAAS,EAAC,kHAAkH;gBAAAC,QAAA,eAC/HvE,OAAA,CAACR,OAAO;kBAAC8E,SAAS,EAAC;gBAAkC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNhF,OAAA;gBAAKsE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,eAC7FvE,OAAA;kBAAKsE,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBvE,OAAA,CAACb,MAAM,CAACqF,GAAG;oBACTI,OAAO,EAAE;sBAAEmB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBAAE,CAAE;oBAChCC,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,KAAK,EAAE;oBAAE,CAAE;oBAC1D9B,SAAS,EAAC;kBAAkC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACFhF,OAAA,CAACb,MAAM,CAACqF,GAAG;oBACTI,OAAO,EAAE;sBAAEmB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBAAE,CAAE;oBAChCC,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,KAAK,EAAE;oBAAI,CAAE;oBAC5D9B,SAAS,EAAC;kBAAkC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACFhF,OAAA,CAACb,MAAM,CAACqF,GAAG;oBACTI,OAAO,EAAE;sBAAEmB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBAAE,CAAE;oBAChCC,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,KAAK,EAAE;oBAAI,CAAE;oBAC5D9B,SAAS,EAAC;kBAAkC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC,eAElBhF,OAAA;YAAKqG,GAAG,EAAEpF;UAAe;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA;MAAKsE,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGvE,OAAA;QAAKsE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhCvE,OAAA,CAACZ,eAAe;UAAAmF,QAAA,EACb9D,YAAY,iBACXT,OAAA,CAACb,MAAM,CAACqF,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE4B,MAAM,EAAE;YAAE,CAAE;YACnC1B,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAE4B,MAAM,EAAE;YAAO,CAAE;YACxCR,IAAI,EAAE;cAAEpB,OAAO,EAAE,CAAC;cAAE4B,MAAM,EAAE;YAAE,CAAE;YAChChC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,eAE9FvE,OAAA;cAAKsE,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CvE,OAAA;gBAAKsE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBvE,OAAA;kBACEyF,GAAG,EAAEhF,YAAa;kBAClBiF,GAAG,EAAC,SAAS;kBACbpB,SAAS,EAAC;gBAA0E;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC,eACFhF,OAAA;kBACEuG,OAAO,EAAE3D,WAAY;kBACrB0B,SAAS,EAAC,uJAAuJ;kBAAAC,QAAA,eAEjKvE,OAAA,CAACT,GAAG;oBAAC+E,SAAS,EAAC;kBAAuB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNhF,OAAA;gBAAKsE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBvE,OAAA;kBAAGsE,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC9EhF,OAAA;kBAAGsE,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAEhE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiG;gBAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBhF,OAAA;UAAKsE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBvE,OAAA;YAAKsE,SAAS,EAAC,6LAA6L;YAAAC,QAAA,gBAE1MvE,OAAA;cACEuG,OAAO,EAAEA,CAAA;gBAAA,IAAAE,qBAAA;gBAAA,QAAAA,qBAAA,GAAMvF,YAAY,CAACa,OAAO,cAAA0E,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;cAAA,CAAC;cAC7CpC,SAAS,EAAC,qFAAqF;cAC/FqC,KAAK,EAAC,cAAc;cAAApC,QAAA,eAEpBvE,OAAA,CAACV,WAAW;gBAACgF,SAAS,EAAC;cAAuB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eAGThF,OAAA;cACEqG,GAAG,EAAElF,WAAY;cACjB0B,KAAK,EAAExC,MAAO;cACduG,QAAQ,EAAGzE,CAAC,IAAK7B,SAAS,CAAC6B,CAAC,CAACE,MAAM,CAACQ,KAAK,CAAE;cAC3CgE,UAAU,EAAE/D,cAAe;cAC3BgE,WAAW,EAAElB,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,oBAAoB,GAAG,yCAA0C;cACzGvB,SAAS,EAAC,oHAAoH;cAC9HyC,IAAI,EAAE,CAAE;cACR9B,KAAK,EAAE;gBACLqB,MAAM,EAAE,MAAM;gBACdU,SAAS,EAAEpB,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACrDF,SAAS,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cACjD,CAAE;cACFoB,OAAO,EAAG9E,CAAC,IAAK;gBACdA,CAAC,CAACE,MAAM,CAAC4C,KAAK,CAACqB,MAAM,GAAG,MAAM;gBAC9B,MAAMX,SAAS,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,EAAE,GAAG,GAAG;gBACrD1D,CAAC,CAACE,MAAM,CAAC4C,KAAK,CAACqB,MAAM,GAAGY,IAAI,CAACC,GAAG,CAAChF,CAAC,CAACE,MAAM,CAAC+E,YAAY,EAAEzB,SAAS,CAAC,GAAG,IAAI;cAC3E;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGFhF,OAAA,CAACb,MAAM,CAACkI,MAAM;cACZC,UAAU,EAAE;gBAAEvB,KAAK,EAAE;cAAK,CAAE;cAC5BwB,QAAQ,EAAE;gBAAExB,KAAK,EAAE;cAAK,CAAE;cAC1BQ,OAAO,EAAEvD,UAAW;cACpBwE,QAAQ,EAAE7G,SAAS,IAAK,CAACN,MAAM,CAAC4C,IAAI,CAAC,CAAC,IAAI,CAAC1C,SAAW;cACtD+D,SAAS,EAAG,iCACV3D,SAAS,IAAK,CAACN,MAAM,CAAC4C,IAAI,CAAC,CAAC,IAAI,CAAC1C,SAAU,GACvC,8CAA8C,GAC9C,oEACL,EAAE;cAAAgE,QAAA,EAEF5D,SAAS,gBACRX,OAAA,CAACL,QAAQ;gBAAC2E,SAAS,EAAC;cAAoC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE3DhF,OAAA,CAACX,MAAM;gBAACiF,SAAS,EAAC;cAAuB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC5C;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAGNhF,OAAA;YACEqG,GAAG,EAAEnF,YAAa;YAClB2C,IAAI,EAAC,MAAM;YACX4D,MAAM,EAAC,SAAS;YAChBb,QAAQ,EAAE1E,iBAAkB;YAC5BoC,SAAS,EAAC;UAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNhF,OAAA;UAAGsE,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EACvDqB,MAAM,CAACC,UAAU,IAAI,GAAG,GACrB,0CAA0C,GAC1C;QAA6E;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9E,EAAA,CA1XQD,kBAAkB;AAAAyH,EAAA,GAAlBzH,kBAAkB;AA4X3B,eAAeA,kBAAkB;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}