{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\n\n// Professional Sound System\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst playSound = type => {\n  try {\n    const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n    const createTone = (frequency, duration, type = 'sine') => {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n      oscillator.type = type;\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);\n      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + duration);\n    };\n    switch (type) {\n      case 'select':\n        // Professional click sound\n        createTone(800, 0.1, 'square');\n        break;\n      case 'navigate':\n        // Smooth navigation sound\n        createTone(600, 0.15, 'sine');\n        setTimeout(() => createTone(800, 0.1, 'sine'), 50);\n        break;\n      case 'submit':\n        // Success sound\n        createTone(523, 0.2, 'sine'); // C\n        setTimeout(() => createTone(659, 0.2, 'sine'), 100); // E\n        setTimeout(() => createTone(784, 0.3, 'sine'), 200); // G\n        break;\n      default:\n        createTone(600, 0.1, 'sine');\n    }\n  } catch (error) {\n    // Fallback for browsers that don't support Web Audio API\n    console.log('Audio not supported');\n  }\n};\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n        const response = await getExamById({\n          examId: id\n        });\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    console.log('🚀 Submit button clicked - showing loading overlay');\n    console.log('Current submitting state:', submitting);\n    try {\n      // Play submit sound\n      playSound('submit');\n\n      // Show loading immediately\n      setSubmitting(true);\n      console.log('✅ setSubmitting(true) called');\n      console.log('📝 Starting quiz marking process...');\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        let isCorrect = false;\n        let actualCorrectAnswer = '';\n\n        // Determine the correct answer based on question type\n        const questionType = question.type || question.answerType || 'mcq';\n        if (questionType.toLowerCase() === 'mcq' || questionType === 'Options') {\n          // For MCQ questions, check both correctAnswer and correctOption\n          if (question.options && typeof question.options === 'object') {\n            // If correctAnswer is a key (like \"B\"), get the actual text\n            if (question.correctAnswer && question.options[question.correctAnswer]) {\n              actualCorrectAnswer = question.options[question.correctAnswer];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctOption is available, use it\n            else if (question.correctOption && question.options[question.correctOption]) {\n              actualCorrectAnswer = question.options[question.correctOption];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctAnswer is already the full text\n            else if (question.correctAnswer) {\n              actualCorrectAnswer = question.correctAnswer;\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n          } else {\n            // Fallback for other option formats\n            actualCorrectAnswer = question.correctAnswer || question.correctOption || '';\n            isCorrect = userAnswer === actualCorrectAnswer;\n          }\n        } else {\n          var _actualCorrectAnswer;\n          // For fill-in-the-blank and other types, direct comparison\n          actualCorrectAnswer = question.correctAnswer || '';\n          isCorrect = (userAnswer === null || userAnswer === void 0 ? void 0 : userAnswer.toLowerCase().trim()) === ((_actualCorrectAnswer = actualCorrectAnswer) === null || _actualCorrectAnswer === void 0 ? void 0 : _actualCorrectAnswer.toLowerCase().trim());\n        }\n        if (isCorrect) correctAnswers++;\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          questionText: question.name || `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: actualCorrectAnswer,\n          isCorrect,\n          questionType: questionType,\n          options: question.options || null,\n          questionImage: question.image || question.questionImage || question.imageUrl || null,\n          image: question.image || question.questionImage || question.imageUrl || null\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n      // Use the exam's actual passing marks instead of hardcoded 60%\n      const passingPercentage = quiz.passingMarks || quiz.passingPercentage || 60;\n      const verdict = percentage >= passingPercentage ? 'Pass' : 'Fail';\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken,\n          // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n      try {\n        const response = await addReport(reportData);\n        if (response.success) {\n          console.log('✅ Quiz submitted successfully, preparing results...');\n\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null,\n            // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category,\n            passingPercentage: passingPercentage,\n            // Include actual passing marks\n            verdict: verdict // Include calculated verdict\n          };\n\n          // Brief delay to show loading screen\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          console.log('🎯 Navigating to results page...');\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          // Show error in the loading overlay instead of notification\n          setTimeout(() => {\n            setSubmitting(false);\n            message.error(response.message || 'Failed to submit quiz');\n          }, 1000);\n          return;\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        // Show error in the loading overlay instead of notification\n        setTimeout(() => {\n          setSubmitting(false);\n          message.error('Network error while submitting quiz');\n        }, 1000);\n        return;\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      // Show error in the loading overlay instead of notification\n      setTimeout(() => {\n        setSubmitting(false);\n        message.error('Failed to submit quiz');\n      }, 1000);\n      return;\n    } finally {\n      setSubmitting(false);\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Don't auto-submit, just stop the timer\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    playSound('select');\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-yellow-800 font-medium\",\n            children: \"No options found for this question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              className: \"text-sm text-yellow-600 cursor-pointer\",\n              children: \"Show question data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\",\n              children: JSON.stringify(currentQ, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: ['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index);\n            const isSelected = answers[currentQuestion] === option;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleAnswerSelect(option),\n              className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n                  children: optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg leading-relaxed flex-1 text-left text-gray-900\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: options.map((option, index) => {\n        const optionLetter = String.fromCharCode(65 + index);\n        const isSelected = answers[currentQuestion] === option;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleAnswerSelect(option),\n          className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n              children: optionLetter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg leading-relaxed flex-1 text-left text-gray-900\",\n              children: typeof option === 'string' ? option : JSON.stringify(option)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-800 text-sm font-medium mb-2\",\n          children: \"Fill in the blank:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700\",\n          children: \"Type your answer in the box below\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: answers[currentQuestion] || '',\n          onChange: e => handleAnswerSelect(e.target.value),\n          placeholder: \"Type your answer here...\",\n          className: \"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\",\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 7\n    }, this);\n  }\n  if (!quiz || !questions.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"No Questions Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"This quiz doesn't have any questions yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Question Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Unable to load the current question.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Invalid Question Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"The question data is corrupted or invalid.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show enhanced loading screen when submitting\n  if (submitting) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n          @keyframes pulse {\n            0%, 100% { opacity: 1; }\n            50% { opacity: 0.5; }\n          }\n          @keyframes bounce {\n            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }\n            40% { transform: translateY(-10px); }\n            60% { transform: translateY(-5px); }\n          }\n          @keyframes fadeIn {\n            0% { opacity: 0; transform: scale(0.8); }\n            100% { opacity: 1; transform: scale(1); }\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(147, 51, 234, 0.9))',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 9999\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            padding: '50px 40px',\n            borderRadius: '25px',\n            textAlign: 'center',\n            boxShadow: '0 25px 50px rgba(0,0,0,0.3)',\n            animation: 'fadeIn 0.5s ease-out',\n            border: '3px solid rgba(59, 130, 246, 0.2)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '60px',\n              marginBottom: '20px',\n              animation: 'bounce 2s infinite'\n            },\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '80px',\n              height: '80px',\n              border: '6px solid #e5e7eb',\n              borderTop: '6px solid #3b82f6',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite',\n              margin: '0 auto 25px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '28px',\n              fontWeight: 'bold',\n              color: '#1f2937',\n              margin: '0 0 10px 0',\n              animation: 'pulse 2s infinite'\n            },\n            children: \"We are marking...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '16px',\n              color: '#6b7280',\n              margin: '0 0 20px 0'\n            },\n            children: \"Please wait while we evaluate your answers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              gap: '8px'\n            },\n            children: [0, 1, 2].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '12px',\n                height: '12px',\n                backgroundColor: '#3b82f6',\n                borderRadius: '50%',\n                animation: `pulse 1.5s infinite ${i * 0.2}s`\n              }\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative\",\n    style: {\n      padding: window.innerWidth <= 768 ? '8px' : '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b border-gray-200 rounded-lg\",\n      style: {\n        marginBottom: window.innerWidth <= 768 ? '12px' : '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mx-auto\",\n        style: {\n          maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '1200px',\n          padding: window.innerWidth <= 768 ? '12px' : '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"font-bold text-gray-900 mb-2\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '18px' : window.innerWidth <= 1024 ? '24px' : '28px'\n              },\n              children: quiz.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n              },\n              children: [\"Question \", currentQuestion + 1, \" of \", questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 724,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-2 rounded-lg ${timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}`,\n              style: {\n                padding: window.innerWidth <= 768 ? '8px 12px' : '12px 16px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                style: {\n                  width: window.innerWidth <= 768 ? '16px' : '20px',\n                  height: window.innerWidth <= 768 ? '16px' : '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold\",\n                style: {\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                },\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600 font-medium\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n              },\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-600 font-bold\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n              },\n              children: [Math.round((currentQuestion + 1) / questions.length * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full shadow-inner\",\n            style: {\n              height: window.innerWidth <= 768 ? '8px' : '10px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-500 ease-out shadow-sm\",\n              style: {\n                width: `${(currentQuestion + 1) / questions.length * 100}%`,\n                height: window.innerWidth <= 768 ? '8px' : '10px',\n                boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 715,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 709,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mx-auto\",\n      style: {\n        maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '1000px',\n        padding: window.innerWidth <= 768 ? '0 8px' : '0 16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 transition-all duration-300\",\n        style: {\n          padding: window.innerWidth <= 768 ? '16px' : window.innerWidth <= 1024 ? '24px' : '32px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"font-bold text-gray-900 text-center\",\n            style: {\n              fontSize: window.innerWidth <= 768 ? '18px' : window.innerWidth <= 1024 ? '24px' : '28px',\n              marginBottom: window.innerWidth <= 768 ? '12px' : '16px'\n            },\n            children: typeof currentQ.name === 'string' ? currentQ.name : 'Question'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 13\n          }, this), currentQ.image && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg\",\n            style: {\n              marginBottom: window.innerWidth <= 768 ? '16px' : '24px',\n              padding: window.innerWidth <= 768 ? '12px' : '16px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: currentQ.image,\n              alt: \"Question diagram\",\n              className: \"max-w-full h-auto rounded-lg shadow-lg mx-auto block\",\n              style: {\n                maxHeight: window.innerWidth <= 768 ? '250px' : '400px'\n              },\n              onError: e => {\n                e.target.style.display = 'none';\n                // Show fallback message\n                const fallback = document.createElement('div');\n                fallback.className = 'text-center py-8 text-gray-500';\n                fallback.innerHTML = '<p>Could not load diagram</p>';\n                e.target.parentNode.appendChild(fallback);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 823,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          style: {\n            marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n          },\n          children: renderAnswerSection()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          style: {\n            flexDirection: window.innerWidth <= 768 ? 'column' : 'row',\n            justifyContent: window.innerWidth <= 768 ? 'center' : 'space-between',\n            gap: window.innerWidth <= 768 ? '12px' : '0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestion === 0,\n            className: `flex items-center gap-2 rounded-lg font-semibold transition-colors ${currentQuestion === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n            style: {\n              padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n              width: window.innerWidth <= 768 ? '100%' : 'auto',\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              style: {\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 885,\n            columnNumber: 13\n          }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSubmitQuiz,\n            disabled: submitting,\n            className: `flex items-center gap-2 rounded-lg font-semibold transition-colors ${submitting ? 'bg-gray-400 text-gray-200 cursor-not-allowed' : 'bg-green-600 text-white hover:bg-green-700'}`,\n            style: {\n              padding: window.innerWidth <= 768 ? '10px 16px' : '12px 32px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n              width: window.innerWidth <= 768 ? '100%' : 'auto',\n              justifyContent: 'center'\n            },\n            children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full border-2 border-white border-t-transparent\",\n                style: {\n                  width: window.innerWidth <= 768 ? '16px' : '20px',\n                  height: window.innerWidth <= 768 ? '16px' : '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 927,\n                columnNumber: 21\n              }, this), \"Submitting...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                style: {\n                  width: window.innerWidth <= 768 ? '16px' : '20px',\n                  height: window.innerWidth <= 768 ? '16px' : '20px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 21\n              }, this), \"Submit Quiz\"]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToNext,\n            className: \"flex items-center gap-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\",\n            style: {\n              padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n              width: window.innerWidth <= 768 ? '100%' : 'auto',\n              justifyContent: 'center'\n            },\n            children: [\"Next\", /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              style: {\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 702,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"lfHb+Do4bl/NRrmGyYYBDhlNoy0=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useParams", "useNavigate", "useSelector", "message", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "playSound", "type", "audioContext", "window", "AudioContext", "webkitAudioContext", "createTone", "frequency", "duration", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "currentTime", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "setTimeout", "error", "console", "log", "QuizPlay", "_s", "id", "navigate", "user", "state", "loading", "setLoading", "submitting", "setSubmitting", "quiz", "setQuiz", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loadQuizData", "_id", "token", "localStorage", "getItem", "response", "examId", "success", "data", "length", "Array", "fill", "Date", "handleSubmitQuiz", "currentUser", "storedUser", "JSON", "parse", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "actualCorrectAnswer", "questionType", "answerType", "toLowerCase", "options", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "_actualCorrectAnswer", "trim", "questionId", "questionName", "name", "questionText", "String", "questionImage", "image", "imageUrl", "percentage", "round", "passingPercentage", "passingMarks", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "timeSpent", "points", "totalQuestions", "navigationState", "xpData", "quizName", "quizSubject", "subject", "category", "Promise", "resolve", "apiError", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "answer", "newAnswers", "goToNext", "goToPrevious", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "renderAnswerSection", "currentQ", "renderMultipleChoice", "renderFillInTheBlank", "renderImageQuestion", "isArray", "Object", "values", "option1", "option2", "option3", "option4", "filter", "Boolean", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stringify", "option", "optionLetter", "fromCharCode", "isSelected", "onClick", "value", "onChange", "e", "target", "placeholder", "autoFocus", "isLastQuestion", "style", "position", "top", "left", "width", "height", "background", "display", "alignItems", "justifyContent", "zIndex", "backgroundColor", "padding", "borderRadius", "textAlign", "boxShadow", "animation", "border", "fontSize", "marginBottom", "borderTop", "margin", "fontWeight", "color", "gap", "i", "innerWidth", "max<PERSON><PERSON><PERSON>", "src", "alt", "maxHeight", "onError", "fallback", "document", "createElement", "innerHTML", "parentNode", "append<PERSON><PERSON><PERSON>", "flexDirection", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { usePara<PERSON>, useNavigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { \n  Tb<PERSON><PERSON>, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\n\n// Professional Sound System\nconst playSound = (type) => {\n  try {\n    const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n    const createTone = (frequency, duration, type = 'sine') => {\n      const oscillator = audioContext.createOscillator();\n      const gainNode = audioContext.createGain();\n\n      oscillator.connect(gainNode);\n      gainNode.connect(audioContext.destination);\n\n      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n      oscillator.type = type;\n\n      gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);\n      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);\n\n      oscillator.start(audioContext.currentTime);\n      oscillator.stop(audioContext.currentTime + duration);\n    };\n\n    switch(type) {\n      case 'select':\n        // Professional click sound\n        createTone(800, 0.1, 'square');\n        break;\n      case 'navigate':\n        // Smooth navigation sound\n        createTone(600, 0.15, 'sine');\n        setTimeout(() => createTone(800, 0.1, 'sine'), 50);\n        break;\n      case 'submit':\n        // Success sound\n        createTone(523, 0.2, 'sine'); // C\n        setTimeout(() => createTone(659, 0.2, 'sine'), 100); // E\n        setTimeout(() => createTone(784, 0.3, 'sine'), 200); // G\n        break;\n      default:\n        createTone(600, 0.1, 'sine');\n    }\n  } catch (error) {\n    // Fallback for browsers that don't support Web Audio API\n    console.log('Audio not supported');\n  }\n};\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  \n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        \n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n        \n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          \n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    console.log('🚀 Submit button clicked - showing loading overlay');\n    console.log('Current submitting state:', submitting);\n\n    try {\n      // Play submit sound\n      playSound('submit');\n\n      // Show loading immediately\n      setSubmitting(true);\n      console.log('✅ setSubmitting(true) called');\n      console.log('📝 Starting quiz marking process...');\n\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        let isCorrect = false;\n        let actualCorrectAnswer = '';\n\n        // Determine the correct answer based on question type\n        const questionType = question.type || question.answerType || 'mcq';\n\n        if (questionType.toLowerCase() === 'mcq' || questionType === 'Options') {\n          // For MCQ questions, check both correctAnswer and correctOption\n          if (question.options && typeof question.options === 'object') {\n            // If correctAnswer is a key (like \"B\"), get the actual text\n            if (question.correctAnswer && question.options[question.correctAnswer]) {\n              actualCorrectAnswer = question.options[question.correctAnswer];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctOption is available, use it\n            else if (question.correctOption && question.options[question.correctOption]) {\n              actualCorrectAnswer = question.options[question.correctOption];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctAnswer is already the full text\n            else if (question.correctAnswer) {\n              actualCorrectAnswer = question.correctAnswer;\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n          } else {\n            // Fallback for other option formats\n            actualCorrectAnswer = question.correctAnswer || question.correctOption || '';\n            isCorrect = userAnswer === actualCorrectAnswer;\n          }\n        } else {\n          // For fill-in-the-blank and other types, direct comparison\n          actualCorrectAnswer = question.correctAnswer || '';\n          isCorrect = userAnswer?.toLowerCase().trim() === actualCorrectAnswer?.toLowerCase().trim();\n        }\n\n        if (isCorrect) correctAnswers++;\n\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          questionText: question.name || `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: actualCorrectAnswer,\n          isCorrect,\n          questionType: questionType,\n          options: question.options || null,\n          questionImage: question.image || question.questionImage || question.imageUrl || null,\n          image: question.image || question.questionImage || question.imageUrl || null\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      // Use the exam's actual passing marks instead of hardcoded 60%\n      const passingPercentage = quiz.passingMarks || quiz.passingPercentage || 60;\n      const verdict = percentage >= passingPercentage ? 'Pass' : 'Fail';\n\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken, // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n\n      try {\n        const response = await addReport(reportData);\n\n        if (response.success) {\n          console.log('✅ Quiz submitted successfully, preparing results...');\n\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null, // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category,\n            passingPercentage: passingPercentage, // Include actual passing marks\n            verdict: verdict // Include calculated verdict\n          };\n\n          // Brief delay to show loading screen\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          console.log('🎯 Navigating to results page...');\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          // Show error in the loading overlay instead of notification\n          setTimeout(() => {\n            setSubmitting(false);\n            message.error(response.message || 'Failed to submit quiz');\n          }, 1000);\n          return;\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        // Show error in the loading overlay instead of notification\n        setTimeout(() => {\n          setSubmitting(false);\n          message.error('Network error while submitting quiz');\n        }, 1000);\n        return;\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      // Show error in the loading overlay instead of notification\n      setTimeout(() => {\n        setSubmitting(false);\n        message.error('Failed to submit quiz');\n      }, 1000);\n      return;\n    } finally {\n      setSubmitting(false);\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Don't auto-submit, just stop the timer\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    playSound('select');\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      playSound('navigate');\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n\n\n\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return (\n        <div className=\"space-y-4\">\n          <div className=\"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <p className=\"text-yellow-800 font-medium\">No options found for this question</p>\n            <details className=\"mt-2\">\n              <summary className=\"text-sm text-yellow-600 cursor-pointer\">Show question data</summary>\n              <pre className=\"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\">\n                {JSON.stringify(currentQ, null, 2)}\n              </pre>\n            </details>\n          </div>\n\n          {/* Fallback test options */}\n          <div className=\"space-y-3\">\n            {['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index);\n              const isSelected = answers[currentQuestion] === option;\n\n              return (\n                <button\n                  key={index}\n                  onClick={() => handleAnswerSelect(option)}\n                  className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                  }`}\n                >\n                  <div className=\"flex items-start gap-4\">\n                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                      isSelected\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}>\n                      {optionLetter}\n                    </div>\n                    <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                      {option}\n                    </span>\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-3\">\n        {options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index);\n          const isSelected = answers[currentQuestion] === option;\n\n          return (\n            <button\n              key={index}\n              onClick={() => handleAnswerSelect(option)}\n              className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n              }`}\n            >\n              <div className=\"flex items-start gap-4\">\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-100 text-gray-600'\n                }`}>\n                  {optionLetter}\n                </div>\n                <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                  {typeof option === 'string' ? option : JSON.stringify(option)}\n                </span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return (\n      <div className=\"space-y-4\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <p className=\"text-blue-800 text-sm font-medium mb-2\">Fill in the blank:</p>\n          <p className=\"text-gray-700\">Type your answer in the box below</p>\n        </div>\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={answers[currentQuestion] || ''}\n            onChange={(e) => handleAnswerSelect(e.target.value)}\n            placeholder=\"Type your answer here...\"\n            className=\"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\"\n            autoFocus\n          />\n        </div>\n      </div>\n    );\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!quiz || !questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">No Questions Available</h2>\n            <p className=\"text-gray-600 mb-6\">This quiz doesn't have any questions yet.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Question Not Found</h2>\n            <p className=\"text-gray-600 mb-6\">Unable to load the current question.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Invalid Question Data</h2>\n            <p className=\"text-gray-600 mb-6\">The question data is corrupted or invalid.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n\n  // Show enhanced loading screen when submitting\n  if (submitting) {\n    return (\n      <>\n        <style>{`\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n          @keyframes pulse {\n            0%, 100% { opacity: 1; }\n            50% { opacity: 0.5; }\n          }\n          @keyframes bounce {\n            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }\n            40% { transform: translateY(-10px); }\n            60% { transform: translateY(-5px); }\n          }\n          @keyframes fadeIn {\n            0% { opacity: 0; transform: scale(0.8); }\n            100% { opacity: 1; transform: scale(1); }\n          }\n        `}</style>\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(147, 51, 234, 0.9))',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 9999\n        }}>\n          <div style={{\n            backgroundColor: 'white',\n            padding: '50px 40px',\n            borderRadius: '25px',\n            textAlign: 'center',\n            boxShadow: '0 25px 50px rgba(0,0,0,0.3)',\n            animation: 'fadeIn 0.5s ease-out',\n            border: '3px solid rgba(59, 130, 246, 0.2)'\n          }}>\n            {/* Animated Icon */}\n            <div style={{\n              fontSize: '60px',\n              marginBottom: '20px',\n              animation: 'bounce 2s infinite'\n            }}>📝</div>\n\n            {/* Spinner */}\n            <div style={{\n              width: '80px',\n              height: '80px',\n              border: '6px solid #e5e7eb',\n              borderTop: '6px solid #3b82f6',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite',\n              margin: '0 auto 25px'\n            }}></div>\n\n            {/* Main Message */}\n            <h2 style={{\n              fontSize: '28px',\n              fontWeight: 'bold',\n              color: '#1f2937',\n              margin: '0 0 10px 0',\n              animation: 'pulse 2s infinite'\n            }}>We are marking...</h2>\n\n            {/* Sub Message */}\n            <p style={{\n              fontSize: '16px',\n              color: '#6b7280',\n              margin: '0 0 20px 0'\n            }}>Please wait while we evaluate your answers</p>\n\n            {/* Progress Dots */}\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              gap: '8px'\n            }}>\n              {[0, 1, 2].map(i => (\n                <div\n                  key={i}\n                  style={{\n                    width: '12px',\n                    height: '12px',\n                    backgroundColor: '#3b82f6',\n                    borderRadius: '50%',\n                    animation: `pulse 1.5s infinite ${i * 0.2}s`\n                  }}\n                ></div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </>\n    );\n  }\n\n  return (\n    <div\n      className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative\"\n      style={{\n        padding: window.innerWidth <= 768 ? '8px' : '16px'\n      }}\n    >\n      {/* Header */}\n      <div\n        className=\"bg-white shadow-sm border-b border-gray-200 rounded-lg\"\n        style={{\n          marginBottom: window.innerWidth <= 768 ? '12px' : '24px'\n        }}\n      >\n        <div\n          className=\"mx-auto\"\n          style={{\n            maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '1200px',\n            padding: window.innerWidth <= 768 ? '12px' : '16px'\n          }}\n        >\n          <div className=\"text-center\">\n            {/* Centered Title and Question Counter */}\n            <div>\n              <h1\n                className=\"font-bold text-gray-900 mb-2\"\n                style={{\n                  fontSize: window.innerWidth <= 768 ? '18px' : window.innerWidth <= 1024 ? '24px' : '28px'\n                }}\n              >\n                {quiz.name}\n              </h1>\n              <p\n                className=\"text-gray-600 mb-4\"\n                style={{\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                }}\n              >\n                Question {currentQuestion + 1} of {questions.length}\n              </p>\n            </div>\n\n            {/* Timer - Centered */}\n            <div className=\"flex justify-center mb-4\">\n              <div className={`flex items-center gap-2 rounded-lg ${\n                timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n              }`}\n              style={{\n                padding: window.innerWidth <= 768 ? '8px 12px' : '12px 16px'\n              }}>\n                <TbClock\n                  style={{\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }}\n                />\n                <span\n                  className=\"font-semibold\"\n                  style={{\n                    fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n                  }}\n                >\n                  {formatTime(timeLeft)}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Enhanced Progress bar */}\n          <div className=\"mb-2\">\n            <div className=\"flex justify-between items-center mb-1\">\n              <span\n                className=\"text-gray-600 font-medium\"\n                style={{\n                  fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n                }}\n              >\n                Progress\n              </span>\n              <span\n                className=\"text-blue-600 font-bold\"\n                style={{\n                  fontSize: window.innerWidth <= 768 ? '12px' : '14px'\n                }}\n              >\n                {Math.round(((currentQuestion + 1) / questions.length) * 100)}%\n              </span>\n            </div>\n            <div\n              className=\"w-full bg-gray-200 rounded-full shadow-inner\"\n              style={{\n                height: window.innerWidth <= 768 ? '8px' : '10px'\n              }}\n            >\n              <div\n                className=\"bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-500 ease-out shadow-sm\"\n                style={{\n                  width: `${((currentQuestion + 1) / questions.length) * 100}%`,\n                  height: window.innerWidth <= 768 ? '8px' : '10px',\n                  boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)'\n                }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div\n        className=\"mx-auto\"\n        style={{\n          maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '1000px',\n          padding: window.innerWidth <= 768 ? '0 8px' : '0 16px'\n        }}\n      >\n        <div\n          className=\"bg-white rounded-2xl shadow-xl border border-gray-200 transition-all duration-300\"\n          style={{\n            padding: window.innerWidth <= 768 ? '16px' : window.innerWidth <= 1024 ? '24px' : '32px'\n          }}\n        >\n          {/* Question */}\n          <div\n            style={{\n              marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n            }}\n          >\n            <h2\n              className=\"font-bold text-gray-900 text-center\"\n              style={{\n                fontSize: window.innerWidth <= 768 ? '18px' : window.innerWidth <= 1024 ? '24px' : '28px',\n                marginBottom: window.innerWidth <= 768 ? '12px' : '16px'\n              }}\n            >\n              {typeof currentQ.name === 'string' ? currentQ.name : 'Question'}\n            </h2>\n\n            {currentQ.image && (\n              <div\n                className=\"bg-gray-50 rounded-lg\"\n                style={{\n                  marginBottom: window.innerWidth <= 768 ? '16px' : '24px',\n                  padding: window.innerWidth <= 768 ? '12px' : '16px'\n                }}\n              >\n                <img\n                  src={currentQ.image}\n                  alt=\"Question diagram\"\n                  className=\"max-w-full h-auto rounded-lg shadow-lg mx-auto block\"\n                  style={{\n                    maxHeight: window.innerWidth <= 768 ? '250px' : '400px'\n                  }}\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                    // Show fallback message\n                    const fallback = document.createElement('div');\n                    fallback.className = 'text-center py-8 text-gray-500';\n                    fallback.innerHTML = '<p>Could not load diagram</p>';\n                    e.target.parentNode.appendChild(fallback);\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Answer Section - Different types based on question type */}\n          <div\n            className=\"space-y-4\"\n            style={{\n              marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n            }}\n          >\n            {renderAnswerSection()}\n          </div>\n\n          {/* Navigation */}\n          <div\n            className=\"flex items-center\"\n            style={{\n              flexDirection: window.innerWidth <= 768 ? 'column' : 'row',\n              justifyContent: window.innerWidth <= 768 ? 'center' : 'space-between',\n              gap: window.innerWidth <= 768 ? '12px' : '0'\n            }}\n          >\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestion === 0}\n              className={`flex items-center gap-2 rounded-lg font-semibold transition-colors ${\n                currentQuestion === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n              style={{\n                padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n                fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                width: window.innerWidth <= 768 ? '100%' : 'auto',\n                justifyContent: 'center'\n              }}\n            >\n              <TbArrowLeft\n                style={{\n                  width: window.innerWidth <= 768 ? '16px' : '20px',\n                  height: window.innerWidth <= 768 ? '16px' : '20px'\n                }}\n              />\n              Previous\n            </button>\n\n            {isLastQuestion ? (\n              <button\n                onClick={handleSubmitQuiz}\n                disabled={submitting}\n                className={`flex items-center gap-2 rounded-lg font-semibold transition-colors ${\n                  submitting\n                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'\n                    : 'bg-green-600 text-white hover:bg-green-700'\n                }`}\n                style={{\n                  padding: window.innerWidth <= 768 ? '10px 16px' : '12px 32px',\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                  width: window.innerWidth <= 768 ? '100%' : 'auto',\n                  justifyContent: 'center'\n                }}\n              >\n                {submitting ? (\n                  <>\n                    <div\n                      className=\"animate-spin rounded-full border-2 border-white border-t-transparent\"\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    ></div>\n                    Submitting...\n                  </>\n                ) : (\n                  <>\n                    <TbCheck\n                      style={{\n                        width: window.innerWidth <= 768 ? '16px' : '20px',\n                        height: window.innerWidth <= 768 ? '16px' : '20px'\n                      }}\n                    />\n                    Submit Quiz\n                  </>\n                )}\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n                style={{\n                  padding: window.innerWidth <= 768 ? '10px 16px' : '12px 24px',\n                  fontSize: window.innerWidth <= 768 ? '14px' : '16px',\n                  width: window.innerWidth <= 768 ? '100%' : 'auto',\n                  justifyContent: 'center'\n                }}\n              >\n                Next\n                <TbArrowRight\n                  style={{\n                    width: window.innerWidth <= 768 ? '16px' : '20px',\n                    height: window.innerWidth <= 768 ? '16px' : '20px'\n                  }}\n                />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAIC,IAAI,IAAK;EAC1B,IAAI;IACF,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;IAE7E,MAAMC,UAAU,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEP,IAAI,GAAG,MAAM,KAAK;MACzD,MAAMQ,UAAU,GAAGP,YAAY,CAACQ,gBAAgB,CAAC,CAAC;MAClD,MAAMC,QAAQ,GAAGT,YAAY,CAACU,UAAU,CAAC,CAAC;MAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;MAC5BA,QAAQ,CAACE,OAAO,CAACX,YAAY,CAACY,WAAW,CAAC;MAE1CL,UAAU,CAACF,SAAS,CAACQ,cAAc,CAACR,SAAS,EAAEL,YAAY,CAACc,WAAW,CAAC;MACxEP,UAAU,CAACR,IAAI,GAAGA,IAAI;MAEtBU,QAAQ,CAACM,IAAI,CAACF,cAAc,CAAC,CAAC,EAAEb,YAAY,CAACc,WAAW,CAAC;MACzDL,QAAQ,CAACM,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEhB,YAAY,CAACc,WAAW,GAAG,IAAI,CAAC;MAC3EL,QAAQ,CAACM,IAAI,CAACE,4BAA4B,CAAC,KAAK,EAAEjB,YAAY,CAACc,WAAW,GAAGR,QAAQ,CAAC;MAEtFC,UAAU,CAACW,KAAK,CAAClB,YAAY,CAACc,WAAW,CAAC;MAC1CP,UAAU,CAACY,IAAI,CAACnB,YAAY,CAACc,WAAW,GAAGR,QAAQ,CAAC;IACtD,CAAC;IAED,QAAOP,IAAI;MACT,KAAK,QAAQ;QACX;QACAK,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC;QAC9B;MACF,KAAK,UAAU;QACb;QACAA,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC;QAC7BgB,UAAU,CAAC,MAAMhB,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC;QAClD;MACF,KAAK,QAAQ;QACX;QACAA,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QAC9BgB,UAAU,CAAC,MAAMhB,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACrDgB,UAAU,CAAC,MAAMhB,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACrD;MACF;QACEA,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC;IAChC;EACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;IACd;IACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;AACF,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAG1C,SAAS,CAAC,CAAC;EAC1B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2C;EAAK,CAAC,GAAG1C,WAAW,CAAE2C,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsD,IAAI,EAAEC,OAAO,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC4D,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8D,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMiE,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFf,UAAU,CAAC,IAAI,CAAC;QAChBT,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,EAAE,CAAC;QAExC,IAAI,CAACE,IAAI,IAAI,CAACA,IAAI,CAACmB,GAAG,EAAE;UACtB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAI,CAACF,KAAK,EAAE;YACV1B,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnDpC,OAAO,CAACkC,KAAK,CAAC,gCAAgC,CAAC;YAC/CtC,eAAe,CAAC,MAAM;cACpB4C,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;QAEA,MAAMwB,QAAQ,GAAG,MAAM3D,WAAW,CAAC;UAAE4D,MAAM,EAAE1B;QAAG,CAAC,CAAC;QAClDJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4B,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;YAClBnE,OAAO,CAACkC,KAAK,CAAC,qBAAqB,CAAC;YACpCtC,eAAe,CAAC,MAAM;cACpB4C,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAACwB,QAAQ,CAACG,IAAI,CAAClB,SAAS,IAAIe,QAAQ,CAACG,IAAI,CAAClB,SAAS,CAACmB,MAAM,KAAK,CAAC,EAAE;YACpEpE,OAAO,CAACkC,KAAK,CAAC,sCAAsC,CAAC;YACrDtC,eAAe,CAAC,MAAM;cACpB4C,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEAQ,OAAO,CAACgB,QAAQ,CAACG,IAAI,CAAC;UACtBjB,YAAY,CAACc,QAAQ,CAACG,IAAI,CAAClB,SAAS,CAAC;UACrCK,UAAU,CAAC,IAAIe,KAAK,CAACL,QAAQ,CAACG,IAAI,CAAClB,SAAS,CAACmB,MAAM,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;UAC9Dd,WAAW,CAACQ,QAAQ,CAACG,IAAI,CAAChD,QAAQ,GAAG,EAAE,CAAC;UACxCuC,YAAY,CAAC,IAAIa,IAAI,CAAC,CAAC,CAAC;UACxBpC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4B,QAAQ,CAACG,IAAI,CAAC;QACzD,CAAC,MAAM;UACLhC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAE8B,QAAQ,CAAChE,OAAO,CAAC;UAClDA,OAAO,CAACkC,KAAK,CAAC8B,QAAQ,CAAChE,OAAO,IAAI,qBAAqB,CAAC;UACxDJ,eAAe,CAAC,MAAM;YACpB4C,QAAQ,CAAC,OAAO,CAAC;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3ClC,OAAO,CAACkC,KAAK,CAAC,wCAAwC,CAAC;QACvDtC,eAAe,CAAC,MAAM;UACpB4C,QAAQ,CAAC,OAAO,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIL,EAAE,IAAIE,IAAI,EAAE;MACdkB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACpB,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAM+B,gBAAgB,GAAG7E,WAAW,CAAC,YAAY;IAC/CwC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACjED,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAES,UAAU,CAAC;IAEpD,IAAI;MACF;MACAlC,SAAS,CAAC,QAAQ,CAAC;;MAEnB;MACAmC,aAAa,CAAC,IAAI,CAAC;MACnBX,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3CD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAIqC,WAAW,GAAGhC,IAAI;MACtB,IAAI,CAACgC,WAAW,IAAI,CAACA,WAAW,CAACb,GAAG,EAAE;QACpC,MAAMc,UAAU,GAAGZ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIW,UAAU,EAAE;UACd,IAAI;YACFD,WAAW,GAAGE,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;UACtC,CAAC,CAAC,OAAOxC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvDtC,eAAe,CAAC,MAAM;cACpB4C,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;MACF;MAEA,IAAI,CAACiC,WAAW,IAAI,CAACA,WAAW,CAACb,GAAG,EAAE;QACpC5D,OAAO,CAACkC,KAAK,CAAC,2CAA2C,CAAC;QAC1DtC,eAAe,CAAC,MAAM;UACpB4C,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF;MACF;MAEA,MAAMqC,OAAO,GAAG,IAAIN,IAAI,CAAC,CAAC;MAC1B,MAAMO,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAGpB,SAAS,IAAI,IAAI,CAAC;MAE1D,IAAIwB,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAGjC,SAAS,CAACkC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAGjC,OAAO,CAACgC,KAAK,CAAC;QACjC,IAAIE,SAAS,GAAG,KAAK;QACrB,IAAIC,mBAAmB,GAAG,EAAE;;QAE5B;QACA,MAAMC,YAAY,GAAGL,QAAQ,CAACxE,IAAI,IAAIwE,QAAQ,CAACM,UAAU,IAAI,KAAK;QAElE,IAAID,YAAY,CAACE,WAAW,CAAC,CAAC,KAAK,KAAK,IAAIF,YAAY,KAAK,SAAS,EAAE;UACtE;UACA,IAAIL,QAAQ,CAACQ,OAAO,IAAI,OAAOR,QAAQ,CAACQ,OAAO,KAAK,QAAQ,EAAE;YAC5D;YACA,IAAIR,QAAQ,CAACS,aAAa,IAAIT,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACS,aAAa,CAAC,EAAE;cACtEL,mBAAmB,GAAGJ,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACS,aAAa,CAAC;cAC9DN,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;YAChD;YACA;YAAA,KACK,IAAIJ,QAAQ,CAACU,aAAa,IAAIV,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACU,aAAa,CAAC,EAAE;cAC3EN,mBAAmB,GAAGJ,QAAQ,CAACQ,OAAO,CAACR,QAAQ,CAACU,aAAa,CAAC;cAC9DP,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;YAChD;YACA;YAAA,KACK,IAAIJ,QAAQ,CAACS,aAAa,EAAE;cAC/BL,mBAAmB,GAAGJ,QAAQ,CAACS,aAAa;cAC5CN,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;YAChD;UACF,CAAC,MAAM;YACL;YACAA,mBAAmB,GAAGJ,QAAQ,CAACS,aAAa,IAAIT,QAAQ,CAACU,aAAa,IAAI,EAAE;YAC5EP,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;UAChD;QACF,CAAC,MAAM;UAAA,IAAAO,oBAAA;UACL;UACAP,mBAAmB,GAAGJ,QAAQ,CAACS,aAAa,IAAI,EAAE;UAClDN,SAAS,GAAG,CAAAD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEK,WAAW,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,QAAAD,oBAAA,GAAKP,mBAAmB,cAAAO,oBAAA,uBAAnBA,oBAAA,CAAqBJ,WAAW,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;QAC5F;QAEA,IAAIT,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLgB,UAAU,EAAEb,QAAQ,CAACxB,GAAG,IAAK,YAAWyB,KAAM,EAAC;UAC/Ca,YAAY,EAAE,OAAOd,QAAQ,CAACe,IAAI,KAAK,QAAQ,GAAGf,QAAQ,CAACe,IAAI,GAAI,YAAWd,KAAK,GAAG,CAAE,EAAC;UACzFe,YAAY,EAAEhB,QAAQ,CAACe,IAAI,IAAK,YAAWd,KAAK,GAAG,CAAE,EAAC;UACtDC,UAAU,EAAE,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGe,MAAM,CAACf,UAAU,IAAI,EAAE,CAAC;UAClFO,aAAa,EAAEL,mBAAmB;UAClCD,SAAS;UACTE,YAAY,EAAEA,YAAY;UAC1BG,OAAO,EAAER,QAAQ,CAACQ,OAAO,IAAI,IAAI;UACjCU,aAAa,EAAElB,QAAQ,CAACmB,KAAK,IAAInB,QAAQ,CAACkB,aAAa,IAAIlB,QAAQ,CAACoB,QAAQ,IAAI,IAAI;UACpFD,KAAK,EAAEnB,QAAQ,CAACmB,KAAK,IAAInB,QAAQ,CAACkB,aAAa,IAAIlB,QAAQ,CAACoB,QAAQ,IAAI;QAC1E,CAAC;MACH,CAAC,CAAC;MAEF,MAAMC,UAAU,GAAG1B,IAAI,CAAC2B,KAAK,CAAEzB,cAAc,GAAGhC,SAAS,CAACmB,MAAM,GAAI,GAAG,CAAC;MACxE;MACA,MAAMuC,iBAAiB,GAAG5D,IAAI,CAAC6D,YAAY,IAAI7D,IAAI,CAAC4D,iBAAiB,IAAI,EAAE;MAC3E,MAAME,OAAO,GAAGJ,UAAU,IAAIE,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEjE,MAAMG,UAAU,GAAG;QACjBC,IAAI,EAAExE,EAAE;QACRE,IAAI,EAAEgC,WAAW,CAACb,GAAG;QACrBoD,MAAM,EAAE;UACN/B,cAAc;UACdgC,YAAY,EAAEhE,SAAS,CAACmB,MAAM,GAAGa,cAAc;UAC/CwB,UAAU;UACVS,KAAK,EAAET,UAAU;UACjBI,OAAO,EAAEA,OAAO;UAChB/B,SAAS;UACTqC,SAAS,EAAErC,SAAS;UAAE;UACtBsC,MAAM,EAAEnC,cAAc,GAAG,EAAE;UAC3BoC,cAAc,EAAEpE,SAAS,CAACmB;QAC5B;MACF,CAAC;MAED,IAAI;QACF,MAAMJ,QAAQ,GAAG,MAAM1D,SAAS,CAACwG,UAAU,CAAC;QAE5C,IAAI9C,QAAQ,CAACE,OAAO,EAAE;UACpB/B,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;UAElE;UACA,MAAMkF,eAAe,GAAG;YACtBb,UAAU;YACVxB,cAAc;YACdoC,cAAc,EAAEpE,SAAS,CAACmB,MAAM;YAChCU,SAAS;YACTI,aAAa;YACbqC,MAAM,EAAEvD,QAAQ,CAACuD,MAAM,IAAI,IAAI;YAAE;YACjCC,QAAQ,EAAEzE,IAAI,CAACoD,IAAI;YACnBsB,WAAW,EAAE1E,IAAI,CAAC2E,OAAO,IAAI3E,IAAI,CAAC4E,QAAQ;YAC1ChB,iBAAiB,EAAEA,iBAAiB;YAAE;YACtCE,OAAO,EAAEA,OAAO,CAAC;UACnB,CAAC;;UAED;UACA,MAAM,IAAIe,OAAO,CAACC,OAAO,IAAI5F,UAAU,CAAC4F,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD1F,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CxC,eAAe,CAAC,MAAM;YACpB4C,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;cAC7BG,KAAK,EAAE4E;YACT,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLnF,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAE8B,QAAQ,CAAChE,OAAO,CAAC;UAC5D;UACAiC,UAAU,CAAC,MAAM;YACfa,aAAa,CAAC,KAAK,CAAC;YACpB9C,OAAO,CAACkC,KAAK,CAAC8B,QAAQ,CAAChE,OAAO,IAAI,uBAAuB,CAAC;UAC5D,CAAC,EAAE,IAAI,CAAC;UACR;QACF;MACF,CAAC,CAAC,OAAO8H,QAAQ,EAAE;QACjB3F,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAE4F,QAAQ,CAAC;QACzD;QACA7F,UAAU,CAAC,MAAM;UACfa,aAAa,CAAC,KAAK,CAAC;UACpB9C,OAAO,CAACkC,KAAK,CAAC,qCAAqC,CAAC;QACtD,CAAC,EAAE,IAAI,CAAC;QACR;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACAD,UAAU,CAAC,MAAM;QACfa,aAAa,CAAC,KAAK,CAAC;QACpB9C,OAAO,CAACkC,KAAK,CAAC,uBAAuB,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;MACR;IACF,CAAC,SAAS;MACRY,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACW,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAEd,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAEvD;EACA/C,SAAS,CAAC,MAAM;IACd,IAAI6D,QAAQ,IAAI,CAAC,EAAE;MACjB;MACA;IACF;IAEA,MAAMwE,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BxE,WAAW,CAACyE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACxE,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM4E,kBAAkB,GAAIC,MAAM,IAAK;IACrCzH,SAAS,CAAC,QAAQ,CAAC;IACnB,MAAM0H,UAAU,GAAG,CAAC,GAAGhF,OAAO,CAAC;IAC/BgF,UAAU,CAAClF,eAAe,CAAC,GAAGiF,MAAM;IACpC9E,UAAU,CAAC+E,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAInF,eAAe,GAAGF,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;MAC1CzD,SAAS,CAAC,UAAU,CAAC;MACrByC,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMoF,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIpF,eAAe,GAAG,CAAC,EAAE;MACvBxC,SAAS,CAAC,UAAU,CAAC;MACrByC,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMqF,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAG3D,IAAI,CAACC,KAAK,CAACyD,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMrD,YAAY,GAAGsD,QAAQ,CAACnI,IAAI,IAAImI,QAAQ,CAACrD,UAAU,IAAI,KAAK;IAIlE,QAAQD,YAAY,CAACE,WAAW,CAAC,CAAC;MAChC,KAAK,KAAK;MACV,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;QACnB,OAAOqD,oBAAoB,CAAC,CAAC;MAE/B,KAAK,MAAM;MACX,KAAK,mBAAmB;MACxB,KAAK,WAAW;MAChB,KAAK,MAAM;QACT,OAAOC,oBAAoB,CAAC,CAAC;MAE/B,KAAK,OAAO;MACZ,KAAK,SAAS;QACZ,OAAOC,mBAAmB,CAAC,CAAC;MAE9B;QACE;QACA,OAAOF,oBAAoB,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMA,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIpD,OAAO,GAAG,EAAE;;IAEhB;IACA,IAAIvB,KAAK,CAAC8E,OAAO,CAACJ,QAAQ,CAACnD,OAAO,CAAC,EAAE;MACnCA,OAAO,GAAGmD,QAAQ,CAACnD,OAAO;IAC5B,CAAC,MAAM,IAAImD,QAAQ,CAACnD,OAAO,IAAI,OAAOmD,QAAQ,CAACnD,OAAO,KAAK,QAAQ,EAAE;MACnE;MACAA,OAAO,GAAGwD,MAAM,CAACC,MAAM,CAACN,QAAQ,CAACnD,OAAO,CAAC;IAC3C,CAAC,MAAM,IAAImD,QAAQ,CAACO,OAAO,IAAIP,QAAQ,CAACQ,OAAO,EAAE;MAC/C;MACA3D,OAAO,GAAG,CAACmD,QAAQ,CAACO,OAAO,EAAEP,QAAQ,CAACQ,OAAO,EAAER,QAAQ,CAACS,OAAO,EAAET,QAAQ,CAACU,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IACpG;IAEA,IAAI,CAAC/D,OAAO,IAAIA,OAAO,CAACxB,MAAM,KAAK,CAAC,EAAE;MACpC;MACA,oBACE5D,OAAA;QAAKoJ,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrJ,OAAA;UAAKoJ,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAChFrJ,OAAA;YAAGoJ,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjFzJ,OAAA;YAASoJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACvBrJ,OAAA;cAASoJ,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACxFzJ,OAAA;cAAKoJ,SAAS,EAAC,yEAAyE;cAAAC,QAAA,EACrFlF,IAAI,CAACuF,SAAS,CAACnB,QAAQ,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGNzJ,OAAA;UAAKoJ,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC1E,GAAG,CAAC,CAACgF,MAAM,EAAE9E,KAAK,KAAK;YACnG,MAAM+E,YAAY,GAAG/D,MAAM,CAACgE,YAAY,CAAC,EAAE,GAAGhF,KAAK,CAAC;YACpD,MAAMiF,UAAU,GAAGjH,OAAO,CAACF,eAAe,CAAC,KAAKgH,MAAM;YAEtD,oBACE3J,OAAA;cAEE+J,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAACgC,MAAM,CAAE;cAC1CP,SAAS,EAAG,wFACVU,UAAU,GACN,oDAAoD,GACpD,iEACL,EAAE;cAAAT,QAAA,eAEHrJ,OAAA;gBAAKoJ,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCrJ,OAAA;kBAAKoJ,SAAS,EAAG,2FACfU,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;kBAAAT,QAAA,EACAO;gBAAY;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNzJ,OAAA;kBAAMoJ,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACrEM;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GAnBD5E,KAAK;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBJ,CAAC;UAEb,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,oBACEzJ,OAAA;MAAKoJ,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBjE,OAAO,CAACT,GAAG,CAAC,CAACgF,MAAM,EAAE9E,KAAK,KAAK;QAC9B,MAAM+E,YAAY,GAAG/D,MAAM,CAACgE,YAAY,CAAC,EAAE,GAAGhF,KAAK,CAAC;QACpD,MAAMiF,UAAU,GAAGjH,OAAO,CAACF,eAAe,CAAC,KAAKgH,MAAM;QAEtD,oBACE3J,OAAA;UAEE+J,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAACgC,MAAM,CAAE;UAC1CP,SAAS,EAAG,wFACVU,UAAU,GACN,oDAAoD,GACpD,iEACL,EAAE;UAAAT,QAAA,eAEHrJ,OAAA;YAAKoJ,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCrJ,OAAA;cAAKoJ,SAAS,EAAG,2FACfU,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;cAAAT,QAAA,EACAO;YAAY;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNzJ,OAAA;cAAMoJ,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EACrE,OAAOM,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGxF,IAAI,CAACuF,SAAS,CAACC,MAAM;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC,GAnBD5E,KAAK;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBJ,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMhB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,oBACEzI,OAAA;MAAKoJ,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBrJ,OAAA;QAAKoJ,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DrJ,OAAA;UAAGoJ,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5EzJ,OAAA;UAAGoJ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACNzJ,OAAA;QAAKoJ,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBrJ,OAAA;UACEI,IAAI,EAAC,MAAM;UACX4J,KAAK,EAAEnH,OAAO,CAACF,eAAe,CAAC,IAAI,EAAG;UACtCsH,QAAQ,EAAGC,CAAC,IAAKvC,kBAAkB,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDI,WAAW,EAAC,0BAA0B;UACtChB,SAAS,EAAC,mHAAmH;UAC7HiB,SAAS;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMf,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIH,QAAQ,CAACnD,OAAO,IAAIvB,KAAK,CAAC8E,OAAO,CAACJ,QAAQ,CAACnD,OAAO,CAAC,IAAImD,QAAQ,CAACnD,OAAO,CAACxB,MAAM,GAAG,CAAC,EAAE;MACtF,OAAO4E,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL,OAAOC,oBAAoB,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,IAAItG,OAAO,EAAE;IACX,oBACEnC,OAAA;MAAKoJ,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGrJ,OAAA;QAAKoJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrJ,OAAA;UAAKoJ,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGzJ,OAAA;UAAGoJ,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAClH,IAAI,IAAI,CAACE,SAAS,CAACmB,MAAM,EAAE;IAC9B,oBACE5D,OAAA;MAAKoJ,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGrJ,OAAA;QAAKoJ,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFrJ,OAAA;UAAKoJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrJ,OAAA;YAAIoJ,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFzJ,OAAA;YAAGoJ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/EzJ,OAAA;YACE+J,OAAO,EAAEA,CAAA,KAAM3K,eAAe,CAAC,MAAM4C,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxDoH,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAAChH,SAAS,CAACE,eAAe,CAAC,EAAE;IAC/B,oBACE3C,OAAA;MAAKoJ,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGrJ,OAAA;QAAKoJ,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFrJ,OAAA;UAAKoJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrJ,OAAA;YAAIoJ,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EzJ,OAAA;YAAGoJ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1EzJ,OAAA;YACE+J,OAAO,EAAEA,CAAA,KAAM3K,eAAe,CAAC,MAAM4C,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxDoH,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMlB,QAAQ,GAAG9F,SAAS,CAACE,eAAe,CAAC;EAC3C,MAAM2H,cAAc,GAAG3H,eAAe,KAAKF,SAAS,CAACmB,MAAM,GAAG,CAAC;;EAE/D;EACA,IAAI,CAAC2E,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC7C,oBACEvI,OAAA;MAAKoJ,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGrJ,OAAA;QAAKoJ,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtFrJ,OAAA;UAAKoJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrJ,OAAA;YAAIoJ,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EzJ,OAAA;YAAGoJ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA0C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFzJ,OAAA;YACE+J,OAAO,EAAEA,CAAA,KAAM3K,eAAe,CAAC,MAAM4C,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxDoH,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAIA;EACA,IAAIpH,UAAU,EAAE;IACd,oBACErC,OAAA,CAAAE,SAAA;MAAAmJ,QAAA,gBACErJ,OAAA;QAAAqJ,QAAA,EAAS;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACVzJ,OAAA;QAAKuK,KAAK,EAAE;UACVC,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,UAAU,EAAE,2EAA2E;UACvFC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE;QACV,CAAE;QAAA5B,QAAA,eACArJ,OAAA;UAAKuK,KAAK,EAAE;YACVW,eAAe,EAAE,OAAO;YACxBC,OAAO,EAAE,WAAW;YACpBC,YAAY,EAAE,MAAM;YACpBC,SAAS,EAAE,QAAQ;YACnBC,SAAS,EAAE,6BAA6B;YACxCC,SAAS,EAAE,sBAAsB;YACjCC,MAAM,EAAE;UACV,CAAE;UAAAnC,QAAA,gBAEArJ,OAAA;YAAKuK,KAAK,EAAE;cACVkB,QAAQ,EAAE,MAAM;cAChBC,YAAY,EAAE,MAAM;cACpBH,SAAS,EAAE;YACb,CAAE;YAAAlC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGXzJ,OAAA;YAAKuK,KAAK,EAAE;cACVI,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,mBAAmB;cAC3BG,SAAS,EAAE,mBAAmB;cAC9BP,YAAY,EAAE,KAAK;cACnBG,SAAS,EAAE,yBAAyB;cACpCK,MAAM,EAAE;YACV;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGTzJ,OAAA;YAAIuK,KAAK,EAAE;cACTkB,QAAQ,EAAE,MAAM;cAChBI,UAAU,EAAE,MAAM;cAClBC,KAAK,EAAE,SAAS;cAChBF,MAAM,EAAE,YAAY;cACpBL,SAAS,EAAE;YACb,CAAE;YAAAlC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGzBzJ,OAAA;YAAGuK,KAAK,EAAE;cACRkB,QAAQ,EAAE,MAAM;cAChBK,KAAK,EAAE,SAAS;cAChBF,MAAM,EAAE;YACV,CAAE;YAAAvC,QAAA,EAAC;UAA0C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGjDzJ,OAAA;YAAKuK,KAAK,EAAE;cACVO,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBe,GAAG,EAAE;YACP,CAAE;YAAA1C,QAAA,EACC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC1E,GAAG,CAACqH,CAAC,iBACdhM,OAAA;cAEEuK,KAAK,EAAE;gBACLI,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdM,eAAe,EAAE,SAAS;gBAC1BE,YAAY,EAAE,KAAK;gBACnBG,SAAS,EAAG,uBAAsBS,CAAC,GAAG,GAAI;cAC5C;YAAE,GAPGA,CAAC;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQF,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CAAC;EAEP;EAEA,oBACEzJ,OAAA;IACEoJ,SAAS,EAAC,oEAAoE;IAC9EmB,KAAK,EAAE;MACLY,OAAO,EAAE7K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;IAC9C,CAAE;IAAA5C,QAAA,gBAGFrJ,OAAA;MACEoJ,SAAS,EAAC,wDAAwD;MAClEmB,KAAK,EAAE;QACLmB,YAAY,EAAEpL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;MACpD,CAAE;MAAA5C,QAAA,eAEFrJ,OAAA;QACEoJ,SAAS,EAAC,SAAS;QACnBmB,KAAK,EAAE;UACL2B,QAAQ,EAAE5L,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG3L,MAAM,CAAC2L,UAAU,IAAI,IAAI,GAAG,KAAK,GAAG,QAAQ;UAC1Fd,OAAO,EAAE7K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;QAC/C,CAAE;QAAA5C,QAAA,gBAEFrJ,OAAA;UAAKoJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAE1BrJ,OAAA;YAAAqJ,QAAA,gBACErJ,OAAA;cACEoJ,SAAS,EAAC,8BAA8B;cACxCmB,KAAK,EAAE;gBACLkB,QAAQ,EAAEnL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG3L,MAAM,CAAC2L,UAAU,IAAI,IAAI,GAAG,MAAM,GAAG;cACrF,CAAE;cAAA5C,QAAA,EAED9G,IAAI,CAACoD;YAAI;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACLzJ,OAAA;cACEoJ,SAAS,EAAC,oBAAoB;cAC9BmB,KAAK,EAAE;gBACLkB,QAAQ,EAAEnL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAChD,CAAE;cAAA5C,QAAA,GACH,WACU,EAAC1G,eAAe,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACmB,MAAM;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNzJ,OAAA;YAAKoJ,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eACvCrJ,OAAA;cAAKoJ,SAAS,EAAG,sCACfrG,QAAQ,IAAI,GAAG,GAAG,yBAAyB,GAAG,2BAC/C,EAAE;cACHwH,KAAK,EAAE;gBACLY,OAAO,EAAE7K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,UAAU,GAAG;cACnD,CAAE;cAAA5C,QAAA,gBACArJ,OAAA,CAACP,OAAO;gBACN8K,KAAK,EAAE;kBACLI,KAAK,EAAErK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDrB,MAAM,EAAEtK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFzJ,OAAA;gBACEoJ,SAAS,EAAC,eAAe;gBACzBmB,KAAK,EAAE;kBACLkB,QAAQ,EAAEnL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAChD,CAAE;gBAAA5C,QAAA,EAEDrB,UAAU,CAACjF,QAAQ;cAAC;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzJ,OAAA;UAAKoJ,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrJ,OAAA;YAAKoJ,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDrJ,OAAA;cACEoJ,SAAS,EAAC,2BAA2B;cACrCmB,KAAK,EAAE;gBACLkB,QAAQ,EAAEnL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAChD,CAAE;cAAA5C,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzJ,OAAA;cACEoJ,SAAS,EAAC,yBAAyB;cACnCmB,KAAK,EAAE;gBACLkB,QAAQ,EAAEnL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAChD,CAAE;cAAA5C,QAAA,GAED9E,IAAI,CAAC2B,KAAK,CAAE,CAACvD,eAAe,GAAG,CAAC,IAAIF,SAAS,CAACmB,MAAM,GAAI,GAAG,CAAC,EAAC,GAChE;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzJ,OAAA;YACEoJ,SAAS,EAAC,8CAA8C;YACxDmB,KAAK,EAAE;cACLK,MAAM,EAAEtK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;YAC7C,CAAE;YAAA5C,QAAA,eAEFrJ,OAAA;cACEoJ,SAAS,EAAC,wGAAwG;cAClHmB,KAAK,EAAE;gBACLI,KAAK,EAAG,GAAG,CAAChI,eAAe,GAAG,CAAC,IAAIF,SAAS,CAACmB,MAAM,GAAI,GAAI,GAAE;gBAC7DgH,MAAM,EAAEtK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;gBACjDX,SAAS,EAAE;cACb;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzJ,OAAA;MACEoJ,SAAS,EAAC,SAAS;MACnBmB,KAAK,EAAE;QACL2B,QAAQ,EAAE5L,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG3L,MAAM,CAAC2L,UAAU,IAAI,IAAI,GAAG,KAAK,GAAG,QAAQ;QAC1Fd,OAAO,EAAE7K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;MAChD,CAAE;MAAA5C,QAAA,eAEFrJ,OAAA;QACEoJ,SAAS,EAAC,mFAAmF;QAC7FmB,KAAK,EAAE;UACLY,OAAO,EAAE7K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG3L,MAAM,CAAC2L,UAAU,IAAI,IAAI,GAAG,MAAM,GAAG;QACpF,CAAE;QAAA5C,QAAA,gBAGFrJ,OAAA;UACEuK,KAAK,EAAE;YACLmB,YAAY,EAAEpL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UACpD,CAAE;UAAA5C,QAAA,gBAEFrJ,OAAA;YACEoJ,SAAS,EAAC,qCAAqC;YAC/CmB,KAAK,EAAE;cACLkB,QAAQ,EAAEnL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG3L,MAAM,CAAC2L,UAAU,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM;cACzFP,YAAY,EAAEpL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YACpD,CAAE;YAAA5C,QAAA,EAED,OAAOd,QAAQ,CAAC5C,IAAI,KAAK,QAAQ,GAAG4C,QAAQ,CAAC5C,IAAI,GAAG;UAAU;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,EAEJlB,QAAQ,CAACxC,KAAK,iBACb/F,OAAA;YACEoJ,SAAS,EAAC,uBAAuB;YACjCmB,KAAK,EAAE;cACLmB,YAAY,EAAEpL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACxDd,OAAO,EAAE7K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAC/C,CAAE;YAAA5C,QAAA,eAEFrJ,OAAA;cACEmM,GAAG,EAAE5D,QAAQ,CAACxC,KAAM;cACpBqG,GAAG,EAAC,kBAAkB;cACtBhD,SAAS,EAAC,sDAAsD;cAChEmB,KAAK,EAAE;gBACL8B,SAAS,EAAE/L,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;cAClD,CAAE;cACFK,OAAO,EAAGpC,CAAC,IAAK;gBACdA,CAAC,CAACC,MAAM,CAACI,KAAK,CAACO,OAAO,GAAG,MAAM;gBAC/B;gBACA,MAAMyB,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;gBAC9CF,QAAQ,CAACnD,SAAS,GAAG,gCAAgC;gBACrDmD,QAAQ,CAACG,SAAS,GAAG,+BAA+B;gBACpDxC,CAAC,CAACC,MAAM,CAACwC,UAAU,CAACC,WAAW,CAACL,QAAQ,CAAC;cAC3C;YAAE;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNzJ,OAAA;UACEoJ,SAAS,EAAC,WAAW;UACrBmB,KAAK,EAAE;YACLmB,YAAY,EAAEpL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UACpD,CAAE;UAAA5C,QAAA,EAEDf,mBAAmB,CAAC;QAAC;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAGNzJ,OAAA;UACEoJ,SAAS,EAAC,mBAAmB;UAC7BmB,KAAK,EAAE;YACLsC,aAAa,EAAEvM,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,KAAK;YAC1DjB,cAAc,EAAE1K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG,eAAe;YACrEF,GAAG,EAAEzL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UAC3C,CAAE;UAAA5C,QAAA,gBAEFrJ,OAAA;YACE+J,OAAO,EAAEhC,YAAa;YACtB+E,QAAQ,EAAEnK,eAAe,KAAK,CAAE;YAChCyG,SAAS,EAAG,sEACVzG,eAAe,KAAK,CAAC,GACjB,8CAA8C,GAC9C,6CACL,EAAE;YACH4H,KAAK,EAAE;cACLY,OAAO,EAAE7K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,WAAW;cAC7DR,QAAQ,EAAEnL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACpDtB,KAAK,EAAErK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDjB,cAAc,EAAE;YAClB,CAAE;YAAA3B,QAAA,gBAEFrJ,OAAA,CAACN,WAAW;cACV6K,KAAK,EAAE;gBACLI,KAAK,EAAErK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACjDrB,MAAM,EAAEtK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAC9C;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,YAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERa,cAAc,gBACbtK,OAAA;YACE+J,OAAO,EAAE/F,gBAAiB;YAC1B8I,QAAQ,EAAEzK,UAAW;YACrB+G,SAAS,EAAG,sEACV/G,UAAU,GACN,8CAA8C,GAC9C,4CACL,EAAE;YACHkI,KAAK,EAAE;cACLY,OAAO,EAAE7K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,WAAW;cAC7DR,QAAQ,EAAEnL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACpDtB,KAAK,EAAErK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDjB,cAAc,EAAE;YAClB,CAAE;YAAA3B,QAAA,EAEDhH,UAAU,gBACTrC,OAAA,CAAAE,SAAA;cAAAmJ,QAAA,gBACErJ,OAAA;gBACEoJ,SAAS,EAAC,sEAAsE;gBAChFmB,KAAK,EAAE;kBACLI,KAAK,EAAErK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDrB,MAAM,EAAEtK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,iBAET;YAAA,eAAE,CAAC,gBAEHzJ,OAAA,CAAAE,SAAA;cAAAmJ,QAAA,gBACErJ,OAAA,CAACJ,OAAO;gBACN2K,KAAK,EAAE;kBACLI,KAAK,EAAErK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;kBACjDrB,MAAM,EAAEtK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;gBAC9C;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEJ;YAAA,eAAE;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,gBAETzJ,OAAA;YACE+J,OAAO,EAAEjC,QAAS;YAClBsB,SAAS,EAAC,6GAA6G;YACvHmB,KAAK,EAAE;cACLY,OAAO,EAAE7K,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,WAAW;cAC7DR,QAAQ,EAAEnL,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACpDtB,KAAK,EAAErK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDjB,cAAc,EAAE;YAClB,CAAE;YAAA3B,QAAA,GACH,MAEC,eAAArJ,OAAA,CAACL,YAAY;cACX4K,KAAK,EAAE;gBACLI,KAAK,EAAErK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACjDrB,MAAM,EAAEtK,MAAM,CAAC2L,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAC9C;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3H,EAAA,CA/4BID,QAAQ;EAAA,QACGxC,SAAS,EACPC,WAAW,EACXC,WAAW;AAAA;AAAAwN,EAAA,GAHxBlL,QAAQ;AAi5Bd,eAAeA,QAAQ;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}