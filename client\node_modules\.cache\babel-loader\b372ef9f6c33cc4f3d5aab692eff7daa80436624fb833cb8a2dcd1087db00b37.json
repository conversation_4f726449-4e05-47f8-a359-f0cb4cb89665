{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbSend, TbPaperclip, TbX, TbRobot, Tb<PERSON>ser, TbPhoto, TbLoader } from \"react-icons/tb\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\nimport ContentRenderer from \"../../../components/ContentRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ChatGPTIntegration() {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [prompt, setPrompt] = useState(\"\");\n  const [imageFile, setImageFile] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const textareaRef = useRef(null);\n\n  // Initialize chat with welcome message\n  React.useEffect(() => {\n    // Load cached messages\n    const cachedMessages = localStorage.getItem('chat_messages');\n    if (cachedMessages) {\n      setMessages(JSON.parse(cachedMessages));\n    } else {\n      // Set welcome message\n      setMessages([{\n        role: \"assistant\",\n        content: \"Hello! I'm your AI study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\n      }]);\n    }\n    setIsInitialized(true);\n  }, []);\n\n  // Save messages to cache\n  React.useEffect(() => {\n    if (isInitialized && messages.length > 0) {\n      localStorage.setItem('chat_messages', JSON.stringify(messages));\n    }\n  }, [messages, isInitialized]);\n  const handleChat = async () => {\n    if (!prompt.trim() && !imageFile) return;\n    setIsLoading(true);\n    try {\n      let imageUrl = null;\n\n      // Step 1: Upload the image to the server (if an image is selected)\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const data = await uploadImg(formData);\n        if (data !== null && data !== void 0 && data.success) {\n          imageUrl = data.url; // Extract the S3 URL\n          console.log(\"Image URL: \", imageUrl);\n        } else {\n          throw new Error(\"Image upload failed\");\n        }\n      }\n\n      // Step 2: Construct the ChatGPT message payload\n      const userMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: prompt\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: prompt\n      };\n      const updatedMessages = [...messages, userMessage];\n      setMessages(updatedMessages);\n      setPrompt(\"\");\n\n      // Step 3: Send the payload to ChatGPT\n      const chatPayload = {\n        messages: updatedMessages\n      };\n      const chatRes = await chatWithChatGPT(chatPayload);\n      const apiResponse = chatRes === null || chatRes === void 0 ? void 0 : chatRes.data;\n      console.log(\"API Response: \", apiResponse);\n\n      // Step 4: Append the assistant's response to the conversation\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: apiResponse\n      }]);\n      setImageFile(null);\n    } catch (error) {\n      console.error(\"Error during chat:\", error);\n      alert(\"An error occurred while processing your request. Please try again.\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleKeyPress = event => {\n    if (event.key === \"Enter\") {\n      handleChat(); // Trigger the handleChat function on Enter key\n    }\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-messages\",\n      children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `message ${msg.role === \"user\" ? \"user-message\" : \"assistant-message\"}`,\n        children: /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: msg.role === \"assistant\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: msg !== null && msg !== void 0 && msg.content ? /*#__PURE__*/_jsxDEV(ContentRenderer, {\n              text: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Unable to get a response from AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 21\n            }, this)\n          }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: typeof msg.content === \"string\" ? msg.content : msg.content.map((item, idx) => item.type === \"text\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n              children: item.text\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 25\n            }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n              src: item.image_url.url,\n              alt: \"User content\",\n              style: {\n                height: \"100px\"\n              }\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 25\n            }, this))\n          }, void 0, false)\n        }, void 0, false)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-indicator\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 23\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-input-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n        className: \"chat-input\",\n        placeholder: \"Type your message here...\",\n        value: prompt,\n        onChange: e => setPrompt(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"file\",\n        accept: \"image/*\",\n        onChange: e => setImageFile(e.target.files[0]),\n        style: {\n          width: \"200px\",\n          borderRadius: \"5px\",\n          marginRight: \"10px\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        disabled: isLoading,\n        className: \"send-button\",\n        onClick: handleChat,\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 111,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatGPTIntegration, \"p9dta6n+AZBE8h5almoP/gf8elA=\");\n_c = ChatGPTIntegration;\nexport default ChatGPTIntegration;\nvar _c;\n$RefreshReg$(_c, \"ChatGPTIntegration\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "TbSend", "TbPaperclip", "TbX", "TbRobot", "TbUser", "TbPhoto", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatWithChatGPT", "uploadImg", "Content<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ChatGPTIntegration", "_s", "messages", "setMessages", "prompt", "setPrompt", "imageFile", "setImageFile", "imagePreview", "setImagePreview", "isLoading", "setIsLoading", "isInitialized", "setIsInitialized", "isTyping", "setIsTyping", "messagesEndRef", "fileInputRef", "textareaRef", "cachedMessages", "localStorage", "getItem", "JSON", "parse", "role", "content", "length", "setItem", "stringify", "handleChat", "trim", "imageUrl", "formData", "FormData", "append", "data", "success", "url", "console", "log", "Error", "userMessage", "type", "text", "image_url", "updatedMessages", "chatPayload", "chatRes", "apiResponse", "prev", "error", "alert", "handleKeyPress", "event", "key", "className", "children", "map", "msg", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "item", "idx", "src", "alt", "style", "height", "placeholder", "value", "onChange", "e", "target", "accept", "files", "width", "borderRadius", "marginRight", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { TbSend, TbPaperclip, TbX, Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON>b<PERSON>oa<PERSON> } from \"react-icons/tb\";\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nfunction ChatGPTIntegration() {\r\n  const [messages, setMessages] = useState([]);\r\n  const [prompt, setPrompt] = useState(\"\");\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [isTyping, setIsTyping] = useState(false);\r\n\r\n  const messagesEndRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const textareaRef = useRef(null);\r\n\r\n  // Initialize chat with welcome message\r\n  React.useEffect(() => {\r\n    // Load cached messages\r\n    const cachedMessages = localStorage.getItem('chat_messages');\r\n    if (cachedMessages) {\r\n      setMessages(JSON.parse(cachedMessages));\r\n    } else {\r\n      // Set welcome message\r\n      setMessages([{\r\n        role: \"assistant\",\r\n        content: \"Hello! I'm your AI study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\r\n      }]);\r\n    }\r\n    setIsInitialized(true);\r\n  }, []);\r\n\r\n  // Save messages to cache\r\n  React.useEffect(() => {\r\n    if (isInitialized && messages.length > 0) {\r\n      localStorage.setItem('chat_messages', JSON.stringify(messages));\r\n    }\r\n  }, [messages, isInitialized]);\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: prompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: prompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      setMessages(updatedMessages);\r\n      setPrompt(\"\");\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: apiResponse },\r\n      ]);\r\n\r\n      setImageFile(null);\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      alert(\"An error occurred while processing your request. Please try again.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (event) => {\r\n    if (event.key === \"Enter\") {\r\n      handleChat(); // Trigger the handleChat function on Enter key\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"chat-container\">\r\n      {/* Chat messages */}\r\n      <div className=\"chat-messages\">\r\n        {messages.map((msg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`message ${msg.role === \"user\" ? \"user-message\" : \"assistant-message\"\r\n              }`}\r\n          >\r\n            <>\r\n              {msg.role === \"assistant\" ? (\r\n                <>\r\n                  {msg?.content ? (\r\n                    <ContentRenderer text={msg.content} />\r\n                  ) : (\r\n                    <p>Unable to get a response from AI</p>\r\n                  )}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {typeof msg.content === \"string\"\r\n                    ? msg.content\r\n                    : msg.content.map((item, idx) =>\r\n                      item.type === \"text\" ? (\r\n                        <p key={idx}>{item.text}</p>\r\n                      ) : (\r\n                        <img\r\n                          key={idx}\r\n                          src={item.image_url.url}\r\n                          alt=\"User content\"\r\n                          style={{ height: \"100px\" }}\r\n                        />\r\n                      )\r\n                    )}\r\n                </>\r\n              )}\r\n            </>\r\n          </div>\r\n        ))}\r\n        {isLoading && <div className=\"loading-indicator\">Loading...</div>}\r\n      </div>\r\n\r\n      {/* Input and upload */}\r\n      <div className=\"chat-input-container\">\r\n        <textarea\r\n          className=\"chat-input\"\r\n          placeholder=\"Type your message here...\"\r\n          value={prompt}\r\n          onChange={(e) => setPrompt(e.target.value)}\r\n        ></textarea>\r\n        <input\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={(e) => setImageFile(e.target.files[0])}\r\n          style={{ width: \"200px\", borderRadius: \"5px\", marginRight: \"10px\" }}\r\n        />\r\n        <button\r\n          disabled={isLoading}\r\n          className=\"send-button\"\r\n          onClick={handleChat}\r\n        >\r\n          Send\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,MAAM,EAAEC,WAAW,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC7F,SAASC,eAAe,EAAEC,SAAS,QAAQ,wBAAwB;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAElE,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMmC,cAAc,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMmC,YAAY,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMoC,WAAW,GAAGpC,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACAF,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB;IACA,MAAMoC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC5D,IAAIF,cAAc,EAAE;MAClBhB,WAAW,CAACmB,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC,CAAC;IACzC,CAAC,MAAM;MACL;MACAhB,WAAW,CAAC,CAAC;QACXqB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL;IACAZ,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjC,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,IAAI6B,aAAa,IAAIV,QAAQ,CAACwB,MAAM,GAAG,CAAC,EAAE;MACxCN,YAAY,CAACO,OAAO,CAAC,eAAe,EAAEL,IAAI,CAACM,SAAS,CAAC1B,QAAQ,CAAC,CAAC;IACjE;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEU,aAAa,CAAC,CAAC;EAE7B,MAAMiB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACzB,MAAM,CAAC0B,IAAI,CAAC,CAAC,IAAI,CAACxB,SAAS,EAAE;IAElCK,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,IAAIoB,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAIzB,SAAS,EAAE;QACb,MAAM0B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE5B,SAAS,CAAC;QAEnC,MAAM6B,IAAI,GAAG,MAAMzC,SAAS,CAACsC,QAAQ,CAAC;QAEtC,IAAIG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,OAAO,EAAE;UACjBL,QAAQ,GAAGI,IAAI,CAACE,GAAG,CAAC,CAAC;UACrBC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAER,QAAQ,CAAC;QACtC,CAAC,MAAM;UACL,MAAM,IAAIS,KAAK,CAAC,qBAAqB,CAAC;QACxC;MACF;;MAEA;MACA,MAAMC,WAAW,GAAGV,QAAQ,GACxB;QACAP,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEiB,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAEvC;QAAO,CAAC,EAC9B;UAAEsC,IAAI,EAAE,WAAW;UAAEE,SAAS,EAAE;YAAEP,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACC;QAAEP,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAErB;MAAO,CAAC;MAErC,MAAMyC,eAAe,GAAG,CAAC,GAAG3C,QAAQ,EAAEuC,WAAW,CAAC;MAClDtC,WAAW,CAAC0C,eAAe,CAAC;MAC5BxC,SAAS,CAAC,EAAE,CAAC;;MAEb;MACA,MAAMyC,WAAW,GAAG;QAAE5C,QAAQ,EAAE2C;MAAgB,CAAC;MAEjD,MAAME,OAAO,GAAG,MAAMtD,eAAe,CAACqD,WAAW,CAAC;MAElD,MAAME,WAAW,GAAGD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEZ,IAAI;MACjCG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAES,WAAW,CAAC;;MAE1C;MACA7C,WAAW,CAAE8C,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEzB,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAEuB;MAAY,CAAC,CAC5C,CAAC;MAEFzC,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CC,KAAK,CAAC,oEAAoE,CAAC;IAC7E,CAAC,SAAS;MACRxC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMyC,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzBzB,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED,oBACEhC,OAAA;IAAK0D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAE7B3D,OAAA;MAAK0D,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3BtD,QAAQ,CAACuD,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvB9D,OAAA;QAEE0D,SAAS,EAAG,WAAUG,GAAG,CAAClC,IAAI,KAAK,MAAM,GAAG,cAAc,GAAG,mBAC1D,EAAE;QAAAgC,QAAA,eAEL3D,OAAA,CAAAE,SAAA;UAAAyD,QAAA,EACGE,GAAG,CAAClC,IAAI,KAAK,WAAW,gBACvB3B,OAAA,CAAAE,SAAA;YAAAyD,QAAA,EACGE,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEjC,OAAO,gBACX5B,OAAA,CAACF,eAAe;cAACgD,IAAI,EAAEe,GAAG,CAACjC;YAAQ;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEtClE,OAAA;cAAA2D,QAAA,EAAG;YAAgC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACvC,gBACD,CAAC,gBAEHlE,OAAA,CAAAE,SAAA;YAAAyD,QAAA,EACG,OAAOE,GAAG,CAACjC,OAAO,KAAK,QAAQ,GAC5BiC,GAAG,CAACjC,OAAO,GACXiC,GAAG,CAACjC,OAAO,CAACgC,GAAG,CAAC,CAACO,IAAI,EAAEC,GAAG,KAC1BD,IAAI,CAACtB,IAAI,KAAK,MAAM,gBAClB7C,OAAA;cAAA2D,QAAA,EAAcQ,IAAI,CAACrB;YAAI,GAAfsB,GAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC,gBAE5BlE,OAAA;cAEEqE,GAAG,EAAEF,IAAI,CAACpB,SAAS,CAACP,GAAI;cACxB8B,GAAG,EAAC,cAAc;cAClBC,KAAK,EAAE;gBAAEC,MAAM,EAAE;cAAQ;YAAE,GAHtBJ,GAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIT,CAEL;UAAC,gBACH;QACH,gBACD;MAAC,GA/BEJ,KAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgCP,CACN,CAAC,EACDrD,SAAS,iBAAIb,OAAA;QAAK0D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAU;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eAGNlE,OAAA;MAAK0D,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC3D,OAAA;QACE0D,SAAS,EAAC,YAAY;QACtBe,WAAW,EAAC,2BAA2B;QACvCC,KAAK,EAAEnE,MAAO;QACdoE,QAAQ,EAAGC,CAAC,IAAKpE,SAAS,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACZlE,OAAA;QACE6C,IAAI,EAAC,MAAM;QACXiC,MAAM,EAAC,SAAS;QAChBH,QAAQ,EAAGC,CAAC,IAAKlE,YAAY,CAACkE,CAAC,CAACC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAE;QACjDR,KAAK,EAAE;UAAES,KAAK,EAAE,OAAO;UAAEC,YAAY,EAAE,KAAK;UAAEC,WAAW,EAAE;QAAO;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eACFlE,OAAA;QACEmF,QAAQ,EAAEtE,SAAU;QACpB6C,SAAS,EAAC,aAAa;QACvB0B,OAAO,EAAEpD,UAAW;QAAA2B,QAAA,EACrB;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9D,EAAA,CA1KQD,kBAAkB;AAAAkF,EAAA,GAAlBlF,kBAAkB;AA4K3B,eAAeA,kBAAkB;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}