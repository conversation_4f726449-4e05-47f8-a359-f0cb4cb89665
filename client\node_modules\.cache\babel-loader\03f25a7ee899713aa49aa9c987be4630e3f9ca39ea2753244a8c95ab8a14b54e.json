{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\AdminReports\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useNavigate } from \"react-router-dom\";\nimport { TbDashboard, TbChartBar, TbUsers, TbTarget, TbTrendingUp, TbDownload, TbFilter, TbEye, TbCheck, TbX, TbCalendar, TbClock, TbFileText } from \"react-icons/tb\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { message, Table, Card, Statistic, Input, Select, DatePicker, Button, Tag, Progress } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReports } from \"../../../apicalls/reports\";\nimport moment from \"moment\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  Search\n} = Input;\nfunction AdminReports() {\n  _s();\n  const navigate = useNavigate();\n  const [reportsData, setReportsData] = useState([]);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [stats, setStats] = useState({\n    totalReports: 0,\n    totalStudents: 0,\n    averageScore: 0,\n    passRate: 0,\n    totalExams: 0,\n    activeToday: 0\n  });\n  const dispatch = useDispatch();\n  const [filters, setFilters] = useState({\n    examName: \"\",\n    userName: \"\",\n    verdict: \"\",\n    dateRange: null\n  });\n  const calculateStats = data => {\n    if (!data || data.length === 0) return;\n    const totalReports = data.length;\n    const uniqueStudents = new Set(data.map(report => {\n      var _report$user;\n      return (_report$user = report.user) === null || _report$user === void 0 ? void 0 : _report$user._id;\n    })).size;\n    const passedReports = data.filter(report => {\n      var _report$result;\n      return ((_report$result = report.result) === null || _report$result === void 0 ? void 0 : _report$result.verdict) === 'Pass';\n    }).length;\n    const passRate = totalReports > 0 ? Math.round(passedReports / totalReports * 100) : 0;\n    const scores = data.map(report => {\n      var _report$result2, _report$result2$corre, _report$exam;\n      const obtained = ((_report$result2 = report.result) === null || _report$result2 === void 0 ? void 0 : (_report$result2$corre = _report$result2.correctAnswers) === null || _report$result2$corre === void 0 ? void 0 : _report$result2$corre.length) || 0;\n      const total = ((_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam.totalMarks) || 1;\n      return obtained / total * 100;\n    });\n    const averageScore = scores.length > 0 ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length) : 0;\n    const uniqueExams = new Set(data.map(report => {\n      var _report$exam2;\n      return (_report$exam2 = report.exam) === null || _report$exam2 === void 0 ? void 0 : _report$exam2._id;\n    })).size;\n    const today = moment().startOf('day');\n    const activeToday = data.filter(report => moment(report.createdAt).isSame(today, 'day')).length;\n    setStats({\n      totalReports,\n      totalStudents: uniqueStudents,\n      averageScore,\n      passRate,\n      totalExams: uniqueExams,\n      activeToday\n    });\n  };\n  const columns = [{\n    title: \"Student\",\n    dataIndex: \"userName\",\n    render: (text, record) => {\n      var _record$user, _record$user2;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(TbUsers, {\n            className: \"w-4 h-4 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-gray-900\",\n            children: ((_record$user = record.user) === null || _record$user === void 0 ? void 0 : _record$user.name) || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: ((_record$user2 = record.user) === null || _record$user2 === void 0 ? void 0 : _record$user2.email) || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this);\n    },\n    width: 200\n  }, {\n    title: \"Exam\",\n    dataIndex: \"examName\",\n    render: (text, record) => {\n      var _record$exam, _record$exam2;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"font-medium text-gray-900\",\n          children: ((_record$exam = record.exam) === null || _record$exam === void 0 ? void 0 : _record$exam.name) || 'N/A'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: ((_record$exam2 = record.exam) === null || _record$exam2 === void 0 ? void 0 : _record$exam2.subject) || 'General'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this);\n    },\n    width: 200\n  }, {\n    title: \"Date & Time\",\n    dataIndex: \"date\",\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(TbCalendar, {\n        className: \"w-4 h-4 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-medium\",\n          children: moment(record.createdAt).format(\"MMM DD, YYYY\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-500\",\n          children: moment(record.createdAt).format(\"HH:mm\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 9\n    }, this),\n    width: 150\n  }, {\n    title: \"Score\",\n    dataIndex: \"score\",\n    render: (text, record) => {\n      var _record$result, _record$result$correc, _record$exam3;\n      const obtained = ((_record$result = record.result) === null || _record$result === void 0 ? void 0 : (_record$result$correc = _record$result.correctAnswers) === null || _record$result$correc === void 0 ? void 0 : _record$result$correc.length) || 0;\n      const total = ((_record$exam3 = record.exam) === null || _record$exam3 === void 0 ? void 0 : _record$exam3.totalMarks) || 1;\n      const percentage = Math.round(obtained / total * 100);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-gray-900\",\n          children: [obtained, \"/\", total]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: percentage,\n          size: \"small\",\n          strokeColor: percentage >= 60 ? '#10b981' : '#ef4444',\n          showInfo: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-sm font-medium ${percentage >= 60 ? 'text-green-600' : 'text-red-600'}`,\n          children: [percentage, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this);\n    },\n    width: 120\n  }, {\n    title: \"Result\",\n    dataIndex: \"verdict\",\n    render: (text, record) => {\n      var _record$result2;\n      const verdict = (_record$result2 = record.result) === null || _record$result2 === void 0 ? void 0 : _record$result2.verdict;\n      const isPassed = verdict === 'Pass';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        icon: isPassed ? /*#__PURE__*/_jsxDEV(TbCheck, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 44\n        }, this),\n        color: isPassed ? 'success' : 'error',\n        className: \"font-medium\",\n        children: verdict || 'N/A'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this);\n    },\n    width: 100\n  }, {\n    title: \"Actions\",\n    key: \"actions\",\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(TbEye, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this),\n      onClick: () => {/* Handle view details */},\n      className: \"bg-blue-500 hover:bg-blue-600\",\n      children: \"View\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 9\n    }, this),\n    width: 80\n  }];\n  const getData = async (tempFilters, page = 1, limit = 10) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReports({\n        ...tempFilters,\n        page,\n        limit\n      });\n      if (response.success) {\n        setReportsData(response.data);\n        calculateStats(response.data);\n        setPagination({\n          ...pagination,\n          current: page,\n          total: response.pagination.totalReports\n        });\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const handleSearch = (value, field) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const handleDateRangeChange = dates => {\n    setFilters(prev => ({\n      ...prev,\n      dateRange: dates\n    }));\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const clearFilters = () => {\n    setFilters({\n      examName: \"\",\n      userName: \"\",\n      verdict: \"\",\n      dateRange: null\n    });\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  useEffect(() => {\n    getData(filters, pagination.current, pagination.pageSize);\n  }, [filters, pagination.current]);\n  const handleTableChange = pagination => {\n    getData(filters, pagination.current, pagination.pageSize);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      title: \"Admin Reports\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n            className: \"w-8 h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: [\"Student \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 21\n          }, this), \" Analytics\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n          children: \"Comprehensive insights into student performance and exam analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbFileText, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Total Reports\",\n              value: stats.totalReports,\n              valueStyle: {\n                color: '#1e40af',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Active Students\",\n              value: stats.totalStudents,\n              valueStyle: {\n                color: '#059669',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbTrendingUp, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Average Score\",\n              value: stats.averageScore,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#7c3aed',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Pass Rate\",\n              value: stats.passRate,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#ea580c',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbFileText, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Total Exams\",\n              value: stats.totalExams,\n              valueStyle: {\n                color: '#db2777',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-indigo-50 to-indigo-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Today's Activity\",\n              value: stats.activeToday,\n              valueStyle: {\n                color: '#4338ca',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbFilter, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Filter Reports\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              placeholder: \"Search by exam name\",\n              value: filters.examName,\n              onChange: e => handleSearch(e.target.value, 'examName'),\n              className: \"w-full sm:w-48\",\n              size: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Search, {\n              placeholder: \"Search by student name\",\n              value: filters.userName,\n              onChange: e => handleSearch(e.target.value, 'userName'),\n              className: \"w-full sm:w-48\",\n              size: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Result\",\n              value: filters.verdict,\n              onChange: value => handleSearch(value, 'verdict'),\n              className: \"w-full sm:w-48\",\n              size: \"large\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"\",\n                children: \"All Results\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Pass\",\n                children: \"Passed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Fail\",\n                children: \"Failed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n              value: filters.dateRange,\n              onChange: handleDateRangeChange,\n              className: \"w-full sm:w-64\",\n              size: \"large\",\n              placeholder: ['Start Date', 'End Date']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: clearFilters,\n              size: \"large\",\n              className: \"w-full sm:w-auto\",\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 23\n              }, this),\n              size: \"large\",\n              className: \"w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 border-none\",\n              children: \"Export\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          columns: columns,\n          dataSource: reportsData,\n          pagination: {\n            current: pagination.current,\n            pageSize: pagination.pageSize,\n            total: pagination.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} reports`,\n            className: \"px-6 py-4\"\n          },\n          onChange: handleTableChange,\n          rowKey: record => record._id,\n          scroll: {\n            x: 1200\n          },\n          className: \"modern-table\",\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 248,\n    columnNumber: 5\n  }, this);\n}\n_s(AdminReports, \"JSVZRJHBn/g1rqCtBSZVtzeib1Y=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = AdminReports;\nexport default AdminReports;\nvar _c;\n$RefreshReg$(_c, \"AdminReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useNavigate", "TbDashboard", "TbChartBar", "TbUsers", "TbTarget", "TbTrendingUp", "TbDownload", "Tb<PERSON><PERSON>er", "TbEye", "TbCheck", "TbX", "TbCalendar", "TbClock", "TbFileText", "Page<PERSON><PERSON>le", "message", "Table", "Card", "Statistic", "Input", "Select", "DatePicker", "<PERSON><PERSON>", "Tag", "Progress", "useDispatch", "HideLoading", "ShowLoading", "getAllReports", "moment", "jsxDEV", "_jsxDEV", "Option", "RangePicker", "Search", "AdminReports", "_s", "navigate", "reportsData", "setReportsData", "pagination", "setPagination", "current", "pageSize", "total", "stats", "setStats", "totalReports", "totalStudents", "averageScore", "passRate", "totalExams", "activeToday", "dispatch", "filters", "setFilters", "examName", "userName", "verdict", "date<PERSON><PERSON><PERSON>", "calculateStats", "data", "length", "uniqueStudents", "Set", "map", "report", "_report$user", "user", "_id", "size", "passedReports", "filter", "_report$result", "result", "Math", "round", "scores", "_report$result2", "_report$result2$corre", "_report$exam", "obtained", "correctAnswers", "exam", "totalMarks", "reduce", "sum", "score", "uniqueExams", "_report$exam2", "today", "startOf", "createdAt", "isSame", "columns", "title", "dataIndex", "render", "text", "record", "_record$user", "_record$user2", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "email", "width", "_record$exam", "_record$exam2", "subject", "format", "_record$result", "_record$result$correc", "_record$exam3", "percentage", "percent", "strokeColor", "showInfo", "_record$result2", "isPassed", "icon", "color", "key", "type", "onClick", "getData", "tempFilters", "page", "limit", "response", "success", "error", "handleSearch", "value", "field", "prev", "handleDateRangeChange", "dates", "clearFilters", "handleTableChange", "div", "initial", "opacity", "y", "animate", "transition", "delay", "valueStyle", "fontSize", "fontWeight", "suffix", "placeholder", "onChange", "e", "target", "dataSource", "showSizeChanger", "showQuickJumper", "showTotal", "range", "<PERSON><PERSON><PERSON>", "scroll", "x", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/AdminReports/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport {\r\n  TbDashboard,\r\n  TbChartBar,\r\n  TbUsers,\r\n  TbTarget,\r\n  TbTrendingUp,\r\n  TbDownload,\r\n  TbFilter,\r\n  TbEye,\r\n  TbCheck,\r\n  TbX,\r\n  TbCalendar,\r\n  TbClock,\r\n  TbFileText\r\n} from \"react-icons/tb\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Table, Card, Statistic, Input, Select, DatePicker, Button, Tag, Progress } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReports } from \"../../../apicalls/reports\";\r\nimport moment from \"moment\";\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\nconst { Search } = Input;\r\n\r\nfunction AdminReports() {\r\n  const navigate = useNavigate();\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [pagination, setPagination] = useState({\r\n    current: 1,\r\n    pageSize: 10,\r\n    total: 0,\r\n  });\r\n  const [stats, setStats] = useState({\r\n    totalReports: 0,\r\n    totalStudents: 0,\r\n    averageScore: 0,\r\n    passRate: 0,\r\n    totalExams: 0,\r\n    activeToday: 0\r\n  });\r\n  const dispatch = useDispatch();\r\n  const [filters, setFilters] = useState({\r\n    examName: \"\",\r\n    userName: \"\",\r\n    verdict: \"\",\r\n    dateRange: null\r\n  });\r\n\r\n  const calculateStats = (data) => {\r\n    if (!data || data.length === 0) return;\r\n\r\n    const totalReports = data.length;\r\n    const uniqueStudents = new Set(data.map(report => report.user?._id)).size;\r\n    const passedReports = data.filter(report => report.result?.verdict === 'Pass').length;\r\n    const passRate = totalReports > 0 ? Math.round((passedReports / totalReports) * 100) : 0;\r\n\r\n    const scores = data.map(report => {\r\n      const obtained = report.result?.correctAnswers?.length || 0;\r\n      const total = report.exam?.totalMarks || 1;\r\n      return (obtained / total) * 100;\r\n    });\r\n\r\n    const averageScore = scores.length > 0 ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length) : 0;\r\n    const uniqueExams = new Set(data.map(report => report.exam?._id)).size;\r\n    const today = moment().startOf('day');\r\n    const activeToday = data.filter(report => moment(report.createdAt).isSame(today, 'day')).length;\r\n\r\n    setStats({\r\n      totalReports,\r\n      totalStudents: uniqueStudents,\r\n      averageScore,\r\n      passRate,\r\n      totalExams: uniqueExams,\r\n      activeToday\r\n    });\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Student\",\r\n      dataIndex: \"userName\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center space-x-3\">\r\n          <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n            <TbUsers className=\"w-4 h-4 text-blue-600\" />\r\n          </div>\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{record.user?.name || 'N/A'}</div>\r\n            <div className=\"text-sm text-gray-500\">{record.user?.email || ''}</div>\r\n          </div>\r\n        </div>\r\n      ),\r\n      width: 200,\r\n    },\r\n    {\r\n      title: \"Exam\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => (\r\n        <div>\r\n          <div className=\"font-medium text-gray-900\">{record.exam?.name || 'N/A'}</div>\r\n          <div className=\"text-sm text-gray-500\">{record.exam?.subject || 'General'}</div>\r\n        </div>\r\n      ),\r\n      width: 200,\r\n    },\r\n    {\r\n      title: \"Date & Time\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <TbCalendar className=\"w-4 h-4 text-gray-400\" />\r\n          <div>\r\n            <div className=\"text-sm font-medium\">{moment(record.createdAt).format(\"MMM DD, YYYY\")}</div>\r\n            <div className=\"text-xs text-gray-500\">{moment(record.createdAt).format(\"HH:mm\")}</div>\r\n          </div>\r\n        </div>\r\n      ),\r\n      width: 150,\r\n    },\r\n    {\r\n      title: \"Score\",\r\n      dataIndex: \"score\",\r\n      render: (text, record) => {\r\n        const obtained = record.result?.correctAnswers?.length || 0;\r\n        const total = record.exam?.totalMarks || 1;\r\n        const percentage = Math.round((obtained / total) * 100);\r\n\r\n        return (\r\n          <div className=\"text-center\">\r\n            <div className=\"text-lg font-bold text-gray-900\">{obtained}/{total}</div>\r\n            <Progress\r\n              percent={percentage}\r\n              size=\"small\"\r\n              strokeColor={percentage >= 60 ? '#10b981' : '#ef4444'}\r\n              showInfo={false}\r\n            />\r\n            <div className={`text-sm font-medium ${percentage >= 60 ? 'text-green-600' : 'text-red-600'}`}>\r\n              {percentage}%\r\n            </div>\r\n          </div>\r\n        );\r\n      },\r\n      width: 120,\r\n    },\r\n    {\r\n      title: \"Result\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => {\r\n        const verdict = record.result?.verdict;\r\n        const isPassed = verdict === 'Pass';\r\n\r\n        return (\r\n          <Tag\r\n            icon={isPassed ? <TbCheck /> : <TbX />}\r\n            color={isPassed ? 'success' : 'error'}\r\n            className=\"font-medium\"\r\n          >\r\n            {verdict || 'N/A'}\r\n          </Tag>\r\n        );\r\n      },\r\n      width: 100,\r\n    },\r\n    {\r\n      title: \"Actions\",\r\n      key: \"actions\",\r\n      render: (text, record) => (\r\n        <Button\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          icon={<TbEye />}\r\n          onClick={() => {/* Handle view details */}}\r\n          className=\"bg-blue-500 hover:bg-blue-600\"\r\n        >\r\n          View\r\n        </Button>\r\n      ),\r\n      width: 80,\r\n    },\r\n  ];\r\n\r\n  const getData = async (tempFilters, page = 1, limit = 10) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReports({\r\n        ...tempFilters,\r\n        page,\r\n        limit,\r\n      });\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        calculateStats(response.data);\r\n        setPagination({\r\n          ...pagination,\r\n          current: page,\r\n          total: response.pagination.totalReports,\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleSearch = (value, field) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n  };\r\n\r\n  const handleDateRangeChange = (dates) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      dateRange: dates\r\n    }));\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    setFilters({\r\n      examName: \"\",\r\n      userName: \"\",\r\n      verdict: \"\",\r\n      dateRange: null\r\n    });\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  }, [filters, pagination.current]);\r\n\r\n  const handleTableChange = (pagination) => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <PageTitle title=\"Admin Reports\" />\r\n\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"text-center mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\">\r\n            <TbChartBar className=\"w-8 h-8 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\r\n            Student <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Performance</span> Analytics\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\r\n            Comprehensive insights into student performance and exam analytics\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Stats Cards */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8\"\r\n        >\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbFileText className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Total Reports\"\r\n                value={stats.totalReports}\r\n                valueStyle={{ color: '#1e40af', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbUsers className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Active Students\"\r\n                value={stats.totalStudents}\r\n                valueStyle={{ color: '#059669', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbTrendingUp className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Average Score\"\r\n                value={stats.averageScore}\r\n                suffix=\"%\"\r\n                valueStyle={{ color: '#7c3aed', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbTarget className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Pass Rate\"\r\n                value={stats.passRate}\r\n                suffix=\"%\"\r\n                valueStyle={{ color: '#ea580c', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbFileText className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Total Exams\"\r\n                value={stats.totalExams}\r\n                valueStyle={{ color: '#db2777', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-indigo-50 to-indigo-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbClock className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Today's Activity\"\r\n                value={stats.activeToday}\r\n                valueStyle={{ color: '#4338ca', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n        </motion.div>\r\n\r\n        {/* Filters Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100\"\r\n        >\r\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <TbFilter className=\"w-5 h-5 text-gray-600\" />\r\n              <h3 className=\"text-lg font-semibold text-gray-900\">Filter Reports</h3>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <Search\r\n                placeholder=\"Search by exam name\"\r\n                value={filters.examName}\r\n                onChange={(e) => handleSearch(e.target.value, 'examName')}\r\n                className=\"w-full sm:w-48\"\r\n                size=\"large\"\r\n              />\r\n\r\n              <Search\r\n                placeholder=\"Search by student name\"\r\n                value={filters.userName}\r\n                onChange={(e) => handleSearch(e.target.value, 'userName')}\r\n                className=\"w-full sm:w-48\"\r\n                size=\"large\"\r\n              />\r\n\r\n              <Select\r\n                placeholder=\"Select Result\"\r\n                value={filters.verdict}\r\n                onChange={(value) => handleSearch(value, 'verdict')}\r\n                className=\"w-full sm:w-48\"\r\n                size=\"large\"\r\n              >\r\n                <Option value=\"\">All Results</Option>\r\n                <Option value=\"Pass\">Passed</Option>\r\n                <Option value=\"Fail\">Failed</Option>\r\n              </Select>\r\n\r\n              <RangePicker\r\n                value={filters.dateRange}\r\n                onChange={handleDateRangeChange}\r\n                className=\"w-full sm:w-64\"\r\n                size=\"large\"\r\n                placeholder={['Start Date', 'End Date']}\r\n              />\r\n\r\n              <Button\r\n                onClick={clearFilters}\r\n                size=\"large\"\r\n                className=\"w-full sm:w-auto\"\r\n              >\r\n                Clear Filters\r\n              </Button>\r\n\r\n              <Button\r\n                type=\"primary\"\r\n                icon={<TbDownload />}\r\n                size=\"large\"\r\n                className=\"w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 border-none\"\r\n              >\r\n                Export\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Reports Table */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\"\r\n        >\r\n          <Table\r\n            columns={columns}\r\n            dataSource={reportsData}\r\n            pagination={{\r\n              current: pagination.current,\r\n              pageSize: pagination.pageSize,\r\n              total: pagination.total,\r\n              showSizeChanger: true,\r\n              showQuickJumper: true,\r\n              showTotal: (total, range) =>\r\n                `${range[0]}-${range[1]} of ${total} reports`,\r\n              className: \"px-6 py-4\"\r\n            }}\r\n            onChange={handleTableChange}\r\n            rowKey={(record) => record._id}\r\n            scroll={{ x: 1200 }}\r\n            className=\"modern-table\"\r\n            size=\"large\"\r\n          />\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AdminReports;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,UAAU,QACL,gBAAgB;AACvB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,OAAO,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,MAAM;AACxG,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAM;EAAEC;AAAO,CAAC,GAAGZ,MAAM;AACzB,MAAM;EAAEa;AAAY,CAAC,GAAGZ,UAAU;AAClC,MAAM;EAAEa;AAAO,CAAC,GAAGf,KAAK;AAExB,SAASgB,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC;IAC3C6C,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC;IACjCkD,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC;IACrC2D,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAIC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IAEhC,MAAMf,YAAY,GAAGc,IAAI,CAACC,MAAM;IAChC,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAACH,IAAI,CAACI,GAAG,CAACC,MAAM;MAAA,IAAAC,YAAA;MAAA,QAAAA,YAAA,GAAID,MAAM,CAACE,IAAI,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,GAAG;IAAA,EAAC,CAAC,CAACC,IAAI;IACzE,MAAMC,aAAa,GAAGV,IAAI,CAACW,MAAM,CAACN,MAAM;MAAA,IAAAO,cAAA;MAAA,OAAI,EAAAA,cAAA,GAAAP,MAAM,CAACQ,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAef,OAAO,MAAK,MAAM;IAAA,EAAC,CAACI,MAAM;IACrF,MAAMZ,QAAQ,GAAGH,YAAY,GAAG,CAAC,GAAG4B,IAAI,CAACC,KAAK,CAAEL,aAAa,GAAGxB,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC;IAExF,MAAM8B,MAAM,GAAGhB,IAAI,CAACI,GAAG,CAACC,MAAM,IAAI;MAAA,IAAAY,eAAA,EAAAC,qBAAA,EAAAC,YAAA;MAChC,MAAMC,QAAQ,GAAG,EAAAH,eAAA,GAAAZ,MAAM,CAACQ,MAAM,cAAAI,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAeI,cAAc,cAAAH,qBAAA,uBAA7BA,qBAAA,CAA+BjB,MAAM,KAAI,CAAC;MAC3D,MAAMlB,KAAK,GAAG,EAAAoC,YAAA,GAAAd,MAAM,CAACiB,IAAI,cAAAH,YAAA,uBAAXA,YAAA,CAAaI,UAAU,KAAI,CAAC;MAC1C,OAAQH,QAAQ,GAAGrC,KAAK,GAAI,GAAG;IACjC,CAAC,CAAC;IAEF,MAAMK,YAAY,GAAG4B,MAAM,CAACf,MAAM,GAAG,CAAC,GAAGa,IAAI,CAACC,KAAK,CAACC,MAAM,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGV,MAAM,CAACf,MAAM,CAAC,GAAG,CAAC;IACtH,MAAM0B,WAAW,GAAG,IAAIxB,GAAG,CAACH,IAAI,CAACI,GAAG,CAACC,MAAM;MAAA,IAAAuB,aAAA;MAAA,QAAAA,aAAA,GAAIvB,MAAM,CAACiB,IAAI,cAAAM,aAAA,uBAAXA,aAAA,CAAapB,GAAG;IAAA,EAAC,CAAC,CAACC,IAAI;IACtE,MAAMoB,KAAK,GAAG7D,MAAM,CAAC,CAAC,CAAC8D,OAAO,CAAC,KAAK,CAAC;IACrC,MAAMvC,WAAW,GAAGS,IAAI,CAACW,MAAM,CAACN,MAAM,IAAIrC,MAAM,CAACqC,MAAM,CAAC0B,SAAS,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC5B,MAAM;IAE/FhB,QAAQ,CAAC;MACPC,YAAY;MACZC,aAAa,EAAEe,cAAc;MAC7Bd,YAAY;MACZC,QAAQ;MACRC,UAAU,EAAEqC,WAAW;MACvBpC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM0C,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,UAAU;IACrBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAC,YAAA,EAAAC,aAAA;MAAA,oBACnBtE,OAAA;QAAKuE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CxE,OAAA;UAAKuE,SAAS,EAAC,mEAAmE;UAAAC,QAAA,eAChFxE,OAAA,CAAC5B,OAAO;YAACmG,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACN5E,OAAA;UAAAwE,QAAA,gBACExE,OAAA;YAAKuE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAE,EAAAH,YAAA,GAAAD,MAAM,CAAC/B,IAAI,cAAAgC,YAAA,uBAAXA,YAAA,CAAaQ,IAAI,KAAI;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7E5E,OAAA;YAAKuE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAE,EAAAF,aAAA,GAAAF,MAAM,CAAC/B,IAAI,cAAAiC,aAAA,uBAAXA,aAAA,CAAaQ,KAAK,KAAI;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,CACP;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAY,YAAA,EAAAC,aAAA;MAAA,oBACnBjF,OAAA;QAAAwE,QAAA,gBACExE,OAAA;UAAKuE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAE,EAAAQ,YAAA,GAAAZ,MAAM,CAAChB,IAAI,cAAA4B,YAAA,uBAAXA,YAAA,CAAaH,IAAI,KAAI;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7E5E,OAAA;UAAKuE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAE,EAAAS,aAAA,GAAAb,MAAM,CAAChB,IAAI,cAAA6B,aAAA,uBAAXA,aAAA,CAAaC,OAAO,KAAI;QAAS;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC;IAAA,CACP;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBpE,OAAA;MAAKuE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CxE,OAAA,CAACpB,UAAU;QAAC2F,SAAS,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChD5E,OAAA;QAAAwE,QAAA,gBACExE,OAAA;UAAKuE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAE1E,MAAM,CAACsE,MAAM,CAACP,SAAS,CAAC,CAACsB,MAAM,CAAC,cAAc;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5F5E,OAAA;UAAKuE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAE1E,MAAM,CAACsE,MAAM,CAACP,SAAS,CAAC,CAACsB,MAAM,CAAC,OAAO;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MAAA,IAAAgB,cAAA,EAAAC,qBAAA,EAAAC,aAAA;MACxB,MAAMpC,QAAQ,GAAG,EAAAkC,cAAA,GAAAhB,MAAM,CAACzB,MAAM,cAAAyC,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAejC,cAAc,cAAAkC,qBAAA,uBAA7BA,qBAAA,CAA+BtD,MAAM,KAAI,CAAC;MAC3D,MAAMlB,KAAK,GAAG,EAAAyE,aAAA,GAAAlB,MAAM,CAAChB,IAAI,cAAAkC,aAAA,uBAAXA,aAAA,CAAajC,UAAU,KAAI,CAAC;MAC1C,MAAMkC,UAAU,GAAG3C,IAAI,CAACC,KAAK,CAAEK,QAAQ,GAAGrC,KAAK,GAAI,GAAG,CAAC;MAEvD,oBACEb,OAAA;QAAKuE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxE,OAAA;UAAKuE,SAAS,EAAC,iCAAiC;UAAAC,QAAA,GAAEtB,QAAQ,EAAC,GAAC,EAACrC,KAAK;QAAA;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzE5E,OAAA,CAACP,QAAQ;UACP+F,OAAO,EAAED,UAAW;UACpBhD,IAAI,EAAC,OAAO;UACZkD,WAAW,EAAEF,UAAU,IAAI,EAAE,GAAG,SAAS,GAAG,SAAU;UACtDG,QAAQ,EAAE;QAAM;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACF5E,OAAA;UAAKuE,SAAS,EAAG,uBAAsBgB,UAAU,IAAI,EAAE,GAAG,gBAAgB,GAAG,cAAe,EAAE;UAAAf,QAAA,GAC3Fe,UAAU,EAAC,GACd;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV,CAAC;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MAAA,IAAAuB,eAAA;MACxB,MAAMhE,OAAO,IAAAgE,eAAA,GAAGvB,MAAM,CAACzB,MAAM,cAAAgD,eAAA,uBAAbA,eAAA,CAAehE,OAAO;MACtC,MAAMiE,QAAQ,GAAGjE,OAAO,KAAK,MAAM;MAEnC,oBACE3B,OAAA,CAACR,GAAG;QACFqG,IAAI,EAAED,QAAQ,gBAAG5F,OAAA,CAACtB,OAAO;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG5E,OAAA,CAACrB,GAAG;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvCkB,KAAK,EAAEF,QAAQ,GAAG,SAAS,GAAG,OAAQ;QACtCrB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAEtB7C,OAAO,IAAI;MAAK;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAEV,CAAC;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,SAAS;IAChB+B,GAAG,EAAE,SAAS;IACd7B,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBpE,OAAA,CAACT,MAAM;MACLyG,IAAI,EAAC,SAAS;MACdzD,IAAI,EAAC,OAAO;MACZsD,IAAI,eAAE7F,OAAA,CAACvB,KAAK;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAChBqB,OAAO,EAAEA,CAAA,KAAM,CAAC,0BAA2B;MAC3C1B,SAAS,EAAC,+BAA+B;MAAAC,QAAA,EAC1C;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;IACDG,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMmB,OAAO,GAAG,MAAAA,CAAOC,WAAW,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,KAAK;IAC3D,IAAI;MACF/E,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM0G,QAAQ,GAAG,MAAMzG,aAAa,CAAC;QACnC,GAAGsG,WAAW;QACdC,IAAI;QACJC;MACF,CAAC,CAAC;MACF,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpB/F,cAAc,CAAC8F,QAAQ,CAACxE,IAAI,CAAC;QAC7BD,cAAc,CAACyE,QAAQ,CAACxE,IAAI,CAAC;QAC7BpB,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAEyF,IAAI;UACbvF,KAAK,EAAEyF,QAAQ,CAAC7F,UAAU,CAACO;QAC7B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLhC,OAAO,CAACwH,KAAK,CAACF,QAAQ,CAACtH,OAAO,CAAC;MACjC;MACAsC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO6G,KAAK,EAAE;MACdlF,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACwH,KAAK,CAACA,KAAK,CAACxH,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMyH,YAAY,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCnF,UAAU,CAACoF,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACD,KAAK,GAAGD;IACX,CAAC,CAAC,CAAC;IACHhG,aAAa,CAACkG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjG,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMkG,qBAAqB,GAAIC,KAAK,IAAK;IACvCtF,UAAU,CAACoF,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPhF,SAAS,EAAEkF;IACb,CAAC,CAAC,CAAC;IACHpG,aAAa,CAACkG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjG,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMoG,YAAY,GAAGA,CAAA,KAAM;IACzBvF,UAAU,CAAC;MACTC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;IACb,CAAC,CAAC;IACFlB,aAAa,CAACkG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjG,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EAClD,CAAC;EAED5C,SAAS,CAAC,MAAM;IACdmI,OAAO,CAAC3E,OAAO,EAAEd,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,CAAC;EAC3D,CAAC,EAAE,CAACW,OAAO,EAAEd,UAAU,CAACE,OAAO,CAAC,CAAC;EAEjC,MAAMqG,iBAAiB,GAAIvG,UAAU,IAAK;IACxCyF,OAAO,CAAC3E,OAAO,EAAEd,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,CAAC;EAC3D,CAAC;EAED,oBACEZ,OAAA;IAAKuE,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBACjFxE,OAAA,CAACjB,SAAS;MAACiF,KAAK,EAAC;IAAe;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEnC5E,OAAA;MAAKuE,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DxE,OAAA,CAAChC,MAAM,CAACiJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9B7C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BxE,OAAA;UAAKuE,SAAS,EAAC,2HAA2H;UAAAC,QAAA,eACxIxE,OAAA,CAAC7B,UAAU;YAACoG,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACN5E,OAAA;UAAIuE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GAAC,UAC5C,eAAAxE,OAAA;YAAMuE,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,cACzH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5E,OAAA;UAAGuE,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb5E,OAAA,CAAChC,MAAM,CAACiJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BhD,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBAEpFxE,OAAA,CAACd,IAAI;UAACqF,SAAS,EAAC,6GAA6G;UAAAC,QAAA,eAC3HxE,OAAA;YAAKuE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCxE,OAAA;cAAKuE,SAAS,EAAC,0EAA0E;cAAAC,QAAA,eACvFxE,OAAA,CAAClB,UAAU;gBAACyF,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN5E,OAAA,CAACb,SAAS;cACR6E,KAAK,EAAC,eAAe;cACrB0C,KAAK,EAAE5F,KAAK,CAACE,YAAa;cAC1BwG,UAAU,EAAE;gBAAE1B,KAAK,EAAE,SAAS;gBAAE2B,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP5E,OAAA,CAACd,IAAI;UAACqF,SAAS,EAAC,+GAA+G;UAAAC,QAAA,eAC7HxE,OAAA;YAAKuE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCxE,OAAA;cAAKuE,SAAS,EAAC,2EAA2E;cAAAC,QAAA,eACxFxE,OAAA,CAAC5B,OAAO;gBAACmG,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN5E,OAAA,CAACb,SAAS;cACR6E,KAAK,EAAC,iBAAiB;cACvB0C,KAAK,EAAE5F,KAAK,CAACG,aAAc;cAC3BuG,UAAU,EAAE;gBAAE1B,KAAK,EAAE,SAAS;gBAAE2B,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP5E,OAAA,CAACd,IAAI;UAACqF,SAAS,EAAC,iHAAiH;UAAAC,QAAA,eAC/HxE,OAAA;YAAKuE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCxE,OAAA;cAAKuE,SAAS,EAAC,4EAA4E;cAAAC,QAAA,eACzFxE,OAAA,CAAC1B,YAAY;gBAACiG,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN5E,OAAA,CAACb,SAAS;cACR6E,KAAK,EAAC,eAAe;cACrB0C,KAAK,EAAE5F,KAAK,CAACI,YAAa;cAC1ByG,MAAM,EAAC,GAAG;cACVH,UAAU,EAAE;gBAAE1B,KAAK,EAAE,SAAS;gBAAE2B,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP5E,OAAA,CAACd,IAAI;UAACqF,SAAS,EAAC,iHAAiH;UAAAC,QAAA,eAC/HxE,OAAA;YAAKuE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCxE,OAAA;cAAKuE,SAAS,EAAC,4EAA4E;cAAAC,QAAA,eACzFxE,OAAA,CAAC3B,QAAQ;gBAACkG,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN5E,OAAA,CAACb,SAAS;cACR6E,KAAK,EAAC,WAAW;cACjB0C,KAAK,EAAE5F,KAAK,CAACK,QAAS;cACtBwG,MAAM,EAAC,GAAG;cACVH,UAAU,EAAE;gBAAE1B,KAAK,EAAE,SAAS;gBAAE2B,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP5E,OAAA,CAACd,IAAI;UAACqF,SAAS,EAAC,6GAA6G;UAAAC,QAAA,eAC3HxE,OAAA;YAAKuE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCxE,OAAA;cAAKuE,SAAS,EAAC,0EAA0E;cAAAC,QAAA,eACvFxE,OAAA,CAAClB,UAAU;gBAACyF,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN5E,OAAA,CAACb,SAAS;cACR6E,KAAK,EAAC,aAAa;cACnB0C,KAAK,EAAE5F,KAAK,CAACM,UAAW;cACxBoG,UAAU,EAAE;gBAAE1B,KAAK,EAAE,SAAS;gBAAE2B,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP5E,OAAA,CAACd,IAAI;UAACqF,SAAS,EAAC,iHAAiH;UAAAC,QAAA,eAC/HxE,OAAA;YAAKuE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCxE,OAAA;cAAKuE,SAAS,EAAC,4EAA4E;cAAAC,QAAA,eACzFxE,OAAA,CAACnB,OAAO;gBAAC0F,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN5E,OAAA,CAACb,SAAS;cACR6E,KAAK,EAAC,kBAAkB;cACxB0C,KAAK,EAAE5F,KAAK,CAACO,WAAY;cACzBmG,UAAU,EAAE;gBAAE1B,KAAK,EAAE,SAAS;gBAAE2B,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGb5E,OAAA,CAAChC,MAAM,CAACiJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BhD,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAE1ExE,OAAA;UAAKuE,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjFxE,OAAA;YAAKuE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCxE,OAAA,CAACxB,QAAQ;cAAC+F,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C5E,OAAA;cAAIuE,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CxE,OAAA,CAACG,MAAM;cACLyH,WAAW,EAAC,qBAAqB;cACjClB,KAAK,EAAEnF,OAAO,CAACE,QAAS;cACxBoG,QAAQ,EAAGC,CAAC,IAAKrB,YAAY,CAACqB,CAAC,CAACC,MAAM,CAACrB,KAAK,EAAE,UAAU,CAAE;cAC1DnC,SAAS,EAAC,gBAAgB;cAC1BhC,IAAI,EAAC;YAAO;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAEF5E,OAAA,CAACG,MAAM;cACLyH,WAAW,EAAC,wBAAwB;cACpClB,KAAK,EAAEnF,OAAO,CAACG,QAAS;cACxBmG,QAAQ,EAAGC,CAAC,IAAKrB,YAAY,CAACqB,CAAC,CAACC,MAAM,CAACrB,KAAK,EAAE,UAAU,CAAE;cAC1DnC,SAAS,EAAC,gBAAgB;cAC1BhC,IAAI,EAAC;YAAO;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAEF5E,OAAA,CAACX,MAAM;cACLuI,WAAW,EAAC,eAAe;cAC3BlB,KAAK,EAAEnF,OAAO,CAACI,OAAQ;cACvBkG,QAAQ,EAAGnB,KAAK,IAAKD,YAAY,CAACC,KAAK,EAAE,SAAS,CAAE;cACpDnC,SAAS,EAAC,gBAAgB;cAC1BhC,IAAI,EAAC,OAAO;cAAAiC,QAAA,gBAEZxE,OAAA,CAACC,MAAM;gBAACyG,KAAK,EAAC,EAAE;gBAAAlC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrC5E,OAAA,CAACC,MAAM;gBAACyG,KAAK,EAAC,MAAM;gBAAAlC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC5E,OAAA,CAACC,MAAM;gBAACyG,KAAK,EAAC,MAAM;gBAAAlC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAET5E,OAAA,CAACE,WAAW;cACVwG,KAAK,EAAEnF,OAAO,CAACK,SAAU;cACzBiG,QAAQ,EAAEhB,qBAAsB;cAChCtC,SAAS,EAAC,gBAAgB;cAC1BhC,IAAI,EAAC,OAAO;cACZqF,WAAW,EAAE,CAAC,YAAY,EAAE,UAAU;YAAE;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eAEF5E,OAAA,CAACT,MAAM;cACL0G,OAAO,EAAEc,YAAa;cACtBxE,IAAI,EAAC,OAAO;cACZgC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC7B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET5E,OAAA,CAACT,MAAM;cACLyG,IAAI,EAAC,SAAS;cACdH,IAAI,eAAE7F,OAAA,CAACzB,UAAU;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACrBrC,IAAI,EAAC,OAAO;cACZgC,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACtF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb5E,OAAA,CAAChC,MAAM,CAACiJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BhD,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eAEjFxE,OAAA,CAACf,KAAK;UACJ8E,OAAO,EAAEA,OAAQ;UACjBiE,UAAU,EAAEzH,WAAY;UACxBE,UAAU,EAAE;YACVE,OAAO,EAAEF,UAAU,CAACE,OAAO;YAC3BC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;YAC7BC,KAAK,EAAEJ,UAAU,CAACI,KAAK;YACvBoH,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE,IAAI;YACrBC,SAAS,EAAEA,CAACtH,KAAK,EAAEuH,KAAK,KACrB,GAAEA,KAAK,CAAC,CAAC,CAAE,IAAGA,KAAK,CAAC,CAAC,CAAE,OAAMvH,KAAM,UAAS;YAC/C0D,SAAS,EAAE;UACb,CAAE;UACFsD,QAAQ,EAAEb,iBAAkB;UAC5BqB,MAAM,EAAGjE,MAAM,IAAKA,MAAM,CAAC9B,GAAI;UAC/BgG,MAAM,EAAE;YAAEC,CAAC,EAAE;UAAK,CAAE;UACpBhE,SAAS,EAAC,cAAc;UACxBhC,IAAI,EAAC;QAAO;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvE,EAAA,CA3aQD,YAAY;EAAA,QACFnC,WAAW,EAeXyB,WAAW;AAAA;AAAA8I,EAAA,GAhBrBpI,YAAY;AA6arB,eAAeA,YAAY;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}