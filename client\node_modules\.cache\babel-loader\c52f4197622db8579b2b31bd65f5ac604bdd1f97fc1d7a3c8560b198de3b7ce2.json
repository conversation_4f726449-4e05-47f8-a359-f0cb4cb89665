{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\AdminReports\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useNavigate } from \"react-router-dom\";\nimport { TbDashboard, TbChartBar, TbUsers, TbTarget, TbTrendingUp, TbDownload, TbFilter, TbEye, TbCheckCircle, TbXCircle, TbCalendar, TbClock, TbFileText } from \"react-icons/tb\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { message, Table, Card, Statistic, Input, Select, DatePicker, Button, Tag, Progress } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReports } from \"../../../apicalls/reports\";\nimport moment from \"moment\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  Search\n} = Input;\nfunction AdminReports() {\n  _s();\n  const navigate = useNavigate();\n  const [reportsData, setReportsData] = useState([]);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [stats, setStats] = useState({\n    totalReports: 0,\n    totalStudents: 0,\n    averageScore: 0,\n    passRate: 0,\n    totalExams: 0,\n    activeToday: 0\n  });\n  const dispatch = useDispatch();\n  const [filters, setFilters] = useState({\n    examName: \"\",\n    userName: \"\",\n    verdict: \"\",\n    dateRange: null\n  });\n  const calculateStats = data => {\n    if (!data || data.length === 0) return;\n    const totalReports = data.length;\n    const uniqueStudents = new Set(data.map(report => {\n      var _report$user;\n      return (_report$user = report.user) === null || _report$user === void 0 ? void 0 : _report$user._id;\n    })).size;\n    const passedReports = data.filter(report => {\n      var _report$result;\n      return ((_report$result = report.result) === null || _report$result === void 0 ? void 0 : _report$result.verdict) === 'Pass';\n    }).length;\n    const passRate = totalReports > 0 ? Math.round(passedReports / totalReports * 100) : 0;\n    const scores = data.map(report => {\n      var _report$result2, _report$result2$corre, _report$exam;\n      const obtained = ((_report$result2 = report.result) === null || _report$result2 === void 0 ? void 0 : (_report$result2$corre = _report$result2.correctAnswers) === null || _report$result2$corre === void 0 ? void 0 : _report$result2$corre.length) || 0;\n      const total = ((_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam.totalMarks) || 1;\n      return obtained / total * 100;\n    });\n    const averageScore = scores.length > 0 ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length) : 0;\n    const uniqueExams = new Set(data.map(report => {\n      var _report$exam2;\n      return (_report$exam2 = report.exam) === null || _report$exam2 === void 0 ? void 0 : _report$exam2._id;\n    })).size;\n    const today = moment().startOf('day');\n    const activeToday = data.filter(report => moment(report.createdAt).isSame(today, 'day')).length;\n    setStats({\n      totalReports,\n      totalStudents: uniqueStudents,\n      averageScore,\n      passRate,\n      totalExams: uniqueExams,\n      activeToday\n    });\n  };\n  const columns = [{\n    title: \"Student\",\n    dataIndex: \"userName\",\n    render: (text, record) => {\n      var _record$user, _record$user2;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(TbUsers, {\n            className: \"w-4 h-4 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-gray-900\",\n            children: ((_record$user = record.user) === null || _record$user === void 0 ? void 0 : _record$user.name) || 'N/A'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: ((_record$user2 = record.user) === null || _record$user2 === void 0 ? void 0 : _record$user2.email) || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this);\n    },\n    width: 200\n  }, {\n    title: \"Exam\",\n    dataIndex: \"examName\",\n    render: (text, record) => {\n      var _record$exam, _record$exam2;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"font-medium text-gray-900\",\n          children: ((_record$exam = record.exam) === null || _record$exam === void 0 ? void 0 : _record$exam.name) || 'N/A'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: ((_record$exam2 = record.exam) === null || _record$exam2 === void 0 ? void 0 : _record$exam2.subject) || 'General'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this);\n    },\n    width: 200\n  }, {\n    title: \"Date & Time\",\n    dataIndex: \"date\",\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(TbCalendar, {\n        className: \"w-4 h-4 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-medium\",\n          children: moment(record.createdAt).format(\"MMM DD, YYYY\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-500\",\n          children: moment(record.createdAt).format(\"HH:mm\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 9\n    }, this),\n    width: 150\n  }, {\n    title: \"Score\",\n    dataIndex: \"score\",\n    render: (text, record) => {\n      var _record$result, _record$result$correc, _record$exam3;\n      const obtained = ((_record$result = record.result) === null || _record$result === void 0 ? void 0 : (_record$result$correc = _record$result.correctAnswers) === null || _record$result$correc === void 0 ? void 0 : _record$result$correc.length) || 0;\n      const total = ((_record$exam3 = record.exam) === null || _record$exam3 === void 0 ? void 0 : _record$exam3.totalMarks) || 1;\n      const percentage = Math.round(obtained / total * 100);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-gray-900\",\n          children: [obtained, \"/\", total]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: percentage,\n          size: \"small\",\n          strokeColor: percentage >= 60 ? '#10b981' : '#ef4444',\n          showInfo: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-sm font-medium ${percentage >= 60 ? 'text-green-600' : 'text-red-600'}`,\n          children: [percentage, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this);\n    },\n    width: 120\n  }, {\n    title: \"Result\",\n    dataIndex: \"verdict\",\n    render: (text, record) => {\n      var _record$result2;\n      const verdict = (_record$result2 = record.result) === null || _record$result2 === void 0 ? void 0 : _record$result2.verdict;\n      const isPassed = verdict === 'Pass';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        icon: isPassed ? /*#__PURE__*/_jsxDEV(TbCheckCircle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(TbXCircle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 50\n        }, this),\n        color: isPassed ? 'success' : 'error',\n        className: \"font-medium\",\n        children: verdict || 'N/A'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this);\n    },\n    width: 100\n  }, {\n    title: \"Actions\",\n    key: \"actions\",\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(TbEye, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this),\n      onClick: () => {/* Handle view details */},\n      className: \"bg-blue-500 hover:bg-blue-600\",\n      children: \"View\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 9\n    }, this),\n    width: 80\n  }];\n  const getData = async (tempFilters, page = 1, limit = 10) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReports({\n        ...tempFilters,\n        page,\n        limit\n      });\n      if (response.success) {\n        setReportsData(response.data);\n        calculateStats(response.data);\n        setPagination({\n          ...pagination,\n          current: page,\n          total: response.pagination.totalReports\n        });\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const handleSearch = (value, field) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const handleDateRangeChange = dates => {\n    setFilters(prev => ({\n      ...prev,\n      dateRange: dates\n    }));\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const clearFilters = () => {\n    setFilters({\n      examName: \"\",\n      userName: \"\",\n      verdict: \"\",\n      dateRange: null\n    });\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  useEffect(() => {\n    getData(filters, pagination.current, pagination.pageSize);\n  }, [filters, pagination.current]);\n  const handleTableChange = pagination => {\n    getData(filters, pagination.current, pagination.pageSize);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-4 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        onClick: () => navigate('/admin/dashboard'),\n        className: \"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(TbDashboard, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"hidden sm:inline text-sm font-medium\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageTitle, {\n        title: \"Reports\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divider\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Exam\",\n        value: filters.examName,\n        onChange: e => setFilters({\n          ...filters,\n          examName: e.target.value\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"User\",\n        value: filters.userName,\n        onChange: e => setFilters({\n          ...filters,\n          userName: e.target.value\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"primary-outlined-btn\",\n        onClick: () => {\n          setFilters({\n            examName: \"\",\n            userName: \"\"\n          });\n          getData({\n            examName: \"\",\n            userName: \"\"\n          });\n        },\n        children: \"Clear\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"primary-contained-btn\",\n        onClick: () => getData(filters, 1, pagination.pageSize),\n        children: \"Search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: reportsData,\n      className: \"mt-2\",\n      pagination: {\n        current: pagination.current,\n        total: pagination.total,\n        showSizeChanger: false,\n        // Disables size changer as per your request\n        onChange: page => {\n          setPagination({\n            ...pagination,\n            current: page\n          });\n          getData(filters, page); // Pass the page, no need to pass pageSize\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 248,\n    columnNumber: 5\n  }, this);\n}\n_s(AdminReports, \"JSVZRJHBn/g1rqCtBSZVtzeib1Y=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = AdminReports;\nexport default AdminReports;\nvar _c;\n$RefreshReg$(_c, \"AdminReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useNavigate", "TbDashboard", "TbChartBar", "TbUsers", "TbTarget", "TbTrendingUp", "TbDownload", "Tb<PERSON><PERSON>er", "TbEye", "TbCheckCircle", "TbXCircle", "TbCalendar", "TbClock", "TbFileText", "Page<PERSON><PERSON>le", "message", "Table", "Card", "Statistic", "Input", "Select", "DatePicker", "<PERSON><PERSON>", "Tag", "Progress", "useDispatch", "HideLoading", "ShowLoading", "getAllReports", "moment", "jsxDEV", "_jsxDEV", "Option", "RangePicker", "Search", "AdminReports", "_s", "navigate", "reportsData", "setReportsData", "pagination", "setPagination", "current", "pageSize", "total", "stats", "setStats", "totalReports", "totalStudents", "averageScore", "passRate", "totalExams", "activeToday", "dispatch", "filters", "setFilters", "examName", "userName", "verdict", "date<PERSON><PERSON><PERSON>", "calculateStats", "data", "length", "uniqueStudents", "Set", "map", "report", "_report$user", "user", "_id", "size", "passedReports", "filter", "_report$result", "result", "Math", "round", "scores", "_report$result2", "_report$result2$corre", "_report$exam", "obtained", "correctAnswers", "exam", "totalMarks", "reduce", "sum", "score", "uniqueExams", "_report$exam2", "today", "startOf", "createdAt", "isSame", "columns", "title", "dataIndex", "render", "text", "record", "_record$user", "_record$user2", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "email", "width", "_record$exam", "_record$exam2", "subject", "format", "_record$result", "_record$result$correc", "_record$exam3", "percentage", "percent", "strokeColor", "showInfo", "_record$result2", "isPassed", "icon", "color", "key", "type", "onClick", "getData", "tempFilters", "page", "limit", "response", "success", "error", "handleSearch", "value", "field", "prev", "handleDateRangeChange", "dates", "clearFilters", "handleTableChange", "button", "whileHover", "scale", "whileTap", "placeholder", "onChange", "e", "target", "dataSource", "showSizeChanger", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/AdminReports/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport {\r\n  TbDashboard,\r\n  TbChartBar,\r\n  TbUsers,\r\n  TbTarget,\r\n  TbTrendingUp,\r\n  TbDownload,\r\n  TbFilter,\r\n  TbEye,\r\n  TbCheckCircle,\r\n  TbXCircle,\r\n  TbCalendar,\r\n  TbClock,\r\n  TbFileText\r\n} from \"react-icons/tb\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Table, Card, Statistic, Input, Select, DatePicker, Button, Tag, Progress } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReports } from \"../../../apicalls/reports\";\r\nimport moment from \"moment\";\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\nconst { Search } = Input;\r\n\r\nfunction AdminReports() {\r\n  const navigate = useNavigate();\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [pagination, setPagination] = useState({\r\n    current: 1,\r\n    pageSize: 10,\r\n    total: 0,\r\n  });\r\n  const [stats, setStats] = useState({\r\n    totalReports: 0,\r\n    totalStudents: 0,\r\n    averageScore: 0,\r\n    passRate: 0,\r\n    totalExams: 0,\r\n    activeToday: 0\r\n  });\r\n  const dispatch = useDispatch();\r\n  const [filters, setFilters] = useState({\r\n    examName: \"\",\r\n    userName: \"\",\r\n    verdict: \"\",\r\n    dateRange: null\r\n  });\r\n\r\n  const calculateStats = (data) => {\r\n    if (!data || data.length === 0) return;\r\n\r\n    const totalReports = data.length;\r\n    const uniqueStudents = new Set(data.map(report => report.user?._id)).size;\r\n    const passedReports = data.filter(report => report.result?.verdict === 'Pass').length;\r\n    const passRate = totalReports > 0 ? Math.round((passedReports / totalReports) * 100) : 0;\r\n\r\n    const scores = data.map(report => {\r\n      const obtained = report.result?.correctAnswers?.length || 0;\r\n      const total = report.exam?.totalMarks || 1;\r\n      return (obtained / total) * 100;\r\n    });\r\n\r\n    const averageScore = scores.length > 0 ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length) : 0;\r\n    const uniqueExams = new Set(data.map(report => report.exam?._id)).size;\r\n    const today = moment().startOf('day');\r\n    const activeToday = data.filter(report => moment(report.createdAt).isSame(today, 'day')).length;\r\n\r\n    setStats({\r\n      totalReports,\r\n      totalStudents: uniqueStudents,\r\n      averageScore,\r\n      passRate,\r\n      totalExams: uniqueExams,\r\n      activeToday\r\n    });\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Student\",\r\n      dataIndex: \"userName\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center space-x-3\">\r\n          <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n            <TbUsers className=\"w-4 h-4 text-blue-600\" />\r\n          </div>\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{record.user?.name || 'N/A'}</div>\r\n            <div className=\"text-sm text-gray-500\">{record.user?.email || ''}</div>\r\n          </div>\r\n        </div>\r\n      ),\r\n      width: 200,\r\n    },\r\n    {\r\n      title: \"Exam\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => (\r\n        <div>\r\n          <div className=\"font-medium text-gray-900\">{record.exam?.name || 'N/A'}</div>\r\n          <div className=\"text-sm text-gray-500\">{record.exam?.subject || 'General'}</div>\r\n        </div>\r\n      ),\r\n      width: 200,\r\n    },\r\n    {\r\n      title: \"Date & Time\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <TbCalendar className=\"w-4 h-4 text-gray-400\" />\r\n          <div>\r\n            <div className=\"text-sm font-medium\">{moment(record.createdAt).format(\"MMM DD, YYYY\")}</div>\r\n            <div className=\"text-xs text-gray-500\">{moment(record.createdAt).format(\"HH:mm\")}</div>\r\n          </div>\r\n        </div>\r\n      ),\r\n      width: 150,\r\n    },\r\n    {\r\n      title: \"Score\",\r\n      dataIndex: \"score\",\r\n      render: (text, record) => {\r\n        const obtained = record.result?.correctAnswers?.length || 0;\r\n        const total = record.exam?.totalMarks || 1;\r\n        const percentage = Math.round((obtained / total) * 100);\r\n\r\n        return (\r\n          <div className=\"text-center\">\r\n            <div className=\"text-lg font-bold text-gray-900\">{obtained}/{total}</div>\r\n            <Progress\r\n              percent={percentage}\r\n              size=\"small\"\r\n              strokeColor={percentage >= 60 ? '#10b981' : '#ef4444'}\r\n              showInfo={false}\r\n            />\r\n            <div className={`text-sm font-medium ${percentage >= 60 ? 'text-green-600' : 'text-red-600'}`}>\r\n              {percentage}%\r\n            </div>\r\n          </div>\r\n        );\r\n      },\r\n      width: 120,\r\n    },\r\n    {\r\n      title: \"Result\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => {\r\n        const verdict = record.result?.verdict;\r\n        const isPassed = verdict === 'Pass';\r\n\r\n        return (\r\n          <Tag\r\n            icon={isPassed ? <TbCheckCircle /> : <TbXCircle />}\r\n            color={isPassed ? 'success' : 'error'}\r\n            className=\"font-medium\"\r\n          >\r\n            {verdict || 'N/A'}\r\n          </Tag>\r\n        );\r\n      },\r\n      width: 100,\r\n    },\r\n    {\r\n      title: \"Actions\",\r\n      key: \"actions\",\r\n      render: (text, record) => (\r\n        <Button\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          icon={<TbEye />}\r\n          onClick={() => {/* Handle view details */}}\r\n          className=\"bg-blue-500 hover:bg-blue-600\"\r\n        >\r\n          View\r\n        </Button>\r\n      ),\r\n      width: 80,\r\n    },\r\n  ];\r\n\r\n  const getData = async (tempFilters, page = 1, limit = 10) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReports({\r\n        ...tempFilters,\r\n        page,\r\n        limit,\r\n      });\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        calculateStats(response.data);\r\n        setPagination({\r\n          ...pagination,\r\n          current: page,\r\n          total: response.pagination.totalReports,\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleSearch = (value, field) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      [field]: value\r\n    }));\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n  };\r\n\r\n  const handleDateRangeChange = (dates) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      dateRange: dates\r\n    }));\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    setFilters({\r\n      examName: \"\",\r\n      userName: \"\",\r\n      verdict: \"\",\r\n      dateRange: null\r\n    });\r\n    setPagination(prev => ({ ...prev, current: 1 }));\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  }, [filters, pagination.current]);\r\n\r\n  const handleTableChange = (pagination) => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"flex items-center gap-4 mb-4\">\r\n        {/* Dashboard Shortcut */}\r\n        <motion.button\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          onClick={() => navigate('/admin/dashboard')}\r\n          className=\"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\"\r\n        >\r\n          <TbDashboard className=\"w-4 h-4\" />\r\n          <span className=\"hidden sm:inline text-sm font-medium\">Dashboard</span>\r\n        </motion.button>\r\n\r\n        <PageTitle title=\"Reports\" />\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n      <div className=\"flex gap-2\">\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"Exam\"\r\n          value={filters.examName}\r\n          onChange={(e) => setFilters({ ...filters, examName: e.target.value })}\r\n        />\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"User\"\r\n          value={filters.userName}\r\n          onChange={(e) => setFilters({ ...filters, userName: e.target.value })}\r\n        />\r\n        <button\r\n          className=\"primary-outlined-btn\"\r\n          onClick={() => {\r\n            setFilters({\r\n              examName: \"\",\r\n              userName: \"\",\r\n            });\r\n            getData({\r\n              examName: \"\",\r\n              userName: \"\",\r\n            });\r\n          }}\r\n        >\r\n          Clear\r\n        </button>\r\n        <button\r\n          className=\"primary-contained-btn\"\r\n          onClick={() => getData(filters, 1, pagination.pageSize)}\r\n        >\r\n          Search\r\n        </button>\r\n      </div>\r\n      <Table\r\n  columns={columns}\r\n  dataSource={reportsData}\r\n  className=\"mt-2\"\r\n  pagination={{\r\n    current: pagination.current,\r\n    total: pagination.total,\r\n    showSizeChanger: false, // Disables size changer as per your request\r\n    onChange: (page) => {\r\n      setPagination({\r\n        ...pagination,\r\n        current: page,\r\n      });\r\n      getData(filters, page); // Pass the page, no need to pass pageSize\r\n    },\r\n  }}\r\n/>\r\n\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AdminReports;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,aAAa,EACbC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,UAAU,QACL,gBAAgB;AACvB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,OAAO,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,MAAM;AACxG,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAM;EAAEC;AAAO,CAAC,GAAGZ,MAAM;AACzB,MAAM;EAAEa;AAAY,CAAC,GAAGZ,UAAU;AAClC,MAAM;EAAEa;AAAO,CAAC,GAAGf,KAAK;AAExB,SAASgB,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC;IAC3C6C,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC;IACjCkD,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE,CAAC;IACfC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC;IACrC2D,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAIC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IAEhC,MAAMf,YAAY,GAAGc,IAAI,CAACC,MAAM;IAChC,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAACH,IAAI,CAACI,GAAG,CAACC,MAAM;MAAA,IAAAC,YAAA;MAAA,QAAAA,YAAA,GAAID,MAAM,CAACE,IAAI,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,GAAG;IAAA,EAAC,CAAC,CAACC,IAAI;IACzE,MAAMC,aAAa,GAAGV,IAAI,CAACW,MAAM,CAACN,MAAM;MAAA,IAAAO,cAAA;MAAA,OAAI,EAAAA,cAAA,GAAAP,MAAM,CAACQ,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAef,OAAO,MAAK,MAAM;IAAA,EAAC,CAACI,MAAM;IACrF,MAAMZ,QAAQ,GAAGH,YAAY,GAAG,CAAC,GAAG4B,IAAI,CAACC,KAAK,CAAEL,aAAa,GAAGxB,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC;IAExF,MAAM8B,MAAM,GAAGhB,IAAI,CAACI,GAAG,CAACC,MAAM,IAAI;MAAA,IAAAY,eAAA,EAAAC,qBAAA,EAAAC,YAAA;MAChC,MAAMC,QAAQ,GAAG,EAAAH,eAAA,GAAAZ,MAAM,CAACQ,MAAM,cAAAI,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAeI,cAAc,cAAAH,qBAAA,uBAA7BA,qBAAA,CAA+BjB,MAAM,KAAI,CAAC;MAC3D,MAAMlB,KAAK,GAAG,EAAAoC,YAAA,GAAAd,MAAM,CAACiB,IAAI,cAAAH,YAAA,uBAAXA,YAAA,CAAaI,UAAU,KAAI,CAAC;MAC1C,OAAQH,QAAQ,GAAGrC,KAAK,GAAI,GAAG;IACjC,CAAC,CAAC;IAEF,MAAMK,YAAY,GAAG4B,MAAM,CAACf,MAAM,GAAG,CAAC,GAAGa,IAAI,CAACC,KAAK,CAACC,MAAM,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGV,MAAM,CAACf,MAAM,CAAC,GAAG,CAAC;IACtH,MAAM0B,WAAW,GAAG,IAAIxB,GAAG,CAACH,IAAI,CAACI,GAAG,CAACC,MAAM;MAAA,IAAAuB,aAAA;MAAA,QAAAA,aAAA,GAAIvB,MAAM,CAACiB,IAAI,cAAAM,aAAA,uBAAXA,aAAA,CAAapB,GAAG;IAAA,EAAC,CAAC,CAACC,IAAI;IACtE,MAAMoB,KAAK,GAAG7D,MAAM,CAAC,CAAC,CAAC8D,OAAO,CAAC,KAAK,CAAC;IACrC,MAAMvC,WAAW,GAAGS,IAAI,CAACW,MAAM,CAACN,MAAM,IAAIrC,MAAM,CAACqC,MAAM,CAAC0B,SAAS,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC5B,MAAM;IAE/FhB,QAAQ,CAAC;MACPC,YAAY;MACZC,aAAa,EAAEe,cAAc;MAC7Bd,YAAY;MACZC,QAAQ;MACRC,UAAU,EAAEqC,WAAW;MACvBpC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM0C,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,UAAU;IACrBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAC,YAAA,EAAAC,aAAA;MAAA,oBACnBtE,OAAA;QAAKuE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CxE,OAAA;UAAKuE,SAAS,EAAC,mEAAmE;UAAAC,QAAA,eAChFxE,OAAA,CAAC5B,OAAO;YAACmG,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACN5E,OAAA;UAAAwE,QAAA,gBACExE,OAAA;YAAKuE,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAE,EAAAH,YAAA,GAAAD,MAAM,CAAC/B,IAAI,cAAAgC,YAAA,uBAAXA,YAAA,CAAaQ,IAAI,KAAI;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7E5E,OAAA;YAAKuE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAE,EAAAF,aAAA,GAAAF,MAAM,CAAC/B,IAAI,cAAAiC,aAAA,uBAAXA,aAAA,CAAaQ,KAAK,KAAI;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,CACP;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAY,YAAA,EAAAC,aAAA;MAAA,oBACnBjF,OAAA;QAAAwE,QAAA,gBACExE,OAAA;UAAKuE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAE,EAAAQ,YAAA,GAAAZ,MAAM,CAAChB,IAAI,cAAA4B,YAAA,uBAAXA,YAAA,CAAaH,IAAI,KAAI;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7E5E,OAAA;UAAKuE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAE,EAAAS,aAAA,GAAAb,MAAM,CAAChB,IAAI,cAAA6B,aAAA,uBAAXA,aAAA,CAAaC,OAAO,KAAI;QAAS;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC;IAAA,CACP;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBpE,OAAA;MAAKuE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CxE,OAAA,CAACpB,UAAU;QAAC2F,SAAS,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChD5E,OAAA;QAAAwE,QAAA,gBACExE,OAAA;UAAKuE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAE1E,MAAM,CAACsE,MAAM,CAACP,SAAS,CAAC,CAACsB,MAAM,CAAC,cAAc;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5F5E,OAAA;UAAKuE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAE1E,MAAM,CAACsE,MAAM,CAACP,SAAS,CAAC,CAACsB,MAAM,CAAC,OAAO;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MAAA,IAAAgB,cAAA,EAAAC,qBAAA,EAAAC,aAAA;MACxB,MAAMpC,QAAQ,GAAG,EAAAkC,cAAA,GAAAhB,MAAM,CAACzB,MAAM,cAAAyC,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAejC,cAAc,cAAAkC,qBAAA,uBAA7BA,qBAAA,CAA+BtD,MAAM,KAAI,CAAC;MAC3D,MAAMlB,KAAK,GAAG,EAAAyE,aAAA,GAAAlB,MAAM,CAAChB,IAAI,cAAAkC,aAAA,uBAAXA,aAAA,CAAajC,UAAU,KAAI,CAAC;MAC1C,MAAMkC,UAAU,GAAG3C,IAAI,CAACC,KAAK,CAAEK,QAAQ,GAAGrC,KAAK,GAAI,GAAG,CAAC;MAEvD,oBACEb,OAAA;QAAKuE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxE,OAAA;UAAKuE,SAAS,EAAC,iCAAiC;UAAAC,QAAA,GAAEtB,QAAQ,EAAC,GAAC,EAACrC,KAAK;QAAA;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzE5E,OAAA,CAACP,QAAQ;UACP+F,OAAO,EAAED,UAAW;UACpBhD,IAAI,EAAC,OAAO;UACZkD,WAAW,EAAEF,UAAU,IAAI,EAAE,GAAG,SAAS,GAAG,SAAU;UACtDG,QAAQ,EAAE;QAAM;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACF5E,OAAA;UAAKuE,SAAS,EAAG,uBAAsBgB,UAAU,IAAI,EAAE,GAAG,gBAAgB,GAAG,cAAe,EAAE;UAAAf,QAAA,GAC3Fe,UAAU,EAAC,GACd;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV,CAAC;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MAAA,IAAAuB,eAAA;MACxB,MAAMhE,OAAO,IAAAgE,eAAA,GAAGvB,MAAM,CAACzB,MAAM,cAAAgD,eAAA,uBAAbA,eAAA,CAAehE,OAAO;MACtC,MAAMiE,QAAQ,GAAGjE,OAAO,KAAK,MAAM;MAEnC,oBACE3B,OAAA,CAACR,GAAG;QACFqG,IAAI,EAAED,QAAQ,gBAAG5F,OAAA,CAACtB,aAAa;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG5E,OAAA,CAACrB,SAAS;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACnDkB,KAAK,EAAEF,QAAQ,GAAG,SAAS,GAAG,OAAQ;QACtCrB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAEtB7C,OAAO,IAAI;MAAK;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAEV,CAAC;IACDG,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,SAAS;IAChB+B,GAAG,EAAE,SAAS;IACd7B,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBpE,OAAA,CAACT,MAAM;MACLyG,IAAI,EAAC,SAAS;MACdzD,IAAI,EAAC,OAAO;MACZsD,IAAI,eAAE7F,OAAA,CAACvB,KAAK;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAChBqB,OAAO,EAAEA,CAAA,KAAM,CAAC,0BAA2B;MAC3C1B,SAAS,EAAC,+BAA+B;MAAAC,QAAA,EAC1C;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;IACDG,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMmB,OAAO,GAAG,MAAAA,CAAOC,WAAW,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,KAAK;IAC3D,IAAI;MACF/E,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM0G,QAAQ,GAAG,MAAMzG,aAAa,CAAC;QACnC,GAAGsG,WAAW;QACdC,IAAI;QACJC;MACF,CAAC,CAAC;MACF,IAAIC,QAAQ,CAACC,OAAO,EAAE;QACpB/F,cAAc,CAAC8F,QAAQ,CAACxE,IAAI,CAAC;QAC7BD,cAAc,CAACyE,QAAQ,CAACxE,IAAI,CAAC;QAC7BpB,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAEyF,IAAI;UACbvF,KAAK,EAAEyF,QAAQ,CAAC7F,UAAU,CAACO;QAC7B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLhC,OAAO,CAACwH,KAAK,CAACF,QAAQ,CAACtH,OAAO,CAAC;MACjC;MACAsC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO6G,KAAK,EAAE;MACdlF,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACwH,KAAK,CAACA,KAAK,CAACxH,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMyH,YAAY,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCnF,UAAU,CAACoF,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACD,KAAK,GAAGD;IACX,CAAC,CAAC,CAAC;IACHhG,aAAa,CAACkG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjG,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMkG,qBAAqB,GAAIC,KAAK,IAAK;IACvCtF,UAAU,CAACoF,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPhF,SAAS,EAAEkF;IACb,CAAC,CAAC,CAAC;IACHpG,aAAa,CAACkG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjG,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAMoG,YAAY,GAAGA,CAAA,KAAM;IACzBvF,UAAU,CAAC;MACTC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;IACb,CAAC,CAAC;IACFlB,aAAa,CAACkG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjG,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EAClD,CAAC;EAED5C,SAAS,CAAC,MAAM;IACdmI,OAAO,CAAC3E,OAAO,EAAEd,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,CAAC;EAC3D,CAAC,EAAE,CAACW,OAAO,EAAEd,UAAU,CAACE,OAAO,CAAC,CAAC;EAEjC,MAAMqG,iBAAiB,GAAIvG,UAAU,IAAK;IACxCyF,OAAO,CAAC3E,OAAO,EAAEd,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,CAAC;EAC3D,CAAC;EAED,oBACEZ,OAAA;IAAAwE,QAAA,gBACExE,OAAA;MAAKuE,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAE3CxE,OAAA,CAAChC,MAAM,CAACiJ,MAAM;QACZC,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAC1BlB,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAC,kBAAkB,CAAE;QAC5CiE,SAAS,EAAC,gIAAgI;QAAAC,QAAA,gBAE1IxE,OAAA,CAAC9B,WAAW;UAACqG,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnC5E,OAAA;UAAMuE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAEhB5E,OAAA,CAACjB,SAAS;QAACiF,KAAK,EAAC;MAAS;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eACN5E,OAAA;MAAKuE,SAAS,EAAC;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC/B5E,OAAA;MAAKuE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBxE,OAAA;QACEgG,IAAI,EAAC,MAAM;QACXqB,WAAW,EAAC,MAAM;QAClBX,KAAK,EAAEnF,OAAO,CAACE,QAAS;QACxB6F,QAAQ,EAAGC,CAAC,IAAK/F,UAAU,CAAC;UAAE,GAAGD,OAAO;UAAEE,QAAQ,EAAE8F,CAAC,CAACC,MAAM,CAACd;QAAM,CAAC;MAAE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACF5E,OAAA;QACEgG,IAAI,EAAC,MAAM;QACXqB,WAAW,EAAC,MAAM;QAClBX,KAAK,EAAEnF,OAAO,CAACG,QAAS;QACxB4F,QAAQ,EAAGC,CAAC,IAAK/F,UAAU,CAAC;UAAE,GAAGD,OAAO;UAAEG,QAAQ,EAAE6F,CAAC,CAACC,MAAM,CAACd;QAAM,CAAC;MAAE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACF5E,OAAA;QACEuE,SAAS,EAAC,sBAAsB;QAChC0B,OAAO,EAAEA,CAAA,KAAM;UACbzE,UAAU,CAAC;YACTC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACFwE,OAAO,CAAC;YACNzE,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ,CAAE;QAAA8C,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT5E,OAAA;QACEuE,SAAS,EAAC,uBAAuB;QACjC0B,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAAC3E,OAAO,EAAE,CAAC,EAAEd,UAAU,CAACG,QAAQ,CAAE;QAAA4D,QAAA,EACzD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACN5E,OAAA,CAACf,KAAK;MACV8E,OAAO,EAAEA,OAAQ;MACjB0D,UAAU,EAAElH,WAAY;MACxBgE,SAAS,EAAC,MAAM;MAChB9D,UAAU,EAAE;QACVE,OAAO,EAAEF,UAAU,CAACE,OAAO;QAC3BE,KAAK,EAAEJ,UAAU,CAACI,KAAK;QACvB6G,eAAe,EAAE,KAAK;QAAE;QACxBJ,QAAQ,EAAGlB,IAAI,IAAK;UAClB1F,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbE,OAAO,EAAEyF;UACX,CAAC,CAAC;UACFF,OAAO,CAAC3E,OAAO,EAAE6E,IAAI,CAAC,CAAC,CAAC;QAC1B;MACF;IAAE;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEO,CAAC;AAEV;AAACvE,EAAA,CAjSQD,YAAY;EAAA,QACFnC,WAAW,EAeXyB,WAAW;AAAA;AAAAiI,EAAA,GAhBrBvH,YAAY;AAmSrB,eAAeA,YAAY;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}