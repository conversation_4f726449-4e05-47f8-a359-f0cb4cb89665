import { message } from "antd";
import React, { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import { getUserInfo } from "../apicalls/users";
import { useDispatch, useSelector } from "react-redux";
import { SetUser } from "../redux/usersSlice.js";
import { useNavigate, useLocation } from "react-router-dom";
import { HideLoading, ShowLoading } from "../redux/loaderSlice";
import { checkPaymentStatus } from "../apicalls/payment.js";
import "./ProtectedRoute.css";
import { SetSubscription } from "../redux/subscriptionSlice.js";
import { setPaymentVerificationNeeded } from "../redux/paymentSlice.js";
import AdminNavigation from "./AdminNavigation";
import ModernSidebar from "./ModernSidebar";
import { TbHome, TbBrandTanzania, TbMenu2, TbX, TbChevronDown, Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ell, TbStar } from "react-icons/tb";
import OnlineStatusIndicator from './common/OnlineStatusIndicator';
import NotificationBell from './common/NotificationBell';
import ProfilePicture from './common/ProfilePicture';
import { setUserOnline, setUserOffline, sendHeartbeat } from '../apicalls/notifications';


function ProtectedRoute({ children }) {
  const { user } = useSelector((state) => state.user);
  const [isPaymentPending, setIsPaymentPending] = useState(false);
  const intervalRef = useRef(null);
  const heartbeatRef = useRef(null);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const { paymentVerificationNeeded } = useSelector((state) => state.payment);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const activeRoute = location.pathname;





  const getUserData = async () => {
    try {
      const response = await getUserInfo();
      if (response.success) {
        dispatch(SetUser(response.data));

        // Store user data in localStorage for consistency
        localStorage.setItem("user", JSON.stringify(response.data));

        // Debug log to help identify admin login issues
        console.log("User data loaded:", {
          name: response.data.name,
          isAdmin: response.data.isAdmin,
          email: response.data.email
        });
      } else {
        message.error(response.message);
        navigate("/login");
      }
    } catch (error) {
      navigate("/login");
      message.error(error.message);
    }
  };

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      // Check if user data already exists in Redux (from login)
      if (!user) {
        // Try to load user from localStorage first
        const storedUser = localStorage.getItem("user");
        if (storedUser) {
          try {
            const userData = JSON.parse(storedUser);
            console.log("ProtectedRoute: Loading user from localStorage", { name: userData.name, isAdmin: userData.isAdmin });
            dispatch(SetUser(userData));
          } catch (error) {
            console.log("ProtectedRoute: Error parsing stored user data, fetching from server");
            getUserData();
          }
        } else {
          console.log("ProtectedRoute: No user in Redux or localStorage, fetching from server");
          getUserData();
        }
      } else {
        console.log("ProtectedRoute: User already in Redux", { name: user.name, isAdmin: user.isAdmin });
      }
    } else {
      navigate("/login");
    }
  }, []);



  useEffect(() => {
    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {
      navigate('/user/plans');
    }
  }, [isPaymentPending, activeRoute, navigate]);

  const verifyPaymentStatus = async () => {
    try {
      const data = await checkPaymentStatus();
      console.log("Payment Status:", data);
      if (data?.error || data?.paymentStatus !== 'paid') {
        if (subscriptionData !== null) {
          dispatch(SetSubscription(null));
        }
        setIsPaymentPending(true);
      }
      else {
        setIsPaymentPending(false);
        dispatch(SetSubscription(data));
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      }
    } catch (error) {
      console.log("Error checking payment status:", error);
      dispatch(SetSubscription(null));
      setIsPaymentPending(true);
    }
  };

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing 2222222...");

      if (paymentVerificationNeeded) {
        console.log('Inside timer in effect 2....');
        intervalRef.current = setInterval(() => {
          console.log('Timer in action...');
          verifyPaymentStatus();
        }, 15000);
        dispatch(setPaymentVerificationNeeded(false));
      }
    }
  }, [paymentVerificationNeeded]);

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing...");
      verifyPaymentStatus();
    }
  }, [user, activeRoute]);

  // Online status management
  useEffect(() => {
    if (user && !user.isAdmin) {
      // Set user as online when component mounts
      setUserOnline().catch(console.error);

      // Send heartbeat every 2 minutes
      heartbeatRef.current = setInterval(() => {
        sendHeartbeat().catch(console.error);
      }, 120000); // 2 minutes

      // Set user as offline when component unmounts or page unloads
      const handleBeforeUnload = () => {
        setUserOffline().catch(console.error);
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
        if (heartbeatRef.current) {
          clearInterval(heartbeatRef.current);
        }
        window.removeEventListener('beforeunload', handleBeforeUnload);
        setUserOffline().catch(console.error);
      };
    }
  }, [user]);


  const getButtonClass = (title) => {
    // Exclude "Plans" and "Profile" buttons from the "button-disabled" class
    if (!user.paymentRequired || title === "Plans" || title === "Profile" || title === "Logout") {
      return ""; // No class applied
    }

    return subscriptionData?.paymentStatus !== "paid" && user?.paymentRequired
      ? "button-disabled"
      : "";
  };




  return (
    <div className="layout-modern min-h-screen flex flex-col">
      {/* Modern Sidebar for regular users */}
      {!user?.isAdmin && <ModernSidebar />}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Modern Responsive Header - Show for all users */}
        {(
          <motion.header
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className={`nav-modern ${
              location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')
                ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'
                : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'
            } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}
          >
          <div className="px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10">
            <div className="flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20">
              {/* Left section - Modern Sidebar */}
              <div className="flex items-center space-x-2">
                {/* Modern Sidebar Toggle Button */}
              </div>

              {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}
              <div className="flex-1 flex justify-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="relative group flex items-center space-x-3"
                >
                  {/* Tanzania Flag - Using actual flag image */}
                  <div
                    className="rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative"
                    style={{
                      width: '32px',
                      height: '24px'
                    }}
                  >
                    <img
                      src="https://flagcdn.com/w40/tz.png"
                      alt="Tanzania Flag"
                      className="w-full h-full object-cover"
                      style={{ objectFit: 'cover' }}
                      onError={(e) => {
                        // Fallback to another flag source if first fails
                        e.target.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png";
                        e.target.onerror = () => {
                          // Final fallback - hide image and show text
                          e.target.style.display = 'none';
                          e.target.parentElement.innerHTML = '<div class="w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold">TZ</div>';
                        };
                      }}
                    />
                  </div>

                  {/* Amazing Animated Brainwave Text */}
                  <div className="relative brainwave-container">
                    <h1 className="text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none"
                        style={{
                          fontFamily: "'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif",
                          letterSpacing: '-0.02em'
                        }}>
                      {/* Brain - with amazing effects */}
                      <motion.span
                        className="relative inline-block"
                        initial={{ opacity: 0, x: -30, scale: 0.8 }}
                        animate={{
                          opacity: 1,
                          x: 0,
                          scale: 1,
                          textShadow: [
                            "0 0 10px rgba(59, 130, 246, 0.5)",
                            "0 0 20px rgba(59, 130, 246, 0.8)",
                            "0 0 10px rgba(59, 130, 246, 0.5)"
                          ]
                        }}
                        transition={{
                          duration: 1,
                          delay: 0.3,
                          textShadow: {
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }
                        }}
                        whileHover={{
                          scale: 1.1,
                          rotate: [0, -2, 2, 0],
                          transition: { duration: 0.3 }
                        }}
                        style={{
                          color: '#1f2937',
                          fontWeight: '900',
                          textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
                        }}
                      >
                        Brain

                        {/* Electric spark */}
                        <motion.div
                          className="absolute -top-1 -right-1 w-2 h-2 rounded-full"
                          animate={{
                            opacity: [0, 1, 0],
                            scale: [0.5, 1.2, 0.5],
                            backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            delay: 2
                          }}
                          style={{
                            backgroundColor: '#3b82f6',
                            boxShadow: '0 0 10px #3b82f6'
                          }}
                        />
                      </motion.span>

                      {/* Wave - with flowing effects (no space) */}
                      <motion.span
                        className="relative inline-block"
                        initial={{ opacity: 0, x: 30, scale: 0.8 }}
                        animate={{
                          opacity: 1,
                          x: 0,
                          scale: 1,
                          y: [0, -2, 0, 2, 0],
                          textShadow: [
                            "0 0 10px rgba(16, 185, 129, 0.5)",
                            "0 0 20px rgba(16, 185, 129, 0.8)",
                            "0 0 10px rgba(16, 185, 129, 0.5)"
                          ]
                        }}
                        transition={{
                          duration: 1,
                          delay: 0.5,
                          y: {
                            duration: 3,
                            repeat: Infinity,
                            ease: "easeInOut"
                          },
                          textShadow: {
                            duration: 2.5,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }
                        }}
                        whileHover={{
                          scale: 1.1,
                          rotate: [0, 2, -2, 0],
                          transition: { duration: 0.3 }
                        }}
                        style={{
                          color: '#059669',
                          fontWeight: '900',
                          textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'
                        }}
                      >
                        wave

                        {/* Wave particle */}
                        <motion.div
                          className="absolute top-0 left-0 w-1.5 h-1.5 rounded-full"
                          animate={{
                            opacity: [0, 1, 0],
                            x: [0, 40, 80],
                            y: [0, -5, 0, 5, 0],
                            backgroundColor: ['#10b981', '#34d399', '#10b981']
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            delay: 1
                          }}
                          style={{
                            backgroundColor: '#10b981',
                            boxShadow: '0 0 8px #10b981'
                          }}
                        />
                      </motion.span>
                    </h1>

                    {/* Glowing underline effect */}
                    <motion.div
                      className="absolute -bottom-1 left-0 h-1 rounded-full"
                      initial={{ width: 0, opacity: 0 }}
                      animate={{
                        width: '100%',
                        opacity: 1,
                        boxShadow: [
                          '0 0 10px rgba(16, 185, 129, 0.5)',
                          '0 0 20px rgba(59, 130, 246, 0.8)',
                          '0 0 10px rgba(16, 185, 129, 0.5)'
                        ]
                      }}
                      transition={{
                        duration: 1.5,
                        delay: 1.2,
                        boxShadow: {
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }
                      }}
                      style={{
                        background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',
                        boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'
                      }}
                    />
                  </div>

                  {/* Official Logo - Small like profile */}
                  <div
                    className="rounded-full overflow-hidden border-2 border-white/20 relative"
                    style={{
                      background: '#f0f0f0',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                      width: '32px',
                      height: '32px'
                    }}
                  >
                    <img
                      src="/favicon.png"
                      alt="Brainwave Logo"
                      className="w-full h-full object-cover"
                      style={{ objectFit: 'cover' }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                    <div
                      className="w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold"
                      style={{
                        display: 'none',
                        fontSize: '12px'
                      }}
                    >
                      🧠
                    </div>
                  </div>

                  {/* Modern Glow Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110"></div>
                </motion.div>
              </div>

              {/* Right Section - Notifications + User Profile */}
              <div className="flex items-center justify-end space-x-2 sm:space-x-3">
                {/* Notification Bell */}
                {!user?.isAdmin && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <NotificationBell unreadCount={3} />
                  </motion.div>
                )}

                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="flex items-center space-x-2 group"
                >
                  {/* Profile Picture with Online Status */}
                  <div style={{ position: 'relative', display: 'inline-block' }}>
                    <ProfilePicture
                      user={user}
                      size="sm"
                      showOnlineStatus={false}
                      style={{
                        width: '32px',
                        height: '32px',
                        border: '3px solid #22c55e',
                        boxShadow: '0 4px 12px rgba(34, 197, 94, 0.4)'
                      }}
                    />
                    {/* Separate Online Dot */}
                    <div
                      style={{
                        position: 'absolute',
                        bottom: '-2px',
                        right: '-2px',
                        width: '12px',
                        height: '12px',
                        backgroundColor: '#22c55e',
                        borderRadius: '50%',
                        border: '2px solid #ffffff',
                        boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',
                        zIndex: 1000
                      }}
                      title="Online"
                    />
                  </div>

                  {/* User Name and Class */}
                  <div className="hidden sm:block text-right">
                    <div className="text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300">
                      {user?.name || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300">
                      Class {user?.class}
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.header>
        )}

        {/* Page Content */}
        <main className={`flex-1 overflow-auto ${
          user?.isAdmin
            ? 'bg-gray-100'
            : 'bg-gradient-to-br from-gray-50 to-blue-50'
        } ${user?.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </main>


      </div>
    </div>
  );
}

export default ProtectedRoute;
