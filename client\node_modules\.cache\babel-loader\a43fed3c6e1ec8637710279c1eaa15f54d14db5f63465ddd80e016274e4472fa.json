{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, startTransition } from \"react\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction BrainwaveAI() {\n  _s();\n  const [messages, setMessages] = useState([{\n    role: \"assistant\",\n    content: \"Hello! I'm Brainwave AI. How can I help you?\"\n  }]);\n  const [inputText, setInputText] = useState(\"\");\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputText.trim() && !selectedImage) return;\n    const userMessage = inputText.trim();\n    const imageFile = selectedImage;\n\n    // Clear input immediately\n    setInputText(\"\");\n    removeImage();\n    setIsLoading(true);\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        if (uploadResult !== null && uploadResult !== void 0 && uploadResult.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: userMessage\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: userMessage\n      };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response\n      const response = await chatWithChatGPT([...messages, newUserMessage]);\n      if (response !== null && response !== void 0 && response.success && response !== null && response !== void 0 && response.data) {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: response.data\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: \"Sorry, I couldn't process your request. Please try again.\"\n        }]);\n      }\n    } catch (error) {\n      console.error(\"Chat error:\", error);\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: \"An error occurred. Please try again.\"\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Render message content\n  const renderMessageContent = message => {\n    if (typeof message.content === 'string') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"whitespace-pre-wrap\",\n        children: message.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 14\n      }, this);\n    }\n    if (Array.isArray(message.content)) {\n      return message.content.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"whitespace-pre-wrap mb-2\",\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 36\n        }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: item.image_url.url,\n          alt: \"User upload\",\n          className: \"max-w-full h-auto rounded-lg shadow-md\",\n          style: {\n            maxHeight: '300px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Invalid message format\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderBottom: '1px solid #e5e7eb',\n        padding: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxWidth: '1024px',\n          margin: '0 auto',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '40px',\n            height: '40px',\n            background: '#3b82f6',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: \"\\uD83E\\uDD16\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '20px',\n              fontWeight: 'bold',\n              color: '#1f2937',\n              margin: 0\n            },\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '14px',\n              color: '#6b7280',\n              margin: 0\n            },\n            children: \"Your intelligent study assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '100%',\n          maxWidth: '1024px',\n          margin: '0 auto',\n          padding: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '100%',\n            overflowY: 'auto',\n            paddingBottom: '16px'\n          },\n          children: [messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px',\n              marginBottom: '16px',\n              justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start'\n            },\n            children: [message.role === 'assistant' && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '32px',\n                height: '32px',\n                background: '#3b82f6',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                flexShrink: 0\n              },\n              children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxWidth: '80%'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  borderRadius: '16px',\n                  padding: '16px',\n                  background: message.role === 'user' ? '#3b82f6' : 'white',\n                  color: message.role === 'user' ? 'white' : '#1f2937',\n                  border: message.role === 'assistant' ? '1px solid #e5e7eb' : 'none',\n                  boxShadow: message.role === 'assistant' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none'\n                },\n                children: typeof message.content === 'string' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    whiteSpace: 'pre-wrap'\n                  },\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 23\n                }, this) : Array.isArray(message.content) ? message.content.map((item, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      whiteSpace: 'pre-wrap',\n                      marginBottom: '8px'\n                    },\n                    children: item.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 52\n                  }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.image_url.url,\n                    alt: \"Upload\",\n                    style: {\n                      maxWidth: '100%',\n                      height: 'auto',\n                      borderRadius: '8px',\n                      maxHeight: '300px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 29\n                  }, this)]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 25\n                }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Invalid message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), message.role === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '32px',\n                height: '32px',\n                background: '#10b981',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                flexShrink: 0\n              },\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px',\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '32px',\n                height: '32px',\n                background: '#3b82f6',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'white',\n                border: '1px solid #e5e7eb',\n                borderRadius: '16px',\n                padding: '16px',\n                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    background: '#9ca3af',\n                    borderRadius: '50%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    background: '#9ca3af',\n                    borderRadius: '50%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    background: '#9ca3af',\n                    borderRadius: '50%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderTop: '1px solid #e5e7eb',\n        padding: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxWidth: '1024px',\n          margin: '0 auto'\n        },\n        children: [imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px',\n            padding: '12px',\n            background: '#f9fafb',\n            borderRadius: '12px',\n            border: '1px solid #e5e7eb'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\",\n                style: {\n                  width: '64px',\n                  height: '64px',\n                  objectFit: 'cover',\n                  borderRadius: '8px',\n                  border: '1px solid #d1d5db'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: removeImage,\n                style: {\n                  position: 'absolute',\n                  top: '-8px',\n                  right: '-8px',\n                  width: '24px',\n                  height: '24px',\n                  background: '#ef4444',\n                  color: 'white',\n                  borderRadius: '50%',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  color: '#374151',\n                  margin: 0\n                },\n                children: \"Image attached\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '12px',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'flex-end',\n            gap: '12px',\n            background: 'white',\n            borderRadius: '16px',\n            border: '1px solid #d1d5db',\n            padding: '12px',\n            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            style: {\n              padding: '8px',\n              background: '#3b82f6',\n              color: 'white',\n              borderRadius: '8px',\n              border: 'none',\n              cursor: 'pointer'\n            },\n            title: \"Attach image\",\n            children: \"\\uD83D\\uDCCE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: inputText,\n            onChange: e => setInputText(e.target.value),\n            placeholder: \"Ask me anything... (Enter for new line)\",\n            style: {\n              flex: 1,\n              resize: 'none',\n              border: 'none',\n              outline: 'none',\n              background: 'transparent',\n              color: '#1f2937',\n              maxHeight: '128px',\n              minHeight: '24px',\n              fontFamily: 'inherit'\n            },\n            rows: 1,\n            onInput: e => {\n              e.target.style.height = 'auto';\n              e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSendMessage,\n            disabled: isLoading || !inputText.trim() && !selectedImage,\n            style: {\n              padding: '8px',\n              borderRadius: '8px',\n              border: 'none',\n              cursor: isLoading || !inputText.trim() && !selectedImage ? 'not-allowed' : 'pointer',\n              background: isLoading || !inputText.trim() && !selectedImage ? '#e5e7eb' : '#3b82f6',\n              color: isLoading || !inputText.trim() && !selectedImage ? '#9ca3af' : 'white'\n            },\n            children: \"\\u27A4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '12px',\n            color: '#6b7280',\n            marginTop: '8px',\n            textAlign: 'center',\n            margin: '8px 0 0 0'\n          },\n          children: \"Click send button to send \\u2022 Enter for new line \\u2022 Upload images for analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n}\n_s(BrainwaveAI, \"lIjsTBlFazwoJrwcDyz7AKelNP8=\");\n_c = BrainwaveAI;\n;\nexport default BrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"BrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "startTransition", "chatWithChatGPT", "uploadImg", "jsxDEV", "_jsxDEV", "BrainwaveAI", "_s", "messages", "setMessages", "role", "content", "inputText", "setInputText", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "isLoading", "setIsLoading", "messagesEndRef", "fileInputRef", "current", "scrollIntoView", "behavior", "handleImageSelect", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeImage", "value", "handleSendMessage", "trim", "userMessage", "imageFile", "imageUrl", "formData", "FormData", "append", "uploadResult", "success", "url", "newUserMessage", "text", "image_url", "prev", "response", "data", "error", "console", "renderMessageContent", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "isArray", "map", "item", "index", "src", "alt", "style", "maxHeight", "height", "background", "display", "flexDirection", "borderBottom", "padding", "max<PERSON><PERSON><PERSON>", "margin", "alignItems", "gap", "width", "borderRadius", "justifyContent", "fontSize", "fontWeight", "color", "flex", "overflow", "overflowY", "paddingBottom", "marginBottom", "flexShrink", "border", "boxShadow", "whiteSpace", "idx", "ref", "borderTop", "position", "objectFit", "onClick", "top", "right", "cursor", "name", "_fileInputRef$current", "click", "title", "onChange", "placeholder", "resize", "outline", "minHeight", "fontFamily", "rows", "onInput", "Math", "min", "scrollHeight", "disabled", "accept", "marginTop", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, startTransition } from \"react\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\n\nfunction BrainwaveAI() {\n  const [messages, setMessages] = useState([\n    { role: \"assistant\", content: \"Hello! I'm Brainwave AI. How can I help you?\" }\n  ]);\n  const [inputText, setInputText] = useState(\"\");\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = (event) => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = (e) => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputText.trim() && !selectedImage) return;\n\n    const userMessage = inputText.trim();\n    const imageFile = selectedImage;\n\n    // Clear input immediately\n    setInputText(\"\");\n    removeImage();\n    setIsLoading(true);\n\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        \n        if (uploadResult?.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl\n        ? {\n            role: \"user\",\n            content: [\n              { type: \"text\", text: userMessage },\n              { type: \"image_url\", image_url: { url: imageUrl } }\n            ]\n          }\n        : { role: \"user\", content: userMessage };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response\n      const response = await chatWithChatGPT([...messages, newUserMessage]);\n      \n      if (response?.success && response?.data) {\n        setMessages(prev => [...prev, { role: \"assistant\", content: response.data }]);\n      } else {\n        setMessages(prev => [...prev, { role: \"assistant\", content: \"Sorry, I couldn't process your request. Please try again.\" }]);\n      }\n\n    } catch (error) {\n      console.error(\"Chat error:\", error);\n      setMessages(prev => [...prev, { role: \"assistant\", content: \"An error occurred. Please try again.\" }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Render message content\n  const renderMessageContent = (message) => {\n    if (typeof message.content === 'string') {\n      return <div className=\"whitespace-pre-wrap\">{message.content}</div>;\n    }\n    \n    if (Array.isArray(message.content)) {\n      return message.content.map((item, index) => (\n        <div key={index}>\n          {item.type === 'text' && <div className=\"whitespace-pre-wrap mb-2\">{item.text}</div>}\n          {item.type === 'image_url' && (\n            <img \n              src={item.image_url.url} \n              alt=\"User upload\" \n              className=\"max-w-full h-auto rounded-lg shadow-md\"\n              style={{ maxHeight: '300px' }}\n            />\n          )}\n        </div>\n      ));\n    }\n    \n    return <div>Invalid message format</div>;\n  };\n\n  return (\n    <div style={{ height: '100vh', background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <div style={{ background: 'white', borderBottom: '1px solid #e5e7eb', padding: '16px' }}>\n        <div style={{ maxWidth: '1024px', margin: '0 auto', display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div style={{ width: '40px', height: '40px', background: '#3b82f6', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            🤖\n          </div>\n          <div>\n            <h1 style={{ fontSize: '20px', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>Brainwave AI</h1>\n            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>Your intelligent study assistant</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div style={{ flex: 1, overflow: 'hidden' }}>\n        <div style={{ height: '100%', maxWidth: '1024px', margin: '0 auto', padding: '16px' }}>\n          <div style={{ height: '100%', overflowY: 'auto', paddingBottom: '16px' }}>\n            {messages.map((message, index) => (\n              <div\n                key={index}\n                style={{\n                  display: 'flex',\n                  gap: '12px',\n                  marginBottom: '16px',\n                  justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start'\n                }}\n              >\n                {message.role === 'assistant' && (\n                  <div style={{ width: '32px', height: '32px', background: '#3b82f6', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', flexShrink: 0 }}>\n                    🤖\n                  </div>\n                )}\n\n                <div style={{ maxWidth: '80%' }}>\n                  <div\n                    style={{\n                      borderRadius: '16px',\n                      padding: '16px',\n                      background: message.role === 'user' ? '#3b82f6' : 'white',\n                      color: message.role === 'user' ? 'white' : '#1f2937',\n                      border: message.role === 'assistant' ? '1px solid #e5e7eb' : 'none',\n                      boxShadow: message.role === 'assistant' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none'\n                    }}\n                  >\n                    {typeof message.content === 'string' ? (\n                      <div style={{ whiteSpace: 'pre-wrap' }}>{message.content}</div>\n                    ) : Array.isArray(message.content) ? (\n                      message.content.map((item, idx) => (\n                        <div key={idx}>\n                          {item.type === 'text' && <div style={{ whiteSpace: 'pre-wrap', marginBottom: '8px' }}>{item.text}</div>}\n                          {item.type === 'image_url' && (\n                            <img\n                              src={item.image_url.url}\n                              alt=\"Upload\"\n                              style={{ maxWidth: '100%', height: 'auto', borderRadius: '8px', maxHeight: '300px' }}\n                            />\n                          )}\n                        </div>\n                      ))\n                    ) : (\n                      <div>Invalid message</div>\n                    )}\n                  </div>\n                </div>\n\n                {message.role === 'user' && (\n                  <div style={{ width: '32px', height: '32px', background: '#10b981', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', flexShrink: 0 }}>\n                    👤\n                  </div>\n                )}\n              </div>\n            ))}\n\n            {isLoading && (\n              <div style={{ display: 'flex', gap: '12px', marginBottom: '16px' }}>\n                <div style={{ width: '32px', height: '32px', background: '#3b82f6', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                  🤖\n                </div>\n                <div style={{ background: 'white', border: '1px solid #e5e7eb', borderRadius: '16px', padding: '16px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>\n                  <div style={{ display: 'flex', gap: '4px' }}>\n                    <div style={{ width: '8px', height: '8px', background: '#9ca3af', borderRadius: '50%' }}></div>\n                    <div style={{ width: '8px', height: '8px', background: '#9ca3af', borderRadius: '50%' }}></div>\n                    <div style={{ width: '8px', height: '8px', background: '#9ca3af', borderRadius: '50%' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </div>\n        </div>\n      </div>\n\n      {/* Input Section */}\n      <div style={{ background: 'white', borderTop: '1px solid #e5e7eb', padding: '16px' }}>\n        <div style={{ maxWidth: '1024px', margin: '0 auto' }}>\n          {/* Image Preview */}\n          {imagePreview && (\n            <div style={{ marginBottom: '16px', padding: '12px', background: '#f9fafb', borderRadius: '12px', border: '1px solid #e5e7eb' }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n                <div style={{ position: 'relative' }}>\n                  <img\n                    src={imagePreview}\n                    alt=\"Preview\"\n                    style={{ width: '64px', height: '64px', objectFit: 'cover', borderRadius: '8px', border: '1px solid #d1d5db' }}\n                  />\n                  <button\n                    onClick={removeImage}\n                    style={{ position: 'absolute', top: '-8px', right: '-8px', width: '24px', height: '24px', background: '#ef4444', color: 'white', borderRadius: '50%', border: 'none', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}\n                  >\n                    ✕\n                  </button>\n                </div>\n                <div style={{ flex: 1 }}>\n                  <p style={{ fontSize: '14px', fontWeight: '500', color: '#374151', margin: 0 }}>Image attached</p>\n                  <p style={{ fontSize: '12px', color: '#6b7280', margin: 0 }}>{selectedImage?.name}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Input Area */}\n          <div style={{ display: 'flex', alignItems: 'flex-end', gap: '12px', background: 'white', borderRadius: '16px', border: '1px solid #d1d5db', padding: '12px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>\n            {/* Attachment Button */}\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              style={{ padding: '8px', background: '#3b82f6', color: 'white', borderRadius: '8px', border: 'none', cursor: 'pointer' }}\n              title=\"Attach image\"\n            >\n              📎\n            </button>\n\n            {/* Text Input */}\n            <textarea\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              placeholder=\"Ask me anything... (Enter for new line)\"\n              style={{\n                flex: 1,\n                resize: 'none',\n                border: 'none',\n                outline: 'none',\n                background: 'transparent',\n                color: '#1f2937',\n                maxHeight: '128px',\n                minHeight: '24px',\n                fontFamily: 'inherit'\n              }}\n              rows={1}\n              onInput={(e) => {\n                e.target.style.height = 'auto';\n                e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';\n              }}\n            />\n\n            {/* Send Button */}\n            <button\n              onClick={handleSendMessage}\n              disabled={isLoading || (!inputText.trim() && !selectedImage)}\n              style={{\n                padding: '8px',\n                borderRadius: '8px',\n                border: 'none',\n                cursor: isLoading || (!inputText.trim() && !selectedImage) ? 'not-allowed' : 'pointer',\n                background: isLoading || (!inputText.trim() && !selectedImage) ? '#e5e7eb' : '#3b82f6',\n                color: isLoading || (!inputText.trim() && !selectedImage) ? '#9ca3af' : 'white'\n              }}\n            >\n              ➤\n            </button>\n          </div>\n\n          {/* Hidden File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={handleImageSelect}\n            style={{ display: 'none' }}\n          />\n\n          {/* Helper Text */}\n          <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '8px', textAlign: 'center', margin: '8px 0 0 0' }}>\n            Click send button to send • Enter for new line • Upload images for analysis\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BrainwaveAI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAC3E,SAASC,eAAe,EAAEC,SAAS,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,CACvC;IAAEY,IAAI,EAAE,WAAW;IAAEC,OAAO,EAAE;EAA+C,CAAC,CAC/E,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMsB,cAAc,GAAGrB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsB,YAAY,GAAGtB,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAC,SAAS,CAAC,MAAM;IACd,IAAIoB,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1ChB,gBAAgB,CAACY,IAAI,CAAC;MACtB,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKlB,eAAe,CAACkB,CAAC,CAACP,MAAM,CAACQ,MAAM,CAAC;MACvDJ,MAAM,CAACK,aAAa,CAACV,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBvB,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAII,YAAY,CAACC,OAAO,EAAE;MACxBD,YAAY,CAACC,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC5B,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAa,EAAE;IAEzC,MAAM4B,WAAW,GAAG9B,SAAS,CAAC6B,IAAI,CAAC,CAAC;IACpC,MAAME,SAAS,GAAG7B,aAAa;;IAE/B;IACAD,YAAY,CAAC,EAAE,CAAC;IAChByB,WAAW,CAAC,CAAC;IACbnB,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,IAAIyB,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAID,SAAS,EAAE;QACb,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,SAAS,CAAC;QACnC,MAAMK,YAAY,GAAG,MAAM7C,SAAS,CAAC0C,QAAQ,CAAC;QAE9C,IAAIG,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,OAAO,EAAE;UACzBL,QAAQ,GAAGI,YAAY,CAACE,GAAG;QAC7B;MACF;;MAEA;MACA,MAAMC,cAAc,GAAGP,QAAQ,GAC3B;QACElC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEmB,IAAI,EAAE,MAAM;UAAEsB,IAAI,EAAEV;QAAY,CAAC,EACnC;UAAEZ,IAAI,EAAE,WAAW;UAAEuB,SAAS,EAAE;YAAEH,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACD;QAAElC,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAE+B;MAAY,CAAC;;MAE1C;MACAjC,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,cAAc,CAAC,CAAC;;MAE9C;MACA,MAAMI,QAAQ,GAAG,MAAMrD,eAAe,CAAC,CAAC,GAAGM,QAAQ,EAAE2C,cAAc,CAAC,CAAC;MAErE,IAAII,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEN,OAAO,IAAIM,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,IAAI,EAAE;QACvC/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAE5C,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAE4C,QAAQ,CAACC;QAAK,CAAC,CAAC,CAAC;MAC/E,CAAC,MAAM;QACL/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAE5C,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAE;QAA4D,CAAC,CAAC,CAAC;MAC7H;IAEF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnChD,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE5C,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAuC,CAAC,CAAC,CAAC;IACxG,CAAC,SAAS;MACRQ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMwC,oBAAoB,GAAIC,OAAO,IAAK;IACxC,IAAI,OAAOA,OAAO,CAACjD,OAAO,KAAK,QAAQ,EAAE;MACvC,oBAAON,OAAA;QAAKwD,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAEF,OAAO,CAACjD;MAAO;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACrE;IAEA,IAAIC,KAAK,CAACC,OAAO,CAACR,OAAO,CAACjD,OAAO,CAAC,EAAE;MAClC,OAAOiD,OAAO,CAACjD,OAAO,CAAC0D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrClE,OAAA;QAAAyD,QAAA,GACGQ,IAAI,CAACxC,IAAI,KAAK,MAAM,iBAAIzB,OAAA;UAAKwD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAEQ,IAAI,CAAClB;QAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACnFI,IAAI,CAACxC,IAAI,KAAK,WAAW,iBACxBzB,OAAA;UACEmE,GAAG,EAAEF,IAAI,CAACjB,SAAS,CAACH,GAAI;UACxBuB,GAAG,EAAC,aAAa;UACjBZ,SAAS,EAAC,wCAAwC;UAClDa,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAQ;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CACF;MAAA,GATOK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUV,CACN,CAAC;IACJ;IAEA,oBAAO7D,OAAA;MAAAyD,QAAA,EAAK;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1C,CAAC;EAED,oBACE7D,OAAA;IAAKqE,KAAK,EAAE;MAAEE,MAAM,EAAE,OAAO;MAAEC,UAAU,EAAE,mDAAmD;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAjB,QAAA,gBAEzIzD,OAAA;MAAKqE,KAAK,EAAE;QAAEG,UAAU,EAAE,OAAO;QAAEG,YAAY,EAAE,mBAAmB;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAnB,QAAA,eACtFzD,OAAA;QAAKqE,KAAK,EAAE;UAAEQ,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE,QAAQ;UAAEL,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAvB,QAAA,gBACvGzD,OAAA;UAAKqE,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEV,MAAM,EAAE,MAAM;YAAEC,UAAU,EAAE,SAAS;YAAEU,YAAY,EAAE,KAAK;YAAET,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE,QAAQ;YAAEI,cAAc,EAAE;UAAS,CAAE;UAAA1B,QAAA,EAAC;QAE5J;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7D,OAAA;UAAAyD,QAAA,gBACEzD,OAAA;YAAIqE,KAAK,EAAE;cAAEe,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE,SAAS;cAAER,MAAM,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnG7D,OAAA;YAAGqE,KAAK,EAAE;cAAEe,QAAQ,EAAE,MAAM;cAAEE,KAAK,EAAE,SAAS;cAAER,MAAM,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKqE,KAAK,EAAE;QAAEkB,IAAI,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAA/B,QAAA,eAC1CzD,OAAA;QAAKqE,KAAK,EAAE;UAAEE,MAAM,EAAE,MAAM;UAAEM,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE,QAAQ;UAAEF,OAAO,EAAE;QAAO,CAAE;QAAAnB,QAAA,eACpFzD,OAAA;UAAKqE,KAAK,EAAE;YAAEE,MAAM,EAAE,MAAM;YAAEkB,SAAS,EAAE,MAAM;YAAEC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,GACtEtD,QAAQ,CAAC6D,GAAG,CAAC,CAACT,OAAO,EAAEW,KAAK,kBAC3BlE,OAAA;YAEEqE,KAAK,EAAE;cACLI,OAAO,EAAE,MAAM;cACfO,GAAG,EAAE,MAAM;cACXW,YAAY,EAAE,MAAM;cACpBR,cAAc,EAAE5B,OAAO,CAAClD,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG;YACzD,CAAE;YAAAoD,QAAA,GAEDF,OAAO,CAAClD,IAAI,KAAK,WAAW,iBAC3BL,OAAA;cAAKqE,KAAK,EAAE;gBAAEY,KAAK,EAAE,MAAM;gBAAEV,MAAM,EAAE,MAAM;gBAAEC,UAAU,EAAE,SAAS;gBAAEU,YAAY,EAAE,KAAK;gBAAET,OAAO,EAAE,MAAM;gBAAEM,UAAU,EAAE,QAAQ;gBAAEI,cAAc,EAAE,QAAQ;gBAAES,UAAU,EAAE;cAAE,CAAE;cAAAnC,QAAA,EAAC;YAE3K;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,eAED7D,OAAA;cAAKqE,KAAK,EAAE;gBAAEQ,QAAQ,EAAE;cAAM,CAAE;cAAApB,QAAA,eAC9BzD,OAAA;gBACEqE,KAAK,EAAE;kBACLa,YAAY,EAAE,MAAM;kBACpBN,OAAO,EAAE,MAAM;kBACfJ,UAAU,EAAEjB,OAAO,CAAClD,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,OAAO;kBACzDiF,KAAK,EAAE/B,OAAO,CAAClD,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,SAAS;kBACpDwF,MAAM,EAAEtC,OAAO,CAAClD,IAAI,KAAK,WAAW,GAAG,mBAAmB,GAAG,MAAM;kBACnEyF,SAAS,EAAEvC,OAAO,CAAClD,IAAI,KAAK,WAAW,GAAG,2BAA2B,GAAG;gBAC1E,CAAE;gBAAAoD,QAAA,EAED,OAAOF,OAAO,CAACjD,OAAO,KAAK,QAAQ,gBAClCN,OAAA;kBAAKqE,KAAK,EAAE;oBAAE0B,UAAU,EAAE;kBAAW,CAAE;kBAAAtC,QAAA,EAAEF,OAAO,CAACjD;gBAAO;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,GAC7DC,KAAK,CAACC,OAAO,CAACR,OAAO,CAACjD,OAAO,CAAC,GAChCiD,OAAO,CAACjD,OAAO,CAAC0D,GAAG,CAAC,CAACC,IAAI,EAAE+B,GAAG,kBAC5BhG,OAAA;kBAAAyD,QAAA,GACGQ,IAAI,CAACxC,IAAI,KAAK,MAAM,iBAAIzB,OAAA;oBAAKqE,KAAK,EAAE;sBAAE0B,UAAU,EAAE,UAAU;sBAAEJ,YAAY,EAAE;oBAAM,CAAE;oBAAAlC,QAAA,EAAEQ,IAAI,CAAClB;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACtGI,IAAI,CAACxC,IAAI,KAAK,WAAW,iBACxBzB,OAAA;oBACEmE,GAAG,EAAEF,IAAI,CAACjB,SAAS,CAACH,GAAI;oBACxBuB,GAAG,EAAC,QAAQ;oBACZC,KAAK,EAAE;sBAAEQ,QAAQ,EAAE,MAAM;sBAAEN,MAAM,EAAE,MAAM;sBAAEW,YAAY,EAAE,KAAK;sBAAEZ,SAAS,EAAE;oBAAQ;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CACF;gBAAA,GAROmC,GAAG;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASR,CACN,CAAC,gBAEF7D,OAAA;kBAAAyD,QAAA,EAAK;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAC1B;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELN,OAAO,CAAClD,IAAI,KAAK,MAAM,iBACtBL,OAAA;cAAKqE,KAAK,EAAE;gBAAEY,KAAK,EAAE,MAAM;gBAAEV,MAAM,EAAE,MAAM;gBAAEC,UAAU,EAAE,SAAS;gBAAEU,YAAY,EAAE,KAAK;gBAAET,OAAO,EAAE,MAAM;gBAAEM,UAAU,EAAE,QAAQ;gBAAEI,cAAc,EAAE,QAAQ;gBAAES,UAAU,EAAE;cAAE,CAAE;cAAAnC,QAAA,EAAC;YAE3K;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,GAlDIK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmDP,CACN,CAAC,EAEDhD,SAAS,iBACRb,OAAA;YAAKqE,KAAK,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEO,GAAG,EAAE,MAAM;cAAEW,YAAY,EAAE;YAAO,CAAE;YAAAlC,QAAA,gBACjEzD,OAAA;cAAKqE,KAAK,EAAE;gBAAEY,KAAK,EAAE,MAAM;gBAAEV,MAAM,EAAE,MAAM;gBAAEC,UAAU,EAAE,SAAS;gBAAEU,YAAY,EAAE,KAAK;gBAAET,OAAO,EAAE,MAAM;gBAAEM,UAAU,EAAE,QAAQ;gBAAEI,cAAc,EAAE;cAAS,CAAE;cAAA1B,QAAA,EAAC;YAE5J;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7D,OAAA;cAAKqE,KAAK,EAAE;gBAAEG,UAAU,EAAE,OAAO;gBAAEqB,MAAM,EAAE,mBAAmB;gBAAEX,YAAY,EAAE,MAAM;gBAAEN,OAAO,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAA4B,CAAE;cAAArC,QAAA,eAC9IzD,OAAA;gBAAKqE,KAAK,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEO,GAAG,EAAE;gBAAM,CAAE;gBAAAvB,QAAA,gBAC1CzD,OAAA;kBAAKqE,KAAK,EAAE;oBAAEY,KAAK,EAAE,KAAK;oBAAEV,MAAM,EAAE,KAAK;oBAAEC,UAAU,EAAE,SAAS;oBAAEU,YAAY,EAAE;kBAAM;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/F7D,OAAA;kBAAKqE,KAAK,EAAE;oBAAEY,KAAK,EAAE,KAAK;oBAAEV,MAAM,EAAE,KAAK;oBAAEC,UAAU,EAAE,SAAS;oBAAEU,YAAY,EAAE;kBAAM;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/F7D,OAAA;kBAAKqE,KAAK,EAAE;oBAAEY,KAAK,EAAE,KAAK;oBAAEV,MAAM,EAAE,KAAK;oBAAEC,UAAU,EAAE,SAAS;oBAAEU,YAAY,EAAE;kBAAM;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED7D,OAAA;YAAKiG,GAAG,EAAElF;UAAe;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKqE,KAAK,EAAE;QAAEG,UAAU,EAAE,OAAO;QAAE0B,SAAS,EAAE,mBAAmB;QAAEtB,OAAO,EAAE;MAAO,CAAE;MAAAnB,QAAA,eACnFzD,OAAA;QAAKqE,KAAK,EAAE;UAAEQ,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAArB,QAAA,GAElD9C,YAAY,iBACXX,OAAA;UAAKqE,KAAK,EAAE;YAAEsB,YAAY,EAAE,MAAM;YAAEf,OAAO,EAAE,MAAM;YAAEJ,UAAU,EAAE,SAAS;YAAEU,YAAY,EAAE,MAAM;YAAEW,MAAM,EAAE;UAAoB,CAAE;UAAApC,QAAA,eAC9HzD,OAAA;YAAKqE,KAAK,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEM,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAO,CAAE;YAAAvB,QAAA,gBACjEzD,OAAA;cAAKqE,KAAK,EAAE;gBAAE8B,QAAQ,EAAE;cAAW,CAAE;cAAA1C,QAAA,gBACnCzD,OAAA;gBACEmE,GAAG,EAAExD,YAAa;gBAClByD,GAAG,EAAC,SAAS;gBACbC,KAAK,EAAE;kBAAEY,KAAK,EAAE,MAAM;kBAAEV,MAAM,EAAE,MAAM;kBAAE6B,SAAS,EAAE,OAAO;kBAAElB,YAAY,EAAE,KAAK;kBAAEW,MAAM,EAAE;gBAAoB;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC,eACF7D,OAAA;gBACEqG,OAAO,EAAEpE,WAAY;gBACrBoC,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,UAAU;kBAAEG,GAAG,EAAE,MAAM;kBAAEC,KAAK,EAAE,MAAM;kBAAEtB,KAAK,EAAE,MAAM;kBAAEV,MAAM,EAAE,MAAM;kBAAEC,UAAU,EAAE,SAAS;kBAAEc,KAAK,EAAE,OAAO;kBAAEJ,YAAY,EAAE,KAAK;kBAAEW,MAAM,EAAE,MAAM;kBAAEW,MAAM,EAAE,SAAS;kBAAE/B,OAAO,EAAE,MAAM;kBAAEM,UAAU,EAAE,QAAQ;kBAAEI,cAAc,EAAE;gBAAS,CAAE;gBAAA1B,QAAA,EAC5P;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7D,OAAA;cAAKqE,KAAK,EAAE;gBAAEkB,IAAI,EAAE;cAAE,CAAE;cAAA9B,QAAA,gBACtBzD,OAAA;gBAAGqE,KAAK,EAAE;kBAAEe,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEC,KAAK,EAAE,SAAS;kBAAER,MAAM,EAAE;gBAAE,CAAE;gBAAArB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClG7D,OAAA;gBAAGqE,KAAK,EAAE;kBAAEe,QAAQ,EAAE,MAAM;kBAAEE,KAAK,EAAE,SAAS;kBAAER,MAAM,EAAE;gBAAE,CAAE;gBAAArB,QAAA,EAAEhD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgG;cAAI;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD7D,OAAA;UAAKqE,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE,UAAU;YAAEC,GAAG,EAAE,MAAM;YAAER,UAAU,EAAE,OAAO;YAAEU,YAAY,EAAE,MAAM;YAAEW,MAAM,EAAE,mBAAmB;YAAEjB,OAAO,EAAE,MAAM;YAAEkB,SAAS,EAAE;UAA4B,CAAE;UAAArC,QAAA,gBAEpMzD,OAAA;YACEqG,OAAO,EAAEA,CAAA;cAAA,IAAAK,qBAAA;cAAA,QAAAA,qBAAA,GAAM1F,YAAY,CAACC,OAAO,cAAAyF,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CtC,KAAK,EAAE;cAAEO,OAAO,EAAE,KAAK;cAAEJ,UAAU,EAAE,SAAS;cAAEc,KAAK,EAAE,OAAO;cAAEJ,YAAY,EAAE,KAAK;cAAEW,MAAM,EAAE,MAAM;cAAEW,MAAM,EAAE;YAAU,CAAE;YACzHI,KAAK,EAAC,cAAc;YAAAnD,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT7D,OAAA;YACEkC,KAAK,EAAE3B,SAAU;YACjBsG,QAAQ,EAAG/E,CAAC,IAAKtB,YAAY,CAACsB,CAAC,CAACP,MAAM,CAACW,KAAK,CAAE;YAC9C4E,WAAW,EAAC,yCAAyC;YACrDzC,KAAK,EAAE;cACLkB,IAAI,EAAE,CAAC;cACPwB,MAAM,EAAE,MAAM;cACdlB,MAAM,EAAE,MAAM;cACdmB,OAAO,EAAE,MAAM;cACfxC,UAAU,EAAE,aAAa;cACzBc,KAAK,EAAE,SAAS;cAChBhB,SAAS,EAAE,OAAO;cAClB2C,SAAS,EAAE,MAAM;cACjBC,UAAU,EAAE;YACd,CAAE;YACFC,IAAI,EAAE,CAAE;YACRC,OAAO,EAAGtF,CAAC,IAAK;cACdA,CAAC,CAACP,MAAM,CAAC8C,KAAK,CAACE,MAAM,GAAG,MAAM;cAC9BzC,CAAC,CAACP,MAAM,CAAC8C,KAAK,CAACE,MAAM,GAAG8C,IAAI,CAACC,GAAG,CAACxF,CAAC,CAACP,MAAM,CAACgG,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;YACrE;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGF7D,OAAA;YACEqG,OAAO,EAAElE,iBAAkB;YAC3BqF,QAAQ,EAAE3G,SAAS,IAAK,CAACN,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAe;YAC7D4D,KAAK,EAAE;cACLO,OAAO,EAAE,KAAK;cACdM,YAAY,EAAE,KAAK;cACnBW,MAAM,EAAE,MAAM;cACdW,MAAM,EAAE3F,SAAS,IAAK,CAACN,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAc,GAAG,aAAa,GAAG,SAAS;cACtF+D,UAAU,EAAE3D,SAAS,IAAK,CAACN,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAc,GAAG,SAAS,GAAG,SAAS;cACtF6E,KAAK,EAAEzE,SAAS,IAAK,CAACN,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAc,GAAG,SAAS,GAAG;YAC1E,CAAE;YAAAgD,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN7D,OAAA;UACEiG,GAAG,EAAEjF,YAAa;UAClBS,IAAI,EAAC,MAAM;UACXgG,MAAM,EAAC,SAAS;UAChBZ,QAAQ,EAAEzF,iBAAkB;UAC5BiD,KAAK,EAAE;YAAEI,OAAO,EAAE;UAAO;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF7D,OAAA;UAAGqE,KAAK,EAAE;YAAEe,QAAQ,EAAE,MAAM;YAAEE,KAAK,EAAE,SAAS;YAAEoC,SAAS,EAAE,KAAK;YAAEC,SAAS,EAAE,QAAQ;YAAE7C,MAAM,EAAE;UAAY,CAAE;UAAArB,QAAA,EAAC;QAE9G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC3D,EAAA,CAxTQD,WAAW;AAAA2H,EAAA,GAAX3H,WAAW;AAwTnB;AAED,eAAeA,WAAW;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}