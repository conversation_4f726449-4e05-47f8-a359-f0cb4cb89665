{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { TbSend, TbPaperclip, TbX, TbRobot, TbUser } from \"react-icons/tb\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BrainwaveAI = () => {\n  _s();\n  var _imageFile;\n  const [messages, setMessages] = useState([{\n    role: \"assistant\",\n    content: \"Hello! I'm <PERSON><PERSON> AI, your intelligent study assistant. How can I help you today?\"\n  }]);\n  const [inputText, setInputText] = useState(\"\");\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const textareaRef = useRef(null);\n\n  // Initialize chat with welcome message\n  useEffect(() => {\n    try {\n      // Load cached messages\n      const cachedMessages = localStorage.getItem('chat_messages');\n      if (cachedMessages) {\n        const parsedMessages = JSON.parse(cachedMessages);\n        startTransition(() => {\n          setMessages(parsedMessages);\n        });\n      } else {\n        // Set welcome message\n        startTransition(() => {\n          setMessages([{\n            role: \"assistant\",\n            content: \"Hello! I'm Brainwave AI, your intelligent study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\n          }]);\n        });\n      }\n      startTransition(() => {\n        setIsInitialized(true);\n      });\n    } catch (error) {\n      console.error('Error initializing chat:', error);\n      // Fallback to welcome message\n      startTransition(() => {\n        setMessages([{\n          role: \"assistant\",\n          content: \"Hello! I'm Brainwave AI, your intelligent study assistant. How can I help you today?\"\n        }]);\n        setIsInitialized(true);\n      });\n    }\n  }, []);\n\n  // Save messages to cache\n  useEffect(() => {\n    if (isInitialized && messages.length > 0) {\n      try {\n        localStorage.setItem('chat_messages', JSON.stringify(messages));\n      } catch (error) {\n        console.error('Error saving messages:', error);\n      }\n    }\n  }, [messages, isInitialized]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [messages]);\n\n  // Handle image file selection\n  const handleImageSelect = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setImageFile(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setImageFile(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle Enter key press - Enter for new line, send only via button\n  const handleKeyPress = e => {\n    // Enter always creates new line, no auto-send\n    if (e.key === 'Enter') {\n      // Allow default behavior (new line)\n      return;\n    }\n  };\n  const handleChat = async () => {\n    if (!prompt.trim() && !imageFile) return;\n    setIsLoading(true);\n    setIsTyping(true);\n    const currentPrompt = prompt.trim();\n    const currentImageFile = imageFile;\n    try {\n      let imageUrl = null;\n\n      // Step 1: Upload the image to the server (if an image is selected)\n      if (currentImageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", currentImageFile);\n        const data = await uploadImg(formData);\n        if (data !== null && data !== void 0 && data.success) {\n          imageUrl = data.url; // Extract the S3 URL\n          console.log(\"Image URL: \", imageUrl);\n        } else {\n          throw new Error(\"Image upload failed\");\n        }\n      }\n\n      // Step 2: Construct the ChatGPT message payload\n      const userMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: currentPrompt\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: currentPrompt\n      };\n      const updatedMessages = [...messages, userMessage];\n      startTransition(() => {\n        setMessages(updatedMessages);\n        setPrompt(\"\");\n      });\n      removeImage();\n\n      // Step 3: Send the payload to ChatGPT\n      const chatPayload = {\n        messages: updatedMessages\n      };\n      const chatRes = await chatWithChatGPT(chatPayload);\n      const apiResponse = chatRes === null || chatRes === void 0 ? void 0 : chatRes.data;\n      console.log(\"API Response: \", apiResponse);\n\n      // Step 4: Append the assistant's response to the conversation\n      startTransition(() => {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: apiResponse\n        }]);\n      });\n    } catch (error) {\n      console.error(\"Error during chat:\", error);\n      startTransition(() => {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: \"Sorry, I encountered an error. Please try again.\"\n        }]);\n      });\n    } finally {\n      setIsLoading(false);\n      setIsTyping(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: `\n          .chat-scroll::-webkit-scrollbar {\n            width: 6px;\n          }\n          .chat-scroll::-webkit-scrollbar-track {\n            background: #f1f5f9;\n            border-radius: 3px;\n          }\n          .chat-scroll::-webkit-scrollbar-thumb {\n            background: #cbd5e1;\n            border-radius: 3px;\n          }\n          .chat-scroll::-webkit-scrollbar-thumb:hover {\n            background: #94a3b8;\n          }\n        `\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(TbRobot, {\n            className: \"w-6 h-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg sm:text-xl font-bold text-gray-800\",\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs sm:text-sm text-gray-600\",\n            children: \"Ask questions, upload images, get instant help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-hidden relative\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chat-scroll h-full overflow-y-auto space-y-3 sm:space-y-6 pb-4 scroll-smooth\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: messages.map((msg, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: `flex gap-2 sm:gap-4 ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n              children: [msg.role === \"assistant\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                  className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `max-w-[85%] sm:max-w-[80%] ${msg.role === \"user\" ? \"order-1\" : \"\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-xl sm:rounded-2xl p-3 sm:p-4 ${msg.role === \"user\" ? \"bg-blue-500 text-white ml-auto\" : \"bg-white border border-gray-200 shadow-sm\"}`,\n                  children: msg.role === \"assistant\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-gray-800\",\n                    children: msg !== null && msg !== void 0 && msg.content ? renderContent(msg.content) : /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-red-500\",\n                      children: \"Unable to get a response from AI\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: typeof msg.content === \"string\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"whitespace-pre-wrap\",\n                      children: msg.content\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 29\n                    }, this) : msg.content.map((item, idx) => item.type === \"text\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"whitespace-pre-wrap mb-2\",\n                      children: item.text\n                    }, idx, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 33\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: item.image_url.url,\n                        alt: \"User uploaded content\",\n                        className: \"rounded-lg max-w-full h-auto shadow-md\",\n                        style: {\n                          maxHeight: window.innerWidth <= 768 ? \"200px\" : \"300px\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 35\n                      }, this)\n                    }, idx, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 33\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), msg.role === \"user\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(TbUser, {\n                  className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              className: \"flex gap-2 sm:gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                  className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white border border-gray-200 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      scale: [1, 1.2, 1]\n                    },\n                    transition: {\n                      duration: 0.6,\n                      repeat: Infinity,\n                      delay: 0\n                    },\n                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      scale: [1, 1.2, 1]\n                    },\n                    transition: {\n                      duration: 0.6,\n                      repeat: Infinity,\n                      delay: 0.2\n                    },\n                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      scale: [1, 1.2, 1]\n                    },\n                    transition: {\n                      duration: 0.6,\n                      repeat: Infinity,\n                      delay: 0.4\n                    },\n                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sticky bottom-0 bg-white/95 backdrop-blur-lg border-t border-gray-200 p-2 sm:p-4 shadow-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: imagePreview && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              height: 0\n            },\n            animate: {\n              opacity: 1,\n              height: \"auto\"\n            },\n            exit: {\n              opacity: 0,\n              height: 0\n            },\n            className: \"mb-3 sm:mb-4 p-2 sm:p-3 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 sm:gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: imagePreview,\n                  alt: \"Preview\",\n                  className: \"w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg border border-gray-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: removeImage,\n                  className: \"absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(TbX, {\n                    className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs sm:text-sm font-medium text-gray-700\",\n                  children: \"Image attached\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 truncate\",\n                  children: (_imageFile = imageFile) === null || _imageFile === void 0 ? void 0 : _imageFile.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end gap-2 sm:gap-3 bg-white rounded-xl sm:rounded-2xl border border-gray-300 p-2 sm:p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _fileInputRef$current;\n                return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n              },\n              className: \"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors shadow-sm\",\n              title: \"Attach image\",\n              children: /*#__PURE__*/_jsxDEV(TbPaperclip, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              ref: textareaRef,\n              value: prompt,\n              onChange: e => setPrompt(e.target.value),\n              onKeyPress: handleKeyPress,\n              placeholder: window.innerWidth <= 768 ? \"Ask me anything...\" : \"Ask me anything... (Enter for new line)\",\n              className: \"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 text-sm sm:text-base\",\n              rows: 1,\n              style: {\n                height: 'auto',\n                minHeight: window.innerWidth <= 768 ? '20px' : '24px',\n                maxHeight: window.innerWidth <= 768 ? '80px' : '128px'\n              },\n              onInput: e => {\n                e.target.style.height = 'auto';\n                const maxHeight = window.innerWidth <= 768 ? 80 : 128;\n                e.target.style.height = Math.min(e.target.scrollHeight, maxHeight) + 'px';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: handleChat,\n              disabled: isLoading || !prompt.trim() && !imageFile,\n              className: `p-2 rounded-lg transition-all ${isLoading || !prompt.trim() && !imageFile ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"}`,\n              children: isLoading ? /*#__PURE__*/_jsxDEV(TbLoader, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(TbSend, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            ref: fileInputRef,\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: handleImageSelect,\n            style: {\n              display: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 mt-2 text-center px-2\",\n          children: window.innerWidth <= 768 ? \"Click send button • Upload images for analysis\" : \"Click send button to send • Enter for new line • Upload images for analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s(BrainwaveAI, \"TOxRHdOS+LiLJONULGSxA4DgSOo=\");\n_c = BrainwaveAI;\nexport default ChatGPTIntegration;\nvar _c;\n$RefreshReg$(_c, \"BrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "TbSend", "TbPaperclip", "TbX", "TbRobot", "TbUser", "chatWithChatGPT", "uploadImg", "jsxDEV", "_jsxDEV", "BrainwaveAI", "_s", "_imageFile", "messages", "setMessages", "role", "content", "inputText", "setInputText", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "isLoading", "setIsLoading", "messagesEndRef", "fileInputRef", "textareaRef", "cachedMessages", "localStorage", "getItem", "parsedMessages", "JSON", "parse", "startTransition", "setIsInitialized", "error", "console", "isInitialized", "length", "setItem", "stringify", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleImageSelect", "e", "file", "target", "files", "setImageFile", "reader", "FileReader", "onload", "result", "readAsDataURL", "removeImage", "value", "handleKeyPress", "key", "handleChat", "prompt", "trim", "imageFile", "setIsTyping", "currentPrompt", "currentImageFile", "imageUrl", "formData", "FormData", "append", "data", "success", "url", "log", "Error", "userMessage", "type", "text", "image_url", "updatedMessages", "setPrompt", "chatPayload", "chatRes", "apiResponse", "prev", "className", "children", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "motion", "div", "initial", "opacity", "y", "animate", "AnimatePresence", "map", "msg", "index", "renderContent", "item", "idx", "src", "alt", "style", "maxHeight", "window", "innerWidth", "isTyping", "exit", "scale", "transition", "duration", "repeat", "Infinity", "delay", "ref", "height", "onClick", "name", "_fileInputRef$current", "click", "title", "onChange", "onKeyPress", "placeholder", "rows", "minHeight", "onInput", "Math", "min", "scrollHeight", "button", "whileHover", "whileTap", "disabled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accept", "display", "_c", "ChatGPTIntegration", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { TbSend, Tb<PERSON><PERSON>c<PERSON>, TbX, Tb<PERSON>ob<PERSON>, TbUser } from \"react-icons/tb\";\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\n\r\nconst BrainwaveAI = () => {\r\n  const [messages, setMessages] = useState([\r\n    {\r\n      role: \"assistant\",\r\n      content: \"Hello! I'm Brain<PERSON> AI, your intelligent study assistant. How can I help you today?\"\r\n    }\r\n  ]);\r\n  const [inputText, setInputText] = useState(\"\");\r\n  const [selectedImage, setSelectedImage] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const messagesEndRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const textareaRef = useRef(null);\r\n\r\n  // Initialize chat with welcome message\r\n  useEffect(() => {\r\n    try {\r\n      // Load cached messages\r\n      const cachedMessages = localStorage.getItem('chat_messages');\r\n      if (cachedMessages) {\r\n        const parsedMessages = JSON.parse(cachedMessages);\r\n        startTransition(() => {\r\n          setMessages(parsedMessages);\r\n        });\r\n      } else {\r\n        // Set welcome message\r\n        startTransition(() => {\r\n          setMessages([{\r\n            role: \"assistant\",\r\n            content: \"Hello! I'm Brainwave AI, your intelligent study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\r\n          }]);\r\n        });\r\n      }\r\n      startTransition(() => {\r\n        setIsInitialized(true);\r\n      });\r\n    } catch (error) {\r\n      console.error('Error initializing chat:', error);\r\n      // Fallback to welcome message\r\n      startTransition(() => {\r\n        setMessages([{\r\n          role: \"assistant\",\r\n          content: \"Hello! I'm Brainwave AI, your intelligent study assistant. How can I help you today?\"\r\n        }]);\r\n        setIsInitialized(true);\r\n      });\r\n    }\r\n  }, []);\r\n\r\n  // Save messages to cache\r\n  useEffect(() => {\r\n    if (isInitialized && messages.length > 0) {\r\n      try {\r\n        localStorage.setItem('chat_messages', JSON.stringify(messages));\r\n      } catch (error) {\r\n        console.error('Error saving messages:', error);\r\n      }\r\n    }\r\n  }, [messages, isInitialized]);\r\n\r\n  // Auto-scroll to bottom when new messages arrive\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [messages]);\r\n\r\n  // Handle image file selection\r\n  const handleImageSelect = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setImageFile(file);\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImagePreview(e.target.result);\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  // Remove selected image\r\n  const removeImage = () => {\r\n    setImageFile(null);\r\n    setImagePreview(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Handle Enter key press - Enter for new line, send only via button\r\n  const handleKeyPress = (e) => {\r\n    // Enter always creates new line, no auto-send\r\n    if (e.key === 'Enter') {\r\n      // Allow default behavior (new line)\r\n      return;\r\n    }\r\n  };\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n    setIsTyping(true);\r\n\r\n    const currentPrompt = prompt.trim();\r\n    const currentImageFile = imageFile;\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (currentImageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", currentImageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: currentPrompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: currentPrompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      startTransition(() => {\r\n        setMessages(updatedMessages);\r\n        setPrompt(\"\");\r\n      });\r\n      removeImage();\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      startTransition(() => {\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          { role: \"assistant\", content: apiResponse },\r\n        ]);\r\n      });\r\n\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      startTransition(() => {\r\n        setMessages((prev) => [\r\n          ...prev,\r\n          { role: \"assistant\", content: \"Sorry, I encountered an error. Please try again.\" },\r\n        ]);\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n      setIsTyping(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col overflow-hidden\">\r\n      {/* Professional Scrollbar Styles */}\r\n      <style dangerouslySetInnerHTML={{\r\n        __html: `\r\n          .chat-scroll::-webkit-scrollbar {\r\n            width: 6px;\r\n          }\r\n          .chat-scroll::-webkit-scrollbar-track {\r\n            background: #f1f5f9;\r\n            border-radius: 3px;\r\n          }\r\n          .chat-scroll::-webkit-scrollbar-thumb {\r\n            background: #cbd5e1;\r\n            border-radius: 3px;\r\n          }\r\n          .chat-scroll::-webkit-scrollbar-thumb:hover {\r\n            background: #94a3b8;\r\n          }\r\n        `\r\n      }} />\r\n      {/* Header */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 shadow-sm\"\r\n      >\r\n        <div className=\"max-w-4xl mx-auto flex items-center gap-3\">\r\n          <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n            <TbRobot className=\"w-6 h-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h1 className=\"text-lg sm:text-xl font-bold text-gray-800\">Brainwave AI</h1>\r\n            <p className=\"text-xs sm:text-sm text-gray-600\">Ask questions, upload images, get instant help</p>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Messages Container - Professional Scrolling */}\r\n      <div className=\"flex-1 overflow-hidden relative\">\r\n        <div className=\"h-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4\">\r\n          <div\r\n            className=\"chat-scroll h-full overflow-y-auto space-y-3 sm:space-y-6 pb-4 scroll-smooth\"\r\n          >\r\n            <AnimatePresence>\r\n              {messages.map((msg, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  className={`flex gap-2 sm:gap-4 ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`}\r\n                >\r\n                  {msg.role === \"assistant\" && (\r\n                    <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                      <TbRobot className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className={`max-w-[85%] sm:max-w-[80%] ${msg.role === \"user\" ? \"order-1\" : \"\"}`}>\r\n                    <div\r\n                      className={`rounded-xl sm:rounded-2xl p-3 sm:p-4 ${\r\n                        msg.role === \"user\"\r\n                          ? \"bg-blue-500 text-white ml-auto\"\r\n                          : \"bg-white border border-gray-200 shadow-sm\"\r\n                      }`}\r\n                    >\r\n                      {msg.role === \"assistant\" ? (\r\n                        <div className=\"text-gray-800\">\r\n                          {msg?.content ? (\r\n                            renderContent(msg.content)\r\n                          ) : (\r\n                            <p className=\"text-red-500\">Unable to get a response from AI</p>\r\n                          )}\r\n                        </div>\r\n                      ) : (\r\n                        <div>\r\n                          {typeof msg.content === \"string\" ? (\r\n                            <p className=\"whitespace-pre-wrap\">{msg.content}</p>\r\n                          ) : (\r\n                            msg.content.map((item, idx) =>\r\n                              item.type === \"text\" ? (\r\n                                <p key={idx} className=\"whitespace-pre-wrap mb-2\">{item.text}</p>\r\n                              ) : (\r\n                                <div key={idx} className=\"mt-2\">\r\n                                  <img\r\n                                    src={item.image_url.url}\r\n                                    alt=\"User uploaded content\"\r\n                                    className=\"rounded-lg max-w-full h-auto shadow-md\"\r\n                                    style={{ maxHeight: window.innerWidth <= 768 ? \"200px\" : \"300px\" }}\r\n                                  />\r\n                                </div>\r\n                              )\r\n                            )\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {msg.role === \"user\" && (\r\n                    <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                      <TbUser className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                    </div>\r\n                  )}\r\n                </motion.div>\r\n              ))}\r\n            </AnimatePresence>\r\n\r\n            {/* Typing Indicator */}\r\n            <AnimatePresence>\r\n              {isTyping && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  exit={{ opacity: 0, y: -20 }}\r\n                  className=\"flex gap-2 sm:gap-4\"\r\n                >\r\n                  <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n                    <TbRobot className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                  </div>\r\n                  <div className=\"bg-white border border-gray-200 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-sm\">\r\n                    <div className=\"flex gap-1\">\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </AnimatePresence>\r\n\r\n            <div ref={messagesEndRef} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Input Section - Pinned at Bottom */}\r\n      <div className=\"sticky bottom-0 bg-white/95 backdrop-blur-lg border-t border-gray-200 p-2 sm:p-4 shadow-lg\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Image Preview */}\r\n          <AnimatePresence>\r\n            {imagePreview && (\r\n              <motion.div\r\n                initial={{ opacity: 0, height: 0 }}\r\n                animate={{ opacity: 1, height: \"auto\" }}\r\n                exit={{ opacity: 0, height: 0 }}\r\n                className=\"mb-3 sm:mb-4 p-2 sm:p-3 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200\"\r\n              >\r\n                <div className=\"flex items-center gap-2 sm:gap-3\">\r\n                  <div className=\"relative\">\r\n                    <img\r\n                      src={imagePreview}\r\n                      alt=\"Preview\"\r\n                      className=\"w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg border border-gray-300\"\r\n                    />\r\n                    <button\r\n                      onClick={removeImage}\r\n                      className=\"absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\"\r\n                    >\r\n                      <TbX className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-xs sm:text-sm font-medium text-gray-700\">Image attached</p>\r\n                    <p className=\"text-xs text-gray-500 truncate\">{imageFile?.name}</p>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n\r\n          {/* Input Area - Responsive */}\r\n          <div className=\"relative\">\r\n            <div className=\"flex items-end gap-2 sm:gap-3 bg-white rounded-xl sm:rounded-2xl border border-gray-300 p-2 sm:p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\">\r\n              {/* Attachment Button - Blue and White with Visible Pins */}\r\n              <button\r\n                onClick={() => fileInputRef.current?.click()}\r\n                className=\"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors shadow-sm\"\r\n                title=\"Attach image\"\r\n              >\r\n                <TbPaperclip className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n              </button>\r\n\r\n              {/* Text Input - Responsive */}\r\n              <textarea\r\n                ref={textareaRef}\r\n                value={prompt}\r\n                onChange={(e) => setPrompt(e.target.value)}\r\n                onKeyPress={handleKeyPress}\r\n                placeholder={window.innerWidth <= 768 ? \"Ask me anything...\" : \"Ask me anything... (Enter for new line)\"}\r\n                className=\"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 text-sm sm:text-base\"\r\n                rows={1}\r\n                style={{\r\n                  height: 'auto',\r\n                  minHeight: window.innerWidth <= 768 ? '20px' : '24px',\r\n                  maxHeight: window.innerWidth <= 768 ? '80px' : '128px'\r\n                }}\r\n                onInput={(e) => {\r\n                  e.target.style.height = 'auto';\r\n                  const maxHeight = window.innerWidth <= 768 ? 80 : 128;\r\n                  e.target.style.height = Math.min(e.target.scrollHeight, maxHeight) + 'px';\r\n                }}\r\n              />\r\n\r\n              {/* Send Button - Responsive */}\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={handleChat}\r\n                disabled={isLoading || (!prompt.trim() && !imageFile)}\r\n                className={`p-2 rounded-lg transition-all ${\r\n                  isLoading || (!prompt.trim() && !imageFile)\r\n                    ? \"bg-gray-200 text-gray-400 cursor-not-allowed\"\r\n                    : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"\r\n                }`}\r\n              >\r\n                {isLoading ? (\r\n                  <TbLoader className=\"w-4 h-4 sm:w-5 sm:h-5 animate-spin\" />\r\n                ) : (\r\n                  <TbSend className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n                )}\r\n              </motion.button>\r\n            </div>\r\n\r\n            {/* Completely Hidden File Input */}\r\n            <input\r\n              ref={fileInputRef}\r\n              type=\"file\"\r\n              accept=\"image/*\"\r\n              onChange={handleImageSelect}\r\n              style={{ display: 'none' }}\r\n            />\r\n          </div>\r\n\r\n          {/* Helper Text - Updated for New Behavior */}\r\n          <p className=\"text-xs text-gray-500 mt-2 text-center px-2\">\r\n            {window.innerWidth <= 768\r\n              ? \"Click send button • Upload images for analysis\"\r\n              : \"Click send button to send • Enter for new line • Upload images for analysis\"\r\n            }\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,WAAW,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AAC1E,SAASC,eAAe,EAAEC,SAAS,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,CACvC;IACEiB,IAAI,EAAE,WAAW;IACjBC,OAAO,EAAE;EACX,CAAC,CACF,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM2B,cAAc,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM2B,YAAY,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM4B,WAAW,GAAG5B,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACAC,SAAS,CAAC,MAAM;IACd,IAAI;MACF;MACA,MAAM4B,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAC5D,IAAIF,cAAc,EAAE;QAClB,MAAMG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;QACjDM,eAAe,CAAC,MAAM;UACpBpB,WAAW,CAACiB,cAAc,CAAC;QAC7B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAG,eAAe,CAAC,MAAM;UACpBpB,WAAW,CAAC,CAAC;YACXC,IAAI,EAAE,WAAW;YACjBC,OAAO,EAAE;UACX,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;MACJ;MACAkB,eAAe,CAAC,MAAM;QACpBC,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACAF,eAAe,CAAC,MAAM;QACpBpB,WAAW,CAAC,CAAC;UACXC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;QACHmB,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnC,SAAS,CAAC,MAAM;IACd,IAAIsC,aAAa,IAAIzB,QAAQ,CAAC0B,MAAM,GAAG,CAAC,EAAE;MACxC,IAAI;QACFV,YAAY,CAACW,OAAO,CAAC,eAAe,EAAER,IAAI,CAACS,SAAS,CAAC5B,QAAQ,CAAC,CAAC;MACjE,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;IACF;EACF,CAAC,EAAE,CAACvB,QAAQ,EAAEyB,aAAa,CAAC,CAAC;;EAE7B;EACAtC,SAAS,CAAC,MAAM;IAAA,IAAA0C,qBAAA;IACd,CAAAA,qBAAA,GAAAjB,cAAc,CAACkB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAAChC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRG,YAAY,CAACH,IAAI,CAAC;MAClB,MAAMI,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIP,CAAC,IAAKzB,eAAe,CAACyB,CAAC,CAACE,MAAM,CAACM,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACR,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACxBN,YAAY,CAAC,IAAI,CAAC;IAClB7B,eAAe,CAAC,IAAI,CAAC;IACrB,IAAII,YAAY,CAACiB,OAAO,EAAE;MACxBjB,YAAY,CAACiB,OAAO,CAACe,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIZ,CAAC,IAAK;IAC5B;IACA,IAAIA,CAAC,CAACa,GAAG,KAAK,OAAO,EAAE;MACrB;MACA;IACF;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACC,SAAS,EAAE;IAElCxC,YAAY,CAAC,IAAI,CAAC;IAClByC,WAAW,CAAC,IAAI,CAAC;IAEjB,MAAMC,aAAa,GAAGJ,MAAM,CAACC,IAAI,CAAC,CAAC;IACnC,MAAMI,gBAAgB,GAAGH,SAAS;IAElC,IAAI;MACF,IAAII,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAID,gBAAgB,EAAE;QACpB,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,gBAAgB,CAAC;QAE1C,MAAMK,IAAI,GAAG,MAAMjE,SAAS,CAAC8D,QAAQ,CAAC;QAEtC,IAAIG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,OAAO,EAAE;UACjBL,QAAQ,GAAGI,IAAI,CAACE,GAAG,CAAC,CAAC;UACrBrC,OAAO,CAACsC,GAAG,CAAC,aAAa,EAAEP,QAAQ,CAAC;QACtC,CAAC,MAAM;UACL,MAAM,IAAIQ,KAAK,CAAC,qBAAqB,CAAC;QACxC;MACF;;MAEA;MACA,MAAMC,WAAW,GAAGT,QAAQ,GACxB;QACArD,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAE8D,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAEb;QAAc,CAAC,EACrC;UAAEY,IAAI,EAAE,WAAW;UAAEE,SAAS,EAAE;YAAEN,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACC;QAAErD,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAEkD;MAAc,CAAC;MAE5C,MAAMe,eAAe,GAAG,CAAC,GAAGpE,QAAQ,EAAEgE,WAAW,CAAC;MAClD3C,eAAe,CAAC,MAAM;QACpBpB,WAAW,CAACmE,eAAe,CAAC;QAC5BC,SAAS,CAAC,EAAE,CAAC;MACf,CAAC,CAAC;MACFzB,WAAW,CAAC,CAAC;;MAEb;MACA,MAAM0B,WAAW,GAAG;QAAEtE,QAAQ,EAAEoE;MAAgB,CAAC;MAEjD,MAAMG,OAAO,GAAG,MAAM9E,eAAe,CAAC6E,WAAW,CAAC;MAElD,MAAME,WAAW,GAAGD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEZ,IAAI;MACjCnC,OAAO,CAACsC,GAAG,CAAC,gBAAgB,EAAEU,WAAW,CAAC;;MAE1C;MACAnD,eAAe,CAAC,MAAM;QACpBpB,WAAW,CAAEwE,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UAAEvE,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAEqE;QAAY,CAAC,CAC5C,CAAC;MACJ,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CF,eAAe,CAAC,MAAM;QACpBpB,WAAW,CAAEwE,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;UAAEvE,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAE;QAAmD,CAAC,CACnF,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRQ,YAAY,CAAC,KAAK,CAAC;MACnByC,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,oBACExD,OAAA;IAAK8E,SAAS,EAAC,mFAAmF;IAAAC,QAAA,gBAEhG/E,OAAA;MAAOgF,uBAAuB,EAAE;QAC9BC,MAAM,EAAG;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACM;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAELrF,OAAA,CAACsF,MAAM,CAACC,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BZ,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAE/E/E,OAAA;QAAK8E,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxD/E,OAAA;UAAK8E,SAAS,EAAC,sGAAsG;UAAAC,QAAA,eACnH/E,OAAA,CAACL,OAAO;YAACmF,SAAS,EAAC;UAAoB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNrF,OAAA;UAAA+E,QAAA,gBACE/E,OAAA;YAAI8E,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5ErF,OAAA;YAAG8E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAA8C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbrF,OAAA;MAAK8E,SAAS,EAAC,iCAAiC;MAAAC,QAAA,eAC9C/E,OAAA;QAAK8E,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eACjE/E,OAAA;UACE8E,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAExF/E,OAAA,CAAC4F,eAAe;YAAAb,QAAA,EACb3E,QAAQ,CAACyF,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvB/F,OAAA,CAACsF,MAAM,CAACC,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BZ,SAAS,EAAG,uBAAsBgB,GAAG,CAACxF,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAgB,EAAE;cAAAyE,QAAA,GAEzFe,GAAG,CAACxF,IAAI,KAAK,WAAW,iBACvBN,OAAA;gBAAK8E,SAAS,EAAC,gIAAgI;gBAAAC,QAAA,eAC7I/E,OAAA,CAACL,OAAO;kBAACmF,SAAS,EAAC;gBAAkC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CACN,eAEDrF,OAAA;gBAAK8E,SAAS,EAAG,8BAA6BgB,GAAG,CAACxF,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,EAAG,EAAE;gBAAAyE,QAAA,eACnF/E,OAAA;kBACE8E,SAAS,EAAG,wCACVgB,GAAG,CAACxF,IAAI,KAAK,MAAM,GACf,gCAAgC,GAChC,2CACL,EAAE;kBAAAyE,QAAA,EAEFe,GAAG,CAACxF,IAAI,KAAK,WAAW,gBACvBN,OAAA;oBAAK8E,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC3Be,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEvF,OAAO,GACXyF,aAAa,CAACF,GAAG,CAACvF,OAAO,CAAC,gBAE1BP,OAAA;sBAAG8E,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAgC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAChE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAENrF,OAAA;oBAAA+E,QAAA,EACG,OAAOe,GAAG,CAACvF,OAAO,KAAK,QAAQ,gBAC9BP,OAAA;sBAAG8E,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAEe,GAAG,CAACvF;oBAAO;sBAAA2E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,GAEpDS,GAAG,CAACvF,OAAO,CAACsF,GAAG,CAAC,CAACI,IAAI,EAAEC,GAAG,KACxBD,IAAI,CAAC5B,IAAI,KAAK,MAAM,gBAClBrE,OAAA;sBAAa8E,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAEkB,IAAI,CAAC3B;oBAAI,GAApD4B,GAAG;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAqD,CAAC,gBAEjErF,OAAA;sBAAe8E,SAAS,EAAC,MAAM;sBAAAC,QAAA,eAC7B/E,OAAA;wBACEmG,GAAG,EAAEF,IAAI,CAAC1B,SAAS,CAACN,GAAI;wBACxBmC,GAAG,EAAC,uBAAuB;wBAC3BtB,SAAS,EAAC,wCAAwC;wBAClDuB,KAAK,EAAE;0BAAEC,SAAS,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;wBAAQ;sBAAE;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE;oBAAC,GANMa,GAAG;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAOR,CAET;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELS,GAAG,CAACxF,IAAI,KAAK,MAAM,iBAClBN,OAAA;gBAAK8E,SAAS,EAAC,+HAA+H;gBAAAC,QAAA,eAC5I/E,OAAA,CAACJ,MAAM;kBAACkF,SAAS,EAAC;gBAAkC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CACN;YAAA,GAxDIU,KAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyDA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa,CAAC,eAGlBrF,OAAA,CAAC4F,eAAe;YAAAb,QAAA,EACb0B,QAAQ,iBACPzG,OAAA,CAACsF,MAAM,CAACC,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BgB,IAAI,EAAE;gBAAEjB,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BZ,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAE/B/E,OAAA;gBAAK8E,SAAS,EAAC,kHAAkH;gBAAAC,QAAA,eAC/H/E,OAAA,CAACL,OAAO;kBAACmF,SAAS,EAAC;gBAAkC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNrF,OAAA;gBAAK8E,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,eAC7F/E,OAAA;kBAAK8E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB/E,OAAA,CAACsF,MAAM,CAACC,GAAG;oBACTI,OAAO,EAAE;sBAAEgB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBAAE,CAAE;oBAChCC,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,KAAK,EAAE;oBAAE,CAAE;oBAC1DlC,SAAS,EAAC;kBAAkC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACFrF,OAAA,CAACsF,MAAM,CAACC,GAAG;oBACTI,OAAO,EAAE;sBAAEgB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBAAE,CAAE;oBAChCC,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,KAAK,EAAE;oBAAI,CAAE;oBAC5DlC,SAAS,EAAC;kBAAkC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACFrF,OAAA,CAACsF,MAAM,CAACC,GAAG;oBACTI,OAAO,EAAE;sBAAEgB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBAAE,CAAE;oBAChCC,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,KAAK,EAAE;oBAAI,CAAE;oBAC5DlC,SAAS,EAAC;kBAAkC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC,eAElBrF,OAAA;YAAKiH,GAAG,EAAEjG;UAAe;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrF,OAAA;MAAK8E,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG/E,OAAA;QAAK8E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhC/E,OAAA,CAAC4F,eAAe;UAAAb,QAAA,EACbnE,YAAY,iBACXZ,OAAA,CAACsF,MAAM,CAACC,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEyB,MAAM,EAAE;YAAE,CAAE;YACnCvB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEyB,MAAM,EAAE;YAAO,CAAE;YACxCR,IAAI,EAAE;cAAEjB,OAAO,EAAE,CAAC;cAAEyB,MAAM,EAAE;YAAE,CAAE;YAChCpC,SAAS,EAAC,oFAAoF;YAAAC,QAAA,eAE9F/E,OAAA;cAAK8E,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C/E,OAAA;gBAAK8E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB/E,OAAA;kBACEmG,GAAG,EAAEvF,YAAa;kBAClBwF,GAAG,EAAC,SAAS;kBACbtB,SAAS,EAAC;gBAA0E;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC,eACFrF,OAAA;kBACEmH,OAAO,EAAEnE,WAAY;kBACrB8B,SAAS,EAAC,uJAAuJ;kBAAAC,QAAA,eAEjK/E,OAAA,CAACN,GAAG;oBAACoF,SAAS,EAAC;kBAAuB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNrF,OAAA;gBAAK8E,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB/E,OAAA;kBAAG8E,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC9ErF,OAAA;kBAAG8E,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,GAAA5E,UAAA,GAAEoD,SAAS,cAAApD,UAAA,uBAATA,UAAA,CAAWiH;gBAAI;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBrF,OAAA;UAAK8E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB/E,OAAA;YAAK8E,SAAS,EAAC,6LAA6L;YAAAC,QAAA,gBAE1M/E,OAAA;cACEmH,OAAO,EAAEA,CAAA;gBAAA,IAAAE,qBAAA;gBAAA,QAAAA,qBAAA,GAAMpG,YAAY,CAACiB,OAAO,cAAAmF,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;cAAA,CAAC;cAC7CxC,SAAS,EAAC,qFAAqF;cAC/FyC,KAAK,EAAC,cAAc;cAAAxC,QAAA,eAEpB/E,OAAA,CAACP,WAAW;gBAACqF,SAAS,EAAC;cAAuB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eAGTrF,OAAA;cACEiH,GAAG,EAAE/F,WAAY;cACjB+B,KAAK,EAAEI,MAAO;cACdmE,QAAQ,EAAGlF,CAAC,IAAKmC,SAAS,CAACnC,CAAC,CAACE,MAAM,CAACS,KAAK,CAAE;cAC3CwE,UAAU,EAAEvE,cAAe;cAC3BwE,WAAW,EAAEnB,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,oBAAoB,GAAG,yCAA0C;cACzG1B,SAAS,EAAC,oHAAoH;cAC9H6C,IAAI,EAAE,CAAE;cACRtB,KAAK,EAAE;gBACLa,MAAM,EAAE,MAAM;gBACdU,SAAS,EAAErB,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACrDF,SAAS,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cACjD,CAAE;cACFqB,OAAO,EAAGvF,CAAC,IAAK;gBACdA,CAAC,CAACE,MAAM,CAAC6D,KAAK,CAACa,MAAM,GAAG,MAAM;gBAC9B,MAAMZ,SAAS,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,EAAE,GAAG,GAAG;gBACrDlE,CAAC,CAACE,MAAM,CAAC6D,KAAK,CAACa,MAAM,GAAGY,IAAI,CAACC,GAAG,CAACzF,CAAC,CAACE,MAAM,CAACwF,YAAY,EAAE1B,SAAS,CAAC,GAAG,IAAI;cAC3E;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGFrF,OAAA,CAACsF,MAAM,CAAC2C,MAAM;cACZC,UAAU,EAAE;gBAAEvB,KAAK,EAAE;cAAK,CAAE;cAC5BwB,QAAQ,EAAE;gBAAExB,KAAK,EAAE;cAAK,CAAE;cAC1BQ,OAAO,EAAE/D,UAAW;cACpBgF,QAAQ,EAAEtH,SAAS,IAAK,CAACuC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACC,SAAW;cACtDuB,SAAS,EAAG,iCACVhE,SAAS,IAAK,CAACuC,MAAM,CAACC,IAAI,CAAC,CAAC,IAAI,CAACC,SAAU,GACvC,8CAA8C,GAC9C,oEACL,EAAE;cAAAwB,QAAA,EAEFjE,SAAS,gBACRd,OAAA,CAACqI,QAAQ;gBAACvD,SAAS,EAAC;cAAoC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE3DrF,OAAA,CAACR,MAAM;gBAACsF,SAAS,EAAC;cAAuB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC5C;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAGNrF,OAAA;YACEiH,GAAG,EAAEhG,YAAa;YAClBoD,IAAI,EAAC,MAAM;YACXiE,MAAM,EAAC,SAAS;YAChBd,QAAQ,EAAEnF,iBAAkB;YAC5BgE,KAAK,EAAE;cAAEkC,OAAO,EAAE;YAAO;UAAE;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNrF,OAAA;UAAG8E,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EACvDwB,MAAM,CAACC,UAAU,IAAI,GAAG,GACrB,gDAAgD,GAChD;QAA6E;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAAnF,EAAA,CA5aKD,WAAW;AAAAuI,EAAA,GAAXvI,WAAW;AA8ajB,eAAewI,kBAAkB;AAAC,IAAAD,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}