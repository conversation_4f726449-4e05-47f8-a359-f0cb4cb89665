import React, { useState, useEffect, useCallback, startTransition } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { message } from 'antd';
import { 
  Tb<PERSON><PERSON>, 
  TbArrowLeft, 
  TbArrowRight, 
  TbCheck
} from 'react-icons/tb';
import { getExamById } from '../../../apicalls/exams';
import { addReport } from '../../../apicalls/reports';

const QuizPlay = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [quiz, setQuiz] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [timeLeft, setTimeLeft] = useState(0);
  const [startTime, setStartTime] = useState(null);

  // Load quiz data
  useEffect(() => {
    const loadQuizData = async () => {
      try {
        setLoading(true);
        console.log('Loading quiz with ID:', id);
        
        if (!user || !user._id) {
          const token = localStorage.getItem('token');
          if (!token) {
            console.log('No token found, redirecting to login');
            message.error('Please login to access quizzes');
            startTransition(() => {
              navigate('/login');
            });
            return;
          }
        }

        const response = await getExamById({ examId: id });
        console.log('Quiz API response:', response);
        
        if (response.success) {
          if (!response.data) {
            message.error('Quiz data not found');
            startTransition(() => {
              navigate('/quiz');
            });
            return;
          }
          
          if (!response.data.questions || response.data.questions.length === 0) {
            message.error('This quiz has no questions available');
            startTransition(() => {
              navigate('/quiz');
            });
            return;
          }

          setQuiz(response.data);
          setQuestions(response.data.questions);
          setAnswers(new Array(response.data.questions.length).fill(''));
          setTimeLeft(response.data.duration * 60);
          setStartTime(new Date());
          console.log('Quiz loaded successfully:', response.data);
        } else {
          console.error('Quiz API error:', response.message);
          message.error(response.message || 'Failed to load quiz');
          startTransition(() => {
            navigate('/quiz');
          });
        }
      } catch (error) {
        console.error('Quiz loading error:', error);
        message.error('Failed to load quiz. Please try again.');
        startTransition(() => {
          navigate('/quiz');
        });
      } finally {
        setLoading(false);
      }
    };

    if (id && user) {
      loadQuizData();
    }
  }, [id, navigate, user]);

  // Submit quiz function
  const handleSubmitQuiz = useCallback(async () => {
    console.log('🚀 Submit button clicked - showing loading overlay');
    console.log('Current submitting state:', submitting);

    try {
      // Show loading immediately
      setSubmitting(true);
      console.log('✅ setSubmitting(true) called');
      console.log('📝 Starting quiz marking process...');

      let currentUser = user;
      if (!currentUser || !currentUser._id) {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          try {
            currentUser = JSON.parse(storedUser);
          } catch (error) {
            console.error('Error parsing stored user data:', error);
            startTransition(() => {
              navigate('/login');
            });
            return;
          }
        }
      }

      if (!currentUser || !currentUser._id) {
        message.error('User session expired. Please login again.');
        startTransition(() => {
          navigate('/login');
        });
        return;
      }

      const endTime = new Date();
      const timeTaken = Math.floor((endTime - startTime) / 1000);

      let correctAnswers = 0;
      const resultDetails = questions.map((question, index) => {
        const userAnswer = answers[index];
        let isCorrect = false;
        let actualCorrectAnswer = '';

        // Determine the correct answer based on question type
        const questionType = question.type || question.answerType || 'mcq';

        if (questionType.toLowerCase() === 'mcq' || questionType === 'Options') {
          // For MCQ questions, check both correctAnswer and correctOption
          if (question.options && typeof question.options === 'object') {
            // If correctAnswer is a key (like "B"), get the actual text
            if (question.correctAnswer && question.options[question.correctAnswer]) {
              actualCorrectAnswer = question.options[question.correctAnswer];
              isCorrect = userAnswer === actualCorrectAnswer;
            }
            // If correctOption is available, use it
            else if (question.correctOption && question.options[question.correctOption]) {
              actualCorrectAnswer = question.options[question.correctOption];
              isCorrect = userAnswer === actualCorrectAnswer;
            }
            // If correctAnswer is already the full text
            else if (question.correctAnswer) {
              actualCorrectAnswer = question.correctAnswer;
              isCorrect = userAnswer === actualCorrectAnswer;
            }
          } else {
            // Fallback for other option formats
            actualCorrectAnswer = question.correctAnswer || question.correctOption || '';
            isCorrect = userAnswer === actualCorrectAnswer;
          }
        } else {
          // For fill-in-the-blank and other types, direct comparison
          actualCorrectAnswer = question.correctAnswer || '';
          isCorrect = userAnswer?.toLowerCase().trim() === actualCorrectAnswer?.toLowerCase().trim();
        }

        if (isCorrect) correctAnswers++;

        return {
          questionId: question._id || `question_${index}`,
          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,
          questionText: question.name || `Question ${index + 1}`,
          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),
          correctAnswer: actualCorrectAnswer,
          isCorrect,
          questionType: questionType,
          options: question.options || null,
          questionImage: question.image || question.questionImage || question.imageUrl || null,
          image: question.image || question.questionImage || question.imageUrl || null
        };
      });

      const percentage = Math.round((correctAnswers / questions.length) * 100);
      // Use the exam's actual passing marks instead of hardcoded 60%
      const passingPercentage = quiz.passingMarks || quiz.passingPercentage || 60;
      const verdict = percentage >= passingPercentage ? 'Pass' : 'Fail';

      const reportData = {
        exam: id,
        user: currentUser._id,
        result: {
          correctAnswers,
          wrongAnswers: questions.length - correctAnswers,
          percentage,
          score: percentage,
          verdict: verdict,
          timeTaken,
          timeSpent: timeTaken, // Add timeSpent for XP calculation
          points: correctAnswers * 10,
          totalQuestions: questions.length
        }
      };

      try {
        const response = await addReport(reportData);

        if (response.success) {
          console.log('✅ Quiz submitted successfully, preparing results...');

          // Include XP data in navigation state
          const navigationState = {
            percentage,
            correctAnswers,
            totalQuestions: questions.length,
            timeTaken,
            resultDetails,
            xpData: response.xpData || null, // Include XP data from server response
            quizName: quiz.name,
            quizSubject: quiz.subject || quiz.category,
            passingPercentage: passingPercentage, // Include actual passing marks
            verdict: verdict // Include calculated verdict
          };

          // Brief delay to show loading screen
          await new Promise(resolve => setTimeout(resolve, 1000));

          console.log('🎯 Navigating to results page...');
          startTransition(() => {
            navigate(`/quiz/${id}/result`, {
              state: navigationState
            });
          });
        } else {
          console.error('❌ Quiz submission failed:', response.message);
          // Show error in the loading overlay instead of notification
          setTimeout(() => {
            setSubmitting(false);
            message.error(response.message || 'Failed to submit quiz');
          }, 1000);
          return;
        }
      } catch (apiError) {
        console.error('❌ API Error during submission:', apiError);
        // Show error in the loading overlay instead of notification
        setTimeout(() => {
          setSubmitting(false);
          message.error('Network error while submitting quiz');
        }, 1000);
        return;
      }
    } catch (error) {
      console.error('Quiz submission error:', error);
      // Show error in the loading overlay instead of notification
      setTimeout(() => {
        setSubmitting(false);
        message.error('Failed to submit quiz');
      }, 1000);
      return;
    } finally {
      setSubmitting(false);
    }
  }, [startTime, questions, answers, id, navigate, user]);

  // Timer countdown
  useEffect(() => {
    if (timeLeft <= 0) {
      // Don't auto-submit, just stop the timer
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Handle answer selection
  const handleAnswerSelect = (answer) => {
    const newAnswers = [...answers];
    newAnswers[currentQuestion] = answer;
    setAnswers(newAnswers);
  };

  // Navigation functions
  const goToNext = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Render different answer sections based on question type
  const renderAnswerSection = () => {
    const questionType = currentQ.type || currentQ.answerType || 'mcq';



    switch (questionType.toLowerCase()) {
      case 'mcq':
      case 'multiple-choice':
      case 'multiplechoice':
        return renderMultipleChoice();

      case 'fill':
      case 'fill-in-the-blank':
      case 'fillblank':
      case 'text':
        return renderFillInTheBlank();

      case 'image':
      case 'diagram':
        return renderImageQuestion();

      default:
        // Default to multiple choice if type is unclear
        return renderMultipleChoice();
    }
  };

  // Render multiple choice options
  const renderMultipleChoice = () => {
    let options = [];

    // Handle different option formats
    if (Array.isArray(currentQ.options)) {
      options = currentQ.options;
    } else if (currentQ.options && typeof currentQ.options === 'object') {
      // Handle object format like {A: "option1", B: "option2"}
      options = Object.values(currentQ.options);
    } else if (currentQ.option1 && currentQ.option2) {
      // Handle individual option properties
      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);
    }

    if (!options || options.length === 0) {
      // Show debug info and fallback options for testing
      return (
        <div className="space-y-4">
          <div className="text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 font-medium">No options found for this question</p>
            <details className="mt-2">
              <summary className="text-sm text-yellow-600 cursor-pointer">Show question data</summary>
              <pre className="text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(currentQ, null, 2)}
              </pre>
            </details>
          </div>

          {/* Fallback test options */}
          <div className="space-y-3">
            {['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {
              const optionLetter = String.fromCharCode(65 + index);
              const isSelected = answers[currentQuestion] === option;

              return (
                <button
                  key={index}
                  onClick={() => handleAnswerSelect(option)}
                  className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${
                    isSelected
                      ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'
                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
                  }`}
                >
                  <div className="flex items-start gap-4">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${
                      isSelected
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {optionLetter}
                    </div>
                    <span className="text-lg leading-relaxed flex-1 text-left text-gray-900">
                      {option}
                    </span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {options.map((option, index) => {
          const optionLetter = String.fromCharCode(65 + index);
          const isSelected = answers[currentQuestion] === option;

          return (
            <button
              key={index}
              onClick={() => handleAnswerSelect(option)}
              className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${
                isSelected
                  ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'
                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
              }`}
            >
              <div className="flex items-start gap-4">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${
                  isSelected
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {optionLetter}
                </div>
                <span className="text-lg leading-relaxed flex-1 text-left text-gray-900">
                  {typeof option === 'string' ? option : JSON.stringify(option)}
                </span>
              </div>
            </button>
          );
        })}
      </div>
    );
  };

  // Render fill in the blank input
  const renderFillInTheBlank = () => {
    return (
      <div className="space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-800 text-sm font-medium mb-2">Fill in the blank:</p>
          <p className="text-gray-700">Type your answer in the box below</p>
        </div>
        <div className="relative">
          <input
            type="text"
            value={answers[currentQuestion] || ''}
            onChange={(e) => handleAnswerSelect(e.target.value)}
            placeholder="Type your answer here..."
            className="w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors"
            autoFocus
          />
        </div>
      </div>
    );
  };

  // Render image/diagram question (could have options or be fill-in)
  const renderImageQuestion = () => {
    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {
      return renderMultipleChoice();
    } else {
      return renderFillInTheBlank();
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading quiz...</p>
        </div>
      </div>
    );
  }

  if (!quiz || !questions.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4">
          <div className="text-center">
            <h2 className="text-xl font-bold text-gray-900 mb-4">No Questions Available</h2>
            <p className="text-gray-600 mb-6">This quiz doesn't have any questions yet.</p>
            <button
              onClick={() => startTransition(() => navigate('/quiz'))}
              className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Quizzes
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Safety check for current question
  if (!questions[currentQuestion]) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4">
          <div className="text-center">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Question Not Found</h2>
            <p className="text-gray-600 mb-6">Unable to load the current question.</p>
            <button
              onClick={() => startTransition(() => navigate('/quiz'))}
              className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Quizzes
            </button>
          </div>
        </div>
      </div>
    );
  }

  const currentQ = questions[currentQuestion];
  const isLastQuestion = currentQuestion === questions.length - 1;

  // Ensure currentQ is a valid object
  if (!currentQ || typeof currentQ !== 'object') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4">
          <div className="text-center">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Invalid Question Data</h2>
            <p className="text-gray-600 mb-6">The question data is corrupted or invalid.</p>
            <button
              onClick={() => startTransition(() => navigate('/quiz'))}
              className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Quizzes
            </button>
          </div>
        </div>
      </div>
    );
  }



  // Show simple loading screen when submitting
  if (submitting) {
    return (
      <>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 9999
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '40px',
            borderRadius: '20px',
            textAlign: 'center',
            boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              border: '4px solid #e5e7eb',
              borderTop: '4px solid #3b82f6',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 20px'
            }}></div>
            <h2 style={{
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#1f2937',
              margin: 0
            }}>Loading...</h2>
          </div>
        </div>
      </>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => startTransition(() => navigate('/quiz'))}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <TbArrowLeft className="w-6 h-6 text-gray-600" />
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{quiz.name}</h1>
                <p className="text-sm text-gray-600">
                  Question {currentQuestion + 1} of {questions.length}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
                timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'
              }`}>
                <TbClock className="w-5 h-5" />
                <span className="font-semibold">{formatTime(timeLeft)}</span>
              </div>
            </div>
          </div>
          
          {/* Progress bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 transition-all duration-300">
          {/* Question */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {typeof currentQ.name === 'string' ? currentQ.name : 'Question'}
            </h2>
            
            {currentQ.image && (
              <div className="mb-6 bg-gray-50 rounded-lg p-4">
                <img
                  src={currentQ.image}
                  alt="Question diagram"
                  className="max-w-full h-auto rounded-lg shadow-lg mx-auto block"
                  style={{ maxHeight: '400px' }}
                  onError={(e) => {
                    e.target.style.display = 'none';
                    // Show fallback message
                    const fallback = document.createElement('div');
                    fallback.className = 'text-center py-8 text-gray-500';
                    fallback.innerHTML = '<p>Could not load diagram</p>';
                    e.target.parentNode.appendChild(fallback);
                  }}
                />
              </div>
            )}
          </div>

          {/* Answer Section - Different types based on question type */}
          <div className="space-y-4 mb-8">
            {renderAnswerSection()}
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center">
            <button
              onClick={goToPrevious}
              disabled={currentQuestion === 0}
              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${
                currentQuestion === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <TbArrowLeft className="w-5 h-5" />
              Previous
            </button>

            {isLastQuestion ? (
              <button
                onClick={handleSubmitQuiz}
                disabled={submitting}
                className={`flex items-center gap-2 px-8 py-3 rounded-lg font-semibold transition-colors ${
                  submitting
                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <TbCheck className="w-5 h-5" />
                    Submit Quiz
                  </>
                )}
              </button>
            ) : (
              <button
                onClick={goToNext}
                className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                Next
                <TbArrowRight className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizPlay;
