import React, { useState, useRef, useEffect } from 'react';
import { TbRobot } from 'react-icons/tb';
import { chatWithChatGPT, uploadImg } from '../apicalls/chat';

const FloatingBrainwaveAI = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState([
    { role: 'assistant', content: 'Hi! I\'m <PERSON><PERSON> AI, your study assistant. How can I help you today?' }
  ]);
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);

  // Auto scroll to bottom
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle image selection
  const handleImageSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  // Remove selected image
  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const sendMessage = async () => {
    if (!input.trim() && !selectedImage) return;

    const userMessage = input.trim();
    const imageFile = selectedImage;

    setInput('');
    removeImage();
    setIsTyping(true);

    try {
      let imageUrl = null;

      // Upload image if selected
      if (imageFile) {
        console.log('Uploading image:', imageFile.name);
        const formData = new FormData();
        formData.append("image", imageFile);
        const uploadResult = await uploadImg(formData);
        console.log('Upload result:', uploadResult);

        if (uploadResult?.success) {
          imageUrl = uploadResult.url;
          console.log('Image uploaded successfully:', imageUrl);
        } else {
          console.error('Image upload failed:', uploadResult);
        }
      }

      // Create user message
      const newUserMessage = imageUrl
        ? {
            role: "user",
            content: [
              { type: "text", text: userMessage },
              { type: "image_url", image_url: { url: imageUrl } }
            ]
          }
        : { role: "user", content: userMessage };

      // Add user message to chat
      setMessages(prev => [...prev, newUserMessage]);

      // Get AI response from ChatGPT
      console.log('Sending to ChatGPT:', [...messages, newUserMessage]);
      const response = await chatWithChatGPT({
        messages: [...messages, newUserMessage]
      });
      console.log('ChatGPT response:', response);

      if (response?.success && response?.data) {
        setMessages(prev => [...prev, {
          role: 'assistant',
          content: response.data
        }]);
      } else {
        console.error('ChatGPT error:', response);
        setMessages(prev => [...prev, {
          role: 'assistant',
          content: `I'm sorry, I couldn't process your request right now. Error: ${response?.error || 'Unknown error'}. Please try again in a moment.`
        }]);
      }
    } catch (error) {
      console.error('Chat error:', error);
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: "I'm experiencing some technical difficulties. Please try again later."
      }]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  if (!isOpen) {
    return (
      <div
        onClick={() => setIsOpen(true)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          width: '60px',
          height: '60px',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',
          zIndex: 1000,
          transition: 'all 0.3s ease',
          animation: 'pulse 2s infinite'
        }}
        onMouseEnter={(e) => {
          e.target.style.transform = 'scale(1.1)';
          e.target.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.6)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = 'scale(1)';
          e.target.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.4)';
        }}
      >
        <TbRobot style={{ color: 'white', fontSize: '28px' }} />
        <style>{`
          @keyframes pulse {
            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }
            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }
            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        width: isMinimized ? '300px' : '380px',
        height: isMinimized ? '60px' : '500px',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)',
        borderRadius: '20px',
        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        transition: 'all 0.3s ease'
      }}
      onWheel={(e) => {
        // Prevent background scrolling when scrolling over the AI chat
        e.stopPropagation();
      }}
      onTouchMove={(e) => {
        // Prevent background scrolling on mobile
        e.stopPropagation();
      }}
    >
      {/* Header */}
      <div
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: '16px 20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderRadius: '20px 20px 0 0'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div
            style={{
              width: '32px',
              height: '32px',
              background: 'rgba(255, 255, 255, 0.2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <TbRobot style={{ color: 'white', fontSize: '18px' }} />
          </div>
          <div>
            <h3 style={{ margin: 0, color: 'white', fontSize: '16px', fontWeight: '600' }}>
              Brainwave AI
            </h3>
            <p style={{ margin: 0, color: 'rgba(255, 255, 255, 0.8)', fontSize: '12px' }}>
              Always here to help
            </p>
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            style={{
              background: 'rgba(255, 255, 255, 0.25)',
              border: 'none',
              borderRadius: '8px',
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              backdropFilter: 'blur(10px)'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'rgba(255, 255, 255, 0.4)';
              e.target.style.transform = 'scale(1.05)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'rgba(255, 255, 255, 0.25)';
              e.target.style.transform = 'scale(1)';
            }}
          >
            {isMinimized ? (
              <span style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>⬜</span>
            ) : (
              <span style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>➖</span>
            )}
          </button>

          <button
            onClick={() => setIsOpen(false)}
            style={{
              background: 'rgba(255, 255, 255, 0.25)',
              border: 'none',
              borderRadius: '8px',
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              backdropFilter: 'blur(10px)'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'rgba(255, 60, 60, 0.4)';
              e.target.style.transform = 'scale(1.05)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'rgba(255, 255, 255, 0.25)';
              e.target.style.transform = 'scale(1)';
            }}
          >
            <span style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>✕</span>
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div
            style={{
              flex: 1,
              padding: '20px 20px 0 20px',
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              gap: '16px',
              scrollBehavior: 'smooth',
              scrollbarWidth: 'thin',
              scrollbarColor: '#cbd5e1 transparent'
            }}
            className="custom-scrollbar"
            onWheel={(e) => {
              // Allow scrolling within the messages container
              const element = e.currentTarget;
              const { scrollTop, scrollHeight, clientHeight } = element;

              // If at top and scrolling up, or at bottom and scrolling down, prevent propagation
              if ((scrollTop === 0 && e.deltaY < 0) ||
                  (scrollTop + clientHeight >= scrollHeight && e.deltaY > 0)) {
                e.preventDefault();
                e.stopPropagation();
              }
            }}
          >
            {messages.map((msg, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'
                }}
              >
                <div
                  style={{
                    maxWidth: '85%',
                    padding: '12px 16px',
                    borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',
                    background: msg.role === 'user'
                      ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                      : '#f8fafc',
                    color: msg.role === 'user' ? 'white' : '#334155',
                    fontSize: '14px',
                    lineHeight: '1.5',
                    boxShadow: msg.role === 'user'
                      ? '0 4px 12px rgba(102, 126, 234, 0.3)'
                      : '0 2px 8px rgba(0, 0, 0, 0.1)',
                    wordWrap: 'break-word'
                  }}
                >
                  {/* Handle different message content types */}
                  {typeof msg.content === 'string' ? (
                    <div style={{ whiteSpace: 'pre-wrap' }}>{msg.content}</div>
                  ) : Array.isArray(msg.content) ? (
                    msg.content.map((item, idx) => (
                      <div key={idx}>
                        {item.type === 'text' && (
                          <div style={{ whiteSpace: 'pre-wrap', marginBottom: item.text ? '8px' : '0' }}>
                            {item.text}
                          </div>
                        )}
                        {item.type === 'image_url' && (
                          <img
                            src={item.image_url.url}
                            alt="User upload"
                            style={{
                              maxWidth: '100%',
                              height: 'auto',
                              borderRadius: '12px',
                              maxHeight: '200px',
                              objectFit: 'cover',
                              border: '2px solid rgba(255, 255, 255, 0.2)',
                              marginTop: '4px'
                            }}
                          />
                        )}
                      </div>
                    ))
                  ) : (
                    <div>Invalid message format</div>
                  )}
                </div>
              </div>
            ))}

            {isTyping && (
              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
                <div
                  style={{
                    padding: '12px 16px',
                    borderRadius: '18px 18px 18px 4px',
                    background: '#f8fafc',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                    <div
                      style={{
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                        background: '#94a3b8',
                        animation: 'bounce 1.4s infinite ease-in-out'
                      }}
                    />
                    <div
                      style={{
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                        background: '#94a3b8',
                        animation: 'bounce 1.4s infinite ease-in-out 0.16s'
                      }}
                    />
                    <div
                      style={{
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                        background: '#94a3b8',
                        animation: 'bounce 1.4s infinite ease-in-out 0.32s'
                      }}
                    />
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input Section - Sticky */}
          <div
            style={{
              position: 'sticky',
              bottom: 0,
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              borderTop: '1px solid rgba(0, 0, 0, 0.08)',
              padding: '16px 20px 20px',
              zIndex: 10
            }}
          >
            {/* Image Preview */}
            {imagePreview && (
              <div
                style={{
                  marginBottom: '12px',
                  padding: '12px',
                  background: '#f1f5f9',
                  borderRadius: '12px',
                  border: '1px solid #e2e8f0'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <div style={{ position: 'relative' }}>
                    <img
                      src={imagePreview}
                      alt="Preview"
                      style={{
                        width: '60px',
                        height: '60px',
                        objectFit: 'cover',
                        borderRadius: '8px',
                        border: '2px solid #e2e8f0'
                      }}
                    />
                    <button
                      onClick={removeImage}
                      style={{
                        position: 'absolute',
                        top: '-6px',
                        right: '-6px',
                        width: '20px',
                        height: '20px',
                        background: '#ef4444',
                        color: 'white',
                        borderRadius: '50%',
                        border: 'none',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold'
                      }}
                    >
                      ×
                    </button>
                  </div>
                  <div style={{ flex: 1 }}>
                    <p style={{ fontSize: '13px', fontWeight: '600', color: '#374151', margin: 0 }}>
                      Image attached
                    </p>
                    <p style={{ fontSize: '11px', color: '#6b7280', margin: 0 }}>
                      {selectedImage?.name}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Input Area */}
            <div
              style={{
                display: 'flex',
                gap: '8px',
                background: '#f8fafc',
                borderRadius: '16px',
                padding: '8px',
                border: '2px solid #e2e8f0',
                transition: 'border-color 0.2s ease'
              }}
              onFocus={(e) => e.target.style.borderColor = '#667eea'}
              onBlur={(e) => e.target.style.borderColor = '#e2e8f0'}
            >
              {/* Attachment Button */}
              <button
                onClick={() => fileInputRef.current?.click()}
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  width: '40px',
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'scale(1.05)';
                  e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'scale(1)';
                  e.target.style.boxShadow = '0 2px 8px rgba(102, 126, 234, 0.3)';
                }}
                title="Attach image"
              >
                <span style={{ color: 'white', fontSize: '18px', fontWeight: 'bold' }}>📎</span>
              </button>

              {/* Text Input */}
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask me anything..."
                style={{
                  flex: 1,
                  border: 'none',
                  background: 'transparent',
                  outline: 'none',
                  fontSize: '14px',
                  color: '#334155',
                  padding: '12px 16px',
                  fontFamily: 'inherit'
                }}
              />

              {/* Send Button */}
              <button
                onClick={sendMessage}
                disabled={!input.trim() && !selectedImage}
                style={{
                  background: (input.trim() || selectedImage)
                    ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                    : '#e2e8f0',
                  border: 'none',
                  borderRadius: '12px',
                  width: '40px',
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: (input.trim() || selectedImage) ? 'pointer' : 'not-allowed',
                  transition: 'all 0.2s ease',
                  boxShadow: (input.trim() || selectedImage)
                    ? '0 2px 8px rgba(102, 126, 234, 0.3)'
                    : 'none'
                }}
                onMouseEnter={(e) => {
                  if (input.trim() || selectedImage) {
                    e.target.style.transform = 'scale(1.05)';
                    e.target.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';
                  }
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'scale(1)';
                  e.target.style.boxShadow = (input.trim() || selectedImage)
                    ? '0 2px 8px rgba(102, 126, 234, 0.3)'
                    : 'none';
                }}
              >
                <span style={{
                  color: (input.trim() || selectedImage) ? 'white' : '#94a3b8',
                  fontSize: '18px',
                  fontWeight: 'bold'
                }}>
                  ➤
                </span>
              </button>
            </div>

            {/* Hidden File Input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageSelect}
              style={{ display: 'none' }}
            />

            {/* Helper Text */}
            <p style={{
              fontSize: '11px',
              color: '#94a3b8',
              textAlign: 'center',
              margin: '8px 0 0 0'
            }}>
              Press Enter to send • Attach images for analysis
            </p>
          </div>
        </>
      )}

      <style>{`
        @keyframes bounce {
          0%, 80%, 100% { transform: scale(0); }
          40% { transform: scale(1); }
        }

        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }

        /* Force white icons in floating AI */
        .floating-ai-white-icon {
          color: #FFFFFF !important;
          fill: #FFFFFF !important;
        }

        .floating-ai-white-icon svg {
          color: #FFFFFF !important;
          fill: #FFFFFF !important;
        }

        .floating-ai-white-icon path {
          fill: #FFFFFF !important;
          stroke: #FFFFFF !important;
        }
      `}</style>
    </div>
  );
};

export default FloatingBrainwaveAI;
