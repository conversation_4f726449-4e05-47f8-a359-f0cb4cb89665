{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbSearch, TbFilter, TbClock, TbQuestionMark, TbTrophy, TbPlayerPlay, TbBrain, TbTarget, TbCheck, TbX, TbStar, TbHome, TbBolt, TbRefresh } from 'react-icons/tb';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReportsByUser } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport './animations.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = () => {\n  _s();\n  const [exams, setExams] = useState([]);\n  const [filteredExams, setFilteredExams] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [userResults, setUserResults] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [lastRefresh, setLastRefresh] = useState(null);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getUserResults = useCallback(async () => {\n    try {\n      if (!(user !== null && user !== void 0 && user._id)) return;\n      const response = await getAllReportsByUser({\n        userId: user._id\n      });\n      if (response.success) {\n        const resultsMap = {};\n        response.data.forEach(report => {\n          var _report$exam;\n          const examId = (_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam._id;\n          if (!examId || !report.result) return;\n\n          // Extract data from the result object\n          const result = report.result;\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\n            resultsMap[examId] = {\n              verdict: result.verdict,\n              percentage: result.percentage,\n              correctAnswers: result.correctAnswers,\n              wrongAnswers: result.wrongAnswers,\n              totalQuestions: result.totalQuestions,\n              obtainedMarks: result.obtainedMarks,\n              totalMarks: result.totalMarks,\n              score: result.score,\n              points: result.points,\n              xpEarned: result.xpEarned || result.points || result.xpGained || 0,\n              timeTaken: report.timeTaken,\n              completedAt: report.createdAt\n            };\n          }\n        });\n        setUserResults(resultsMap);\n      }\n    } catch (error) {\n      console.error('Error fetching user results:', error);\n    }\n  }, [user === null || user === void 0 ? void 0 : user._id]);\n\n  // Define getExams function outside useEffect so it can be called from other functions\n  const getExams = useCallback(async (isRefresh = false) => {\n    try {\n      // Safety check: ensure user exists before proceeding\n      if (!user) {\n        console.log(\"User not loaded yet, skipping exam fetch\");\n        return;\n      }\n\n      // Check cache first (unless refreshing)\n      if (!isRefresh) {\n        const cachedExams = localStorage.getItem('user_exams_cache');\n        const cacheTime = localStorage.getItem('user_exams_cache_time');\n        const now = Date.now();\n\n        // Use cache if less than 3 minutes old\n        if (cachedExams && cacheTime && now - parseInt(cacheTime) < 180000) {\n          const cached = JSON.parse(cachedExams);\n          setExams(cached);\n          setLastRefresh(new Date(parseInt(cacheTime)));\n          if (user !== null && user !== void 0 && user.class) {\n            setSelectedClass(String(user.class));\n          }\n          setLoading(false);\n          return;\n        }\n      }\n      if (isRefresh) {\n        setRefreshing(true);\n      } else {\n        dispatch(ShowLoading());\n      }\n      const response = await getAllExams();\n      if (isRefresh) {\n        setRefreshing(false);\n      } else {\n        dispatch(HideLoading());\n      }\n      if (response.success) {\n        console.log('Raw exams from API:', response.data.length);\n        console.log('User level:', user === null || user === void 0 ? void 0 : user.level);\n\n        // Filter exams by user's level with proper null checks\n        const userLevelExams = response.data.filter(exam => {\n          if (!exam.level || !user || !user.level) return false;\n          return exam.level.toLowerCase() === user.level.toLowerCase();\n        });\n        console.log('User level exams after filtering:', userLevelExams.length);\n        const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n        setExams(sortedExams);\n        setLastRefresh(new Date());\n\n        // Cache the exams data\n        localStorage.setItem('user_exams_cache', JSON.stringify(sortedExams));\n        localStorage.setItem('user_exams_cache_time', Date.now().toString());\n\n        // Set default class filter to user's class\n        if (user !== null && user !== void 0 && user.class) {\n          setSelectedClass(String(user.class));\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      if (isRefresh) {\n        setRefreshing(false);\n      } else {\n        dispatch(HideLoading());\n      }\n      message.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [dispatch, user]);\n  useEffect(() => {\n    getExams(false); // Initial load\n    getUserResults();\n  }, [getExams, getUserResults]);\n\n  // Real-time updates for quiz completion and new exams\n  useEffect(() => {\n    // Listen for real-time updates from quiz completion\n    const handleRankingUpdate = () => {\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\n      getUserResults(); // Refresh user results to show updated XP\n    };\n\n    // Listen for new exam creation events\n    const handleNewExam = () => {\n      console.log('🆕 New exam created - refreshing exam list...');\n      if (user) {\n        getExams(true); // Use refresh mode\n        getUserResults();\n      }\n    };\n\n    // Listen for window focus to refresh data when returning from quiz\n    const handleWindowFocus = () => {\n      console.log('🎯 Quiz listing - window focused, refreshing data...');\n      getUserResults();\n      // Also refresh exams list to show newly generated exams\n      if (user) {\n        console.log('🔄 Refreshing exams list for new exams...');\n        getExams(true); // Use refresh mode\n      }\n    };\n\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('newExamCreated', handleNewExam);\n    return () => {\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('newExamCreated', handleNewExam);\n    };\n  }, []);\n\n  // Periodic refresh to ensure quiz list stays up to date\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      if (user && !loading && !refreshing) {\n        console.log('🔄 Periodic refresh of quiz list...');\n        getExams(true); // Use refresh mode\n      }\n    }, 5 * 60 * 1000); // Refresh every 5 minutes\n\n    return () => clearInterval(refreshInterval);\n  }, [user, loading, refreshing]);\n  useEffect(() => {\n    console.log('Filtering exams:', {\n      exams: exams.length,\n      searchTerm,\n      selectedClass\n    });\n    let filtered = exams;\n    if (searchTerm) {\n      filtered = filtered.filter(exam => {\n        var _exam$name, _exam$subject;\n        return ((_exam$name = exam.name) === null || _exam$name === void 0 ? void 0 : _exam$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.toLowerCase().includes(searchTerm.toLowerCase()));\n      });\n    }\n    if (selectedClass) {\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\n    }\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n    console.log('Filtered exams result:', filtered.length);\n    setFilteredExams(filtered);\n  }, [exams, searchTerm, selectedClass]);\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\n  const handleQuizStart = quiz => {\n    if (!quiz || !quiz._id) {\n      message.error('Invalid quiz selected. Please try again.');\n      return;\n    }\n\n    // Validate MongoDB ObjectId format (24 character hex string)\n    const objectIdRegex = /^[0-9a-fA-F]{24}$/;\n    if (!objectIdRegex.test(quiz._id)) {\n      message.error('Invalid quiz ID format. Please try again.');\n      return;\n    }\n    startTransition(() => {\n      navigate(`/quiz/${quiz._id}/play`);\n    });\n  };\n\n  // Manual refresh function\n  const handleRefresh = async () => {\n    console.log('🔄 Manual refresh triggered...');\n    if (refreshing || loading) return; // Prevent multiple simultaneous refreshes\n\n    try {\n      if (user) {\n        await getExams(true); // Use refresh mode\n        await getUserResults();\n        message.success('Quiz list refreshed successfully!');\n      }\n    } catch (error) {\n      message.error('Failed to refresh quiz list');\n    }\n  };\n  const handleQuizView = quiz => {\n    if (!quiz || !quiz._id) {\n      message.error('Invalid quiz selected. Please try again.');\n      return;\n    }\n    // Check if user has attempted this quiz\n    const userResult = userResults[quiz._id];\n    if (!userResult) {\n      message.info('You need to attempt this quiz first to view results.');\n      return;\n    }\n    startTransition(() => {\n      navigate(`/quiz/${quiz._id}/result`);\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading quizzes...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8 sm:mb-12 opacity-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbBrain, {\n            className: \"w-8 h-8 sm:w-10 sm:h-10 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\",\n          children: \"Challenge Your Brain, Beat the Rest\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\",\n          children: \"Test your knowledge with our comprehensive quizzes designed for You!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-green-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [filteredExams.length, \" Available Quizzes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-blue-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Level: \", (user === null || user === void 0 ? void 0 : user.level) || 'All Levels']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), lastRefresh && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-xs text-gray-400\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-gray-400 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Updated: \", lastRefresh.toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto mb-8 sm:mb-12 opacity-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-4 sm:p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-3 sm:gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(TbSearch, {\n                  className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search quizzes by name or subject...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sm:w-48 md:w-64\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(TbFilter, {\n                    className: \"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: selectedClass,\n                  onChange: e => setSelectedClass(e.target.value),\n                  className: \"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Classes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 21\n                  }, this), availableClasses.map(className => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: className,\n                    children: [\"Class \", className]\n                  }, className, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleRefresh,\n              disabled: loading || refreshing,\n              className: \"flex items-center justify-center px-4 py-2.5 sm:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95\",\n              title: \"Refresh quiz list\",\n              children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                className: `h-4 w-4 sm:h-5 sm:w-5 ${loading || refreshing ? 'animate-spin' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 hidden sm:inline text-sm sm:text-base\",\n                children: refreshing ? 'Refreshing...' : 'Refresh'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"opacity-100\",\n        children: filteredExams.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12 sm:py-16\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n              className: \"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-2\",\n              children: \"No Quizzes Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm sm:text-base\",\n              children: searchTerm || selectedClass ? \"Try adjusting your search or filter criteria.\" : \"No quizzes are available for your level at the moment.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n          children: filteredExams.map((quiz, index) => /*#__PURE__*/_jsxDEV(QuizCard, {\n            quiz: quiz,\n            userResult: userResults[quiz._id],\n            showResults: true,\n            onStart: handleQuizStart,\n            onView: () => handleQuizView(quiz),\n            index: index\n          }, quiz._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 297,\n    columnNumber: 5\n  }, this);\n};\n\n// Simple QuizCard component without Framer Motion\n_s(Quiz, \"FmJwpU6An2DF6t9gL/MsElfZCNA=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = Quiz;\nconst QuizCard = ({\n  quiz,\n  userResult,\n  onStart,\n  onView,\n  index\n}) => {\n  const formatTime = seconds => {\n    if (!seconds) return 'N/A';\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const formatCompletionTime = timeInSeconds => {\n    if (!timeInSeconds || timeInSeconds === 0) return 'N/A';\n    const minutes = Math.floor(timeInSeconds / 60);\n    const seconds = timeInSeconds % 60;\n    if (minutes > 0) {\n      return `${minutes}m ${seconds}s`;\n    }\n    return `${timeInSeconds}s`;\n  };\n\n  // Safety checks for quiz object\n  if (!quiz || typeof quiz !== 'object') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Invalid quiz data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-500 p-8 transform hover:scale-110 opacity-100 relative border-4\",\n    style: {\n      background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',\n      borderColor: userResult ? userResult.verdict === 'Pass' ? '#10b981' : '#ef4444' : '#3b82f6',\n      boxShadow: userResult ? userResult.verdict === 'Pass' ? '0 20px 40px rgba(16, 185, 129, 0.3), 0 0 0 1px rgba(255,255,255,0.5)' : '0 20px 40px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(255,255,255,0.5)' : '0 20px 40px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(255,255,255,0.5)',\n      backdropFilter: 'blur(10px)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-2xl font-bold mb-4 line-clamp-2\",\n        style: {\n          color: '#1f2937',\n          textShadow: '0 2px 4px rgba(0,0,0,0.1)',\n          lineHeight: '1.3'\n        },\n        children: typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 text-center\",\n      children: userResult ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-2 rounded-full text-sm font-bold text-white shadow-lg border-2\",\n          style: {\n            backgroundColor: userResult.verdict === 'Pass' ? '#10b981' : '#ef4444',\n            borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5'\n          },\n          children: userResult.verdict === 'Pass' ? '✅ PASSED' : '❌ FAILED'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-2 rounded-full text-sm font-bold text-center shadow-lg border-2\",\n          style: {\n            backgroundColor: '#ffffff',\n            color: '#1f2937',\n            borderColor: '#d1d5db'\n          },\n          children: [typeof userResult.percentage === 'number' ? userResult.percentage : 0, \"% \\u2022 \", userResult.xpEarned || userResult.points || 0, \" XP\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-2 rounded-full text-sm font-bold text-white shadow-lg border-2\",\n        style: {\n          backgroundColor: '#3b82f6',\n          borderColor: '#60a5fa'\n        },\n        children: \"\\uD83C\\uDD95 NOT ATTEMPTED\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center rounded-xl py-4 px-4 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n            style: {\n              background: 'linear-gradient(to bottom right, #eff6ff, #e0e7ff)',\n              borderColor: '#bfdbfe'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n              className: \"w-6 h-6 mb-2\",\n              style: {\n                color: '#2563eb'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold\",\n              style: {\n                color: '#1e40af'\n              },\n              children: Array.isArray(quiz.questions) ? quiz.questions.length : 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold\",\n              style: {\n                color: '#1d4ed8'\n              },\n              children: \"Questions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center rounded-xl py-4 px-4 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n            style: {\n              background: 'linear-gradient(to bottom right, #fdf4ff, #fce7f3)',\n              borderColor: '#e9d5ff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-6 h-6 mb-2\",\n              style: {\n                color: '#9333ea'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold\",\n              style: {\n                color: '#7c3aed'\n              },\n              children: [typeof quiz.duration === 'number' ? Math.round(quiz.duration / 60) : 0, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold\",\n              style: {\n                color: '#8b5cf6'\n              },\n              children: \"Duration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-1 mb-6 rounded-full\",\n          style: {\n            background: 'linear-gradient(to right, #e2e8f0, #cbd5e1, #e2e8f0)',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-3 flex-wrap\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\",\n            style: {\n              background: 'linear-gradient(to right, #4ade80, #3b82f6)',\n              borderColor: '#86efac'\n            },\n            children: [\"\\uD83D\\uDCD6 Class \", typeof quiz.class === 'string' || typeof quiz.class === 'number' ? quiz.class : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\",\n            style: {\n              background: 'linear-gradient(to right, #667eea, #764ba2)',\n              borderColor: '#a78bfa'\n            },\n            children: [\"\\uD83D\\uDCDA \", quiz.subject]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\",\n            style: {\n              background: 'linear-gradient(to right, #10b981, #059669)',\n              borderColor: '#86efac'\n            },\n            children: [\"\\uD83D\\uDCD6 \", quiz.topic || 'General']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this), quiz.xpPoints && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\",\n            style: {\n              background: 'linear-gradient(to right, #fbbf24, #f97316)',\n              borderColor: '#fde047'\n            },\n            children: [\"\\u2B50 \", quiz.xpPoints, \" XP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 15\n          }, this), quiz.passingMarks && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\",\n            style: {\n              background: 'linear-gradient(to right, #f87171, #ec4899)',\n              borderColor: '#fca5a5'\n            },\n            children: [\"\\uD83C\\uDFAF \", quiz.passingMarks, \"% Pass\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full h-1 mb-6 rounded-full\",\n      style: {\n        background: 'linear-gradient(to right, #e2e8f0, #cbd5e1, #e2e8f0)',\n        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this), userResult && typeof userResult === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 p-6 rounded-2xl border-4 shadow-2xl transform hover:scale-102 transition-all duration-300\",\n      style: {\n        background: userResult.verdict === 'Pass' ? 'linear-gradient(to bottom right, #f0fdf4, #ecfdf5, #ccfbf1)' : 'linear-gradient(to bottom right, #fef2f2, #fdf2f8, #fce7f3)',\n        borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5',\n        boxShadow: userResult.verdict === 'Pass' ? '0 25px 50px rgba(34, 197, 94, 0.25)' : '0 25px 50px rgba(239, 68, 68, 0.25)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [userResult.verdict === 'Pass' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\",\n            style: {\n              background: 'linear-gradient(to right, #10b981, #059669)',\n              borderColor: '#86efac'\n            },\n            children: /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-6 h-6 font-bold\",\n              style: {\n                color: '#ffffff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\",\n            style: {\n              background: 'linear-gradient(to right, #ef4444, #dc2626)',\n              borderColor: '#fca5a5'\n            },\n            children: /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-6 h-6 font-bold\",\n              style: {\n                color: '#ffffff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg font-bold\",\n              style: {\n                color: '#1f2937'\n              },\n              children: \"\\uD83C\\uDFC6 Last Result\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm\",\n              style: {\n                color: '#6b7280'\n              },\n              children: new Date(userResult.completedAt || userResult.createdAt || Date.now()).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-3xl font-bold shadow-lg\",\n          style: {\n            color: userResult.verdict === 'Pass' ? '#059669' : '#dc2626'\n          },\n          children: [typeof userResult.percentage === 'number' ? userResult.percentage : 0, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-3 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center rounded-xl py-4 px-3 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n          style: {\n            background: 'linear-gradient(to bottom right, #dbeafe, #c7d2fe)',\n            borderColor: '#93c5fd'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n            className: \"w-8 h-8 mx-auto mb-2\",\n            style: {\n              color: '#2563eb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold\",\n            style: {\n              color: '#1e40af'\n            },\n            children: typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm font-semibold\",\n            style: {\n              color: '#1d4ed8'\n            },\n            children: \"Correct\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center rounded-xl py-4 px-3 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n          style: {\n            background: 'linear-gradient(to bottom right, #fef3c7, #fed7aa)',\n            borderColor: '#fde047'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-3xl mb-2 block\",\n            children: \"\\u2B50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold\",\n            style: {\n              color: '#92400e'\n            },\n            children: userResult.xpEarned || userResult.points || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm font-semibold\",\n            style: {\n              color: '#a16207'\n            },\n            children: \"XP Earned\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center rounded-xl py-4 px-3 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n          style: {\n            background: 'linear-gradient(to bottom right, #e9d5ff, #f3e8ff)',\n            borderColor: '#c4b5fd'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TbClock, {\n            className: \"w-8 h-8 mx-auto mb-2\",\n            style: {\n              color: '#9333ea'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold\",\n            style: {\n              color: '#7c3aed'\n            },\n            children: formatCompletionTime(userResult.timeTaken)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 704,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm font-semibold\",\n            style: {\n              color: '#8b5cf6'\n            },\n            children: \"Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onStart(quiz),\n        className: \"flex-1 flex items-center justify-center gap-3 px-6 py-4 rounded-xl font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-110 active:scale-95 text-white border-2\",\n        style: {\n          background: userResult ? 'linear-gradient(to right, #f97316, #ef4444, #ec4899)' : 'linear-gradient(to right, #3b82f6, #8b5cf6, #4338ca)',\n          borderColor: userResult ? '#fb923c' : '#60a5fa'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this), userResult ? '🔄 Retake Quiz' : '🚀 Start Quiz']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 9\n      }, this), userResult && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onView(quiz),\n        className: \"px-6 py-4 rounded-xl transition-all duration-300 font-bold text-lg transform hover:scale-110 active:scale-95 shadow-xl hover:shadow-2xl text-white border-2\",\n        style: {\n          background: userResult.verdict === 'Pass' ? 'linear-gradient(to right, #fbbf24, #f97316, #ef4444)' : 'linear-gradient(to right, #6b7280, #64748b, #475569)',\n          borderColor: userResult.verdict === 'Pass' ? '#fde047' : '#9ca3af'\n        },\n        title: \"View Results\",\n        children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n          className: \"w-6 h-6\",\n          style: {\n            color: userResult.verdict === 'Pass' ? '#fef3c7' : '#e5e7eb'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 729,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 713,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 445,\n    columnNumber: 5\n  }, this);\n};\n_c2 = QuizCard;\nexport default Quiz;\nvar _c, _c2;\n$RefreshReg$(_c, \"Quiz\");\n$RefreshReg$(_c2, \"QuizCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useNavigate", "useDispatch", "useSelector", "message", "TbSearch", "Tb<PERSON><PERSON>er", "TbClock", "TbQuestionMark", "TbTrophy", "TbPlayerPlay", "TbBrain", "TbTarget", "TbCheck", "TbX", "TbStar", "TbHome", "TbBolt", "TbRefresh", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Quiz", "_s", "exams", "setExams", "filteredExams", "setFilteredExams", "searchTerm", "setSearchTerm", "selectedClass", "setSelectedClass", "userResults", "setUserResults", "loading", "setLoading", "refreshing", "setRefreshing", "lastRefresh", "setLastRefresh", "navigate", "dispatch", "user", "state", "getUserResults", "_id", "response", "userId", "success", "resultsMap", "data", "for<PERSON>ach", "report", "_report$exam", "examId", "exam", "result", "Date", "createdAt", "verdict", "percentage", "correctAnswers", "wrongAnswers", "totalQuestions", "obtainedMarks", "totalMarks", "score", "points", "xpEarned", "xpGained", "timeTaken", "completedAt", "error", "console", "getExams", "isRefresh", "log", "cachedExams", "localStorage", "getItem", "cacheTime", "now", "parseInt", "cached", "JSON", "parse", "class", "String", "length", "level", "userLevelExams", "filter", "toLowerCase", "sortedExams", "sort", "a", "b", "setItem", "stringify", "toString", "handleRankingUpdate", "handleNewExam", "handleWindowFocus", "window", "addEventListener", "removeEventListener", "refreshInterval", "setInterval", "clearInterval", "filtered", "_exam$name", "_exam$subject", "name", "includes", "subject", "availableClasses", "Set", "map", "e", "Boolean", "handleQuizStart", "quiz", "objectIdRegex", "test", "handleRefresh", "handleQuizView", "userResult", "info", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleTimeString", "type", "placeholder", "value", "onChange", "target", "onClick", "disabled", "title", "index", "QuizCard", "showResults", "onStart", "onView", "_c", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "padStart", "formatCompletionTime", "timeInSeconds", "style", "background", "borderColor", "boxShadow", "<PERSON><PERSON>ilter", "color", "textShadow", "lineHeight", "backgroundColor", "Array", "isArray", "questions", "duration", "round", "topic", "xpPoints", "passingMarks", "toLocaleDateString", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { message } from 'antd';\r\nimport {\r\n  Tb<PERSON>earch,\r\n  Tb<PERSON><PERSON><PERSON>,\r\n  TbClock,\r\n  TbQuestionMark,\r\n  TbTrophy,\r\n  TbPlayerPlay,\r\n  TbBrain,\r\n  TbTarget,\r\n  TbCheck,\r\n  TbX,\r\n  TbStar,\r\n  TbHome,\r\n  TbBolt,\r\n  TbRefresh\r\n} from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { getAllReportsByUser } from '../../../apicalls/reports';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport './animations.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedClass, setSelectedClass] = useState('');\r\n  const [userResults, setUserResults] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [refreshing, setRefreshing] = useState(false);\r\n  const [lastRefresh, setLastRefresh] = useState(null);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const getUserResults = useCallback(async () => {\r\n    try {\r\n      if (!user?._id) return;\r\n\r\n      const response = await getAllReportsByUser({ userId: user._id });\r\n\r\n      if (response.success) {\r\n        const resultsMap = {};\r\n        response.data.forEach(report => {\r\n          const examId = report.exam?._id;\r\n          if (!examId || !report.result) return;\r\n\r\n          // Extract data from the result object\r\n          const result = report.result;\r\n\r\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\r\n            resultsMap[examId] = {\r\n              verdict: result.verdict,\r\n              percentage: result.percentage,\r\n              correctAnswers: result.correctAnswers,\r\n              wrongAnswers: result.wrongAnswers,\r\n              totalQuestions: result.totalQuestions,\r\n              obtainedMarks: result.obtainedMarks,\r\n              totalMarks: result.totalMarks,\r\n              score: result.score,\r\n              points: result.points,\r\n              xpEarned: result.xpEarned || result.points || result.xpGained || 0,\r\n              timeTaken: report.timeTaken,\r\n              completedAt: report.createdAt,\r\n            };\r\n          }\r\n        });\r\n        setUserResults(resultsMap);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching user results:', error);\r\n    }\r\n  }, [user?._id]);\r\n\r\n  // Define getExams function outside useEffect so it can be called from other functions\r\n  const getExams = useCallback(async (isRefresh = false) => {\r\n      try {\r\n        // Safety check: ensure user exists before proceeding\r\n        if (!user) {\r\n          console.log(\"User not loaded yet, skipping exam fetch\");\r\n          return;\r\n        }\r\n\r\n        // Check cache first (unless refreshing)\r\n        if (!isRefresh) {\r\n          const cachedExams = localStorage.getItem('user_exams_cache');\r\n          const cacheTime = localStorage.getItem('user_exams_cache_time');\r\n          const now = Date.now();\r\n\r\n          // Use cache if less than 3 minutes old\r\n          if (cachedExams && cacheTime && (now - parseInt(cacheTime)) < 180000) {\r\n            const cached = JSON.parse(cachedExams);\r\n            setExams(cached);\r\n            setLastRefresh(new Date(parseInt(cacheTime)));\r\n            if (user?.class) {\r\n              setSelectedClass(String(user.class));\r\n            }\r\n            setLoading(false);\r\n            return;\r\n          }\r\n        }\r\n\r\n        if (isRefresh) {\r\n          setRefreshing(true);\r\n        } else {\r\n          dispatch(ShowLoading());\r\n        }\r\n\r\n        const response = await getAllExams();\r\n\r\n        if (isRefresh) {\r\n          setRefreshing(false);\r\n        } else {\r\n          dispatch(HideLoading());\r\n        }\r\n\r\n        if (response.success) {\r\n          console.log('Raw exams from API:', response.data.length);\r\n          console.log('User level:', user?.level);\r\n\r\n          // Filter exams by user's level with proper null checks\r\n          const userLevelExams = response.data.filter(exam => {\r\n            if (!exam.level || !user || !user.level) return false;\r\n            return exam.level.toLowerCase() === user.level.toLowerCase();\r\n          });\r\n\r\n          console.log('User level exams after filtering:', userLevelExams.length);\r\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n          setExams(sortedExams);\r\n          setLastRefresh(new Date());\r\n\r\n          // Cache the exams data\r\n          localStorage.setItem('user_exams_cache', JSON.stringify(sortedExams));\r\n          localStorage.setItem('user_exams_cache_time', Date.now().toString());\r\n\r\n          // Set default class filter to user's class\r\n          if (user?.class) {\r\n            setSelectedClass(String(user.class));\r\n          }\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        if (isRefresh) {\r\n          setRefreshing(false);\r\n        } else {\r\n          dispatch(HideLoading());\r\n        }\r\n        message.error(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n  }, [dispatch, user]);\r\n\r\n  useEffect(() => {\r\n    getExams(false); // Initial load\r\n    getUserResults();\r\n  }, [getExams, getUserResults]);\r\n\r\n  // Real-time updates for quiz completion and new exams\r\n  useEffect(() => {\r\n    // Listen for real-time updates from quiz completion\r\n    const handleRankingUpdate = () => {\r\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\r\n      getUserResults(); // Refresh user results to show updated XP\r\n    };\r\n\r\n    // Listen for new exam creation events\r\n    const handleNewExam = () => {\r\n      console.log('🆕 New exam created - refreshing exam list...');\r\n      if (user) {\r\n        getExams(true); // Use refresh mode\r\n        getUserResults();\r\n      }\r\n    };\r\n\r\n    // Listen for window focus to refresh data when returning from quiz\r\n    const handleWindowFocus = () => {\r\n      console.log('🎯 Quiz listing - window focused, refreshing data...');\r\n      getUserResults();\r\n      // Also refresh exams list to show newly generated exams\r\n      if (user) {\r\n        console.log('🔄 Refreshing exams list for new exams...');\r\n        getExams(true); // Use refresh mode\r\n      }\r\n    };\r\n\r\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\r\n    window.addEventListener('focus', handleWindowFocus);\r\n    window.addEventListener('newExamCreated', handleNewExam);\r\n\r\n    return () => {\r\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\r\n      window.removeEventListener('focus', handleWindowFocus);\r\n      window.removeEventListener('newExamCreated', handleNewExam);\r\n    };\r\n  }, []);\r\n\r\n  // Periodic refresh to ensure quiz list stays up to date\r\n  useEffect(() => {\r\n    const refreshInterval = setInterval(() => {\r\n      if (user && !loading && !refreshing) {\r\n        console.log('🔄 Periodic refresh of quiz list...');\r\n        getExams(true); // Use refresh mode\r\n      }\r\n    }, 5 * 60 * 1000); // Refresh every 5 minutes\r\n\r\n    return () => clearInterval(refreshInterval);\r\n  }, [user, loading, refreshing]);\r\n\r\n  useEffect(() => {\r\n    console.log('Filtering exams:', { exams: exams.length, searchTerm, selectedClass });\r\n    let filtered = exams;\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(exam =>\r\n        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n    if (selectedClass) {\r\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\r\n    }\r\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n    console.log('Filtered exams result:', filtered.length);\r\n    setFilteredExams(filtered);\r\n  }, [exams, searchTerm, selectedClass]);\r\n\r\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n\r\n    // Validate MongoDB ObjectId format (24 character hex string)\r\n    const objectIdRegex = /^[0-9a-fA-F]{24}$/;\r\n    if (!objectIdRegex.test(quiz._id)) {\r\n      message.error('Invalid quiz ID format. Please try again.');\r\n      return;\r\n    }\r\n\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/play`);\r\n    });\r\n  };\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = async () => {\r\n    console.log('🔄 Manual refresh triggered...');\r\n    if (refreshing || loading) return; // Prevent multiple simultaneous refreshes\r\n\r\n    try {\r\n      if (user) {\r\n        await getExams(true); // Use refresh mode\r\n        await getUserResults();\r\n        message.success('Quiz list refreshed successfully!');\r\n      }\r\n    } catch (error) {\r\n      message.error('Failed to refresh quiz list');\r\n    }\r\n  };\r\n\r\n  const handleQuizView = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n    // Check if user has attempted this quiz\r\n    const userResult = userResults[quiz._id];\r\n    if (!userResult) {\r\n      message.info('You need to attempt this quiz first to view results.');\r\n      return;\r\n    }\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/result`);\r\n    });\r\n  };\r\n\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading quizzes...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n      <div className=\"container mx-auto px-3 sm:px-4 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Hero Section */}\r\n        <div className=\"text-center mb-8 sm:mb-12 opacity-100\">\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-4 sm:mb-6 shadow-lg\">\r\n            <TbBrain className=\"w-8 h-8 sm:w-10 sm:h-10 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Challenge Your Brain, Beat the Rest\r\n          </h1>\r\n          <p className=\"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto mb-4 sm:mb-6 px-4\">\r\n            Test your knowledge with our comprehensive quizzes designed for You!\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm text-gray-500 px-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n              <span>{filteredExams.length} Available Quizzes</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n              <span>Level: {user?.level || 'All Levels'}</span>\r\n            </div>\r\n            {lastRefresh && (\r\n              <div className=\"flex items-center gap-2 text-xs text-gray-400\">\r\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full\"></div>\r\n                <span>Updated: {lastRefresh.toLocaleTimeString()}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filter */}\r\n        <div className=\"max-w-4xl mx-auto mb-8 sm:mb-12 opacity-100\">\r\n          <div className=\"bg-white rounded-2xl shadow-lg p-4 sm:p-6\">\r\n            <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                  <TbSearch className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search quizzes by name or subject...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm sm:text-base\"\r\n                />\r\n              </div>\r\n              <div className=\"sm:w-48 md:w-64\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none\">\r\n                    <TbFilter className=\"h-4 w-4 sm:h-5 sm:w-5 text-gray-400\" />\r\n                  </div>\r\n                  <select\r\n                    value={selectedClass}\r\n                    onChange={(e) => setSelectedClass(e.target.value)}\r\n                    className=\"block w-full pl-10 sm:pl-12 pr-8 py-2.5 sm:py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm sm:text-base\"\r\n                  >\r\n                    <option value=\"\">All Classes</option>\r\n                    {availableClasses.map((className) => (\r\n                      <option key={className} value={className}>Class {className}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Refresh Button */}\r\n              <button\r\n                onClick={handleRefresh}\r\n                disabled={loading || refreshing}\r\n                className=\"flex items-center justify-center px-4 py-2.5 sm:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95\"\r\n                title=\"Refresh quiz list\"\r\n              >\r\n                <TbRefresh className={`h-4 w-4 sm:h-5 sm:w-5 ${(loading || refreshing) ? 'animate-spin' : ''}`} />\r\n                <span className=\"ml-2 hidden sm:inline text-sm sm:text-base\">\r\n                  {refreshing ? 'Refreshing...' : 'Refresh'}\r\n                </span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quiz Grid */}\r\n        <div className=\"opacity-100\">\r\n\r\n\r\n          {filteredExams.length === 0 ? (\r\n            <div className=\"text-center py-12 sm:py-16\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-8 sm:p-12 max-w-md mx-auto\">\r\n                <TbTarget className=\"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600 text-sm sm:text-base\">\r\n                  {searchTerm || selectedClass\r\n                    ? \"Try adjusting your search or filter criteria.\"\r\n                    : \"No quizzes are available for your level at the moment.\"\r\n                  }\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n              {filteredExams.map((quiz, index) => (\r\n                <QuizCard\r\n                  key={quiz._id}\r\n                  quiz={quiz}\r\n                  userResult={userResults[quiz._id]}\r\n                  showResults={true}\r\n                  onStart={handleQuizStart}\r\n                  onView={() => handleQuizView(quiz)}\r\n                  index={index}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Simple QuizCard component without Framer Motion\r\nconst QuizCard = ({ quiz, userResult, onStart, onView, index }) => {\r\n  const formatTime = (seconds) => {\r\n    if (!seconds) return 'N/A';\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const formatCompletionTime = (timeInSeconds) => {\r\n    if (!timeInSeconds || timeInSeconds === 0) return 'N/A';\r\n    const minutes = Math.floor(timeInSeconds / 60);\r\n    const seconds = timeInSeconds % 60;\r\n    if (minutes > 0) {\r\n      return `${minutes}m ${seconds}s`;\r\n    }\r\n    return `${timeInSeconds}s`;\r\n  };\r\n\r\n  // Safety checks for quiz object\r\n  if (!quiz || typeof quiz !== 'object') {\r\n    return (\r\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\r\n        <p className=\"text-gray-500\">Invalid quiz data</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-500 p-8 transform hover:scale-110 opacity-100 relative border-4\"\r\n      style={{\r\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',\r\n        borderColor: userResult\r\n          ? (userResult.verdict === 'Pass' ? '#10b981' : '#ef4444')\r\n          : '#3b82f6',\r\n        boxShadow: userResult\r\n          ? (userResult.verdict === 'Pass'\r\n              ? '0 20px 40px rgba(16, 185, 129, 0.3), 0 0 0 1px rgba(255,255,255,0.5)'\r\n              : '0 20px 40px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(255,255,255,0.5)')\r\n          : '0 20px 40px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(255,255,255,0.5)',\r\n        backdropFilter: 'blur(10px)'\r\n      }}\r\n    >\r\n      {/* Quiz Title - At Top */}\r\n      <div className=\"mb-6 text-center\">\r\n        <h3\r\n          className=\"text-2xl font-bold mb-4 line-clamp-2\"\r\n          style={{\r\n            color: '#1f2937',\r\n            textShadow: '0 2px 4px rgba(0,0,0,0.1)',\r\n            lineHeight: '1.3'\r\n          }}\r\n        >\r\n          {typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'}\r\n        </h3>\r\n      </div>\r\n\r\n      {/* Status Tags - Centered */}\r\n      <div className=\"mb-6 text-center\">\r\n        {userResult ? (\r\n          <div className=\"flex items-center justify-center gap-3\">\r\n            <div\r\n              className=\"px-4 py-2 rounded-full text-sm font-bold text-white shadow-lg border-2\"\r\n              style={{\r\n                backgroundColor: userResult.verdict === 'Pass' ? '#10b981' : '#ef4444',\r\n                borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5'\r\n              }}\r\n            >\r\n              {userResult.verdict === 'Pass' ? '✅ PASSED' : '❌ FAILED'}\r\n            </div>\r\n            <div\r\n              className=\"px-4 py-2 rounded-full text-sm font-bold text-center shadow-lg border-2\"\r\n              style={{\r\n                backgroundColor: '#ffffff',\r\n                color: '#1f2937',\r\n                borderColor: '#d1d5db'\r\n              }}\r\n            >\r\n              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}% • {userResult.xpEarned || userResult.points || 0} XP\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div\r\n            className=\"px-4 py-2 rounded-full text-sm font-bold text-white shadow-lg border-2\"\r\n            style={{\r\n              backgroundColor: '#3b82f6',\r\n              borderColor: '#60a5fa'\r\n            }}\r\n          >\r\n            🆕 NOT ATTEMPTED\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"text-center mb-6\">\r\n        <div className=\"flex-1\">\r\n\r\n          <div className=\"grid grid-cols-2 gap-4 mb-6\">\r\n            <div\r\n              className=\"flex flex-col items-center rounded-xl py-4 px-4 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\"\r\n              style={{\r\n                background: 'linear-gradient(to bottom right, #eff6ff, #e0e7ff)',\r\n                borderColor: '#bfdbfe'\r\n              }}\r\n            >\r\n              <TbQuestionMark className=\"w-6 h-6 mb-2\" style={{ color: '#2563eb' }} />\r\n              <span className=\"text-xl font-bold\" style={{ color: '#1e40af' }}>\r\n                {Array.isArray(quiz.questions) ? quiz.questions.length : 0}\r\n              </span>\r\n              <span className=\"text-sm font-semibold\" style={{ color: '#1d4ed8' }}>Questions</span>\r\n            </div>\r\n            <div\r\n              className=\"flex flex-col items-center rounded-xl py-4 px-4 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\"\r\n              style={{\r\n                background: 'linear-gradient(to bottom right, #fdf4ff, #fce7f3)',\r\n                borderColor: '#e9d5ff'\r\n              }}\r\n            >\r\n              <TbClock className=\"w-6 h-6 mb-2\" style={{ color: '#9333ea' }} />\r\n              <span className=\"text-xl font-bold\" style={{ color: '#7c3aed' }}>\r\n                {typeof quiz.duration === 'number' ? Math.round(quiz.duration / 60) : 0}m\r\n              </span>\r\n              <span className=\"text-sm font-semibold\" style={{ color: '#8b5cf6' }}>Duration</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Border Line Separator */}\r\n          <div\r\n            className=\"w-full h-1 mb-6 rounded-full\"\r\n            style={{\r\n              background: 'linear-gradient(to right, #e2e8f0, #cbd5e1, #e2e8f0)',\r\n              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\r\n            }}\r\n          ></div>\r\n\r\n          <div className=\"flex items-center justify-center gap-3 flex-wrap\">\r\n            <span\r\n              className=\"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #4ade80, #3b82f6)',\r\n                borderColor: '#86efac'\r\n              }}\r\n            >\r\n              📖 Class {typeof quiz.class === 'string' || typeof quiz.class === 'number' ? quiz.class : 'N/A'}\r\n            </span>\r\n            <span\r\n              className=\"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #667eea, #764ba2)',\r\n                borderColor: '#a78bfa'\r\n              }}\r\n            >\r\n              📚 {quiz.subject}\r\n            </span>\r\n            {/* Topic Tag */}\r\n            <span\r\n              className=\"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #10b981, #059669)',\r\n                borderColor: '#86efac'\r\n              }}\r\n            >\r\n              📖 {quiz.topic || 'General'}\r\n            </span>\r\n            {quiz.xpPoints && (\r\n              <span\r\n                className=\"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\"\r\n                style={{\r\n                  background: 'linear-gradient(to right, #fbbf24, #f97316)',\r\n                  borderColor: '#fde047'\r\n                }}\r\n              >\r\n                ⭐ {quiz.xpPoints} XP\r\n              </span>\r\n            )}\r\n            {quiz.passingMarks && (\r\n              <span\r\n                className=\"inline-block px-4 py-2 text-sm font-bold rounded-full text-white shadow-lg border-2 transform hover:scale-105 transition-all duration-200\"\r\n                style={{\r\n                  background: 'linear-gradient(to right, #f87171, #ec4899)',\r\n                  borderColor: '#fca5a5'\r\n                }}\r\n              >\r\n                🎯 {quiz.passingMarks}% Pass\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Border Line Separator */}\r\n      <div\r\n        className=\"w-full h-1 mb-6 rounded-full\"\r\n        style={{\r\n          background: 'linear-gradient(to right, #e2e8f0, #cbd5e1, #e2e8f0)',\r\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\r\n        }}\r\n      ></div>\r\n\r\n      {userResult && typeof userResult === 'object' && (\r\n        <div\r\n          className=\"mb-6 p-6 rounded-2xl border-4 shadow-2xl transform hover:scale-102 transition-all duration-300\"\r\n          style={{\r\n            background: userResult.verdict === 'Pass'\r\n              ? 'linear-gradient(to bottom right, #f0fdf4, #ecfdf5, #ccfbf1)'\r\n              : 'linear-gradient(to bottom right, #fef2f2, #fdf2f8, #fce7f3)',\r\n            borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5',\r\n            boxShadow: userResult.verdict === 'Pass'\r\n              ? '0 25px 50px rgba(34, 197, 94, 0.25)'\r\n              : '0 25px 50px rgba(239, 68, 68, 0.25)'\r\n          }}\r\n        >\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              {userResult.verdict === 'Pass' ? (\r\n                <div\r\n                  className=\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\"\r\n                  style={{\r\n                    background: 'linear-gradient(to right, #10b981, #059669)',\r\n                    borderColor: '#86efac'\r\n                  }}\r\n                >\r\n                  <TbCheck className=\"w-6 h-6 font-bold\" style={{ color: '#ffffff' }} />\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className=\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\"\r\n                  style={{\r\n                    background: 'linear-gradient(to right, #ef4444, #dc2626)',\r\n                    borderColor: '#fca5a5'\r\n                  }}\r\n                >\r\n                  <TbX className=\"w-6 h-6 font-bold\" style={{ color: '#ffffff' }} />\r\n                </div>\r\n              )}\r\n              <div>\r\n                <span className=\"text-lg font-bold\" style={{ color: '#1f2937' }}>🏆 Last Result</span>\r\n                <div className=\"text-sm\" style={{ color: '#6b7280' }}>\r\n                  {new Date(userResult.completedAt || userResult.createdAt || Date.now()).toLocaleDateString()}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span\r\n              className=\"text-3xl font-bold shadow-lg\"\r\n              style={{\r\n                color: userResult.verdict === 'Pass' ? '#059669' : '#dc2626'\r\n              }}\r\n            >\r\n              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-3 gap-4\">\r\n            <div\r\n              className=\"text-center rounded-xl py-4 px-3 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\"\r\n              style={{\r\n                background: 'linear-gradient(to bottom right, #dbeafe, #c7d2fe)',\r\n                borderColor: '#93c5fd'\r\n              }}\r\n            >\r\n              <TbTarget className=\"w-8 h-8 mx-auto mb-2\" style={{ color: '#2563eb' }} />\r\n              <div className=\"text-2xl font-bold\" style={{ color: '#1e40af' }}>\r\n                {typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0}\r\n              </div>\r\n              <div className=\"text-sm font-semibold\" style={{ color: '#1d4ed8' }}>Correct</div>\r\n            </div>\r\n            <div\r\n              className=\"text-center rounded-xl py-4 px-3 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\"\r\n              style={{\r\n                background: 'linear-gradient(to bottom right, #fef3c7, #fed7aa)',\r\n                borderColor: '#fde047'\r\n              }}\r\n            >\r\n              <span className=\"text-3xl mb-2 block\">⭐</span>\r\n              <div className=\"text-2xl font-bold\" style={{ color: '#92400e' }}>\r\n                {userResult.xpEarned || userResult.points || 0}\r\n              </div>\r\n              <div className=\"text-sm font-semibold\" style={{ color: '#a16207' }}>XP Earned</div>\r\n            </div>\r\n            <div\r\n              className=\"text-center rounded-xl py-4 px-3 border-2 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\"\r\n              style={{\r\n                background: 'linear-gradient(to bottom right, #e9d5ff, #f3e8ff)',\r\n                borderColor: '#c4b5fd'\r\n              }}\r\n            >\r\n              <TbClock className=\"w-8 h-8 mx-auto mb-2\" style={{ color: '#9333ea' }} />\r\n              <div className=\"text-2xl font-bold\" style={{ color: '#7c3aed' }}>\r\n                {formatCompletionTime(userResult.timeTaken)}\r\n              </div>\r\n              <div className=\"text-sm font-semibold\" style={{ color: '#8b5cf6' }}>Completed</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex gap-4\">\r\n        <button\r\n          onClick={() => onStart(quiz)}\r\n          className=\"flex-1 flex items-center justify-center gap-3 px-6 py-4 rounded-xl font-bold text-lg transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-110 active:scale-95 text-white border-2\"\r\n          style={{\r\n            background: userResult\r\n              ? 'linear-gradient(to right, #f97316, #ef4444, #ec4899)'\r\n              : 'linear-gradient(to right, #3b82f6, #8b5cf6, #4338ca)',\r\n            borderColor: userResult ? '#fb923c' : '#60a5fa'\r\n          }}\r\n        >\r\n          <TbPlayerPlay className=\"w-6 h-6\" />\r\n          {userResult ? '🔄 Retake Quiz' : '🚀 Start Quiz'}\r\n        </button>\r\n\r\n        {userResult && (\r\n          <button\r\n            onClick={() => onView(quiz)}\r\n            className=\"px-6 py-4 rounded-xl transition-all duration-300 font-bold text-lg transform hover:scale-110 active:scale-95 shadow-xl hover:shadow-2xl text-white border-2\"\r\n            style={{\r\n              background: userResult.verdict === 'Pass'\r\n                ? 'linear-gradient(to right, #fbbf24, #f97316, #ef4444)'\r\n                : 'linear-gradient(to right, #6b7280, #64748b, #475569)',\r\n              borderColor: userResult.verdict === 'Pass' ? '#fde047' : '#9ca3af'\r\n            }}\r\n            title=\"View Results\"\r\n          >\r\n            <TbTrophy\r\n              className=\"w-6 h-6\"\r\n              style={{\r\n                color: userResult.verdict === 'Pass' ? '#fef3c7' : '#e5e7eb'\r\n              }}\r\n            />\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,SAAS,QACJ,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM8C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2C;EAAK,CAAC,GAAG1C,WAAW,CAAE2C,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,cAAc,GAAGhD,WAAW,CAAC,YAAY;IAC7C,IAAI;MACF,IAAI,EAAC8C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,GAAG,GAAE;MAEhB,MAAMC,QAAQ,GAAG,MAAM7B,mBAAmB,CAAC;QAAE8B,MAAM,EAAEL,IAAI,CAACG;MAAI,CAAC,CAAC;MAEhE,IAAIC,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,UAAU,GAAG,CAAC,CAAC;QACrBH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAACC,MAAM,IAAI;UAAA,IAAAC,YAAA;UAC9B,MAAMC,MAAM,IAAAD,YAAA,GAAGD,MAAM,CAACG,IAAI,cAAAF,YAAA,uBAAXA,YAAA,CAAaR,GAAG;UAC/B,IAAI,CAACS,MAAM,IAAI,CAACF,MAAM,CAACI,MAAM,EAAE;;UAE/B;UACA,MAAMA,MAAM,GAAGJ,MAAM,CAACI,MAAM;UAE5B,IAAI,CAACP,UAAU,CAACK,MAAM,CAAC,IAAI,IAAIG,IAAI,CAACL,MAAM,CAACM,SAAS,CAAC,GAAG,IAAID,IAAI,CAACR,UAAU,CAACK,MAAM,CAAC,CAACI,SAAS,CAAC,EAAE;YAC9FT,UAAU,CAACK,MAAM,CAAC,GAAG;cACnBK,OAAO,EAAEH,MAAM,CAACG,OAAO;cACvBC,UAAU,EAAEJ,MAAM,CAACI,UAAU;cAC7BC,cAAc,EAAEL,MAAM,CAACK,cAAc;cACrCC,YAAY,EAAEN,MAAM,CAACM,YAAY;cACjCC,cAAc,EAAEP,MAAM,CAACO,cAAc;cACrCC,aAAa,EAAER,MAAM,CAACQ,aAAa;cACnCC,UAAU,EAAET,MAAM,CAACS,UAAU;cAC7BC,KAAK,EAAEV,MAAM,CAACU,KAAK;cACnBC,MAAM,EAAEX,MAAM,CAACW,MAAM;cACrBC,QAAQ,EAAEZ,MAAM,CAACY,QAAQ,IAAIZ,MAAM,CAACW,MAAM,IAAIX,MAAM,CAACa,QAAQ,IAAI,CAAC;cAClEC,SAAS,EAAElB,MAAM,CAACkB,SAAS;cAC3BC,WAAW,EAAEnB,MAAM,CAACM;YACtB,CAAC;UACH;QACF,CAAC,CAAC;QACFzB,cAAc,CAACgB,UAAU,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC,EAAE,CAAC9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,GAAG,CAAC,CAAC;;EAEf;EACA,MAAM6B,QAAQ,GAAG9E,WAAW,CAAC,OAAO+E,SAAS,GAAG,KAAK,KAAK;IACtD,IAAI;MACF;MACA,IAAI,CAACjC,IAAI,EAAE;QACT+B,OAAO,CAACG,GAAG,CAAC,0CAA0C,CAAC;QACvD;MACF;;MAEA;MACA,IAAI,CAACD,SAAS,EAAE;QACd,MAAME,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;QAC5D,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;QAC/D,MAAME,GAAG,GAAGxB,IAAI,CAACwB,GAAG,CAAC,CAAC;;QAEtB;QACA,IAAIJ,WAAW,IAAIG,SAAS,IAAKC,GAAG,GAAGC,QAAQ,CAACF,SAAS,CAAC,GAAI,MAAM,EAAE;UACpE,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACR,WAAW,CAAC;UACtCpD,QAAQ,CAAC0D,MAAM,CAAC;UAChB5C,cAAc,CAAC,IAAIkB,IAAI,CAACyB,QAAQ,CAACF,SAAS,CAAC,CAAC,CAAC;UAC7C,IAAItC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4C,KAAK,EAAE;YACfvD,gBAAgB,CAACwD,MAAM,CAAC7C,IAAI,CAAC4C,KAAK,CAAC,CAAC;UACtC;UACAnD,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;MAEA,IAAIwC,SAAS,EAAE;QACbtC,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACLI,QAAQ,CAACtB,WAAW,CAAC,CAAC,CAAC;MACzB;MAEA,MAAM2B,QAAQ,GAAG,MAAM9B,WAAW,CAAC,CAAC;MAEpC,IAAI2D,SAAS,EAAE;QACbtC,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,MAAM;QACLI,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACzB;MAEA,IAAI4B,QAAQ,CAACE,OAAO,EAAE;QACpByB,OAAO,CAACG,GAAG,CAAC,qBAAqB,EAAE9B,QAAQ,CAACI,IAAI,CAACsC,MAAM,CAAC;QACxDf,OAAO,CAACG,GAAG,CAAC,aAAa,EAAElC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,KAAK,CAAC;;QAEvC;QACA,MAAMC,cAAc,GAAG5C,QAAQ,CAACI,IAAI,CAACyC,MAAM,CAACpC,IAAI,IAAI;UAClD,IAAI,CAACA,IAAI,CAACkC,KAAK,IAAI,CAAC/C,IAAI,IAAI,CAACA,IAAI,CAAC+C,KAAK,EAAE,OAAO,KAAK;UACrD,OAAOlC,IAAI,CAACkC,KAAK,CAACG,WAAW,CAAC,CAAC,KAAKlD,IAAI,CAAC+C,KAAK,CAACG,WAAW,CAAC,CAAC;QAC9D,CAAC,CAAC;QAEFnB,OAAO,CAACG,GAAG,CAAC,mCAAmC,EAAEc,cAAc,CAACF,MAAM,CAAC;QACvE,MAAMK,WAAW,GAAGH,cAAc,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvC,IAAI,CAACuC,CAAC,CAACtC,SAAS,CAAC,GAAG,IAAID,IAAI,CAACsC,CAAC,CAACrC,SAAS,CAAC,CAAC;QAChGjC,QAAQ,CAACoE,WAAW,CAAC;QACrBtD,cAAc,CAAC,IAAIkB,IAAI,CAAC,CAAC,CAAC;;QAE1B;QACAqB,YAAY,CAACmB,OAAO,CAAC,kBAAkB,EAAEb,IAAI,CAACc,SAAS,CAACL,WAAW,CAAC,CAAC;QACrEf,YAAY,CAACmB,OAAO,CAAC,uBAAuB,EAAExC,IAAI,CAACwB,GAAG,CAAC,CAAC,CAACkB,QAAQ,CAAC,CAAC,CAAC;;QAEpE;QACA,IAAIzD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4C,KAAK,EAAE;UACfvD,gBAAgB,CAACwD,MAAM,CAAC7C,IAAI,CAAC4C,KAAK,CAAC,CAAC;QACtC;MACF,CAAC,MAAM;QACLrF,OAAO,CAACuE,KAAK,CAAC1B,QAAQ,CAAC7C,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuE,KAAK,EAAE;MACd,IAAIG,SAAS,EAAE;QACbtC,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,MAAM;QACLI,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACzB;MACAjB,OAAO,CAACuE,KAAK,CAACA,KAAK,CAACvE,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRkC,UAAU,CAAC,KAAK,CAAC;IACnB;EACJ,CAAC,EAAE,CAACM,QAAQ,EAAEC,IAAI,CAAC,CAAC;EAEpB/C,SAAS,CAAC,MAAM;IACd+E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IACjB9B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC8B,QAAQ,EAAE9B,cAAc,CAAC,CAAC;;EAE9B;EACAjD,SAAS,CAAC,MAAM;IACd;IACA,MAAMyG,mBAAmB,GAAGA,CAAA,KAAM;MAChC3B,OAAO,CAACG,GAAG,CAAC,4DAA4D,CAAC;MACzEhC,cAAc,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMyD,aAAa,GAAGA,CAAA,KAAM;MAC1B5B,OAAO,CAACG,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAIlC,IAAI,EAAE;QACRgC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAChB9B,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;;IAED;IACA,MAAM0D,iBAAiB,GAAGA,CAAA,KAAM;MAC9B7B,OAAO,CAACG,GAAG,CAAC,sDAAsD,CAAC;MACnEhC,cAAc,CAAC,CAAC;MAChB;MACA,IAAIF,IAAI,EAAE;QACR+B,OAAO,CAACG,GAAG,CAAC,2CAA2C,CAAC;QACxDF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAClB;IACF,CAAC;;IAED6B,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEJ,mBAAmB,CAAC;IAC7DG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;IACnDC,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,EAAEH,aAAa,CAAC;IAExD,OAAO,MAAM;MACXE,MAAM,CAACE,mBAAmB,CAAC,eAAe,EAAEL,mBAAmB,CAAC;MAChEG,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEH,iBAAiB,CAAC;MACtDC,MAAM,CAACE,mBAAmB,CAAC,gBAAgB,EAAEJ,aAAa,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1G,SAAS,CAAC,MAAM;IACd,MAAM+G,eAAe,GAAGC,WAAW,CAAC,MAAM;MACxC,IAAIjE,IAAI,IAAI,CAACR,OAAO,IAAI,CAACE,UAAU,EAAE;QACnCqC,OAAO,CAACG,GAAG,CAAC,qCAAqC,CAAC;QAClDF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAClB;IACF,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;IAEnB,OAAO,MAAMkC,aAAa,CAACF,eAAe,CAAC;EAC7C,CAAC,EAAE,CAAChE,IAAI,EAAER,OAAO,EAAEE,UAAU,CAAC,CAAC;EAE/BzC,SAAS,CAAC,MAAM;IACd8E,OAAO,CAACG,GAAG,CAAC,kBAAkB,EAAE;MAAEpD,KAAK,EAAEA,KAAK,CAACgE,MAAM;MAAE5D,UAAU;MAAEE;IAAc,CAAC,CAAC;IACnF,IAAI+E,QAAQ,GAAGrF,KAAK;IACpB,IAAII,UAAU,EAAE;MACdiF,QAAQ,GAAGA,QAAQ,CAAClB,MAAM,CAACpC,IAAI;QAAA,IAAAuD,UAAA,EAAAC,aAAA;QAAA,OAC7B,EAAAD,UAAA,GAAAvD,IAAI,CAACyD,IAAI,cAAAF,UAAA,uBAATA,UAAA,CAAWlB,WAAW,CAAC,CAAC,CAACqB,QAAQ,CAACrF,UAAU,CAACgE,WAAW,CAAC,CAAC,CAAC,OAAAmB,aAAA,GAC3DxD,IAAI,CAAC2D,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAcnB,WAAW,CAAC,CAAC,CAACqB,QAAQ,CAACrF,UAAU,CAACgE,WAAW,CAAC,CAAC,CAAC;MAAA,CAChE,CAAC;IACH;IACA,IAAI9D,aAAa,EAAE;MACjB+E,QAAQ,GAAGA,QAAQ,CAAClB,MAAM,CAACpC,IAAI,IAAIgC,MAAM,CAAChC,IAAI,CAAC+B,KAAK,CAAC,KAAKC,MAAM,CAACzD,aAAa,CAAC,CAAC;IAClF;IACA+E,QAAQ,CAACf,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIvC,IAAI,CAACuC,CAAC,CAACtC,SAAS,CAAC,GAAG,IAAID,IAAI,CAACsC,CAAC,CAACrC,SAAS,CAAC,CAAC;IACtEe,OAAO,CAACG,GAAG,CAAC,wBAAwB,EAAEiC,QAAQ,CAACrB,MAAM,CAAC;IACtD7D,gBAAgB,CAACkF,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACrF,KAAK,EAAEI,UAAU,EAAEE,aAAa,CAAC,CAAC;EAEtC,MAAMqF,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC5F,KAAK,CAAC6F,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,CAAC,CAACK,MAAM,CAAC4B,OAAO,CAAC,CAAC,CAAC,CAACzB,IAAI,CAAC,CAAC;EAErF,MAAM0B,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAAC5E,GAAG,EAAE;MACtB5C,OAAO,CAACuE,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;;IAEA;IACA,MAAMkD,aAAa,GAAG,mBAAmB;IACzC,IAAI,CAACA,aAAa,CAACC,IAAI,CAACF,IAAI,CAAC5E,GAAG,CAAC,EAAE;MACjC5C,OAAO,CAACuE,KAAK,CAAC,2CAA2C,CAAC;MAC1D;IACF;IAEA3E,eAAe,CAAC,MAAM;MACpB2C,QAAQ,CAAE,SAAQiF,IAAI,CAAC5E,GAAI,OAAM,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM+E,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCnD,OAAO,CAACG,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAIxC,UAAU,IAAIF,OAAO,EAAE,OAAO,CAAC;;IAEnC,IAAI;MACF,IAAIQ,IAAI,EAAE;QACR,MAAMgC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACtB,MAAM9B,cAAc,CAAC,CAAC;QACtB3C,OAAO,CAAC+C,OAAO,CAAC,mCAAmC,CAAC;MACtD;IACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdvE,OAAO,CAACuE,KAAK,CAAC,6BAA6B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMqD,cAAc,GAAIJ,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAAC5E,GAAG,EAAE;MACtB5C,OAAO,CAACuE,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;IACA;IACA,MAAMsD,UAAU,GAAG9F,WAAW,CAACyF,IAAI,CAAC5E,GAAG,CAAC;IACxC,IAAI,CAACiF,UAAU,EAAE;MACf7H,OAAO,CAAC8H,IAAI,CAAC,sDAAsD,CAAC;MACpE;IACF;IACAlI,eAAe,CAAC,MAAM;MACpB2C,QAAQ,CAAE,SAAQiF,IAAI,CAAC5E,GAAI,SAAQ,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC;EAID,IAAIX,OAAO,EAAE;IACX,oBACEb,OAAA;MAAK2G,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG5G,OAAA;QAAK2G,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5G,OAAA;UAAK2G,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGhH,OAAA;UAAG2G,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhH,OAAA;IAAK2G,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACxE5G,OAAA;MAAK2G,SAAS,EAAC,6DAA6D;MAAAC,QAAA,gBAE1E5G,OAAA;QAAK2G,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD5G,OAAA;UAAK2G,SAAS,EAAC,qJAAqJ;UAAAC,QAAA,eAClK5G,OAAA,CAACb,OAAO;YAACwH,SAAS,EAAC;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACNhH,OAAA;UAAI2G,SAAS,EAAC,wFAAwF;UAAAC,QAAA,EAAC;QAEvG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhH,OAAA;UAAG2G,SAAS,EAAC,mFAAmF;UAAAC,QAAA,EAAC;QAEjG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhH,OAAA;UAAK2G,SAAS,EAAC,iGAAiG;UAAAC,QAAA,gBAC9G5G,OAAA;YAAK2G,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC5G,OAAA;cAAK2G,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDhH,OAAA;cAAA4G,QAAA,GAAOvG,aAAa,CAAC8D,MAAM,EAAC,oBAAkB;YAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACNhH,OAAA;YAAK2G,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC5G,OAAA;cAAK2G,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDhH,OAAA;cAAA4G,QAAA,GAAM,SAAO,EAAC,CAAAvF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,KAAK,KAAI,YAAY;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EACL/F,WAAW,iBACVjB,OAAA;YAAK2G,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5D5G,OAAA;cAAK2G,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDhH,OAAA;cAAA4G,QAAA,GAAM,WAAS,EAAC3F,WAAW,CAACgG,kBAAkB,CAAC,CAAC;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhH,OAAA;QAAK2G,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1D5G,OAAA;UAAK2G,SAAS,EAAC,2CAA2C;UAAAC,QAAA,eACxD5G,OAAA;YAAK2G,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvD5G,OAAA;cAAK2G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B5G,OAAA;gBAAK2G,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,eAC3F5G,OAAA,CAACnB,QAAQ;kBAAC8H,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNhH,OAAA;gBACEkH,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,sCAAsC;gBAClDC,KAAK,EAAE7G,UAAW;gBAClB8G,QAAQ,EAAGpB,CAAC,IAAKzF,aAAa,CAACyF,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;gBAC/CT,SAAS,EAAC;cAAmO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9O,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhH,OAAA;cAAK2G,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B5G,OAAA;gBAAK2G,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB5G,OAAA;kBAAK2G,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,eAC3F5G,OAAA,CAAClB,QAAQ;oBAAC6H,SAAS,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACNhH,OAAA;kBACEoH,KAAK,EAAE3G,aAAc;kBACrB4G,QAAQ,EAAGpB,CAAC,IAAKvF,gBAAgB,CAACuF,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;kBAClDT,SAAS,EAAC,2OAA2O;kBAAAC,QAAA,gBAErP5G,OAAA;oBAAQoH,KAAK,EAAC,EAAE;oBAAAR,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpClB,gBAAgB,CAACE,GAAG,CAAEW,SAAS,iBAC9B3G,OAAA;oBAAwBoH,KAAK,EAAET,SAAU;oBAAAC,QAAA,GAAC,QAAM,EAACD,SAAS;kBAAA,GAA7CA,SAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6C,CACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhH,OAAA;cACEuH,OAAO,EAAEhB,aAAc;cACvBiB,QAAQ,EAAE3G,OAAO,IAAIE,UAAW;cAChC4F,SAAS,EAAC,4VAA4V;cACtWc,KAAK,EAAC,mBAAmB;cAAAb,QAAA,gBAEzB5G,OAAA,CAACN,SAAS;gBAACiH,SAAS,EAAG,yBAAyB9F,OAAO,IAAIE,UAAU,GAAI,cAAc,GAAG,EAAG;cAAE;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClGhH,OAAA;gBAAM2G,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACzD7F,UAAU,GAAG,eAAe,GAAG;cAAS;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhH,OAAA;QAAK2G,SAAS,EAAC,aAAa;QAAAC,QAAA,EAGzBvG,aAAa,CAAC8D,MAAM,KAAK,CAAC,gBACzBnE,OAAA;UAAK2G,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzC5G,OAAA;YAAK2G,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBAC1E5G,OAAA,CAACZ,QAAQ;cAACuH,SAAS,EAAC;YAAsD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7EhH,OAAA;cAAI2G,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzFhH,OAAA;cAAG2G,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAC9CrG,UAAU,IAAIE,aAAa,GACxB,+CAA+C,GAC/C;YAAwD;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENhH,OAAA;UAAK2G,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEvG,aAAa,CAAC2F,GAAG,CAAC,CAACI,IAAI,EAAEsB,KAAK,kBAC7B1H,OAAA,CAAC2H,QAAQ;YAEPvB,IAAI,EAAEA,IAAK;YACXK,UAAU,EAAE9F,WAAW,CAACyF,IAAI,CAAC5E,GAAG,CAAE;YAClCoG,WAAW,EAAE,IAAK;YAClBC,OAAO,EAAE1B,eAAgB;YACzB2B,MAAM,EAAEA,CAAA,KAAMtB,cAAc,CAACJ,IAAI,CAAE;YACnCsB,KAAK,EAAEA;UAAM,GANRtB,IAAI,CAAC5E,GAAG;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOd,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA9G,EAAA,CAtYMD,IAAI;EAAA,QASSxB,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAAoJ,EAAA,GAXxB9H,IAAI;AAuYV,MAAM0H,QAAQ,GAAGA,CAAC;EAAEvB,IAAI;EAAEK,UAAU;EAAEoB,OAAO;EAAEC,MAAM;EAAEJ;AAAM,CAAC,KAAK;EACjE,MAAMM,UAAU,GAAIC,OAAO,IAAK;IAC9B,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACvD,QAAQ,CAAC,CAAC,CAACwD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,oBAAoB,GAAIC,aAAa,IAAK;IAC9C,IAAI,CAACA,aAAa,IAAIA,aAAa,KAAK,CAAC,EAAE,OAAO,KAAK;IACvD,MAAMN,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACI,aAAa,GAAG,EAAE,CAAC;IAC9C,MAAMP,OAAO,GAAGO,aAAa,GAAG,EAAE;IAClC,IAAIN,OAAO,GAAG,CAAC,EAAE;MACf,OAAQ,GAAEA,OAAQ,KAAID,OAAQ,GAAE;IAClC;IACA,OAAQ,GAAEO,aAAc,GAAE;EAC5B,CAAC;;EAED;EACA,IAAI,CAACpC,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,oBACEpG,OAAA;MAAK2G,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eACxE5G,OAAA;QAAG2G,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEV;EAEA,oBACEhH,OAAA;IACE2G,SAAS,EAAC,iIAAiI;IAC3I8B,KAAK,EAAE;MACLC,UAAU,EAAE,gEAAgE;MAC5EC,WAAW,EAAElC,UAAU,GAClBA,UAAU,CAACnE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS,GACtD,SAAS;MACbsG,SAAS,EAAEnC,UAAU,GAChBA,UAAU,CAACnE,OAAO,KAAK,MAAM,GAC1B,sEAAsE,GACtE,qEAAqE,GACzE,sEAAsE;MAC1EuG,cAAc,EAAE;IAClB,CAAE;IAAAjC,QAAA,gBAGF5G,OAAA;MAAK2G,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B5G,OAAA;QACE2G,SAAS,EAAC,sCAAsC;QAChD8B,KAAK,EAAE;UACLK,KAAK,EAAE,SAAS;UAChBC,UAAU,EAAE,2BAA2B;UACvCC,UAAU,EAAE;QACd,CAAE;QAAApC,QAAA,EAED,OAAOR,IAAI,CAACT,IAAI,KAAK,QAAQ,GAAGS,IAAI,CAACT,IAAI,GAAG;MAAe;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNhH,OAAA;MAAK2G,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BH,UAAU,gBACTzG,OAAA;QAAK2G,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5G,OAAA;UACE2G,SAAS,EAAC,wEAAwE;UAClF8B,KAAK,EAAE;YACLQ,eAAe,EAAExC,UAAU,CAACnE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;YACtEqG,WAAW,EAAElC,UAAU,CAACnE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG;UAC3D,CAAE;UAAAsE,QAAA,EAEDH,UAAU,CAACnE,OAAO,KAAK,MAAM,GAAG,UAAU,GAAG;QAAU;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNhH,OAAA;UACE2G,SAAS,EAAC,yEAAyE;UACnF8B,KAAK,EAAE;YACLQ,eAAe,EAAE,SAAS;YAC1BH,KAAK,EAAE,SAAS;YAChBH,WAAW,EAAE;UACf,CAAE;UAAA/B,QAAA,GAED,OAAOH,UAAU,CAAClE,UAAU,KAAK,QAAQ,GAAGkE,UAAU,CAAClE,UAAU,GAAG,CAAC,EAAC,WAAI,EAACkE,UAAU,CAAC1D,QAAQ,IAAI0D,UAAU,CAAC3D,MAAM,IAAI,CAAC,EAAC,KAC5H;QAAA;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENhH,OAAA;QACE2G,SAAS,EAAC,wEAAwE;QAClF8B,KAAK,EAAE;UACLQ,eAAe,EAAE,SAAS;UAC1BN,WAAW,EAAE;QACf,CAAE;QAAA/B,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENhH,OAAA;MAAK2G,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B5G,OAAA;QAAK2G,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBAErB5G,OAAA;UAAK2G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C5G,OAAA;YACE2G,SAAS,EAAC,0IAA0I;YACpJ8B,KAAK,EAAE;cACLC,UAAU,EAAE,oDAAoD;cAChEC,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,gBAEF5G,OAAA,CAAChB,cAAc;cAAC2H,SAAS,EAAC,cAAc;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxEhH,OAAA;cAAM2G,SAAS,EAAC,mBAAmB;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAlC,QAAA,EAC7DsC,KAAK,CAACC,OAAO,CAAC/C,IAAI,CAACgD,SAAS,CAAC,GAAGhD,IAAI,CAACgD,SAAS,CAACjF,MAAM,GAAG;YAAC;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACPhH,OAAA;cAAM2G,SAAS,EAAC,uBAAuB;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAlC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACNhH,OAAA;YACE2G,SAAS,EAAC,0IAA0I;YACpJ8B,KAAK,EAAE;cACLC,UAAU,EAAE,oDAAoD;cAChEC,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,gBAEF5G,OAAA,CAACjB,OAAO;cAAC4H,SAAS,EAAC,cAAc;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjEhH,OAAA;cAAM2G,SAAS,EAAC,mBAAmB;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAlC,QAAA,GAC7D,OAAOR,IAAI,CAACiD,QAAQ,KAAK,QAAQ,GAAGlB,IAAI,CAACmB,KAAK,CAAClD,IAAI,CAACiD,QAAQ,GAAG,EAAE,CAAC,GAAG,CAAC,EAAC,GAC1E;YAAA;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPhH,OAAA;cAAM2G,SAAS,EAAC,uBAAuB;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAlC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhH,OAAA;UACE2G,SAAS,EAAC,8BAA8B;UACxC8B,KAAK,EAAE;YACLC,UAAU,EAAE,sDAAsD;YAClEE,SAAS,EAAE;UACb;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPhH,OAAA;UAAK2G,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/D5G,OAAA;YACE2G,SAAS,EAAC,2IAA2I;YACrJ8B,KAAK,EAAE;cACLC,UAAU,EAAE,6CAA6C;cACzDC,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,GACH,qBACU,EAAC,OAAOR,IAAI,CAACnC,KAAK,KAAK,QAAQ,IAAI,OAAOmC,IAAI,CAACnC,KAAK,KAAK,QAAQ,GAAGmC,IAAI,CAACnC,KAAK,GAAG,KAAK;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,eACPhH,OAAA;YACE2G,SAAS,EAAC,2IAA2I;YACrJ8B,KAAK,EAAE;cACLC,UAAU,EAAE,6CAA6C;cACzDC,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,GACH,eACI,EAACR,IAAI,CAACP,OAAO;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAEPhH,OAAA;YACE2G,SAAS,EAAC,2IAA2I;YACrJ8B,KAAK,EAAE;cACLC,UAAU,EAAE,6CAA6C;cACzDC,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,GACH,eACI,EAACR,IAAI,CAACmD,KAAK,IAAI,SAAS;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EACNZ,IAAI,CAACoD,QAAQ,iBACZxJ,OAAA;YACE2G,SAAS,EAAC,2IAA2I;YACrJ8B,KAAK,EAAE;cACLC,UAAU,EAAE,6CAA6C;cACzDC,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,GACH,SACG,EAACR,IAAI,CAACoD,QAAQ,EAAC,KACnB;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EACAZ,IAAI,CAACqD,YAAY,iBAChBzJ,OAAA;YACE2G,SAAS,EAAC,2IAA2I;YACrJ8B,KAAK,EAAE;cACLC,UAAU,EAAE,6CAA6C;cACzDC,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,GACH,eACI,EAACR,IAAI,CAACqD,YAAY,EAAC,QACxB;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhH,OAAA;MACE2G,SAAS,EAAC,8BAA8B;MACxC8B,KAAK,EAAE;QACLC,UAAU,EAAE,sDAAsD;QAClEE,SAAS,EAAE;MACb;IAAE;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAENP,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,iBAC3CzG,OAAA;MACE2G,SAAS,EAAC,gGAAgG;MAC1G8B,KAAK,EAAE;QACLC,UAAU,EAAEjC,UAAU,CAACnE,OAAO,KAAK,MAAM,GACrC,6DAA6D,GAC7D,6DAA6D;QACjEqG,WAAW,EAAElC,UAAU,CAACnE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;QAClEsG,SAAS,EAAEnC,UAAU,CAACnE,OAAO,KAAK,MAAM,GACpC,qCAAqC,GACrC;MACN,CAAE;MAAAsE,QAAA,gBAEF5G,OAAA;QAAK2G,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5G,OAAA;UAAK2G,SAAS,EAAC,yBAAyB;UAAAC,QAAA,GACrCH,UAAU,CAACnE,OAAO,KAAK,MAAM,gBAC5BtC,OAAA;YACE2G,SAAS,EAAC,0FAA0F;YACpG8B,KAAK,EAAE;cACLC,UAAU,EAAE,6CAA6C;cACzDC,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,eAEF5G,OAAA,CAACX,OAAO;cAACsH,SAAS,EAAC,mBAAmB;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,gBAENhH,OAAA;YACE2G,SAAS,EAAC,0FAA0F;YACpG8B,KAAK,EAAE;cACLC,UAAU,EAAE,6CAA6C;cACzDC,WAAW,EAAE;YACf,CAAE;YAAA/B,QAAA,eAEF5G,OAAA,CAACV,GAAG;cAACqH,SAAS,EAAC,mBAAmB;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CACN,eACDhH,OAAA;YAAA4G,QAAA,gBACE5G,OAAA;cAAM2G,SAAS,EAAC,mBAAmB;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAlC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtFhH,OAAA;cAAK2G,SAAS,EAAC,SAAS;cAAC8B,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAlC,QAAA,EAClD,IAAIxE,IAAI,CAACqE,UAAU,CAACvD,WAAW,IAAIuD,UAAU,CAACpE,SAAS,IAAID,IAAI,CAACwB,GAAG,CAAC,CAAC,CAAC,CAAC8F,kBAAkB,CAAC;YAAC;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhH,OAAA;UACE2G,SAAS,EAAC,8BAA8B;UACxC8B,KAAK,EAAE;YACLK,KAAK,EAAErC,UAAU,CAACnE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG;UACrD,CAAE;UAAAsE,QAAA,GAED,OAAOH,UAAU,CAAClE,UAAU,KAAK,QAAQ,GAAGkE,UAAU,CAAClE,UAAU,GAAG,CAAC,EAAC,GACzE;QAAA;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENhH,OAAA;QAAK2G,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC5G,OAAA;UACE2G,SAAS,EAAC,2HAA2H;UACrI8B,KAAK,EAAE;YACLC,UAAU,EAAE,oDAAoD;YAChEC,WAAW,EAAE;UACf,CAAE;UAAA/B,QAAA,gBAEF5G,OAAA,CAACZ,QAAQ;YAACuH,SAAS,EAAC,sBAAsB;YAAC8B,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1EhH,OAAA;YAAK2G,SAAS,EAAC,oBAAoB;YAAC8B,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAlC,QAAA,EAC7D,OAAOH,UAAU,CAACjE,cAAc,KAAK,QAAQ,GAAGiE,UAAU,CAACjE,cAAc,GAAG;UAAC;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACNhH,OAAA;YAAK2G,SAAS,EAAC,uBAAuB;YAAC8B,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAlC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACNhH,OAAA;UACE2G,SAAS,EAAC,2HAA2H;UACrI8B,KAAK,EAAE;YACLC,UAAU,EAAE,oDAAoD;YAChEC,WAAW,EAAE;UACf,CAAE;UAAA/B,QAAA,gBAEF5G,OAAA;YAAM2G,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9ChH,OAAA;YAAK2G,SAAS,EAAC,oBAAoB;YAAC8B,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAlC,QAAA,EAC7DH,UAAU,CAAC1D,QAAQ,IAAI0D,UAAU,CAAC3D,MAAM,IAAI;UAAC;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNhH,OAAA;YAAK2G,SAAS,EAAC,uBAAuB;YAAC8B,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAlC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eACNhH,OAAA;UACE2G,SAAS,EAAC,2HAA2H;UACrI8B,KAAK,EAAE;YACLC,UAAU,EAAE,oDAAoD;YAChEC,WAAW,EAAE;UACf,CAAE;UAAA/B,QAAA,gBAEF5G,OAAA,CAACjB,OAAO;YAAC4H,SAAS,EAAC,sBAAsB;YAAC8B,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzEhH,OAAA;YAAK2G,SAAS,EAAC,oBAAoB;YAAC8B,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAlC,QAAA,EAC7D2B,oBAAoB,CAAC9B,UAAU,CAACxD,SAAS;UAAC;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNhH,OAAA;YAAK2G,SAAS,EAAC,uBAAuB;YAAC8B,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAlC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDhH,OAAA;MAAK2G,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB5G,OAAA;QACEuH,OAAO,EAAEA,CAAA,KAAMM,OAAO,CAACzB,IAAI,CAAE;QAC7BO,SAAS,EAAC,2MAA2M;QACrN8B,KAAK,EAAE;UACLC,UAAU,EAAEjC,UAAU,GAClB,sDAAsD,GACtD,sDAAsD;UAC1DkC,WAAW,EAAElC,UAAU,GAAG,SAAS,GAAG;QACxC,CAAE;QAAAG,QAAA,gBAEF5G,OAAA,CAACd,YAAY;UAACyH,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACnCP,UAAU,GAAG,gBAAgB,GAAG,eAAe;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,EAERP,UAAU,iBACTzG,OAAA;QACEuH,OAAO,EAAEA,CAAA,KAAMO,MAAM,CAAC1B,IAAI,CAAE;QAC5BO,SAAS,EAAC,6JAA6J;QACvK8B,KAAK,EAAE;UACLC,UAAU,EAAEjC,UAAU,CAACnE,OAAO,KAAK,MAAM,GACrC,sDAAsD,GACtD,sDAAsD;UAC1DqG,WAAW,EAAElC,UAAU,CAACnE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG;QAC3D,CAAE;QACFmF,KAAK,EAAC,cAAc;QAAAb,QAAA,eAEpB5G,OAAA,CAACf,QAAQ;UACP0H,SAAS,EAAC,SAAS;UACnB8B,KAAK,EAAE;YACLK,KAAK,EAAErC,UAAU,CAACnE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG;UACrD;QAAE;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC2C,GAAA,GA9UIhC,QAAQ;AAgVd,eAAe1H,IAAI;AAAC,IAAA8H,EAAA,EAAA4B,GAAA;AAAAC,YAAA,CAAA7B,EAAA;AAAA6B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}