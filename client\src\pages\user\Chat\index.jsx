import React, { useState, useRef, useEffect, startTransition } from "react";
import { chatWithChatGPT, uploadImg } from "../../../apicalls/chat";

function BrainwaveAI() {
  const [messages, setMessages] = useState([
    { role: "assistant", content: "Hello! I'm Brainwave AI. How can I help you?" }
  ]);
  const [inputText, setInputText] = useState("");
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);

  // Auto scroll to bottom
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Handle image selection
  const handleImageSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      startTransition(() => {
        setSelectedImage(file);
      });
      const reader = new FileReader();
      reader.onload = (e) => {
        startTransition(() => {
          setImagePreview(e.target.result);
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove selected image
  const removeImage = () => {
    startTransition(() => {
      setSelectedImage(null);
      setImagePreview(null);
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle sending message
  const handleSendMessage = async () => {
    if (!inputText.trim() && !selectedImage) return;

    const userMessage = inputText.trim();
    const imageFile = selectedImage;

    // Clear input immediately
    startTransition(() => {
      setInputText("");
      setIsLoading(true);
    });
    removeImage();

    try {
      let imageUrl = null;

      // Upload image if selected
      if (imageFile) {
        const formData = new FormData();
        formData.append("image", imageFile);
        const uploadResult = await uploadImg(formData);
        
        if (uploadResult?.success) {
          imageUrl = uploadResult.url;
        }
      }

      // Create user message
      const newUserMessage = imageUrl
        ? {
            role: "user",
            content: [
              { type: "text", text: userMessage },
              { type: "image_url", image_url: { url: imageUrl } }
            ]
          }
        : { role: "user", content: userMessage };

      // Add user message to chat
      startTransition(() => {
        setMessages(prev => [...prev, newUserMessage]);
      });

      // Get AI response
      const response = await chatWithChatGPT([...messages, newUserMessage]);

      if (response?.success && response?.data) {
        startTransition(() => {
          setMessages(prev => [...prev, { role: "assistant", content: response.data }]);
        });
      } else {
        startTransition(() => {
          setMessages(prev => [...prev, { role: "assistant", content: "Sorry, I couldn't process your request. Please try again." }]);
        });
      }

    } catch (error) {
      console.error("Chat error:", error);
      startTransition(() => {
        setMessages(prev => [...prev, { role: "assistant", content: "An error occurred. Please try again." }]);
      });
    } finally {
      startTransition(() => {
        setIsLoading(false);
      });
    }
  };

  // Render message content
  const renderMessageContent = (message) => {
    if (typeof message.content === 'string') {
      return <div className="whitespace-pre-wrap">{message.content}</div>;
    }
    
    if (Array.isArray(message.content)) {
      return message.content.map((item, index) => (
        <div key={index}>
          {item.type === 'text' && <div className="whitespace-pre-wrap mb-2">{item.text}</div>}
          {item.type === 'image_url' && (
            <img 
              src={item.image_url.url} 
              alt="User upload" 
              className="max-w-full h-auto rounded-lg shadow-md"
              style={{ maxHeight: '300px' }}
            />
          )}
        </div>
      ));
    }
    
    return <div>Invalid message format</div>;
  };

  return (
    <div style={{ height: '100vh', background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <div style={{ background: 'white', borderBottom: '1px solid #e5e7eb', padding: '16px' }}>
        <div style={{ maxWidth: '1024px', margin: '0 auto', display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{ width: '40px', height: '40px', background: '#3b82f6', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            🤖
          </div>
          <div>
            <h1 style={{ fontSize: '20px', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>Brainwave AI</h1>
            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>Your intelligent study assistant</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <div style={{ height: '100%', maxWidth: '1024px', margin: '0 auto', padding: '16px' }}>
          <div style={{ height: '100%', overflowY: 'auto', paddingBottom: '16px' }}>
            {messages.map((message, index) => (
              <div
                key={index}
                style={{
                  display: 'flex',
                  gap: '12px',
                  marginBottom: '16px',
                  justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start'
                }}
              >
                {message.role === 'assistant' && (
                  <div style={{ width: '32px', height: '32px', background: '#3b82f6', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', flexShrink: 0 }}>
                    🤖
                  </div>
                )}

                <div style={{ maxWidth: '80%' }}>
                  <div
                    style={{
                      borderRadius: '16px',
                      padding: '16px',
                      background: message.role === 'user' ? '#3b82f6' : 'white',
                      color: message.role === 'user' ? 'white' : '#1f2937',
                      border: message.role === 'assistant' ? '1px solid #e5e7eb' : 'none',
                      boxShadow: message.role === 'assistant' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none'
                    }}
                  >
                    {typeof message.content === 'string' ? (
                      <div style={{ whiteSpace: 'pre-wrap' }}>{message.content}</div>
                    ) : Array.isArray(message.content) ? (
                      message.content.map((item, idx) => (
                        <div key={idx}>
                          {item.type === 'text' && <div style={{ whiteSpace: 'pre-wrap', marginBottom: '8px' }}>{item.text}</div>}
                          {item.type === 'image_url' && (
                            <img
                              src={item.image_url.url}
                              alt="Upload"
                              style={{ maxWidth: '100%', height: 'auto', borderRadius: '8px', maxHeight: '300px' }}
                            />
                          )}
                        </div>
                      ))
                    ) : (
                      <div>Invalid message</div>
                    )}
                  </div>
                </div>

                {message.role === 'user' && (
                  <div style={{ width: '32px', height: '32px', background: '#10b981', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', flexShrink: 0 }}>
                    👤
                  </div>
                )}
              </div>
            ))}

            {isLoading && (
              <div style={{ display: 'flex', gap: '12px', marginBottom: '16px' }}>
                <div style={{ width: '32px', height: '32px', background: '#3b82f6', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  🤖
                </div>
                <div style={{ background: 'white', border: '1px solid #e5e7eb', borderRadius: '16px', padding: '16px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <div style={{ width: '8px', height: '8px', background: '#9ca3af', borderRadius: '50%' }}></div>
                    <div style={{ width: '8px', height: '8px', background: '#9ca3af', borderRadius: '50%' }}></div>
                    <div style={{ width: '8px', height: '8px', background: '#9ca3af', borderRadius: '50%' }}></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>
      </div>

      {/* Input Section */}
      <div style={{ background: 'white', borderTop: '1px solid #e5e7eb', padding: '16px' }}>
        <div style={{ maxWidth: '1024px', margin: '0 auto' }}>
          {/* Image Preview */}
          {imagePreview && (
            <div style={{ marginBottom: '16px', padding: '12px', background: '#f9fafb', borderRadius: '12px', border: '1px solid #e5e7eb' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <div style={{ position: 'relative' }}>
                  <img
                    src={imagePreview}
                    alt="Preview"
                    style={{ width: '64px', height: '64px', objectFit: 'cover', borderRadius: '8px', border: '1px solid #d1d5db' }}
                  />
                  <button
                    onClick={removeImage}
                    style={{ position: 'absolute', top: '-8px', right: '-8px', width: '24px', height: '24px', background: '#ef4444', color: 'white', borderRadius: '50%', border: 'none', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                  >
                    ✕
                  </button>
                </div>
                <div style={{ flex: 1 }}>
                  <p style={{ fontSize: '14px', fontWeight: '500', color: '#374151', margin: 0 }}>Image attached</p>
                  <p style={{ fontSize: '12px', color: '#6b7280', margin: 0 }}>{selectedImage?.name}</p>
                </div>
              </div>
            </div>
          )}

          {/* Input Area */}
          <div style={{ display: 'flex', alignItems: 'flex-end', gap: '12px', background: 'white', borderRadius: '16px', border: '1px solid #d1d5db', padding: '12px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
            {/* Attachment Button */}
            <button
              onClick={() => fileInputRef.current?.click()}
              style={{ padding: '8px', background: '#3b82f6', color: 'white', borderRadius: '8px', border: 'none', cursor: 'pointer' }}
              title="Attach image"
            >
              📎
            </button>

            {/* Text Input */}
            <textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Ask me anything... (Enter for new line)"
              style={{
                flex: 1,
                resize: 'none',
                border: 'none',
                outline: 'none',
                background: 'transparent',
                color: '#1f2937',
                maxHeight: '128px',
                minHeight: '24px',
                fontFamily: 'inherit'
              }}
              rows={1}
              onInput={(e) => {
                e.target.style.height = 'auto';
                e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';
              }}
            />

            {/* Send Button */}
            <button
              onClick={handleSendMessage}
              disabled={isLoading || (!inputText.trim() && !selectedImage)}
              style={{
                padding: '8px',
                borderRadius: '8px',
                border: 'none',
                cursor: isLoading || (!inputText.trim() && !selectedImage) ? 'not-allowed' : 'pointer',
                background: isLoading || (!inputText.trim() && !selectedImage) ? '#e5e7eb' : '#3b82f6',
                color: isLoading || (!inputText.trim() && !selectedImage) ? '#9ca3af' : 'white'
              }}
            >
              ➤
            </button>
          </div>

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageSelect}
            style={{ display: 'none' }}
          />

          {/* Helper Text */}
          <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '8px', textAlign: 'center', margin: '8px 0 0 0' }}>
            Click send button to send • Enter for new line • Upload images for analysis
          </p>
        </div>
      </div>
    </div>
  );
};

export default BrainwaveAI;
