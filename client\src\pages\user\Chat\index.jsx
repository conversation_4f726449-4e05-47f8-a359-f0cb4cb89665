import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { TbSend, TbPaperclip, TbX, Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON>b<PERSON>oa<PERSON> } from "react-icons/tb";
import { chatWithChatGPT, uploadImg } from "../../../apicalls/chat";
import ContentRenderer from "../../../components/ContentRenderer";

function ChatGPTIntegration() {
  const [messages, setMessages] = useState([]);
  const [prompt, setPrompt] = useState("");
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const textareaRef = useRef(null);

  // Initialize chat with welcome message
  React.useEffect(() => {
    // Load cached messages
    const cachedMessages = localStorage.getItem('chat_messages');
    if (cachedMessages) {
      setMessages(JSON.parse(cachedMessages));
    } else {
      // Set welcome message
      setMessages([{
        role: "assistant",
        content: "Hello! I'm your AI study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?"
      }]);
    }
    setIsInitialized(true);
  }, []);

  // Save messages to cache
  React.useEffect(() => {
    if (isInitialized && messages.length > 0) {
      localStorage.setItem('chat_messages', JSON.stringify(messages));
    }
  }, [messages, isInitialized]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Handle image file selection
  const handleImageSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  // Remove selected image
  const removeImage = () => {
    setImageFile(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleChat();
    }
  };

  const handleChat = async () => {
    if (!prompt.trim() && !imageFile) return;

    setIsLoading(true);
    setIsTyping(true);

    try {
      let imageUrl = null;

      // Step 1: Upload the image to the server (if an image is selected)
      if (imageFile) {
        const formData = new FormData();
        formData.append("image", imageFile);

        const data = await uploadImg(formData);

        if (data?.success) {
          imageUrl = data.url; // Extract the S3 URL
          console.log("Image URL: ", imageUrl);
        } else {
          throw new Error("Image upload failed");
        }
      }

      // Step 2: Construct the ChatGPT message payload
      const userMessage = imageUrl
        ? {
          role: "user",
          content: [
            { type: "text", text: prompt },
            { type: "image_url", image_url: { url: imageUrl } },
          ],
        }
        : { role: "user", content: prompt };

      const updatedMessages = [...messages, userMessage];
      setMessages(updatedMessages);
      setPrompt("");
      removeImage();

      // Step 3: Send the payload to ChatGPT
      const chatPayload = { messages: updatedMessages };

      const chatRes = await chatWithChatGPT(chatPayload);

      const apiResponse = chatRes?.data;
      console.log("API Response: ", apiResponse);

      // Step 4: Append the assistant's response to the conversation
      setMessages((prev) => [
        ...prev,
        { role: "assistant", content: apiResponse },
      ]);

    } catch (error) {
      console.error("Error during chat:", error);
      setMessages((prev) => [
        ...prev,
        { role: "assistant", content: "Sorry, I encountered an error. Please try again." },
      ]);
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 shadow-sm"
      >
        <div className="max-w-4xl mx-auto flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <TbRobot className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-lg sm:text-xl font-bold text-gray-800">Brainwave AI</h1>
            <p className="text-xs sm:text-sm text-gray-600">Ask questions, upload images, get instant help</p>
          </div>
        </div>
      </motion.div>

      {/* Messages Container - Responsive */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4">
          <div className="h-full overflow-y-auto space-y-3 sm:space-y-6 pb-4">
            <AnimatePresence>
              {messages.map((msg, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex gap-2 sm:gap-4 ${msg.role === "user" ? "justify-end" : "justify-start"}`}
                >
                  {msg.role === "assistant" && (
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <TbRobot className="w-3 h-3 sm:w-5 sm:h-5 text-white" />
                    </div>
                  )}

                  <div className={`max-w-[85%] sm:max-w-[80%] ${msg.role === "user" ? "order-1" : ""}`}>
                    <div
                      className={`rounded-xl sm:rounded-2xl p-3 sm:p-4 ${
                        msg.role === "user"
                          ? "bg-blue-500 text-white ml-auto"
                          : "bg-white border border-gray-200 shadow-sm"
                      }`}
                    >
                      {msg.role === "assistant" ? (
                        <div className="text-gray-800">
                          {msg?.content ? (
                            <ContentRenderer text={msg.content} />
                          ) : (
                            <p className="text-red-500">Unable to get a response from AI</p>
                          )}
                        </div>
                      ) : (
                        <div>
                          {typeof msg.content === "string" ? (
                            <p className="whitespace-pre-wrap">{msg.content}</p>
                          ) : (
                            msg.content.map((item, idx) =>
                              item.type === "text" ? (
                                <p key={idx} className="whitespace-pre-wrap mb-2">{item.text}</p>
                              ) : (
                                <div key={idx} className="mt-2">
                                  <img
                                    src={item.image_url.url}
                                    alt="User uploaded content"
                                    className="rounded-lg max-w-full h-auto shadow-md"
                                    style={{ maxHeight: window.innerWidth <= 768 ? "200px" : "300px" }}
                                  />
                                </div>
                              )
                            )
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {msg.role === "user" && (
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <TbUser className="w-3 h-3 sm:w-5 sm:h-5 text-white" />
                    </div>
                  )}
                </motion.div>
              ))}
            </AnimatePresence>

            {/* Typing Indicator */}
            <AnimatePresence>
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="flex gap-2 sm:gap-4"
                >
                  <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <TbRobot className="w-3 h-3 sm:w-5 sm:h-5 text-white" />
                  </div>
                  <div className="bg-white border border-gray-200 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-sm">
                    <div className="flex gap-1">
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
                        className="w-2 h-2 bg-gray-400 rounded-full"
                      />
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
                        className="w-2 h-2 bg-gray-400 rounded-full"
                      />
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
                        className="w-2 h-2 bg-gray-400 rounded-full"
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <div ref={messagesEndRef} />
          </div>
        </div>
      </div>

      {/* Input Section - Responsive */}
      <div className="bg-white/80 backdrop-blur-lg border-t border-gray-200 p-2 sm:p-4">
        <div className="max-w-4xl mx-auto">
          {/* Image Preview */}
          <AnimatePresence>
            {imagePreview && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="mb-3 sm:mb-4 p-2 sm:p-3 bg-gray-50 rounded-lg sm:rounded-xl border border-gray-200"
              >
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg border border-gray-300"
                    />
                    <button
                      onClick={removeImage}
                      className="absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                    >
                      <TbX className="w-3 h-3 sm:w-4 sm:h-4" />
                    </button>
                  </div>
                  <div className="flex-1">
                    <p className="text-xs sm:text-sm font-medium text-gray-700">Image attached</p>
                    <p className="text-xs text-gray-500 truncate">{imageFile?.name}</p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Input Area - Responsive */}
          <div className="relative">
            <div className="flex items-end gap-2 sm:gap-3 bg-white rounded-xl sm:rounded-2xl border border-gray-300 p-2 sm:p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20">
              {/* Attachment Button - Blue and White with Visible Pins */}
              <button
                onClick={() => fileInputRef.current?.click()}
                className="p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors shadow-sm"
                title="Attach image"
              >
                <TbPaperclip className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>

              {/* Text Input - Responsive */}
              <textarea
                ref={textareaRef}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={window.innerWidth <= 768 ? "Ask me anything..." : "Ask me anything... (Shift+Enter for new line)"}
                className="flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 text-sm sm:text-base"
                rows={1}
                style={{
                  height: 'auto',
                  minHeight: window.innerWidth <= 768 ? '20px' : '24px',
                  maxHeight: window.innerWidth <= 768 ? '80px' : '128px'
                }}
                onInput={(e) => {
                  e.target.style.height = 'auto';
                  const maxHeight = window.innerWidth <= 768 ? 80 : 128;
                  e.target.style.height = Math.min(e.target.scrollHeight, maxHeight) + 'px';
                }}
              />

              {/* Send Button - Responsive */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleChat}
                disabled={isLoading || (!prompt.trim() && !imageFile)}
                className={`p-2 rounded-lg transition-all ${
                  isLoading || (!prompt.trim() && !imageFile)
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg"
                }`}
              >
                {isLoading ? (
                  <TbLoader className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
                ) : (
                  <TbSend className="w-4 h-4 sm:w-5 sm:h-5" />
                )}
              </motion.button>
            </div>

            {/* Hidden File Input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageSelect}
              className="hidden"
            />
          </div>

          {/* Helper Text - Responsive */}
          <p className="text-xs text-gray-500 mt-2 text-center px-2">
            {window.innerWidth <= 768
              ? "Tap to send • Upload images for analysis"
              : "Press Enter to send • Shift+Enter for new line • Upload images for analysis"
            }
          </p>
        </div>
      </div>
    </div>
  );
}

export default ChatGPTIntegration;
