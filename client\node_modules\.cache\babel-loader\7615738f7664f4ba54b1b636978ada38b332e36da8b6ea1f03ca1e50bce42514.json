{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    id\n  } = useParams();\n  const {\n    user\n  } = useSelector(state => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n  const [showAnimation, setShowAnimation] = useState(false);\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n    // Trigger entrance animation\n    setTimeout(() => setShowAnimation(true), 100);\n\n    // Play enhanced sound effects based on pass/fail\n    const playSound = () => {\n      try {\n        if (isPassed) {\n          // Success sound - create a pleasant success tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play success melody: C-E-G-C (major chord progression)\n          const now = audioContext.currentTime;\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate enhanced animations based on pass/fail\n    if (isPassed) {\n      // Enhanced confetti for pass\n      const newConfetti = [];\n      for (let i = 0; i < 100; i++) {\n        newConfetti.push({\n          id: i,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 3 + Math.random() * 3,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FF69B4', '#32CD32', '#FF4500'][Math.floor(Math.random() * 8)],\n          shape: Math.random() > 0.5 ? 'circle' : 'square',\n          size: 2 + Math.random() * 4\n        });\n      }\n      setConfetti(newConfetti);\n\n      // Add clapping hands animation\n      setTimeout(() => {\n        const clapElements = [];\n        for (let i = 0; i < 6; i++) {\n          clapElements.push({\n            id: `clap_${i}`,\n            left: 20 + Math.random() * 60,\n            top: 20 + Math.random() * 60,\n            delay: Math.random() * 2\n          });\n        }\n        setConfetti(prev => [...prev, ...clapElements.map(clap => ({\n          ...clap,\n          isClap: true,\n          duration: 2,\n          color: '#FFD700'\n        }))]);\n      }, 1000);\n\n      // Remove all animations after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    } else {\n      // Creative fail animations - floating motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡'];\n      for (let i = 0; i < 20; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 4 + Math.random() * 2,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 6 seconds\n      setTimeout(() => setConfetti([]), 6000);\n    }\n    playSound();\n  }, [isPassed]);\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n    try {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: true\n      }));\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: false\n      }));\n    }\n  };\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen flex items-center justify-center p-4 relative overflow-hidden ${isPassed ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100' : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'}`,\n    children: [confetti.map(piece => {\n      if (piece.isClap) {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute text-4xl opacity-90\",\n          style: {\n            left: `${piece.left}%`,\n            top: `${piece.top}%`,\n            animation: `clap ${piece.duration}s ease-in-out ${piece.delay}s infinite`\n          },\n          children: \"\\uD83D\\uDC4F\"\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this);\n      }\n      if (piece.isMotivational) {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute text-3xl opacity-80\",\n          style: {\n            left: `${piece.left}%`,\n            animation: `float-up ${piece.duration}s ease-out ${piece.delay}s forwards`,\n            top: '100vh'\n          },\n          children: piece.emoji\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute opacity-80\",\n        style: {\n          left: `${piece.left}%`,\n          width: `${piece.size || 2}px`,\n          height: `${piece.size || 2}px`,\n          backgroundColor: piece.color,\n          borderRadius: piece.shape === 'circle' ? '50%' : '0%',\n          animation: `confetti-fall ${piece.duration}s linear ${piece.delay}s forwards`,\n          top: '-10px'\n        }\n      }, piece.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-10px) rotate(0deg);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) rotate(720deg);\n            opacity: 0;\n          }\n        }\n\n        @keyframes clap {\n          0%, 100% { transform: scale(1) rotate(0deg); }\n          25% { transform: scale(1.2) rotate(-5deg); }\n          75% { transform: scale(1.2) rotate(5deg); }\n        }\n\n        @keyframes float-up {\n          0% {\n            transform: translateY(0px) scale(1);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(-100px) scale(0.5);\n            opacity: 0;\n          }\n        }\n\n        @keyframes sad-bounce {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-10px); }\n        }\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes bounce-in {\n          0% {\n            transform: scale(0.3) rotate(-10deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.1) rotate(5deg);\n          }\n          100% {\n            transform: scale(1) rotate(0deg);\n            opacity: 1;\n          }\n        }\n\n        @keyframes pulse-glow {\n          0%, 100% {\n            box-shadow: 0 0 30px rgba(34, 197, 94, 0.6), 0 0 60px rgba(34, 197, 94, 0.3);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 50px rgba(34, 197, 94, 1), 0 0 100px rgba(34, 197, 94, 0.5);\n            transform: scale(1.02);\n          }\n        }\n\n        @keyframes shake {\n          0%, 100% { transform: translateX(0); }\n          10%, 30%, 50%, 70%, 90% { transform: translateX(-8px); }\n          20%, 40%, 60%, 80% { transform: translateX(8px); }\n        }\n\n        @keyframes fail-pulse {\n          0%, 100% {\n            box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 40px rgba(239, 68, 68, 0.7);\n            transform: scale(1.01);\n          }\n        }\n\n        @keyframes celebration {\n          0%, 100% { transform: rotate(0deg) scale(1); }\n          25% { transform: rotate(-5deg) scale(1.1); }\n          75% { transform: rotate(5deg) scale(1.1); }\n        }\n\n        .result-card {\n          animation: bounce-in 0.8s ease-out forwards;\n        }\n\n        .pass-glow {\n          animation: pulse-glow 2s ease-in-out infinite, celebration 3s ease-in-out infinite;\n        }\n\n        .fail-shake {\n          animation: shake 0.6s ease-in-out, fail-pulse 2s ease-in-out infinite 0.6s;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-2xl w-full result-card ${showAnimation ? isPassed ? 'pass-glow' : 'fail-shake' : ''} ${isPassed ? 'border-green-300' : 'border-red-300'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'} ${showAnimation ? 'animate-bounce' : ''}`,\n          children: [isPassed && showAnimation && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 rounded-full border-4 border-green-300 animate-ping\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-2 rounded-full border-2 border-green-400 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: `w-12 h-12 ${isPassed ? 'text-green-600' : 'text-red-600'} ${showAnimation ? 'animate-pulse' : ''}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), isPassed && showAnimation && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-2 -right-2 w-3 h-3 bg-yellow-400 rounded-full animate-ping\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-2 -left-2 w-2 h-2 bg-yellow-300 rounded-full animate-ping\",\n              style: {\n                animationDelay: '0.5s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-0 left-0 w-2 h-2 bg-yellow-500 rounded-full animate-ping\",\n              style: {\n                animationDelay: '1s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `text-6xl font-bold mb-4 ${showAnimation ? 'animate-bounce' : ''} ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n          children: isPassed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-3\",\n            children: \"\\uD83C\\uDF89 Congratulations! \\uD83C\\uDF89\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-3\",\n            children: \"\\uD83D\\uDCAA Keep Going! \\uD83D\\uDCAA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-2xl font-semibold mb-2 ${showAnimation ? 'animate-pulse' : ''} text-gray-700`,\n          children: isPassed ? 'You Passed!' : 'You Can Do It!'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2 text-lg\",\n          children: [\"\\uD83D\\uDCDA \", quizName, \" - \", quizSubject]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-block px-8 py-4 rounded-2xl ${isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-5xl font-bold mb-2 ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-600\",\n            children: \"Your Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-green-600\",\n            children: correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-green-600 font-medium\",\n            children: \"\\u2705 Correct\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-red-600\",\n            children: totalQuestions - correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-600 font-medium\",\n            children: \"\\u274C Wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\uD83D\\uDCCA Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-blue-600\",\n            children: [Math.floor(timeTaken / 60), \":\", (timeTaken % 60).toString().padStart(2, '0')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\u23F1\\uFE0F Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 9\n      }, this), xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-purple-600 font-bold text-xl\",\n                children: [\"+\", xpData.xpAwarded || 0, \" XP\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Earned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (((user === null || user === void 0 ? void 0 : user.totalXP) || 0) + (xpData.xpAwarded || 0)).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", (user === null || user === void 0 ? void 0 : user.currentLevel) || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }, this), !xpData && user && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Your Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (user.totalXP || 0).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", user.currentLevel || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCDA Learning Summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this), resultDetails && resultDetails.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCCB Question by Question Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 max-h-96 overflow-y-auto\",\n          children: resultDetails.map((detail, index) => {\n            // Debug: Log question data to see what's available\n            console.log(`Question ${index + 1} data:`, detail);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl ${detail.isCorrect ? 'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300' : 'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'}`,\n              style: {\n                boxShadow: detail.isCorrect ? '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)' : '0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 ${detail.isCorrect ? 'bg-green-300 border-b-4 border-green-500' : 'bg-red-300 border-b-4 border-red-500'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-10 h-10 rounded-full flex items-center justify-center font-bold ${detail.isCorrect ? 'bg-green-500 text-white shadow-lg' : 'bg-red-500 text-white shadow-lg'}`,\n                    children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 45\n                    }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 596,\n                      columnNumber: 79\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-bold text-gray-900 text-lg\",\n                      children: [\"Question \", index + 1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-3 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 604,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 603,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 bg-white p-3 rounded-lg border\",\n                    children: detail.questionText || detail.questionName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this), (detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-lg border-2 ${detail.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-semibold text-gray-700\",\n                        children: \"\\uD83D\\uDCF7 Question Image:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 633,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 632,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-2 rounded-lg border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: detail.questionImage || detail.image || detail.imageUrl,\n                        alt: \"Question Image\",\n                        className: \"max-w-full h-auto rounded-lg shadow-sm mx-auto block\",\n                        style: {\n                          maxHeight: '300px'\n                        },\n                        onError: e => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'block';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 643,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\",\n                        style: {\n                          display: 'none'\n                        },\n                        children: \"\\uD83D\\uDCF7 Image could not be loaded\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 653,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-4 rounded-lg border-4 ${detail.isCorrect ? 'bg-green-50 border-green-500' : 'bg-red-50 border-red-500'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-6 h-6 rounded-full flex items-center justify-center ${detail.isCorrect ? 'bg-green-500' : 'bg-red-500'}`,\n                        children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 676,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 678,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Your Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `p-3 rounded-lg font-bold text-lg ${detail.isCorrect ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'}`,\n                      children: detail.userAnswer || 'No answer provided'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 23\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-50 p-4 rounded-lg border-4 border-green-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 696,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 695,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Correct Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 698,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 694,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\",\n                      children: detail.correctAnswer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 700,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 693,\n                    columnNumber: 25\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${loadingExplanations[`question_${index}`] ? 'bg-gray-400 cursor-not-allowed' : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'}`,\n                      onClick: () => fetchAIExplanation(index, detail),\n                      disabled: loadingExplanations[`question_${index}`],\n                      children: loadingExplanations[`question_${index}`] ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 720,\n                          columnNumber: 33\n                        }, this), \"Getting AI Explanation...\"]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 725,\n                          columnNumber: 33\n                        }, this), \"Get AI Explanation\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 27\n                    }, this), explanations[`question_${index}`] && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-2 mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5 text-blue-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 735,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-bold text-blue-800\",\n                          children: \"\\uD83E\\uDD16 AI Explanation:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 736,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-blue-700 leading-relaxed whitespace-pre-wrap\",\n                        children: explanations[`question_${index}`]\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 738,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this)]\n            }, detail.questionId || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 More Quizzes button clicked!');\n            handleBackToQuizzes();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbHome, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this), \"More Quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 Retake Quiz button clicked!');\n            handleRetakeQuiz();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this), \"Retake Quiz\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 771,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 757,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"/IVRgUSfidSdspZEOaby52r5SFo=\", false, function () {\n  return [useNavigate, useLocation, useParams, useSelector];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "startTransition", "useEffect", "useState", "useNavigate", "useLocation", "useParams", "useSelector", "TbTrophy", "TbCheck", "TbX", "TbHome", "TbStar", "TbBrain", "TbChartBar", "chatWithChatGPTToExplainAns", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "navigate", "location", "id", "user", "state", "resultData", "percentage", "correctAnswers", "totalQuestions", "timeTaken", "resultDetails", "xpData", "quizName", "quizSubject", "passingPercentage", "verdict", "isPassed", "showAnimation", "setShowAnimation", "confetti", "set<PERSON>on<PERSON>tti", "explanations", "setExplanations", "loadingExplanations", "setLoadingExplanations", "setTimeout", "playSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "playTone", "frequency", "startTime", "duration", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "type", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "now", "currentTime", "error", "console", "log", "newConfetti", "i", "push", "left", "Math", "random", "delay", "color", "floor", "shape", "size", "clapElements", "top", "prev", "map", "clap", "isClap", "motivationalElements", "motivationalEmojis", "emoji", "length", "isMotivational", "fetchExplanation", "questionIndex", "detail", "<PERSON><PERSON><PERSON>", "response", "question", "questionText", "questionName", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "userAnswer", "imageUrl", "questionImage", "image", "success", "explanation", "handleBackToQuizzes", "handleRetakeQuiz", "className", "children", "piece", "style", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "backgroundColor", "borderRadius", "jsx", "animationDelay", "toString", "padStart", "xpAwarded", "totalXP", "toLocaleString", "currentLevel", "index", "isCorrect", "boxShadow", "questionType", "src", "alt", "maxHeight", "onError", "e", "target", "display", "nextS<PERSON>ling", "onClick", "fetchAIExplanation", "disabled", "questionId", "preventDefault", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\n\nconst QuizResult = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { id } = useParams();\n  const { user } = useSelector((state) => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n\n  const [showAnimation, setShowAnimation] = useState(false);\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n    // Trigger entrance animation\n    setTimeout(() => setShowAnimation(true), 100);\n\n    // Play enhanced sound effects based on pass/fail\n    const playSound = () => {\n      try {\n        if (isPassed) {\n          // Success sound - create a pleasant success tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play success melody: C-E-G-C (major chord progression)\n          const now = audioContext.currentTime;\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate enhanced animations based on pass/fail\n    if (isPassed) {\n      // Enhanced confetti for pass\n      const newConfetti = [];\n      for (let i = 0; i < 100; i++) {\n        newConfetti.push({\n          id: i,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 3 + Math.random() * 3,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FF69B4', '#32CD32', '#FF4500'][Math.floor(Math.random() * 8)],\n          shape: Math.random() > 0.5 ? 'circle' : 'square',\n          size: 2 + Math.random() * 4\n        });\n      }\n      setConfetti(newConfetti);\n\n      // Add clapping hands animation\n      setTimeout(() => {\n        const clapElements = [];\n        for (let i = 0; i < 6; i++) {\n          clapElements.push({\n            id: `clap_${i}`,\n            left: 20 + Math.random() * 60,\n            top: 20 + Math.random() * 60,\n            delay: Math.random() * 2\n          });\n        }\n        setConfetti(prev => [...prev, ...clapElements.map(clap => ({\n          ...clap,\n          isClap: true,\n          duration: 2,\n          color: '#FFD700'\n        }))]);\n      }, 1000);\n\n      // Remove all animations after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    } else {\n      // Creative fail animations - floating motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡'];\n\n      for (let i = 0; i < 20; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 4 + Math.random() * 2,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 6 seconds\n      setTimeout(() => setConfetti([]), 6000);\n    }\n\n    playSound();\n  }, [isPassed]);\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n\n    try {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: true }));\n\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: false }));\n    }\n  };\n\n\n\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center p-4 relative overflow-hidden ${\n      isPassed\n        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100'\n        : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'\n    }`}>\n\n      {/* Enhanced Animations */}\n      {confetti.map((piece) => {\n        if (piece.isClap) {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute text-4xl opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                top: `${piece.top}%`,\n                animation: `clap ${piece.duration}s ease-in-out ${piece.delay}s infinite`\n              }}\n            >\n              👏\n            </div>\n          );\n        }\n\n        if (piece.isMotivational) {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute text-3xl opacity-80\"\n              style={{\n                left: `${piece.left}%`,\n                animation: `float-up ${piece.duration}s ease-out ${piece.delay}s forwards`,\n                top: '100vh'\n              }}\n            >\n              {piece.emoji}\n            </div>\n          );\n        }\n\n        return (\n          <div\n            key={piece.id}\n            className=\"absolute opacity-80\"\n            style={{\n              left: `${piece.left}%`,\n              width: `${piece.size || 2}px`,\n              height: `${piece.size || 2}px`,\n              backgroundColor: piece.color,\n              borderRadius: piece.shape === 'circle' ? '50%' : '0%',\n              animation: `confetti-fall ${piece.duration}s linear ${piece.delay}s forwards`,\n              top: '-10px'\n            }}\n          />\n        );\n      })}\n\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-10px) rotate(0deg);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) rotate(720deg);\n            opacity: 0;\n          }\n        }\n\n        @keyframes clap {\n          0%, 100% { transform: scale(1) rotate(0deg); }\n          25% { transform: scale(1.2) rotate(-5deg); }\n          75% { transform: scale(1.2) rotate(5deg); }\n        }\n\n        @keyframes float-up {\n          0% {\n            transform: translateY(0px) scale(1);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(-100px) scale(0.5);\n            opacity: 0;\n          }\n        }\n\n        @keyframes sad-bounce {\n          0%, 100% { transform: translateY(0px); }\n          50% { transform: translateY(-10px); }\n        }\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes bounce-in {\n          0% {\n            transform: scale(0.3) rotate(-10deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.1) rotate(5deg);\n          }\n          100% {\n            transform: scale(1) rotate(0deg);\n            opacity: 1;\n          }\n        }\n\n        @keyframes pulse-glow {\n          0%, 100% {\n            box-shadow: 0 0 30px rgba(34, 197, 94, 0.6), 0 0 60px rgba(34, 197, 94, 0.3);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 50px rgba(34, 197, 94, 1), 0 0 100px rgba(34, 197, 94, 0.5);\n            transform: scale(1.02);\n          }\n        }\n\n        @keyframes shake {\n          0%, 100% { transform: translateX(0); }\n          10%, 30%, 50%, 70%, 90% { transform: translateX(-8px); }\n          20%, 40%, 60%, 80% { transform: translateX(8px); }\n        }\n\n        @keyframes fail-pulse {\n          0%, 100% {\n            box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);\n            transform: scale(1);\n          }\n          50% {\n            box-shadow: 0 0 40px rgba(239, 68, 68, 0.7);\n            transform: scale(1.01);\n          }\n        }\n\n        @keyframes celebration {\n          0%, 100% { transform: rotate(0deg) scale(1); }\n          25% { transform: rotate(-5deg) scale(1.1); }\n          75% { transform: rotate(5deg) scale(1.1); }\n        }\n\n        .result-card {\n          animation: bounce-in 0.8s ease-out forwards;\n        }\n\n        .pass-glow {\n          animation: pulse-glow 2s ease-in-out infinite, celebration 3s ease-in-out infinite;\n        }\n\n        .fail-shake {\n          animation: shake 0.6s ease-in-out, fail-pulse 2s ease-in-out infinite 0.6s;\n        }\n      `}</style>\n\n      <div className={`bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-2xl w-full result-card ${\n        showAnimation ? (isPassed ? 'pass-glow' : 'fail-shake') : ''\n      } ${isPassed ? 'border-green-300' : 'border-red-300'}`}>\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${\n            isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'\n          } ${showAnimation ? 'animate-bounce' : ''}`}>\n\n            {/* Animated rings for pass */}\n            {isPassed && showAnimation && (\n              <>\n                <div className=\"absolute inset-0 rounded-full border-4 border-green-300 animate-ping\"></div>\n                <div className=\"absolute inset-2 rounded-full border-2 border-green-400 animate-pulse\"></div>\n              </>\n            )}\n\n            <TbTrophy className={`w-12 h-12 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            } ${showAnimation ? 'animate-pulse' : ''}`} />\n\n            {/* Sparkles for pass */}\n            {isPassed && showAnimation && (\n              <>\n                <div className=\"absolute -top-2 -right-2 w-3 h-3 bg-yellow-400 rounded-full animate-ping\"></div>\n                <div className=\"absolute -bottom-2 -left-2 w-2 h-2 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                <div className=\"absolute top-0 left-0 w-2 h-2 bg-yellow-500 rounded-full animate-ping\" style={{animationDelay: '1s'}}></div>\n              </>\n            )}\n          </div>\n          \n          <h1 className={`text-6xl font-bold mb-4 ${\n            showAnimation ? 'animate-bounce' : ''\n          } ${isPassed ? 'text-green-600' : 'text-red-600'}`}>\n            {isPassed ? (\n              <span className=\"flex items-center justify-center gap-3\">\n                🎉 Congratulations! 🎉\n              </span>\n            ) : (\n              <span className=\"flex items-center justify-center gap-3\">\n                💪 Keep Going! 💪\n              </span>\n            )}\n          </h1>\n\n          <div className={`text-2xl font-semibold mb-2 ${\n            showAnimation ? 'animate-pulse' : ''\n          } text-gray-700`}>\n            {isPassed ? 'You Passed!' : 'You Can Do It!'}\n          </div>\n\n          <p className=\"text-gray-600 mt-2 text-lg\">\n            📚 {quizName} - {quizSubject}\n          </p>\n        </div>\n\n        {/* Score Display */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-block px-8 py-4 rounded-2xl ${\n            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'\n          }`}>\n            <div className={`text-5xl font-bold mb-2 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {percentage}%\n            </div>\n            <div className=\"text-gray-600\">\n              Your Score\n            </div>\n          </div>\n        </div>\n\n\n\n        {/* Streamlined Results Cards */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-green-600\">{correctAnswers}</div>\n            <div className=\"text-sm text-green-600 font-medium\">✅ Correct</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-red-600\">{totalQuestions - correctAnswers}</div>\n            <div className=\"text-sm text-red-600 font-medium\">❌ Wrong</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className={`text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`}>{percentage}%</div>\n            <div className=\"text-sm text-gray-600 font-medium\">📊 Score</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-blue-600\">\n              {Math.floor(timeTaken / 60)}:{(timeTaken % 60).toString().padStart(2, '0')}\n            </div>\n            <div className=\"text-sm text-gray-600 font-medium\">⏱️ Time</div>\n          </div>\n        </div>\n\n        {/* Streamlined XP Section */}\n        {xpData && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-purple-600 font-bold text-xl\">+{xpData.xpAwarded || 0} XP</div>\n                  <div className=\"text-sm text-gray-600\">Earned</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {((user?.totalXP || 0) + (xpData.xpAwarded || 0)).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user?.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Streamlined XP Section (for users without XP data) */}\n        {!xpData && user && (\n          <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-sm text-gray-600\">Your Progress</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {(user.totalXP || 0).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Summary Section */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n              <TbBrain className=\"w-6 h-6 text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">📚 Learning Summary</h3>\n          </div>\n\n          {/* Simplified Learning Summary - Only Question Review */}\n        </div>\n\n        {/* Detailed Question Breakdown */}\n        {resultDetails && resultDetails.length > 0 && (\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\">\n                <TbChartBar className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">📋 Question by Question Review</h3>\n            </div>\n\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              {resultDetails.map((detail, index) => {\n                // Debug: Log question data to see what's available\n                console.log(`Question ${index + 1} data:`, detail);\n                return (\n                <div\n                  key={detail.questionId || index}\n                  className={`rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl ${\n                    detail.isCorrect\n                      ? 'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300'\n                      : 'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'\n                  }`}\n                  style={{\n                    boxShadow: detail.isCorrect\n                      ? '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)'\n                      : '0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'\n                  }}\n                >\n                  {/* Question Header */}\n                  <div className={`p-4 ${\n                    detail.isCorrect\n                      ? 'bg-green-300 border-b-4 border-green-500'\n                      : 'bg-red-300 border-b-4 border-red-500'\n                  }`}>\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${\n                        detail.isCorrect\n                          ? 'bg-green-500 text-white shadow-lg'\n                          : 'bg-red-500 text-white shadow-lg'\n                      }`}>\n                        {detail.isCorrect ? <TbCheck className=\"w-5 h-5\" /> : <TbX className=\"w-5 h-5\" />}\n                      </div>\n\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-bold text-gray-900 text-lg\">\n                          Question {index + 1}\n                        </h4>\n                        <div className=\"flex items-center gap-2 mt-1\">\n                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${\n                            detail.isCorrect\n                              ? 'bg-green-500 text-white'\n                              : 'bg-red-500 text-white'\n                          }`}>\n                            {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4\">\n                    <div className=\"mb-4\">\n                      <p className=\"text-gray-700 bg-white p-3 rounded-lg border\">\n                        {detail.questionText || detail.questionName}\n                      </p>\n                    </div>\n\n                    {/* Show Image for image questions or any question with an image */}\n                    {(detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && (\n                      <div className=\"mb-4\">\n                        <div className={`p-3 rounded-lg border-2 ${\n                          detail.isCorrect\n                            ? 'bg-green-50 border-green-200'\n                            : 'bg-red-50 border-red-200'\n                        }`}>\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <span className=\"text-sm font-semibold text-gray-700\">📷 Question Image:</span>\n                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${\n                              detail.isCorrect\n                                ? 'bg-green-500 text-white'\n                                : 'bg-red-500 text-white'\n                            }`}>\n                              {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                            </span>\n                          </div>\n                          <div className=\"bg-white p-2 rounded-lg border\">\n                            <img\n                              src={detail.questionImage || detail.image || detail.imageUrl}\n                              alt=\"Question Image\"\n                              className=\"max-w-full h-auto rounded-lg shadow-sm mx-auto block\"\n                              style={{ maxHeight: '300px' }}\n                              onError={(e) => {\n                                e.target.style.display = 'none';\n                                e.target.nextSibling.style.display = 'block';\n                              }}\n                            />\n                            <div\n                              className=\"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\"\n                              style={{ display: 'none' }}\n                            >\n                              📷 Image could not be loaded\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Enhanced Answer Section with Color Indicators */}\n                    <div className=\"space-y-3\">\n                      <div className={`p-4 rounded-lg border-4 ${\n                        detail.isCorrect\n                          ? 'bg-green-50 border-green-500'\n                          : 'bg-red-50 border-red-500'\n                      }`}>\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${\n                            detail.isCorrect ? 'bg-green-500' : 'bg-red-500'\n                          }`}>\n                            {detail.isCorrect ? (\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            ) : (\n                              <TbX className=\"w-4 h-4 text-white\" />\n                            )}\n                          </div>\n                          <span className=\"font-semibold text-gray-700\">Your Answer:</span>\n                        </div>\n                        <div className={`p-3 rounded-lg font-bold text-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-100 text-green-700 border border-green-200'\n                            : 'bg-red-100 text-red-700 border border-red-200'\n                        }`}>\n                          {detail.userAnswer || 'No answer provided'}\n                        </div>\n                      </div>\n\n                      {!detail.isCorrect && (\n                        <div className=\"bg-green-50 p-4 rounded-lg border-4 border-green-500\">\n                          <div className=\"flex items-center gap-3 mb-2\">\n                            <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\">\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            </div>\n                            <span className=\"font-semibold text-gray-700\">Correct Answer:</span>\n                          </div>\n                          <div className=\"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\">\n                            {detail.correctAnswer}\n                          </div>\n                        </div>\n                      )}\n\n                      {/* AI Explanation Button for Wrong Answers */}\n                      {!detail.isCorrect && (\n                        <div className=\"mt-3\">\n                          <button\n                            className={`w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${\n                              loadingExplanations[`question_${index}`]\n                                ? 'bg-gray-400 cursor-not-allowed'\n                                : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'\n                            }`}\n                            onClick={() => fetchAIExplanation(index, detail)}\n                            disabled={loadingExplanations[`question_${index}`]}\n                          >\n                            {loadingExplanations[`question_${index}`] ? (\n                              <>\n                                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                                Getting AI Explanation...\n                              </>\n                            ) : (\n                              <>\n                                <TbBrain className=\"w-5 h-5\" />\n                                Get AI Explanation\n                              </>\n                            )}\n                          </button>\n\n                          {/* AI Explanation Display */}\n                          {explanations[`question_${index}`] && (\n                            <div className=\"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\">\n                              <div className=\"flex items-center gap-2 mb-3\">\n                                <TbBrain className=\"w-5 h-5 text-blue-600\" />\n                                <h6 className=\"font-bold text-blue-800\">🤖 AI Explanation:</h6>\n                              </div>\n                              <div className=\"text-blue-700 leading-relaxed whitespace-pre-wrap\">\n                                {explanations[`question_${index}`]}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                );\n              })}\n            </div>\n\n\n          </div>\n        )}\n\n        {/* Actions */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 More Quizzes button clicked!');\n              handleBackToQuizzes();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbHome className=\"w-5 h-5\" />\n            More Quizzes\n          </button>\n\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 Retake Quiz button clicked!');\n              handleRetakeQuiz();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbTrophy className=\"w-5 h-5\" />\n            Retake Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACnE,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AAC5F,SAASC,2BAA2B,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB;EAAG,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEmB;EAAK,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD;EACA,MAAME,UAAU,GAAGJ,QAAQ,CAACG,KAAK,IAAI;IACnCE,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,EAAE;IACjBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,SAAS;IACtBC,iBAAiB,EAAE,EAAE;IACrBC,OAAO,EAAE;EACX,CAAC;EAED,MAAM;IACJT,UAAU;IACVC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTE,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,iBAAiB;IACjBC,OAAO;IACPL;EACF,CAAC,GAAGL,UAAU;EACd,MAAMW,QAAQ,GAAGD,OAAO,KAAK,MAAM,IAAIT,UAAU,KAAKQ,iBAAiB,IAAI,EAAE,CAAC;EAE9E,MAAM,CAACG,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC0C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAElE;EACAD,SAAS,CAAC,MAAM;IACd;IACA6C,UAAU,CAAC,MAAMP,gBAAgB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;;IAE7C;IACA,MAAMQ,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,IAAIV,QAAQ,EAAE;UACZ;UACA,MAAMW,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;;UAE7E;UACA,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,KAAK;YACnD,MAAMC,UAAU,GAAGR,YAAY,CAACS,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGV,YAAY,CAACW,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACZ,YAAY,CAACa,WAAW,CAAC;YAE1CL,UAAU,CAACH,SAAS,CAACS,cAAc,CAACT,SAAS,EAAEC,SAAS,CAAC;YACzDE,UAAU,CAACO,IAAI,GAAG,MAAM;YAExBL,QAAQ,CAACM,IAAI,CAACF,cAAc,CAAC,CAAC,EAAER,SAAS,CAAC;YAC1CI,QAAQ,CAACM,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC5DI,QAAQ,CAACM,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEC,UAAU,CAACW,KAAK,CAACb,SAAS,CAAC;YAC3BE,UAAU,CAACY,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAMc,GAAG,GAAGrB,YAAY,CAACsB,WAAW;UACpClB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAC5BjB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClCjB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClCjB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAEpC,CAAC,MAAM;UACL;UACA,MAAMrB,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;UAE7E,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,KAAK;YACnD,MAAMC,UAAU,GAAGR,YAAY,CAACS,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGV,YAAY,CAACW,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACZ,YAAY,CAACa,WAAW,CAAC;YAE1CL,UAAU,CAACH,SAAS,CAACS,cAAc,CAACT,SAAS,EAAEC,SAAS,CAAC;YACzDE,UAAU,CAACO,IAAI,GAAG,MAAM;YAExBL,QAAQ,CAACM,IAAI,CAACF,cAAc,CAAC,CAAC,EAAER,SAAS,CAAC;YAC1CI,QAAQ,CAACM,IAAI,CAACC,uBAAuB,CAAC,IAAI,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC7DI,QAAQ,CAACM,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEC,UAAU,CAACW,KAAK,CAACb,SAAS,CAAC;YAC3BE,UAAU,CAACY,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAMc,GAAG,GAAGrB,YAAY,CAACsB,WAAW;UACpClB,QAAQ,CAAC,GAAG,EAAEiB,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UACzBjB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACpC;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACpC;IACF,CAAC;;IAED;IACA,IAAIpC,QAAQ,EAAE;MACZ;MACA,MAAMqC,WAAW,GAAG,EAAE;MACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5BD,WAAW,CAACE,IAAI,CAAC;UACfrD,EAAE,EAAEoD,CAAC;UACLE,IAAI,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBC,KAAK,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBxB,QAAQ,EAAE,CAAC,GAAGuB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BE,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACH,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAC9HI,KAAK,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;UAChDK,IAAI,EAAE,CAAC,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAC5B,CAAC,CAAC;MACJ;MACAtC,WAAW,CAACiC,WAAW,CAAC;;MAExB;MACA5B,UAAU,CAAC,MAAM;QACf,MAAMuC,YAAY,GAAG,EAAE;QACvB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1BU,YAAY,CAACT,IAAI,CAAC;YAChBrD,EAAE,EAAG,QAAOoD,CAAE,EAAC;YACfE,IAAI,EAAE,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YAC7BO,GAAG,EAAE,EAAE,GAAGR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YAC5BC,KAAK,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAC,CAAC;QACJ;QACAtC,WAAW,CAAC8C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGF,YAAY,CAACG,GAAG,CAACC,IAAI,KAAK;UACzD,GAAGA,IAAI;UACPC,MAAM,EAAE,IAAI;UACZnC,QAAQ,EAAE,CAAC;UACX0B,KAAK,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,EAAE,IAAI,CAAC;;MAER;MACAnC,UAAU,CAAC,MAAML,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACzC,CAAC,MAAM;MACL;MACA,MAAMkD,oBAAoB,GAAG,EAAE;MAC/B,MAAMC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAE/D,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BgB,oBAAoB,CAACf,IAAI,CAAC;UACxBrD,EAAE,EAAG,YAAWoD,CAAE,EAAC;UACnBE,IAAI,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBC,KAAK,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBxB,QAAQ,EAAE,CAAC,GAAGuB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Bc,KAAK,EAAED,kBAAkB,CAACd,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGa,kBAAkB,CAACE,MAAM,CAAC,CAAC;UAChFC,cAAc,EAAE;QAClB,CAAC,CAAC;MACJ;MACAtD,WAAW,CAACkD,oBAAoB,CAAC;;MAEjC;MACA7C,UAAU,CAAC,MAAML,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACzC;IAEAM,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACV,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM2D,gBAAgB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,MAAM,KAAK;IACxD,MAAMC,WAAW,GAAI,YAAWF,aAAc,EAAC;;IAE/C;IACA,IAAIrD,mBAAmB,CAACuD,WAAW,CAAC,IAAIzD,YAAY,CAACyD,WAAW,CAAC,EAAE;MACjE;IACF;IAEA,IAAI;MACFtD,sBAAsB,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACY,WAAW,GAAG;MAAK,CAAC,CAAC,CAAC;MAElE,MAAMC,QAAQ,GAAG,MAAMtF,2BAA2B,CAAC;QACjDuF,QAAQ,EAAEH,MAAM,CAACI,YAAY,IAAIJ,MAAM,CAACK,YAAY;QACpDC,cAAc,EAAEN,MAAM,CAACO,aAAa;QACpCC,UAAU,EAAER,MAAM,CAACQ,UAAU;QAC7BC,QAAQ,EAAET,MAAM,CAACU,aAAa,IAAIV,MAAM,CAACW,KAAK,IAAIX,MAAM,CAACS,QAAQ,IAAI;MACvE,CAAC,CAAC;MAEF,IAAIP,QAAQ,CAACU,OAAO,EAAE;QACpBnE,eAAe,CAAC4C,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACY,WAAW,GAAGC,QAAQ,CAACW;QAC1B,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLvC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAE6B,QAAQ,CAAC7B,KAAK,CAAC;QAC7D5B,eAAe,CAAC4C,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACY,WAAW,GAAG;QACjB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD5B,eAAe,CAAC4C,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACY,WAAW,GAAG;MACjB,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRtD,sBAAsB,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACY,WAAW,GAAG;MAAM,CAAC,CAAC,CAAC;IACrE;EACF,CAAC;EAID,MAAMa,mBAAmB,GAAGA,CAAA,KAAM;IAChCxC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CzE,eAAe,CAAC,MAAM;MACpBqB,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM4F,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAElD,EAAE,CAAC;IAC5C,IAAIA,EAAE,EAAE;MACNvB,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAE,SAAQE,EAAG,OAAM,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLiD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DzE,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEL,OAAA;IAAKkG,SAAS,EAAG,8EACf7E,QAAQ,GACJ,4DAA4D,GAC5D,yDACL,EAAE;IAAA8E,QAAA,GAGA3E,QAAQ,CAACgD,GAAG,CAAE4B,KAAK,IAAK;MACvB,IAAIA,KAAK,CAAC1B,MAAM,EAAE;QAChB,oBACE1E,OAAA;UAEEkG,SAAS,EAAC,8BAA8B;UACxCG,KAAK,EAAE;YACLxC,IAAI,EAAG,GAAEuC,KAAK,CAACvC,IAAK,GAAE;YACtBS,GAAG,EAAG,GAAE8B,KAAK,CAAC9B,GAAI,GAAE;YACpBgC,SAAS,EAAG,QAAOF,KAAK,CAAC7D,QAAS,iBAAgB6D,KAAK,CAACpC,KAAM;UAChE,CAAE;UAAAmC,QAAA,EACH;QAED,GATOC,KAAK,CAAC7F,EAAE;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CAAC;MAEV;MAEA,IAAIN,KAAK,CAACrB,cAAc,EAAE;QACxB,oBACE/E,OAAA;UAEEkG,SAAS,EAAC,8BAA8B;UACxCG,KAAK,EAAE;YACLxC,IAAI,EAAG,GAAEuC,KAAK,CAACvC,IAAK,GAAE;YACtByC,SAAS,EAAG,YAAWF,KAAK,CAAC7D,QAAS,cAAa6D,KAAK,CAACpC,KAAM,YAAW;YAC1EM,GAAG,EAAE;UACP,CAAE;UAAA6B,QAAA,EAEDC,KAAK,CAACvB;QAAK,GARPuB,KAAK,CAAC7F,EAAE;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CAAC;MAEV;MAEA,oBACE1G,OAAA;QAEEkG,SAAS,EAAC,qBAAqB;QAC/BG,KAAK,EAAE;UACLxC,IAAI,EAAG,GAAEuC,KAAK,CAACvC,IAAK,GAAE;UACtB8C,KAAK,EAAG,GAAEP,KAAK,CAAChC,IAAI,IAAI,CAAE,IAAG;UAC7BwC,MAAM,EAAG,GAAER,KAAK,CAAChC,IAAI,IAAI,CAAE,IAAG;UAC9ByC,eAAe,EAAET,KAAK,CAACnC,KAAK;UAC5B6C,YAAY,EAAEV,KAAK,CAACjC,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,IAAI;UACrDmC,SAAS,EAAG,iBAAgBF,KAAK,CAAC7D,QAAS,YAAW6D,KAAK,CAACpC,KAAM,YAAW;UAC7EM,GAAG,EAAE;QACP;MAAE,GAVG8B,KAAK,CAAC7F,EAAE;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWd,CAAC;IAEN,CAAC,CAAC,eAGF1G,OAAA;MAAO+G,GAAG;MAAAZ,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEV1G,OAAA;MAAKkG,SAAS,EAAG,0FACf5E,aAAa,GAAID,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAI,EAC3D,IAAGA,QAAQ,GAAG,kBAAkB,GAAG,gBAAiB,EAAE;MAAA8E,QAAA,gBAErDnG,OAAA;QAAKkG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BnG,OAAA;UAAKkG,SAAS,EAAG,gFACf7E,QAAQ,GAAG,iDAAiD,GAAG,4CAChE,IAAGC,aAAa,GAAG,gBAAgB,GAAG,EAAG,EAAE;UAAA6E,QAAA,GAGzC9E,QAAQ,IAAIC,aAAa,iBACxBtB,OAAA,CAAAE,SAAA;YAAAiG,QAAA,gBACEnG,OAAA;cAAKkG,SAAS,EAAC;YAAsE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5F1G,OAAA;cAAKkG,SAAS,EAAC;YAAuE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eAC7F,CACH,eAED1G,OAAA,CAACT,QAAQ;YAAC2G,SAAS,EAAG,aACpB7E,QAAQ,GAAG,gBAAgB,GAAG,cAC/B,IAAGC,aAAa,GAAG,eAAe,GAAG,EAAG;UAAE;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAG7CrF,QAAQ,IAAIC,aAAa,iBACxBtB,OAAA,CAAAE,SAAA;YAAAiG,QAAA,gBACEnG,OAAA;cAAKkG,SAAS,EAAC;YAA0E;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChG1G,OAAA;cAAKkG,SAAS,EAAC,4EAA4E;cAACG,KAAK,EAAE;gBAACW,cAAc,EAAE;cAAM;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnI1G,OAAA;cAAKkG,SAAS,EAAC,uEAAuE;cAACG,KAAK,EAAE;gBAACW,cAAc,EAAE;cAAI;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eAC5H,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1G,OAAA;UAAIkG,SAAS,EAAG,2BACd5E,aAAa,GAAG,gBAAgB,GAAG,EACpC,IAAGD,QAAQ,GAAG,gBAAgB,GAAG,cAAe,EAAE;UAAA8E,QAAA,EAChD9E,QAAQ,gBACPrB,OAAA;YAAMkG,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEzD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAEP1G,OAAA;YAAMkG,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEzD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEL1G,OAAA;UAAKkG,SAAS,EAAG,+BACf5E,aAAa,GAAG,eAAe,GAAG,EACnC,gBAAgB;UAAA6E,QAAA,EACd9E,QAAQ,GAAG,aAAa,GAAG;QAAgB;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAEN1G,OAAA;UAAGkG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,eACrC,EAAClF,QAAQ,EAAC,KAAG,EAACC,WAAW;QAAA;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN1G,OAAA;QAAKkG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BnG,OAAA;UAAKkG,SAAS,EAAG,sCACf7E,QAAQ,GAAG,uCAAuC,GAAG,mCACtD,EAAE;UAAA8E,QAAA,gBACDnG,OAAA;YAAKkG,SAAS,EAAG,2BACf7E,QAAQ,GAAG,gBAAgB,GAAG,cAC/B,EAAE;YAAA8E,QAAA,GACAxF,UAAU,EAAC,GACd;UAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN1G,OAAA;YAAKkG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAKN1G,OAAA;QAAKkG,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDnG,OAAA;UAAKkG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFnG,OAAA;YAAKkG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAEvF;UAAc;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzE1G,OAAA;YAAKkG,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACN1G,OAAA;UAAKkG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFnG,OAAA;YAAKkG,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEtF,cAAc,GAAGD;UAAc;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxF1G,OAAA;YAAKkG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eACN1G,OAAA;UAAKkG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFnG,OAAA;YAAKkG,SAAS,EAAG,sBAAqB7E,QAAQ,GAAG,gBAAgB,GAAG,cAAe,EAAE;YAAA8E,QAAA,GAAExF,UAAU,EAAC,GAAC;UAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzG1G,OAAA;YAAKkG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACN1G,OAAA;UAAKkG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFnG,OAAA;YAAKkG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAC9CrC,IAAI,CAACI,KAAK,CAACpD,SAAS,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC,CAACA,SAAS,GAAG,EAAE,EAAEmG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACN1G,OAAA;YAAKkG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL1F,MAAM,iBACLhB,OAAA;QAAKkG,SAAS,EAAC,2FAA2F;QAAAC,QAAA,eACxGnG,OAAA;UAAKkG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnG,OAAA;YAAKkG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnG,OAAA;cAAKkG,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eACpFnG,OAAA,CAACL,MAAM;gBAACuG,SAAS,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACN1G,OAAA;cAAAmG,QAAA,gBACEnG,OAAA;gBAAKkG,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,GAAC,EAACnF,MAAM,CAACmG,SAAS,IAAI,CAAC,EAAC,KAAG;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpF1G,OAAA;gBAAKkG,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1G,OAAA;YAAKkG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnG,OAAA;cAAKkG,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD,CAAC,CAAC,CAAA3F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4G,OAAO,KAAI,CAAC,KAAKpG,MAAM,CAACmG,SAAS,IAAI,CAAC,CAAC,EAAEE,cAAc,CAAC;YAAC;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACN1G,OAAA;cAAKkG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBAAiB,EAAC,CAAA3F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8G,YAAY,KAAI,CAAC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAC1F,MAAM,IAAIR,IAAI,iBACdR,OAAA;QAAKkG,SAAS,EAAC,2FAA2F;QAAAC,QAAA,eACxGnG,OAAA;UAAKkG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDnG,OAAA;YAAKkG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCnG,OAAA;cAAKkG,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eACpFnG,OAAA,CAACL,MAAM;gBAACuG,SAAS,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACN1G,OAAA;cAAAmG,QAAA,eACEnG,OAAA;gBAAKkG,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1G,OAAA;YAAKkG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnG,OAAA;cAAKkG,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD,CAAC3F,IAAI,CAAC4G,OAAO,IAAI,CAAC,EAAEC,cAAc,CAAC;YAAC;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN1G,OAAA;cAAKkG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBAAiB,EAAC3F,IAAI,CAAC8G,YAAY,IAAI,CAAC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD1G,OAAA;QAAKkG,SAAS,EAAC,qFAAqF;QAAAC,QAAA,eAClGnG,OAAA;UAAKkG,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CnG,OAAA;YAAKkG,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClFnG,OAAA,CAACJ,OAAO;cAACsG,SAAS,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACN1G,OAAA;YAAIkG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CAAC,EAGL3F,aAAa,IAAIA,aAAa,CAAC+D,MAAM,GAAG,CAAC,iBACxC9E,OAAA;QAAKkG,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBACnGnG,OAAA;UAAKkG,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CnG,OAAA;YAAKkG,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClFnG,OAAA,CAACH,UAAU;cAACqG,SAAS,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN1G,OAAA;YAAIkG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAA8B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAEN1G,OAAA;UAAKkG,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChDpF,aAAa,CAACyD,GAAG,CAAC,CAACU,MAAM,EAAEqC,KAAK,KAAK;YACpC;YACA/D,OAAO,CAACC,GAAG,CAAE,YAAW8D,KAAK,GAAG,CAAE,QAAO,EAAErC,MAAM,CAAC;YAClD,oBACAlF,OAAA;cAEEkG,SAAS,EAAG,mFACVhB,MAAM,CAACsC,SAAS,GACZ,4FAA4F,GAC5F,mFACL,EAAE;cACHnB,KAAK,EAAE;gBACLoB,SAAS,EAAEvC,MAAM,CAACsC,SAAS,GACvB,sEAAsE,GACtE;cACN,CAAE;cAAArB,QAAA,gBAGFnG,OAAA;gBAAKkG,SAAS,EAAG,OACfhB,MAAM,CAACsC,SAAS,GACZ,0CAA0C,GAC1C,sCACL,EAAE;gBAAArB,QAAA,eACDnG,OAAA;kBAAKkG,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCnG,OAAA;oBAAKkG,SAAS,EAAG,qEACfhB,MAAM,CAACsC,SAAS,GACZ,mCAAmC,GACnC,iCACL,EAAE;oBAAArB,QAAA,EACAjB,MAAM,CAACsC,SAAS,gBAAGxH,OAAA,CAACR,OAAO;sBAAC0G,SAAS,EAAC;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG1G,OAAA,CAACP,GAAG;sBAACyG,SAAS,EAAC;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eAEN1G,OAAA;oBAAKkG,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBnG,OAAA;sBAAIkG,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,GAAC,WACrC,EAACoB,KAAK,GAAG,CAAC;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACL1G,OAAA;sBAAKkG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,eAC3CnG,OAAA;wBAAMkG,SAAS,EAAG,4CAChBhB,MAAM,CAACsC,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAArB,QAAA,EACAjB,MAAM,CAACsC,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN1G,OAAA;gBAAKkG,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBnG,OAAA;kBAAKkG,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBnG,OAAA;oBAAGkG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EACxDjB,MAAM,CAACI,YAAY,IAAIJ,MAAM,CAACK;kBAAY;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EAGL,CAACxB,MAAM,CAACwC,YAAY,KAAK,OAAO,IAAIxC,MAAM,CAACU,aAAa,IAAIV,MAAM,CAACW,KAAK,IAAIX,MAAM,CAACS,QAAQ,MAAMT,MAAM,CAACU,aAAa,IAAIV,MAAM,CAACW,KAAK,IAAIX,MAAM,CAACS,QAAQ,CAAC,iBACxJ3F,OAAA;kBAAKkG,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBnG,OAAA;oBAAKkG,SAAS,EAAG,2BACfhB,MAAM,CAACsC,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;oBAAArB,QAAA,gBACDnG,OAAA;sBAAKkG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CnG,OAAA;wBAAMkG,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/E1G,OAAA;wBAAMkG,SAAS,EAAG,4CAChBhB,MAAM,CAACsC,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAArB,QAAA,EACAjB,MAAM,CAACsC,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN1G,OAAA;sBAAKkG,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBAC7CnG,OAAA;wBACE2H,GAAG,EAAEzC,MAAM,CAACU,aAAa,IAAIV,MAAM,CAACW,KAAK,IAAIX,MAAM,CAACS,QAAS;wBAC7DiC,GAAG,EAAC,gBAAgB;wBACpB1B,SAAS,EAAC,sDAAsD;wBAChEG,KAAK,EAAE;0BAAEwB,SAAS,EAAE;wBAAQ,CAAE;wBAC9BC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAC4B,OAAO,GAAG,MAAM;0BAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAAC7B,KAAK,CAAC4B,OAAO,GAAG,OAAO;wBAC9C;sBAAE;wBAAA1B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF1G,OAAA;wBACEkG,SAAS,EAAC,8DAA8D;wBACxEG,KAAK,EAAE;0BAAE4B,OAAO,EAAE;wBAAO,CAAE;wBAAA9B,QAAA,EAC5B;sBAED;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGD1G,OAAA;kBAAKkG,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBnG,OAAA;oBAAKkG,SAAS,EAAG,2BACfhB,MAAM,CAACsC,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;oBAAArB,QAAA,gBACDnG,OAAA;sBAAKkG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CnG,OAAA;wBAAKkG,SAAS,EAAG,yDACfhB,MAAM,CAACsC,SAAS,GAAG,cAAc,GAAG,YACrC,EAAE;wBAAArB,QAAA,EACAjB,MAAM,CAACsC,SAAS,gBACfxH,OAAA,CAACR,OAAO;0BAAC0G,SAAS,EAAC;wBAAoB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAE1C1G,OAAA,CAACP,GAAG;0BAACyG,SAAS,EAAC;wBAAoB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBACtC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACN1G,OAAA;wBAAMkG,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAY;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACN1G,OAAA;sBAAKkG,SAAS,EAAG,oCACfhB,MAAM,CAACsC,SAAS,GACZ,qDAAqD,GACrD,+CACL,EAAE;sBAAArB,QAAA,EACAjB,MAAM,CAACQ,UAAU,IAAI;oBAAoB;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,CAACxB,MAAM,CAACsC,SAAS,iBAChBxH,OAAA;oBAAKkG,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,gBACnEnG,OAAA;sBAAKkG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CnG,OAAA;wBAAKkG,SAAS,EAAC,oEAAoE;wBAAAC,QAAA,eACjFnG,OAAA,CAACR,OAAO;0BAAC0G,SAAS,EAAC;wBAAoB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACN1G,OAAA;wBAAMkG,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAe;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACN1G,OAAA;sBAAKkG,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,EACpGjB,MAAM,CAACO;oBAAa;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA,CAACxB,MAAM,CAACsC,SAAS,iBAChBxH,OAAA;oBAAKkG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBnG,OAAA;sBACEkG,SAAS,EAAG,gHACVtE,mBAAmB,CAAE,YAAW2F,KAAM,EAAC,CAAC,GACpC,gCAAgC,GAChC,2HACL,EAAE;sBACHY,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACb,KAAK,EAAErC,MAAM,CAAE;sBACjDmD,QAAQ,EAAEzG,mBAAmB,CAAE,YAAW2F,KAAM,EAAC,CAAE;sBAAApB,QAAA,EAElDvE,mBAAmB,CAAE,YAAW2F,KAAM,EAAC,CAAC,gBACvCvH,OAAA,CAAAE,SAAA;wBAAAiG,QAAA,gBACEnG,OAAA;0BAAKkG,SAAS,EAAC;wBAA8E;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,6BAEtG;sBAAA,eAAE,CAAC,gBAEH1G,OAAA,CAAAE,SAAA;wBAAAiG,QAAA,gBACEnG,OAAA,CAACJ,OAAO;0BAACsG,SAAS,EAAC;wBAAS;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,sBAEjC;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC,EAGRhF,YAAY,CAAE,YAAW6F,KAAM,EAAC,CAAC,iBAChCvH,OAAA;sBAAKkG,SAAS,EAAC,yFAAyF;sBAAAC,QAAA,gBACtGnG,OAAA;wBAAKkG,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,gBAC3CnG,OAAA,CAACJ,OAAO;0BAACsG,SAAS,EAAC;wBAAuB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7C1G,OAAA;0BAAIkG,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,EAAC;wBAAkB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D,CAAC,eACN1G,OAAA;wBAAKkG,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAC/DzE,YAAY,CAAE,YAAW6F,KAAM,EAAC;sBAAC;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA9KDxB,MAAM,CAACoD,UAAU,IAAIf,KAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+K5B,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CACN,eAGD1G,OAAA;QAAKkG,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CnG,OAAA;UACEmI,OAAO,EAAGJ,CAAC,IAAK;YACdA,CAAC,CAACQ,cAAc,CAAC,CAAC;YAClB/E,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9CuC,mBAAmB,CAAC,CAAC;UACvB,CAAE;UACFE,SAAS,EAAC,+NAA+N;UACzOnD,IAAI,EAAC,QAAQ;UAAAoD,QAAA,gBAEbnG,OAAA,CAACN,MAAM;YAACwG,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET1G,OAAA;UACEmI,OAAO,EAAGJ,CAAC,IAAK;YACdA,CAAC,CAACQ,cAAc,CAAC,CAAC;YAClB/E,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7CwC,gBAAgB,CAAC,CAAC;UACpB,CAAE;UACFC,SAAS,EAAC,kOAAkO;UAC5OnD,IAAI,EAAC,QAAQ;UAAAoD,QAAA,gBAEbnG,OAAA,CAACT,QAAQ;YAAC2G,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtG,EAAA,CA5wBID,UAAU;EAAA,QACGhB,WAAW,EACXC,WAAW,EACbC,SAAS,EACPC,WAAW;AAAA;AAAAkJ,EAAA,GAJxBrI,UAAU;AA8wBhB,eAAeA,UAAU;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}