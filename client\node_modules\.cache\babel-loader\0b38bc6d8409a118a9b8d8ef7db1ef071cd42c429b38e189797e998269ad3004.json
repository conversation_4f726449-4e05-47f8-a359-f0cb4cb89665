{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { createPortal } from 'react-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n        const response = await getExamById({\n          examId: id\n        });\n        console.log('Quiz API response:', response);\n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    console.log('🚀 Submit button clicked - showing loading overlay');\n    console.log('Current submitting state:', submitting);\n    try {\n      // Show loading immediately\n      setSubmitting(true);\n      console.log('✅ setSubmitting(true) called');\n\n      // Longer delay to ensure loading overlay is visible and for marking simulation\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      console.log('📝 Starting quiz marking process...');\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        let isCorrect = false;\n        let actualCorrectAnswer = '';\n\n        // Determine the correct answer based on question type\n        const questionType = question.type || question.answerType || 'mcq';\n        if (questionType.toLowerCase() === 'mcq' || questionType === 'Options') {\n          // For MCQ questions, check both correctAnswer and correctOption\n          if (question.options && typeof question.options === 'object') {\n            // If correctAnswer is a key (like \"B\"), get the actual text\n            if (question.correctAnswer && question.options[question.correctAnswer]) {\n              actualCorrectAnswer = question.options[question.correctAnswer];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctOption is available, use it\n            else if (question.correctOption && question.options[question.correctOption]) {\n              actualCorrectAnswer = question.options[question.correctOption];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctAnswer is already the full text\n            else if (question.correctAnswer) {\n              actualCorrectAnswer = question.correctAnswer;\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n          } else {\n            // Fallback for other option formats\n            actualCorrectAnswer = question.correctAnswer || question.correctOption || '';\n            isCorrect = userAnswer === actualCorrectAnswer;\n          }\n        } else {\n          var _actualCorrectAnswer;\n          // For fill-in-the-blank and other types, direct comparison\n          actualCorrectAnswer = question.correctAnswer || '';\n          isCorrect = (userAnswer === null || userAnswer === void 0 ? void 0 : userAnswer.toLowerCase().trim()) === ((_actualCorrectAnswer = actualCorrectAnswer) === null || _actualCorrectAnswer === void 0 ? void 0 : _actualCorrectAnswer.toLowerCase().trim());\n        }\n        if (isCorrect) correctAnswers++;\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          questionText: question.name || `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: actualCorrectAnswer,\n          isCorrect,\n          questionType: questionType,\n          options: question.options || null\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n      // Use the exam's actual passing marks instead of hardcoded 60%\n      const passingPercentage = quiz.passingMarks || quiz.passingPercentage || 60;\n      const verdict = percentage >= passingPercentage ? 'Pass' : 'Fail';\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken,\n          // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n      try {\n        const response = await addReport(reportData);\n        if (response.success) {\n          console.log('✅ Quiz submitted successfully, preparing results...');\n\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null,\n            // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category,\n            passingPercentage: passingPercentage,\n            // Include actual passing marks\n            verdict: verdict // Include calculated verdict\n          };\n\n          // Show loading for at least 3 seconds to simulate marking process\n          console.log('⏳ Showing marking animation for 3 seconds...');\n          await new Promise(resolve => setTimeout(resolve, 3000));\n          console.log('🎯 Navigating to results page...');\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          // Show error in the loading overlay instead of notification\n          setTimeout(() => {\n            setSubmitting(false);\n            message.error(response.message || 'Failed to submit quiz');\n          }, 1000);\n          return;\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        // Show error in the loading overlay instead of notification\n        setTimeout(() => {\n          setSubmitting(false);\n          message.error('Network error while submitting quiz');\n        }, 1000);\n        return;\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      // Show error in the loading overlay instead of notification\n      setTimeout(() => {\n        setSubmitting(false);\n        message.error('Failed to submit quiz');\n      }, 1000);\n      return;\n    } finally {\n      setSubmitting(false);\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Don't auto-submit, just stop the timer\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-yellow-800 font-medium\",\n            children: \"No options found for this question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              className: \"text-sm text-yellow-600 cursor-pointer\",\n              children: \"Show question data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\",\n              children: JSON.stringify(currentQ, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: ['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index);\n            const isSelected = answers[currentQuestion] === option;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleAnswerSelect(option),\n              className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n                  children: optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-lg leading-relaxed flex-1 text-left text-gray-900\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: options.map((option, index) => {\n        const optionLetter = String.fromCharCode(65 + index);\n        const isSelected = answers[currentQuestion] === option;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleAnswerSelect(option),\n          className: `w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n              children: optionLetter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg leading-relaxed flex-1 text-left text-gray-900\",\n              children: typeof option === 'string' ? option : JSON.stringify(option)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-800 text-sm font-medium mb-2\",\n          children: \"Fill in the blank:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700\",\n          children: \"Type your answer in the box below\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: answers[currentQuestion] || '',\n          onChange: e => handleAnswerSelect(e.target.value),\n          placeholder: \"Type your answer here...\",\n          className: \"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\",\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 7\n    }, this);\n  }\n  if (!quiz || !questions.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"No Questions Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"This quiz doesn't have any questions yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Question Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Unable to load the current question.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold text-gray-900 mb-4\",\n            children: \"Invalid Question Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"The question data is corrupted or invalid.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => startTransition(() => navigate('/quiz')),\n            className: \"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Back to Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [submitting && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 w-full h-full bg-black bg-opacity-80 flex items-center justify-center\",\n      style: {\n        zIndex: 999999,\n        position: 'fixed !important'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-3xl p-12 max-w-sm mx-4 text-center shadow-2xl\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-24 w-24 border-6 border-gray-200 border-t-blue-600 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-blue-600 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-3xl font-bold text-gray-900 mb-4\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-4 h-4 bg-blue-600 rounded-full animate-bounce\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-4 h-4 bg-blue-600 rounded-full animate-bounce\",\n            style: {\n              animationDelay: '0.2s'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-4 h-4 bg-blue-600 rounded-full animate-bounce\",\n            style: {\n              animationDelay: '0.4s'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-6xl mx-auto px-4 py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => startTransition(() => navigate('/quiz')),\n                className: \"p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                  className: \"w-6 h-6 text-gray-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl font-bold text-gray-900\",\n                  children: quiz.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [\"Question \", currentQuestion + 1, \" of \", questions.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `flex items-center gap-2 px-4 py-2 rounded-lg ${timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}`,\n                children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: formatTime(timeLeft)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                style: {\n                  width: `${(currentQuestion + 1) / questions.length * 100}%`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-4\",\n              children: typeof currentQ.name === 'string' ? currentQ.name : 'Question'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 13\n            }, this), currentQ.image && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6 bg-gray-50 rounded-lg p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: currentQ.image,\n                alt: \"Question diagram\",\n                className: \"max-w-full h-auto rounded-lg shadow-lg mx-auto block\",\n                style: {\n                  maxHeight: '400px'\n                },\n                onError: e => {\n                  e.target.style.display = 'none';\n                  // Show fallback message\n                  const fallback = document.createElement('div');\n                  fallback.className = 'text-center py-8 text-gray-500';\n                  fallback.innerHTML = '<p>Could not load diagram</p>';\n                  e.target.parentNode.appendChild(fallback);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 mb-8\",\n            children: renderAnswerSection()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goToPrevious,\n              disabled: currentQuestion === 0,\n              className: `flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${currentQuestion === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 15\n              }, this), \"Previous\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 13\n            }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmitQuiz,\n              disabled: submitting,\n              className: `flex items-center gap-2 px-8 py-3 rounded-lg font-semibold transition-colors ${submitting ? 'bg-gray-400 text-gray-200 cursor-not-allowed' : 'bg-green-600 text-white hover:bg-green-700'}`,\n              children: submitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 21\n                }, this), \"Submitting...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 21\n                }, this), \"Submit Quiz\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 15\n            }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goToNext,\n              className: \"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\",\n              children: [\"Next\", /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(QuizPlay, \"lfHb+Do4bl/NRrmGyYYBDhlNoy0=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useParams", "useNavigate", "createPortal", "useSelector", "message", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizPlay", "_s", "id", "navigate", "user", "state", "loading", "setLoading", "submitting", "setSubmitting", "quiz", "setQuiz", "questions", "setQuestions", "currentQuestion", "setCurrentQuestion", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loadQuizData", "console", "log", "_id", "token", "localStorage", "getItem", "error", "response", "examId", "success", "data", "length", "Array", "fill", "duration", "Date", "handleSubmitQuiz", "Promise", "resolve", "setTimeout", "currentUser", "storedUser", "JSON", "parse", "endTime", "timeTaken", "Math", "floor", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "actualCorrectAnswer", "questionType", "type", "answerType", "toLowerCase", "options", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "_actualCorrectAnswer", "trim", "questionId", "questionName", "name", "questionText", "String", "percentage", "round", "passingPercentage", "passingMarks", "verdict", "reportData", "exam", "result", "wrongAnswers", "score", "timeSpent", "points", "totalQuestions", "navigationState", "xpData", "quizName", "quizSubject", "subject", "category", "apiError", "timer", "setInterval", "prev", "clearInterval", "handleAnswerSelect", "answer", "newAnswers", "goToNext", "goToPrevious", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "renderAnswerSection", "currentQ", "renderMultipleChoice", "renderFillInTheBlank", "renderImageQuestion", "isArray", "Object", "values", "option1", "option2", "option3", "option4", "filter", "Boolean", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stringify", "option", "optionLetter", "fromCharCode", "isSelected", "onClick", "value", "onChange", "e", "target", "placeholder", "autoFocus", "isLastQuestion", "style", "zIndex", "position", "animationDelay", "width", "image", "src", "alt", "maxHeight", "onError", "display", "fallback", "document", "createElement", "innerHTML", "parentNode", "append<PERSON><PERSON><PERSON>", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { createPortal } from 'react-dom';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { \n  Tb<PERSON>lock, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  \n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [quiz, setQuiz] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState([]);\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [startTime, setStartTime] = useState(null);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        console.log('Loading quiz with ID:', id);\n        \n        if (!user || !user._id) {\n          const token = localStorage.getItem('token');\n          if (!token) {\n            console.log('No token found, redirecting to login');\n            message.error('Please login to access quizzes');\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n\n        const response = await getExamById({ examId: id });\n        console.log('Quiz API response:', response);\n        \n        if (response.success) {\n          if (!response.data) {\n            message.error('Quiz data not found');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n          \n          if (!response.data.questions || response.data.questions.length === 0) {\n            message.error('This quiz has no questions available');\n            startTransition(() => {\n              navigate('/quiz');\n            });\n            return;\n          }\n\n          setQuiz(response.data);\n          setQuestions(response.data.questions);\n          setAnswers(new Array(response.data.questions.length).fill(''));\n          setTimeLeft(response.data.duration * 60);\n          setStartTime(new Date());\n          console.log('Quiz loaded successfully:', response.data);\n        } else {\n          console.error('Quiz API error:', response.message);\n          message.error(response.message || 'Failed to load quiz');\n          startTransition(() => {\n            navigate('/quiz');\n          });\n        }\n      } catch (error) {\n        console.error('Quiz loading error:', error);\n        message.error('Failed to load quiz. Please try again.');\n        startTransition(() => {\n          navigate('/quiz');\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id && user) {\n      loadQuizData();\n    }\n  }, [id, navigate, user]);\n\n  // Submit quiz function\n  const handleSubmitQuiz = useCallback(async () => {\n    console.log('🚀 Submit button clicked - showing loading overlay');\n    console.log('Current submitting state:', submitting);\n\n    try {\n      // Show loading immediately\n      setSubmitting(true);\n      console.log('✅ setSubmitting(true) called');\n\n      // Longer delay to ensure loading overlay is visible and for marking simulation\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      console.log('📝 Starting quiz marking process...');\n\n      let currentUser = user;\n      if (!currentUser || !currentUser._id) {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            currentUser = JSON.parse(storedUser);\n          } catch (error) {\n            console.error('Error parsing stored user data:', error);\n            startTransition(() => {\n              navigate('/login');\n            });\n            return;\n          }\n        }\n      }\n\n      if (!currentUser || !currentUser._id) {\n        message.error('User session expired. Please login again.');\n        startTransition(() => {\n          navigate('/login');\n        });\n        return;\n      }\n\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        let isCorrect = false;\n        let actualCorrectAnswer = '';\n\n        // Determine the correct answer based on question type\n        const questionType = question.type || question.answerType || 'mcq';\n\n        if (questionType.toLowerCase() === 'mcq' || questionType === 'Options') {\n          // For MCQ questions, check both correctAnswer and correctOption\n          if (question.options && typeof question.options === 'object') {\n            // If correctAnswer is a key (like \"B\"), get the actual text\n            if (question.correctAnswer && question.options[question.correctAnswer]) {\n              actualCorrectAnswer = question.options[question.correctAnswer];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctOption is available, use it\n            else if (question.correctOption && question.options[question.correctOption]) {\n              actualCorrectAnswer = question.options[question.correctOption];\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n            // If correctAnswer is already the full text\n            else if (question.correctAnswer) {\n              actualCorrectAnswer = question.correctAnswer;\n              isCorrect = userAnswer === actualCorrectAnswer;\n            }\n          } else {\n            // Fallback for other option formats\n            actualCorrectAnswer = question.correctAnswer || question.correctOption || '';\n            isCorrect = userAnswer === actualCorrectAnswer;\n          }\n        } else {\n          // For fill-in-the-blank and other types, direct comparison\n          actualCorrectAnswer = question.correctAnswer || '';\n          isCorrect = userAnswer?.toLowerCase().trim() === actualCorrectAnswer?.toLowerCase().trim();\n        }\n\n        if (isCorrect) correctAnswers++;\n\n        return {\n          questionId: question._id || `question_${index}`,\n          questionName: typeof question.name === 'string' ? question.name : `Question ${index + 1}`,\n          questionText: question.name || `Question ${index + 1}`,\n          userAnswer: typeof userAnswer === 'string' ? userAnswer : String(userAnswer || ''),\n          correctAnswer: actualCorrectAnswer,\n          isCorrect,\n          questionType: questionType,\n          options: question.options || null\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      // Use the exam's actual passing marks instead of hardcoded 60%\n      const passingPercentage = quiz.passingMarks || quiz.passingPercentage || 60;\n      const verdict = percentage >= passingPercentage ? 'Pass' : 'Fail';\n\n      const reportData = {\n        exam: id,\n        user: currentUser._id,\n        result: {\n          correctAnswers,\n          wrongAnswers: questions.length - correctAnswers,\n          percentage,\n          score: percentage,\n          verdict: verdict,\n          timeTaken,\n          timeSpent: timeTaken, // Add timeSpent for XP calculation\n          points: correctAnswers * 10,\n          totalQuestions: questions.length\n        }\n      };\n\n      try {\n        const response = await addReport(reportData);\n\n        if (response.success) {\n          console.log('✅ Quiz submitted successfully, preparing results...');\n\n          // Include XP data in navigation state\n          const navigationState = {\n            percentage,\n            correctAnswers,\n            totalQuestions: questions.length,\n            timeTaken,\n            resultDetails,\n            xpData: response.xpData || null, // Include XP data from server response\n            quizName: quiz.name,\n            quizSubject: quiz.subject || quiz.category,\n            passingPercentage: passingPercentage, // Include actual passing marks\n            verdict: verdict // Include calculated verdict\n          };\n\n          // Show loading for at least 3 seconds to simulate marking process\n          console.log('⏳ Showing marking animation for 3 seconds...');\n          await new Promise(resolve => setTimeout(resolve, 3000));\n\n          console.log('🎯 Navigating to results page...');\n          startTransition(() => {\n            navigate(`/quiz/${id}/result`, {\n              state: navigationState\n            });\n          });\n        } else {\n          console.error('❌ Quiz submission failed:', response.message);\n          // Show error in the loading overlay instead of notification\n          setTimeout(() => {\n            setSubmitting(false);\n            message.error(response.message || 'Failed to submit quiz');\n          }, 1000);\n          return;\n        }\n      } catch (apiError) {\n        console.error('❌ API Error during submission:', apiError);\n        // Show error in the loading overlay instead of notification\n        setTimeout(() => {\n          setSubmitting(false);\n          message.error('Network error while submitting quiz');\n        }, 1000);\n        return;\n      }\n    } catch (error) {\n      console.error('Quiz submission error:', error);\n      // Show error in the loading overlay instead of notification\n      setTimeout(() => {\n        setSubmitting(false);\n        message.error('Failed to submit quiz');\n      }, 1000);\n      return;\n    } finally {\n      setSubmitting(false);\n    }\n  }, [startTime, questions, answers, id, navigate, user]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      // Don't auto-submit, just stop the timer\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    const newAnswers = [...answers];\n    newAnswers[currentQuestion] = answer;\n    setAnswers(newAnswers);\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestion < questions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestion > 0) {\n      setCurrentQuestion(currentQuestion - 1);\n    }\n  };\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Render different answer sections based on question type\n  const renderAnswerSection = () => {\n    const questionType = currentQ.type || currentQ.answerType || 'mcq';\n\n\n\n    switch (questionType.toLowerCase()) {\n      case 'mcq':\n      case 'multiple-choice':\n      case 'multiplechoice':\n        return renderMultipleChoice();\n\n      case 'fill':\n      case 'fill-in-the-blank':\n      case 'fillblank':\n      case 'text':\n        return renderFillInTheBlank();\n\n      case 'image':\n      case 'diagram':\n        return renderImageQuestion();\n\n      default:\n        // Default to multiple choice if type is unclear\n        return renderMultipleChoice();\n    }\n  };\n\n  // Render multiple choice options\n  const renderMultipleChoice = () => {\n    let options = [];\n\n    // Handle different option formats\n    if (Array.isArray(currentQ.options)) {\n      options = currentQ.options;\n    } else if (currentQ.options && typeof currentQ.options === 'object') {\n      // Handle object format like {A: \"option1\", B: \"option2\"}\n      options = Object.values(currentQ.options);\n    } else if (currentQ.option1 && currentQ.option2) {\n      // Handle individual option properties\n      options = [currentQ.option1, currentQ.option2, currentQ.option3, currentQ.option4].filter(Boolean);\n    }\n\n    if (!options || options.length === 0) {\n      // Show debug info and fallback options for testing\n      return (\n        <div className=\"space-y-4\">\n          <div className=\"text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <p className=\"text-yellow-800 font-medium\">No options found for this question</p>\n            <details className=\"mt-2\">\n              <summary className=\"text-sm text-yellow-600 cursor-pointer\">Show question data</summary>\n              <pre className=\"text-xs text-left mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-32\">\n                {JSON.stringify(currentQ, null, 2)}\n              </pre>\n            </details>\n          </div>\n\n          {/* Fallback test options */}\n          <div className=\"space-y-3\">\n            {['Option A (Test)', 'Option B (Test)', 'Option C (Test)', 'Option D (Test)'].map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index);\n              const isSelected = answers[currentQuestion] === option;\n\n              return (\n                <button\n                  key={index}\n                  onClick={() => handleAnswerSelect(option)}\n                  className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                      : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n                  }`}\n                >\n                  <div className=\"flex items-start gap-4\">\n                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                      isSelected\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 text-gray-600'\n                    }`}>\n                      {optionLetter}\n                    </div>\n                    <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                      {option}\n                    </span>\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-3\">\n        {options.map((option, index) => {\n          const optionLetter = String.fromCharCode(65 + index);\n          const isSelected = answers[currentQuestion] === option;\n\n          return (\n            <button\n              key={index}\n              onClick={() => handleAnswerSelect(option)}\n              className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${\n                isSelected\n                  ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-md'\n                  : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\n              }`}\n            >\n              <div className=\"flex items-start gap-4\">\n                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 ${\n                  isSelected\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-100 text-gray-600'\n                }`}>\n                  {optionLetter}\n                </div>\n                <span className=\"text-lg leading-relaxed flex-1 text-left text-gray-900\">\n                  {typeof option === 'string' ? option : JSON.stringify(option)}\n                </span>\n              </div>\n            </button>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // Render fill in the blank input\n  const renderFillInTheBlank = () => {\n    return (\n      <div className=\"space-y-4\">\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <p className=\"text-blue-800 text-sm font-medium mb-2\">Fill in the blank:</p>\n          <p className=\"text-gray-700\">Type your answer in the box below</p>\n        </div>\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={answers[currentQuestion] || ''}\n            onChange={(e) => handleAnswerSelect(e.target.value)}\n            placeholder=\"Type your answer here...\"\n            className=\"w-full p-4 text-lg border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:outline-none transition-colors\"\n            autoFocus\n          />\n        </div>\n      </div>\n    );\n  };\n\n  // Render image/diagram question (could have options or be fill-in)\n  const renderImageQuestion = () => {\n    if (currentQ.options && Array.isArray(currentQ.options) && currentQ.options.length > 0) {\n      return renderMultipleChoice();\n    } else {\n      return renderFillInTheBlank();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!quiz || !questions.length) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">No Questions Available</h2>\n            <p className=\"text-gray-600 mb-6\">This quiz doesn't have any questions yet.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Safety check for current question\n  if (!questions[currentQuestion]) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Question Not Found</h2>\n            <p className=\"text-gray-600 mb-6\">Unable to load the current question.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQ = questions[currentQuestion];\n  const isLastQuestion = currentQuestion === questions.length - 1;\n\n  // Ensure currentQ is a valid object\n  if (!currentQ || typeof currentQ !== 'object') {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-md mx-4\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold text-gray-900 mb-4\">Invalid Question Data</h2>\n            <p className=\"text-gray-600 mb-6\">The question data is corrupted or invalid.</p>\n            <button\n              onClick={() => startTransition(() => navigate('/quiz'))}\n              className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Back to Quizzes\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n\n  return (\n    <>\n      {/* Loading Screen - Shows immediately after clicking Submit */}\n      {(submitting) && (\n        <div\n          className=\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-80 flex items-center justify-center\"\n          style={{ zIndex: 999999, position: 'fixed !important' }}\n        >\n          <div className=\"bg-white rounded-3xl p-12 max-w-sm mx-4 text-center shadow-2xl\">\n            {/* Large Loading Spinner */}\n            <div className=\"relative mb-8\">\n              <div className=\"animate-spin rounded-full h-24 w-24 border-6 border-gray-200 border-t-blue-600 mx-auto\"></div>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-full animate-pulse\"></div>\n              </div>\n            </div>\n\n            {/* Simple Loading Text */}\n            <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">Loading...</h3>\n\n            {/* Animated Dots */}\n            <div className=\"flex justify-center space-x-2\">\n              <div className=\"w-4 h-4 bg-blue-600 rounded-full animate-bounce\"></div>\n              <div className=\"w-4 h-4 bg-blue-600 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n              <div className=\"w-4 h-4 bg-blue-600 rounded-full animate-bounce\" style={{animationDelay: '0.4s'}}></div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 relative\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <button\n                onClick={() => startTransition(() => navigate('/quiz'))}\n                className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <TbArrowLeft className=\"w-6 h-6 text-gray-600\" />\n              </button>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">{quiz.name}</h1>\n                <p className=\"text-sm text-gray-600\">\n                  Question {currentQuestion + 1} of {questions.length}\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center gap-4\">\n              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${\n                timeLeft <= 300 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n              }`}>\n                <TbClock className=\"w-5 h-5\" />\n                <span className=\"font-semibold\">{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n          </div>\n          \n          {/* Progress bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div \n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto p-6\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 p-8 transition-all duration-300\">\n          {/* Question */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {typeof currentQ.name === 'string' ? currentQ.name : 'Question'}\n            </h2>\n            \n            {currentQ.image && (\n              <div className=\"mb-6 bg-gray-50 rounded-lg p-4\">\n                <img\n                  src={currentQ.image}\n                  alt=\"Question diagram\"\n                  className=\"max-w-full h-auto rounded-lg shadow-lg mx-auto block\"\n                  style={{ maxHeight: '400px' }}\n                  onError={(e) => {\n                    e.target.style.display = 'none';\n                    // Show fallback message\n                    const fallback = document.createElement('div');\n                    fallback.className = 'text-center py-8 text-gray-500';\n                    fallback.innerHTML = '<p>Could not load diagram</p>';\n                    e.target.parentNode.appendChild(fallback);\n                  }}\n                />\n              </div>\n            )}\n          </div>\n\n          {/* Answer Section - Different types based on question type */}\n          <div className=\"space-y-4 mb-8\">\n            {renderAnswerSection()}\n          </div>\n\n          {/* Navigation */}\n          <div className=\"flex justify-between items-center\">\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestion === 0}\n              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-colors ${\n                currentQuestion === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5\" />\n              Previous\n            </button>\n\n            {isLastQuestion ? (\n              <button\n                onClick={handleSubmitQuiz}\n                disabled={submitting}\n                className={`flex items-center gap-2 px-8 py-3 rounded-lg font-semibold transition-colors ${\n                  submitting\n                    ? 'bg-gray-400 text-gray-200 cursor-not-allowed'\n                    : 'bg-green-600 text-white hover:bg-green-700'\n                }`}\n              >\n                {submitting ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent\"></div>\n                    Submitting...\n                  </>\n                ) : (\n                  <>\n                    <TbCheck className=\"w-5 h-5\" />\n                    Submit Quiz\n                  </>\n                )}\n              </button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n              >\n                Next\n                <TbArrowRight className=\"w-5 h-5\" />\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAC1B,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB;EAAK,CAAC,GAAGhB,WAAW,CAAEiB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,IAAI,EAAEC,OAAO,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFf,UAAU,CAAC,IAAI,CAAC;QAChBgB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEtB,EAAE,CAAC;QAExC,IAAI,CAACE,IAAI,IAAI,CAACA,IAAI,CAACqB,GAAG,EAAE;UACtB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAI,CAACF,KAAK,EAAE;YACVH,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnDnC,OAAO,CAACwC,KAAK,CAAC,gCAAgC,CAAC;YAC/C7C,eAAe,CAAC,MAAM;cACpBmB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;QAEA,MAAM2B,QAAQ,GAAG,MAAMpC,WAAW,CAAC;UAAEqC,MAAM,EAAE7B;QAAG,CAAC,CAAC;QAClDqB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;YAClB5C,OAAO,CAACwC,KAAK,CAAC,qBAAqB,CAAC;YACpC7C,eAAe,CAAC,MAAM;cACpBmB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEA,IAAI,CAAC2B,QAAQ,CAACG,IAAI,CAACrB,SAAS,IAAIkB,QAAQ,CAACG,IAAI,CAACrB,SAAS,CAACsB,MAAM,KAAK,CAAC,EAAE;YACpE7C,OAAO,CAACwC,KAAK,CAAC,sCAAsC,CAAC;YACrD7C,eAAe,CAAC,MAAM;cACpBmB,QAAQ,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC;YACF;UACF;UAEAQ,OAAO,CAACmB,QAAQ,CAACG,IAAI,CAAC;UACtBpB,YAAY,CAACiB,QAAQ,CAACG,IAAI,CAACrB,SAAS,CAAC;UACrCK,UAAU,CAAC,IAAIkB,KAAK,CAACL,QAAQ,CAACG,IAAI,CAACrB,SAAS,CAACsB,MAAM,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;UAC9DjB,WAAW,CAACW,QAAQ,CAACG,IAAI,CAACI,QAAQ,GAAG,EAAE,CAAC;UACxChB,YAAY,CAAC,IAAIiB,IAAI,CAAC,CAAC,CAAC;UACxBf,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEM,QAAQ,CAACG,IAAI,CAAC;QACzD,CAAC,MAAM;UACLV,OAAO,CAACM,KAAK,CAAC,iBAAiB,EAAEC,QAAQ,CAACzC,OAAO,CAAC;UAClDA,OAAO,CAACwC,KAAK,CAACC,QAAQ,CAACzC,OAAO,IAAI,qBAAqB,CAAC;UACxDL,eAAe,CAAC,MAAM;YACpBmB,QAAQ,CAAC,OAAO,CAAC;UACnB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CxC,OAAO,CAACwC,KAAK,CAAC,wCAAwC,CAAC;QACvD7C,eAAe,CAAC,MAAM;UACpBmB,QAAQ,CAAC,OAAO,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,SAAS;QACRI,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIL,EAAE,IAAIE,IAAI,EAAE;MACdkB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACpB,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAExB;EACA,MAAMmC,gBAAgB,GAAGxD,WAAW,CAAC,YAAY;IAC/CwC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACjED,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhB,UAAU,CAAC;IAEpD,IAAI;MACF;MACAC,aAAa,CAAC,IAAI,CAAC;MACnBc,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;;MAE3C;MACA,MAAM,IAAIgB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvDlB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAImB,WAAW,GAAGvC,IAAI;MACtB,IAAI,CAACuC,WAAW,IAAI,CAACA,WAAW,CAAClB,GAAG,EAAE;QACpC,MAAMmB,UAAU,GAAGjB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIgB,UAAU,EAAE;UACd,IAAI;YACFD,WAAW,GAAGE,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC;UACtC,CAAC,CAAC,OAAOf,KAAK,EAAE;YACdN,OAAO,CAACM,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvD7C,eAAe,CAAC,MAAM;cACpBmB,QAAQ,CAAC,QAAQ,CAAC;YACpB,CAAC,CAAC;YACF;UACF;QACF;MACF;MAEA,IAAI,CAACwC,WAAW,IAAI,CAACA,WAAW,CAAClB,GAAG,EAAE;QACpCpC,OAAO,CAACwC,KAAK,CAAC,2CAA2C,CAAC;QAC1D7C,eAAe,CAAC,MAAM;UACpBmB,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,CAAC;QACF;MACF;MAEA,MAAM4C,OAAO,GAAG,IAAIT,IAAI,CAAC,CAAC;MAC1B,MAAMU,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,OAAO,GAAG3B,SAAS,IAAI,IAAI,CAAC;MAE1D,IAAI+B,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAGxC,SAAS,CAACyC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAGxC,OAAO,CAACuC,KAAK,CAAC;QACjC,IAAIE,SAAS,GAAG,KAAK;QACrB,IAAIC,mBAAmB,GAAG,EAAE;;QAE5B;QACA,MAAMC,YAAY,GAAGL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACO,UAAU,IAAI,KAAK;QAElE,IAAIF,YAAY,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,IAAIH,YAAY,KAAK,SAAS,EAAE;UACtE;UACA,IAAIL,QAAQ,CAACS,OAAO,IAAI,OAAOT,QAAQ,CAACS,OAAO,KAAK,QAAQ,EAAE;YAC5D;YACA,IAAIT,QAAQ,CAACU,aAAa,IAAIV,QAAQ,CAACS,OAAO,CAACT,QAAQ,CAACU,aAAa,CAAC,EAAE;cACtEN,mBAAmB,GAAGJ,QAAQ,CAACS,OAAO,CAACT,QAAQ,CAACU,aAAa,CAAC;cAC9DP,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;YAChD;YACA;YAAA,KACK,IAAIJ,QAAQ,CAACW,aAAa,IAAIX,QAAQ,CAACS,OAAO,CAACT,QAAQ,CAACW,aAAa,CAAC,EAAE;cAC3EP,mBAAmB,GAAGJ,QAAQ,CAACS,OAAO,CAACT,QAAQ,CAACW,aAAa,CAAC;cAC9DR,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;YAChD;YACA;YAAA,KACK,IAAIJ,QAAQ,CAACU,aAAa,EAAE;cAC/BN,mBAAmB,GAAGJ,QAAQ,CAACU,aAAa;cAC5CP,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;YAChD;UACF,CAAC,MAAM;YACL;YACAA,mBAAmB,GAAGJ,QAAQ,CAACU,aAAa,IAAIV,QAAQ,CAACW,aAAa,IAAI,EAAE;YAC5ER,SAAS,GAAGD,UAAU,KAAKE,mBAAmB;UAChD;QACF,CAAC,MAAM;UAAA,IAAAQ,oBAAA;UACL;UACAR,mBAAmB,GAAGJ,QAAQ,CAACU,aAAa,IAAI,EAAE;UAClDP,SAAS,GAAG,CAAAD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEM,WAAW,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,QAAAD,oBAAA,GAAKR,mBAAmB,cAAAQ,oBAAA,uBAAnBA,oBAAA,CAAqBJ,WAAW,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;QAC5F;QAEA,IAAIV,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLiB,UAAU,EAAEd,QAAQ,CAAC7B,GAAG,IAAK,YAAW8B,KAAM,EAAC;UAC/Cc,YAAY,EAAE,OAAOf,QAAQ,CAACgB,IAAI,KAAK,QAAQ,GAAGhB,QAAQ,CAACgB,IAAI,GAAI,YAAWf,KAAK,GAAG,CAAE,EAAC;UACzFgB,YAAY,EAAEjB,QAAQ,CAACgB,IAAI,IAAK,YAAWf,KAAK,GAAG,CAAE,EAAC;UACtDC,UAAU,EAAE,OAAOA,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGgB,MAAM,CAAChB,UAAU,IAAI,EAAE,CAAC;UAClFQ,aAAa,EAAEN,mBAAmB;UAClCD,SAAS;UACTE,YAAY,EAAEA,YAAY;UAC1BI,OAAO,EAAET,QAAQ,CAACS,OAAO,IAAI;QAC/B,CAAC;MACH,CAAC,CAAC;MAEF,MAAMU,UAAU,GAAGxB,IAAI,CAACyB,KAAK,CAAEvB,cAAc,GAAGvC,SAAS,CAACsB,MAAM,GAAI,GAAG,CAAC;MACxE;MACA,MAAMyC,iBAAiB,GAAGjE,IAAI,CAACkE,YAAY,IAAIlE,IAAI,CAACiE,iBAAiB,IAAI,EAAE;MAC3E,MAAME,OAAO,GAAGJ,UAAU,IAAIE,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEjE,MAAMG,UAAU,GAAG;QACjBC,IAAI,EAAE7E,EAAE;QACRE,IAAI,EAAEuC,WAAW,CAAClB,GAAG;QACrBuD,MAAM,EAAE;UACN7B,cAAc;UACd8B,YAAY,EAAErE,SAAS,CAACsB,MAAM,GAAGiB,cAAc;UAC/CsB,UAAU;UACVS,KAAK,EAAET,UAAU;UACjBI,OAAO,EAAEA,OAAO;UAChB7B,SAAS;UACTmC,SAAS,EAAEnC,SAAS;UAAE;UACtBoC,MAAM,EAAEjC,cAAc,GAAG,EAAE;UAC3BkC,cAAc,EAAEzE,SAAS,CAACsB;QAC5B;MACF,CAAC;MAED,IAAI;QACF,MAAMJ,QAAQ,GAAG,MAAMnC,SAAS,CAACmF,UAAU,CAAC;QAE5C,IAAIhD,QAAQ,CAACE,OAAO,EAAE;UACpBT,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;UAElE;UACA,MAAM8D,eAAe,GAAG;YACtBb,UAAU;YACVtB,cAAc;YACdkC,cAAc,EAAEzE,SAAS,CAACsB,MAAM;YAChCc,SAAS;YACTI,aAAa;YACbmC,MAAM,EAAEzD,QAAQ,CAACyD,MAAM,IAAI,IAAI;YAAE;YACjCC,QAAQ,EAAE9E,IAAI,CAAC4D,IAAI;YACnBmB,WAAW,EAAE/E,IAAI,CAACgF,OAAO,IAAIhF,IAAI,CAACiF,QAAQ;YAC1ChB,iBAAiB,EAAEA,iBAAiB;YAAE;YACtCE,OAAO,EAAEA,OAAO,CAAC;UACnB,CAAC;;UAED;UACAtD,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D,MAAM,IAAIgB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvDlB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CxC,eAAe,CAAC,MAAM;YACpBmB,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;cAC7BG,KAAK,EAAEiF;YACT,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACL/D,OAAO,CAACM,KAAK,CAAC,2BAA2B,EAAEC,QAAQ,CAACzC,OAAO,CAAC;UAC5D;UACAqD,UAAU,CAAC,MAAM;YACfjC,aAAa,CAAC,KAAK,CAAC;YACpBpB,OAAO,CAACwC,KAAK,CAACC,QAAQ,CAACzC,OAAO,IAAI,uBAAuB,CAAC;UAC5D,CAAC,EAAE,IAAI,CAAC;UACR;QACF;MACF,CAAC,CAAC,OAAOuG,QAAQ,EAAE;QACjBrE,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAE+D,QAAQ,CAAC;QACzD;QACAlD,UAAU,CAAC,MAAM;UACfjC,aAAa,CAAC,KAAK,CAAC;UACpBpB,OAAO,CAACwC,KAAK,CAAC,qCAAqC,CAAC;QACtD,CAAC,EAAE,IAAI,CAAC;QACR;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACAa,UAAU,CAAC,MAAM;QACfjC,aAAa,CAAC,KAAK,CAAC;QACpBpB,OAAO,CAACwC,KAAK,CAAC,uBAAuB,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;MACR;IACF,CAAC,SAAS;MACRpB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACW,SAAS,EAAER,SAAS,EAAEI,OAAO,EAAEd,EAAE,EAAEC,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAEvD;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIoC,QAAQ,IAAI,CAAC,EAAE;MACjB;MACA;IACF;IAEA,MAAM2E,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B3E,WAAW,CAAC4E,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAAC3E,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM+E,kBAAkB,GAAIC,MAAM,IAAK;IACrC,MAAMC,UAAU,GAAG,CAAC,GAAGnF,OAAO,CAAC;IAC/BmF,UAAU,CAACrF,eAAe,CAAC,GAAGoF,MAAM;IACpCjF,UAAU,CAACkF,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAItF,eAAe,GAAGF,SAAS,CAACsB,MAAM,GAAG,CAAC,EAAE;MAC1CnB,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;EAED,MAAMuF,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvF,eAAe,GAAG,CAAC,EAAE;MACvBC,kBAAkB,CAACD,eAAe,GAAG,CAAC,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMwF,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGvD,IAAI,CAACC,KAAK,CAACqD,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMjD,YAAY,GAAGkD,QAAQ,CAACjD,IAAI,IAAIiD,QAAQ,CAAChD,UAAU,IAAI,KAAK;IAIlE,QAAQF,YAAY,CAACG,WAAW,CAAC,CAAC;MAChC,KAAK,KAAK;MACV,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;QACnB,OAAOgD,oBAAoB,CAAC,CAAC;MAE/B,KAAK,MAAM;MACX,KAAK,mBAAmB;MACxB,KAAK,WAAW;MAChB,KAAK,MAAM;QACT,OAAOC,oBAAoB,CAAC,CAAC;MAE/B,KAAK,OAAO;MACZ,KAAK,SAAS;QACZ,OAAOC,mBAAmB,CAAC,CAAC;MAE9B;QACE;QACA,OAAOF,oBAAoB,CAAC,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMA,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI/C,OAAO,GAAG,EAAE;;IAEhB;IACA,IAAI5B,KAAK,CAAC8E,OAAO,CAACJ,QAAQ,CAAC9C,OAAO,CAAC,EAAE;MACnCA,OAAO,GAAG8C,QAAQ,CAAC9C,OAAO;IAC5B,CAAC,MAAM,IAAI8C,QAAQ,CAAC9C,OAAO,IAAI,OAAO8C,QAAQ,CAAC9C,OAAO,KAAK,QAAQ,EAAE;MACnE;MACAA,OAAO,GAAGmD,MAAM,CAACC,MAAM,CAACN,QAAQ,CAAC9C,OAAO,CAAC;IAC3C,CAAC,MAAM,IAAI8C,QAAQ,CAACO,OAAO,IAAIP,QAAQ,CAACQ,OAAO,EAAE;MAC/C;MACAtD,OAAO,GAAG,CAAC8C,QAAQ,CAACO,OAAO,EAAEP,QAAQ,CAACQ,OAAO,EAAER,QAAQ,CAACS,OAAO,EAAET,QAAQ,CAACU,OAAO,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IACpG;IAEA,IAAI,CAAC1D,OAAO,IAAIA,OAAO,CAAC7B,MAAM,KAAK,CAAC,EAAE;MACpC;MACA,oBACErC,OAAA;QAAK6H,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9H,OAAA;UAAK6H,SAAS,EAAC,mEAAmE;UAAAC,QAAA,gBAChF9H,OAAA;YAAG6H,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjFlI,OAAA;YAAS6H,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACvB9H,OAAA;cAAS6H,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACxFlI,OAAA;cAAK6H,SAAS,EAAC,yEAAyE;cAAAC,QAAA,EACrF9E,IAAI,CAACmF,SAAS,CAACnB,QAAQ,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGNlI,OAAA;UAAK6H,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAACtE,GAAG,CAAC,CAAC4E,MAAM,EAAE1E,KAAK,KAAK;YACnG,MAAM2E,YAAY,GAAG1D,MAAM,CAAC2D,YAAY,CAAC,EAAE,GAAG5E,KAAK,CAAC;YACpD,MAAM6E,UAAU,GAAGpH,OAAO,CAACF,eAAe,CAAC,KAAKmH,MAAM;YAEtD,oBACEpI,OAAA;cAEEwI,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAACgC,MAAM,CAAE;cAC1CP,SAAS,EAAG,wFACVU,UAAU,GACN,oDAAoD,GACpD,iEACL,EAAE;cAAAT,QAAA,eAEH9H,OAAA;gBAAK6H,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC9H,OAAA;kBAAK6H,SAAS,EAAG,2FACfU,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;kBAAAT,QAAA,EACAO;gBAAY;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNlI,OAAA;kBAAM6H,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACrEM;gBAAM;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GAnBDxE,KAAK;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBJ,CAAC;UAEb,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,oBACElI,OAAA;MAAK6H,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvB5D,OAAO,CAACV,GAAG,CAAC,CAAC4E,MAAM,EAAE1E,KAAK,KAAK;QAC9B,MAAM2E,YAAY,GAAG1D,MAAM,CAAC2D,YAAY,CAAC,EAAE,GAAG5E,KAAK,CAAC;QACpD,MAAM6E,UAAU,GAAGpH,OAAO,CAACF,eAAe,CAAC,KAAKmH,MAAM;QAEtD,oBACEpI,OAAA;UAEEwI,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAACgC,MAAM,CAAE;UAC1CP,SAAS,EAAG,wFACVU,UAAU,GACN,oDAAoD,GACpD,iEACL,EAAE;UAAAT,QAAA,eAEH9H,OAAA;YAAK6H,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC9H,OAAA;cAAK6H,SAAS,EAAG,2FACfU,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;cAAAT,QAAA,EACAO;YAAY;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlI,OAAA;cAAM6H,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EACrE,OAAOM,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGpF,IAAI,CAACmF,SAAS,CAACC,MAAM;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC,GAnBDxE,KAAK;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBJ,CAAC;MAEb,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMhB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,oBACElH,OAAA;MAAK6H,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB9H,OAAA;QAAK6H,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/D9H,OAAA;UAAG6H,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5ElI,OAAA;UAAG6H,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACNlI,OAAA;QAAK6H,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvB9H,OAAA;UACE+D,IAAI,EAAC,MAAM;UACX0E,KAAK,EAAEtH,OAAO,CAACF,eAAe,CAAC,IAAI,EAAG;UACtCyH,QAAQ,EAAGC,CAAC,IAAKvC,kBAAkB,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDI,WAAW,EAAC,0BAA0B;UACtChB,SAAS,EAAC,mHAAmH;UAC7HiB,SAAS;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMf,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIH,QAAQ,CAAC9C,OAAO,IAAI5B,KAAK,CAAC8E,OAAO,CAACJ,QAAQ,CAAC9C,OAAO,CAAC,IAAI8C,QAAQ,CAAC9C,OAAO,CAAC7B,MAAM,GAAG,CAAC,EAAE;MACtF,OAAO4E,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL,OAAOC,oBAAoB,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,IAAIzG,OAAO,EAAE;IACX,oBACET,OAAA;MAAK6H,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG9H,OAAA;QAAK6H,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9H,OAAA;UAAK6H,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGlI,OAAA;UAAG6H,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACrH,IAAI,IAAI,CAACE,SAAS,CAACsB,MAAM,EAAE;IAC9B,oBACErC,OAAA;MAAK6H,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG9H,OAAA;QAAK6H,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtF9H,OAAA;UAAK6H,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9H,OAAA;YAAI6H,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFlI,OAAA;YAAG6H,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/ElI,OAAA;YACEwI,OAAO,EAAEA,CAAA,KAAMrJ,eAAe,CAAC,MAAMmB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxDuH,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACnH,SAAS,CAACE,eAAe,CAAC,EAAE;IAC/B,oBACEjB,OAAA;MAAK6H,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG9H,OAAA;QAAK6H,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtF9H,OAAA;UAAK6H,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9H,OAAA;YAAI6H,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5ElI,OAAA;YAAG6H,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1ElI,OAAA;YACEwI,OAAO,EAAEA,CAAA,KAAMrJ,eAAe,CAAC,MAAMmB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxDuH,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMlB,QAAQ,GAAGjG,SAAS,CAACE,eAAe,CAAC;EAC3C,MAAM8H,cAAc,GAAG9H,eAAe,KAAKF,SAAS,CAACsB,MAAM,GAAG,CAAC;;EAE/D;EACA,IAAI,CAAC2E,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC7C,oBACEhH,OAAA;MAAK6H,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG9H,OAAA;QAAK6H,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eACtF9H,OAAA;UAAK6H,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9H,OAAA;YAAI6H,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/ElI,OAAA;YAAG6H,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA0C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFlI,OAAA;YACEwI,OAAO,EAAEA,CAAA,KAAMrJ,eAAe,CAAC,MAAMmB,QAAQ,CAAC,OAAO,CAAC,CAAE;YACxDuH,SAAS,EAAC,sFAAsF;YAAAC,QAAA,EACjG;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAIA,oBACElI,OAAA,CAAAE,SAAA;IAAA4H,QAAA,GAEInH,UAAU,iBACVX,OAAA;MACE6H,SAAS,EAAC,0FAA0F;MACpGmB,KAAK,EAAE;QAAEC,MAAM,EAAE,MAAM;QAAEC,QAAQ,EAAE;MAAmB,CAAE;MAAApB,QAAA,eAExD9H,OAAA;QAAK6H,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAE7E9H,OAAA;UAAK6H,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9H,OAAA;YAAK6H,SAAS,EAAC;UAAwF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9GlI,OAAA;YAAK6H,SAAS,EAAC,mDAAmD;YAAAC,QAAA,eAChE9H,OAAA;cAAK6H,SAAS,EAAC;YAAgD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlI,OAAA;UAAI6H,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGrElI,OAAA;UAAK6H,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5C9H,OAAA;YAAK6H,SAAS,EAAC;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvElI,OAAA;YAAK6H,SAAS,EAAC,iDAAiD;YAACmB,KAAK,EAAE;cAACG,cAAc,EAAE;YAAM;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxGlI,OAAA;YAAK6H,SAAS,EAAC,iDAAiD;YAACmB,KAAK,EAAE;cAACG,cAAc,EAAE;YAAM;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDlI,OAAA;MAAK6H,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBAEnF9H,OAAA;QAAK6H,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1D9H,OAAA;UAAK6H,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C9H,OAAA;YAAK6H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9H,OAAA;cAAK6H,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC9H,OAAA;gBACEwI,OAAO,EAAEA,CAAA,KAAMrJ,eAAe,CAAC,MAAMmB,QAAQ,CAAC,OAAO,CAAC,CAAE;gBACxDuH,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,eAE9D9H,OAAA,CAACN,WAAW;kBAACmI,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACTlI,OAAA;gBAAA8H,QAAA,gBACE9H,OAAA;kBAAI6H,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAEjH,IAAI,CAAC4D;gBAAI;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChElI,OAAA;kBAAG6H,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,WAC1B,EAAC7G,eAAe,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACsB,MAAM;gBAAA;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlI,OAAA;cAAK6H,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACtC9H,OAAA;gBAAK6H,SAAS,EAAG,gDACfxG,QAAQ,IAAI,GAAG,GAAG,yBAAyB,GAAG,2BAC/C,EAAE;gBAAAyG,QAAA,gBACD9H,OAAA,CAACP,OAAO;kBAACoI,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/BlI,OAAA;kBAAM6H,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAErB,UAAU,CAACpF,QAAQ;gBAAC;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlI,OAAA;YAAK6H,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB9H,OAAA;cAAK6H,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClD9H,OAAA;gBACE6H,SAAS,EAAC,0DAA0D;gBACpEmB,KAAK,EAAE;kBAAEI,KAAK,EAAG,GAAG,CAACnI,eAAe,GAAG,CAAC,IAAIF,SAAS,CAACsB,MAAM,GAAI,GAAI;gBAAG;cAAE;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlI,OAAA;QAAK6H,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC9H,OAAA;UAAK6H,SAAS,EAAC,uFAAuF;UAAAC,QAAA,gBAEpG9H,OAAA;YAAK6H,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB9H,OAAA;cAAI6H,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAClD,OAAOd,QAAQ,CAACvC,IAAI,KAAK,QAAQ,GAAGuC,QAAQ,CAACvC,IAAI,GAAG;YAAU;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,EAEJlB,QAAQ,CAACqC,KAAK,iBACbrJ,OAAA;cAAK6H,SAAS,EAAC,gCAAgC;cAAAC,QAAA,eAC7C9H,OAAA;gBACEsJ,GAAG,EAAEtC,QAAQ,CAACqC,KAAM;gBACpBE,GAAG,EAAC,kBAAkB;gBACtB1B,SAAS,EAAC,sDAAsD;gBAChEmB,KAAK,EAAE;kBAAEQ,SAAS,EAAE;gBAAQ,CAAE;gBAC9BC,OAAO,EAAGd,CAAC,IAAK;kBACdA,CAAC,CAACC,MAAM,CAACI,KAAK,CAACU,OAAO,GAAG,MAAM;kBAC/B;kBACA,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;kBAC9CF,QAAQ,CAAC9B,SAAS,GAAG,gCAAgC;kBACrD8B,QAAQ,CAACG,SAAS,GAAG,+BAA+B;kBACpDnB,CAAC,CAACC,MAAM,CAACmB,UAAU,CAACC,WAAW,CAACL,QAAQ,CAAC;gBAC3C;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlI,OAAA;YAAK6H,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5Bf,mBAAmB,CAAC;UAAC;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAGNlI,OAAA;YAAK6H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9H,OAAA;cACEwI,OAAO,EAAEhC,YAAa;cACtByD,QAAQ,EAAEhJ,eAAe,KAAK,CAAE;cAChC4G,SAAS,EAAG,gFACV5G,eAAe,KAAK,CAAC,GACjB,8CAA8C,GAC9C,6CACL,EAAE;cAAA6G,QAAA,gBAEH9H,OAAA,CAACN,WAAW;gBAACmI,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAERa,cAAc,gBACb/I,OAAA;cACEwI,OAAO,EAAE9F,gBAAiB;cAC1BuH,QAAQ,EAAEtJ,UAAW;cACrBkH,SAAS,EAAG,gFACVlH,UAAU,GACN,8CAA8C,GAC9C,4CACL,EAAE;cAAAmH,QAAA,EAEFnH,UAAU,gBACTX,OAAA,CAAAE,SAAA;gBAAA4H,QAAA,gBACE9H,OAAA;kBAAK6H,SAAS,EAAC;gBAA8E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,iBAEtG;cAAA,eAAE,CAAC,gBAEHlI,OAAA,CAAAE,SAAA;gBAAA4H,QAAA,gBACE9H,OAAA,CAACJ,OAAO;kBAACiI,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEjC;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,gBAETlI,OAAA;cACEwI,OAAO,EAAEjC,QAAS;cAClBsB,SAAS,EAAC,uHAAuH;cAAAC,QAAA,GAClI,MAEC,eAAA9H,OAAA,CAACL,YAAY;gBAACkI,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC9H,EAAA,CAlrBID,QAAQ;EAAA,QACGf,SAAS,EACPC,WAAW,EACXE,WAAW;AAAA;AAAA2K,EAAA,GAHxB/J,QAAQ;AAorBd,eAAeA,QAAQ;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}