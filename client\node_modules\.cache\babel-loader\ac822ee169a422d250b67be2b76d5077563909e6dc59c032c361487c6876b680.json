{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\OnlineStatusIndicator.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { getUserOnlineStatus } from '../../apicalls/notifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OnlineStatusIndicator = ({\n  userId,\n  size = 'sm',\n  showLabel = false,\n  className = '',\n  refreshInterval = 30000 // 30 seconds\n}) => {\n  _s();\n  const [isOnline, setIsOnline] = useState(false);\n  const [lastSeen, setLastSeen] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    if (!userId) return;\n    const checkStatus = async () => {\n      try {\n        const response = await getUserOnlineStatus(userId);\n        console.log('Online status response:', response); // Debug log\n        if (response.success) {\n          setIsOnline(response.data.isOnline);\n          setLastSeen(response.data.lastSeen);\n          console.log('User online status:', response.data.isOnline); // Debug log\n        }\n      } catch (error) {\n        console.error('Error checking online status:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Initial check\n    checkStatus();\n\n    // Set up interval for periodic checks\n    const interval = setInterval(checkStatus, refreshInterval);\n    return () => clearInterval(interval);\n  }, [userId, refreshInterval]);\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'xs':\n        return 'w-2 h-2';\n      case 'sm':\n        return 'w-4 h-4';\n      // Made slightly larger for better visibility\n      case 'md':\n        return 'w-5 h-5';\n      case 'lg':\n        return 'w-6 h-6';\n      default:\n        return 'w-4 h-4';\n      // Made slightly larger for better visibility\n    }\n  };\n\n  const getLastSeenText = () => {\n    if (!lastSeen) return '';\n    const now = new Date();\n    const lastSeenDate = new Date(lastSeen);\n    const diffInMinutes = Math.floor((now - lastSeenDate) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    return lastSeenDate.toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative inline-flex items-center ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${getSizeClasses()} rounded-full border-2 border-white shadow-lg`,\n      style: {\n        backgroundColor: loading ? '#60a5fa' : isOnline ? '#10b981' : '#9ca3af',\n        boxShadow: '0 2px 8px rgba(0,0,0,0.2)',\n        zIndex: 10,\n        animation: loading || isOnline ? 'pulse 2s infinite' : 'none'\n      },\n      title: loading ? 'Loading...' : isOnline ? 'Online' : `Last seen ${getLastSeenText()}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), showLabel && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `ml-2 text-sm ${loading ? 'text-blue-500' : isOnline ? 'text-green-600' : 'text-gray-500'}`,\n      children: loading ? 'Loading...' : isOnline ? 'Online' : getLastSeenText()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(OnlineStatusIndicator, \"CquBWosDigGOd6dJmOzwWtGwW0M=\");\n_c = OnlineStatusIndicator;\nexport default OnlineStatusIndicator;\nvar _c;\n$RefreshReg$(_c, \"OnlineStatusIndicator\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getUserOnlineStatus", "jsxDEV", "_jsxDEV", "OnlineStatusIndicator", "userId", "size", "showLabel", "className", "refreshInterval", "_s", "isOnline", "setIsOnline", "lastSeen", "setLastSeen", "loading", "setLoading", "checkStatus", "response", "console", "log", "success", "data", "error", "interval", "setInterval", "clearInterval", "getSizeClasses", "getLastSeenText", "now", "Date", "lastSeenDate", "diffInMinutes", "Math", "floor", "diffInHours", "diffInDays", "toLocaleDateString", "children", "style", "backgroundColor", "boxShadow", "zIndex", "animation", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/OnlineStatusIndicator.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { getUserOnlineStatus } from '../../apicalls/notifications';\n\nconst OnlineStatusIndicator = ({ \n  userId, \n  size = 'sm', \n  showLabel = false, \n  className = '',\n  refreshInterval = 30000 // 30 seconds\n}) => {\n  const [isOnline, setIsOnline] = useState(false);\n  const [lastSeen, setLastSeen] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (!userId) return;\n\n    const checkStatus = async () => {\n      try {\n        const response = await getUserOnlineStatus(userId);\n        console.log('Online status response:', response); // Debug log\n        if (response.success) {\n          setIsOnline(response.data.isOnline);\n          setLastSeen(response.data.lastSeen);\n          console.log('User online status:', response.data.isOnline); // Debug log\n        }\n      } catch (error) {\n        console.error('Error checking online status:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Initial check\n    checkStatus();\n\n    // Set up interval for periodic checks\n    const interval = setInterval(checkStatus, refreshInterval);\n\n    return () => clearInterval(interval);\n  }, [userId, refreshInterval]);\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'xs':\n        return 'w-2 h-2';\n      case 'sm':\n        return 'w-4 h-4'; // Made slightly larger for better visibility\n      case 'md':\n        return 'w-5 h-5';\n      case 'lg':\n        return 'w-6 h-6';\n      default:\n        return 'w-4 h-4'; // Made slightly larger for better visibility\n    }\n  };\n\n  const getLastSeenText = () => {\n    if (!lastSeen) return '';\n    \n    const now = new Date();\n    const lastSeenDate = new Date(lastSeen);\n    const diffInMinutes = Math.floor((now - lastSeenDate) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    \n    return lastSeenDate.toLocaleDateString();\n  };\n\n  return (\n    <div className={`relative inline-flex items-center ${className}`}>\n      {/* Online status dot */}\n      <div\n        className={`${getSizeClasses()} rounded-full border-2 border-white shadow-lg`}\n        style={{\n          backgroundColor: loading\n            ? '#60a5fa'\n            : isOnline\n              ? '#10b981'\n              : '#9ca3af',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',\n          zIndex: 10,\n          animation: loading || isOnline ? 'pulse 2s infinite' : 'none'\n        }}\n        title={loading ? 'Loading...' : isOnline ? 'Online' : `Last seen ${getLastSeenText()}`}\n      />\n\n      {/* Optional label */}\n      {showLabel && (\n        <span className={`ml-2 text-sm ${loading ? 'text-blue-500' : isOnline ? 'text-green-600' : 'text-gray-500'}`}>\n          {loading ? 'Loading...' : isOnline ? 'Online' : getLastSeenText()}\n        </span>\n      )}\n    </div>\n  );\n};\n\nexport default OnlineStatusIndicator;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,mBAAmB,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,MAAM;EACNC,IAAI,GAAG,IAAI;EACXC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,EAAE;EACdC,eAAe,GAAG,KAAK,CAAC;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,IAAI,CAACK,MAAM,EAAE;IAEb,MAAMY,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMjB,mBAAmB,CAACI,MAAM,CAAC;QAClDc,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,QAAQ,CAAC,CAAC,CAAC;QAClD,IAAIA,QAAQ,CAACG,OAAO,EAAE;UACpBT,WAAW,CAACM,QAAQ,CAACI,IAAI,CAACX,QAAQ,CAAC;UACnCG,WAAW,CAACI,QAAQ,CAACI,IAAI,CAACT,QAAQ,CAAC;UACnCM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,QAAQ,CAACI,IAAI,CAACX,QAAQ,CAAC,CAAC,CAAC;QAC9D;MACF,CAAC,CAAC,OAAOY,KAAK,EAAE;QACdJ,OAAO,CAACI,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD,CAAC,SAAS;QACRP,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACAC,WAAW,CAAC,CAAC;;IAEb;IACA,MAAMO,QAAQ,GAAGC,WAAW,CAACR,WAAW,EAAER,eAAe,CAAC;IAE1D,OAAO,MAAMiB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACnB,MAAM,EAAEI,eAAe,CAAC,CAAC;EAE7B,MAAMkB,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQrB,IAAI;MACV,KAAK,IAAI;QACP,OAAO,SAAS;MAClB,KAAK,IAAI;QACP,OAAO,SAAS;MAAE;MACpB,KAAK,IAAI;QACP,OAAO,SAAS;MAClB,KAAK,IAAI;QACP,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;MAAE;IACtB;EACF,CAAC;;EAED,MAAMsB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACf,QAAQ,EAAE,OAAO,EAAE;IAExB,MAAMgB,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,IAAID,IAAI,CAACjB,QAAQ,CAAC;IACvC,MAAMmB,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,GAAGE,YAAY,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEpE,IAAIC,aAAa,GAAG,CAAC,EAAE,OAAO,UAAU;IACxC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAQ,GAAEA,aAAc,OAAM;IAEtD,MAAMG,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAQ,GAAEA,WAAY,OAAM;IAElD,MAAMC,UAAU,GAAGH,IAAI,CAACC,KAAK,CAACC,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE,OAAQ,GAAEA,UAAW,OAAM;IAE/C,OAAOL,YAAY,CAACM,kBAAkB,CAAC,CAAC;EAC1C,CAAC;EAED,oBACElC,OAAA;IAAKK,SAAS,EAAG,qCAAoCA,SAAU,EAAE;IAAA8B,QAAA,gBAE/DnC,OAAA;MACEK,SAAS,EAAG,GAAEmB,cAAc,CAAC,CAAE,+CAA+C;MAC9EY,KAAK,EAAE;QACLC,eAAe,EAAEzB,OAAO,GACpB,SAAS,GACTJ,QAAQ,GACN,SAAS,GACT,SAAS;QACf8B,SAAS,EAAE,2BAA2B;QACtCC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE5B,OAAO,IAAIJ,QAAQ,GAAG,mBAAmB,GAAG;MACzD,CAAE;MACFiC,KAAK,EAAE7B,OAAO,GAAG,YAAY,GAAGJ,QAAQ,GAAG,QAAQ,GAAI,aAAYiB,eAAe,CAAC,CAAE;IAAE;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxF,CAAC,EAGDzC,SAAS,iBACRJ,OAAA;MAAMK,SAAS,EAAG,gBAAeO,OAAO,GAAG,eAAe,GAAGJ,QAAQ,GAAG,gBAAgB,GAAG,eAAgB,EAAE;MAAA2B,QAAA,EAC1GvB,OAAO,GAAG,YAAY,GAAGJ,QAAQ,GAAG,QAAQ,GAAGiB,eAAe,CAAC;IAAC;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtC,EAAA,CAnGIN,qBAAqB;AAAA6C,EAAA,GAArB7C,qBAAqB;AAqG3B,eAAeA,qBAAqB;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}