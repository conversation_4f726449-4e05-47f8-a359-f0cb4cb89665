{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Dashboard\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { Progress } from 'antd';\nimport { TbUsers, TbBook, TbFileText, TbChartBar, TbTrendingUp, TbTarget, TbAward, TbClock, TbPlus, TbEye, TbRobot, TbBell, TbMessageCircle } from 'react-icons/tb';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReports } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport AdminLayout from '../../../components/AdminLayout';\nimport AdminCard from '../../../components/AdminCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    totalExams: 0,\n    totalReports: 0,\n    averageScore: 0,\n    completionRate: 0\n  });\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n\n      // Fetch users data\n      const usersResponse = await getAllUsers();\n      const users = usersResponse.success ? usersResponse.users : [];\n\n      // Fetch exams data\n      const examsResponse = await getAllExams();\n      const exams = examsResponse.success ? examsResponse.data : [];\n\n      // Fetch reports data (with empty filters to get all reports)\n      const reportsResponse = await getAllReports({\n        examName: '',\n        userName: '',\n        page: 1,\n        limit: 1000\n      });\n      const reports = reportsResponse.success ? reportsResponse.data : [];\n\n      // Calculate statistics\n      const totalUsers = users.length;\n      const activeUsers = users.filter(u => !u.isBlocked).length;\n      const totalExams = exams.length;\n      const totalReports = reports.length;\n\n      // Calculate average score from reports\n      const averageScore = reports.length > 0 ? reports.reduce((sum, report) => sum + (report.percentage || 0), 0) / reports.length : 0;\n\n      // Calculate completion rate\n      const completionRate = totalUsers > 0 ? totalReports / totalUsers * 100 : 0;\n      setStats({\n        totalUsers,\n        activeUsers,\n        totalExams,\n        totalReports,\n        averageScore: Math.round(averageScore),\n        completionRate: Math.round(completionRate)\n      });\n      setLoading(false);\n      dispatch(HideLoading());\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n  const quickActions = [{\n    title: 'Manage Users',\n    description: 'View and manage student accounts',\n    icon: TbUsers,\n    path: '/admin/users',\n    color: 'bg-blue-500'\n  }, {\n    title: 'Create Exam',\n    description: 'Add new exams and questions',\n    icon: TbFileText,\n    path: '/admin/exams/add',\n    color: 'bg-green-500'\n  }, {\n    title: 'Study Materials',\n    description: 'Manage learning resources',\n    icon: TbBook,\n    path: '/admin/study-materials',\n    color: 'bg-orange-500'\n  }, {\n    title: 'View Reports',\n    description: 'Analytics and performance',\n    icon: TbChartBar,\n    path: '/admin/reports',\n    color: 'bg-indigo-500'\n  }, {\n    title: 'Notifications',\n    description: 'Send announcements',\n    icon: TbBell,\n    path: '/admin/notifications',\n    color: 'bg-pink-500'\n  }, {\n    title: 'Forum Management',\n    description: 'Manage community forum',\n    icon: TbMessageCircle,\n    path: '/admin/forum',\n    color: 'bg-purple-500'\n  }];\n  const quickActionButtons = [/*#__PURE__*/_jsxDEV(motion.button, {\n    whileHover: {\n      scale: 1.02\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    onClick: () => navigate('/admin/users'),\n    className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n      className: \"w-4 h-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"hidden sm:inline\",\n      children: \"Manage Users\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this)]\n  }, \"users\", true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n    whileHover: {\n      scale: 1.02\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    onClick: () => navigate('/admin/exams/add'),\n    className: \"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(TbPlus, {\n      className: \"w-4 h-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"hidden sm:inline\",\n      children: \"Create Exam\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this)]\n  }, \"exams\", true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n    whileHover: {\n      scale: 1.02\n    },\n    whileTap: {\n      scale: 0.98\n    },\n    onClick: () => navigate('/admin/ai-questions'),\n    className: \"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(TbRobot, {\n      className: \"w-4 h-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"hidden sm:inline\",\n      children: \"AI Questions\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)]\n  }, \"ai\", true, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this)];\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    showHeader: false,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 sm:mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 sm:p-8 text-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl sm:text-3xl font-bold mb-2\",\n              children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"! \\uD83D\\uDC4B\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 text-sm sm:text-base\",\n              children: \"Here's what's happening with your educational platform today.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: quickActionButtons\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(AdminCard, {\n        className: \"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 text-sm font-medium mb-1\",\n              children: \"Total Students\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl sm:text-3xl font-bold\",\n              children: stats.totalUsers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-200 text-xs mt-1\",\n              children: \"Registered users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbUsers, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminCard, {\n        className: \"bg-gradient-to-br from-green-500 to-green-600 text-white border-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-100 text-sm font-medium mb-1\",\n              children: \"Active Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl sm:text-3xl font-bold\",\n              children: stats.activeUsers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-200 text-xs mt-1\",\n              children: \"Currently active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbTrendingUp, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminCard, {\n        className: \"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-purple-100 text-sm font-medium mb-1\",\n              children: \"Total Exams\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl sm:text-3xl font-bold\",\n              children: stats.totalExams\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-purple-200 text-xs mt-1\",\n              children: \"Available exams\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbFileText, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminCard, {\n        className: \"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-orange-100 text-sm font-medium mb-1\",\n              children: \"Avg Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl sm:text-3xl font-bold\",\n              children: [stats.averageScore, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-orange-200 text-xs mt-1\",\n              children: \"Overall performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbAward, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 sm:mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(AdminCard, {\n        title: \"Quick Actions\",\n        subtitle: \"Common administrative tasks\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n          children: quickActions.map((action, index) => {\n            const IconComponent = action.icon;\n            return /*#__PURE__*/_jsxDEV(motion.button, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.3,\n                delay: index * 0.1\n              },\n              onClick: () => navigate(action.path),\n              className: `p-4 rounded-xl ${action.color} text-white hover:shadow-lg transition-all duration-300 transform hover:scale-105 text-left`,\n              children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                className: \"w-8 h-8 mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-sm sm:text-base mb-1\",\n                children: action.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs sm:text-sm opacity-90\",\n                children: action.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this)]\n            }, action.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminCard, {\n        title: \"Performance Analytics\",\n        subtitle: \"System performance overview\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-slate-700\",\n                children: \"Completion Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-bold text-slate-900\",\n                children: [stats.completionRate, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: stats.completionRate,\n              strokeColor: {\n                '0%': '#3B82F6',\n                '100%': '#8B5CF6'\n              },\n              className: \"mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-slate-700\",\n                children: \"Average Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-bold text-slate-900\",\n                children: [stats.averageScore, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: stats.averageScore,\n              strokeColor: {\n                '0%': '#10B981',\n                '100%': '#059669'\n              },\n              className: \"mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4 pt-4 border-t border-slate-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-slate-900\",\n                children: stats.totalReports\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-slate-600\",\n                children: \"Total Attempts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-slate-900\",\n                children: [Math.round(stats.activeUsers / stats.totalUsers * 100) || 0, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-slate-600\",\n                children: \"User Activity\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"HuTO5TDeorrXNEcOSrf7tMirYDI=\", false, function () {\n  return [useDispatch, useNavigate, useSelector];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useDispatch", "useSelector", "useNavigate", "Progress", "TbUsers", "TbBook", "TbFileText", "TbChartBar", "TbTrendingUp", "TbTarget", "TbAward", "TbClock", "TbPlus", "TbEye", "TbRobot", "TbBell", "TbMessageCircle", "getAllUsers", "getAllExams", "getAllReports", "HideLoading", "ShowLoading", "AdminLayout", "AdminCard", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "dispatch", "navigate", "user", "state", "stats", "setStats", "totalUsers", "activeUsers", "totalExams", "totalReports", "averageScore", "completionRate", "loading", "setLoading", "fetchDashboardData", "usersResponse", "users", "success", "examsResponse", "exams", "data", "reportsResponse", "examName", "userName", "page", "limit", "reports", "length", "filter", "u", "isBlocked", "reduce", "sum", "report", "percentage", "Math", "round", "error", "console", "quickActions", "title", "description", "icon", "path", "color", "quickActionButtons", "button", "whileHover", "scale", "whileTap", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showHeader", "name", "subtitle", "map", "action", "index", "IconComponent", "initial", "opacity", "y", "animate", "transition", "duration", "delay", "percent", "strokeColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Dashboard/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { Progress } from 'antd';\nimport {\n  TbU<PERSON><PERSON>,\n  TbB<PERSON>,\n  TbFileText,\n  TbChartBar,\n  TbTrendingUp,\n  TbTarget,\n  TbAward,\n  TbClock,\n  TbPlus,\n  TbEye,\n  TbRobot,\n  TbBell,\n  TbMessageCircle\n} from 'react-icons/tb';\nimport { getAllUsers } from '../../../apicalls/users';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReports } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport AdminLayout from '../../../components/AdminLayout';\nimport AdminCard from '../../../components/AdminCard';\n\nconst AdminDashboard = () => {\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n  const [stats, setStats] = useState({\n    totalUsers: 0,\n    activeUsers: 0,\n    totalExams: 0,\n    totalReports: 0,\n    averageScore: 0,\n    completionRate: 0\n  });\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      \n      // Fetch users data\n      const usersResponse = await getAllUsers();\n      const users = usersResponse.success ? usersResponse.users : [];\n\n      // Fetch exams data\n      const examsResponse = await getAllExams();\n      const exams = examsResponse.success ? examsResponse.data : [];\n\n      // Fetch reports data (with empty filters to get all reports)\n      const reportsResponse = await getAllReports({ examName: '', userName: '', page: 1, limit: 1000 });\n      const reports = reportsResponse.success ? reportsResponse.data : [];\n\n      // Calculate statistics\n      const totalUsers = users.length;\n      const activeUsers = users.filter(u => !u.isBlocked).length;\n      const totalExams = exams.length;\n      const totalReports = reports.length;\n      \n      // Calculate average score from reports\n      const averageScore = reports.length > 0 \n        ? reports.reduce((sum, report) => sum + (report.percentage || 0), 0) / reports.length\n        : 0;\n      \n      // Calculate completion rate\n      const completionRate = totalUsers > 0 ? (totalReports / totalUsers) * 100 : 0;\n\n      setStats({\n        totalUsers,\n        activeUsers,\n        totalExams,\n        totalReports,\n        averageScore: Math.round(averageScore),\n        completionRate: Math.round(completionRate)\n      });\n\n      setLoading(false);\n      dispatch(HideLoading());\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n\n\n  const quickActions = [\n    {\n      title: 'Manage Users',\n      description: 'View and manage student accounts',\n      icon: TbUsers,\n      path: '/admin/users',\n      color: 'bg-blue-500'\n    },\n    {\n      title: 'Create Exam',\n      description: 'Add new exams and questions',\n      icon: TbFileText,\n      path: '/admin/exams/add',\n      color: 'bg-green-500'\n    },\n    {\n      title: 'Study Materials',\n      description: 'Manage learning resources',\n      icon: TbBook,\n      path: '/admin/study-materials',\n      color: 'bg-orange-500'\n    },\n    {\n      title: 'View Reports',\n      description: 'Analytics and performance',\n      icon: TbChartBar,\n      path: '/admin/reports',\n      color: 'bg-indigo-500'\n    },\n    {\n      title: 'Notifications',\n      description: 'Send announcements',\n      icon: TbBell,\n      path: '/admin/notifications',\n      color: 'bg-pink-500'\n    },\n    {\n      title: 'Forum Management',\n      description: 'Manage community forum',\n      icon: TbMessageCircle,\n      path: '/admin/forum',\n      color: 'bg-purple-500'\n    }\n  ];\n\n  const quickActionButtons = [\n    <motion.button\n      key=\"users\"\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => navigate('/admin/users')}\n      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2\"\n    >\n      <TbUsers className=\"w-4 h-4\" />\n      <span className=\"hidden sm:inline\">Manage Users</span>\n    </motion.button>,\n    <motion.button\n      key=\"exams\"\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => navigate('/admin/exams/add')}\n      className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2\"\n    >\n      <TbPlus className=\"w-4 h-4\" />\n      <span className=\"hidden sm:inline\">Create Exam</span>\n    </motion.button>,\n    <motion.button\n      key=\"ai\"\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={() => navigate('/admin/ai-questions')}\n      className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2\"\n    >\n      <TbRobot className=\"w-4 h-4\" />\n      <span className=\"hidden sm:inline\">AI Questions</span>\n    </motion.button>,\n  ];\n\n  return (\n    <AdminLayout showHeader={false}>\n      {/* Welcome Section */}\n      <div className=\"mb-6 sm:mb-8\">\n        <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 sm:p-8 text-white\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl sm:text-3xl font-bold mb-2\">\n                Welcome back, {user?.name}! 👋\n              </h1>\n              <p className=\"text-blue-100 text-sm sm:text-base\">\n                Here's what's happening with your educational platform today.\n              </p>\n            </div>\n            <div className=\"flex flex-wrap gap-2\">\n              {quickActionButtons}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8\">\n        <AdminCard className=\"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-blue-100 text-sm font-medium mb-1\">Total Students</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.totalUsers}</p>\n              <p className=\"text-blue-200 text-xs mt-1\">Registered users</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbUsers className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n\n        <AdminCard className=\"bg-gradient-to-br from-green-500 to-green-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-green-100 text-sm font-medium mb-1\">Active Users</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.activeUsers}</p>\n              <p className=\"text-green-200 text-xs mt-1\">Currently active</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbTrendingUp className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n\n        <AdminCard className=\"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-purple-100 text-sm font-medium mb-1\">Total Exams</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.totalExams}</p>\n              <p className=\"text-purple-200 text-xs mt-1\">Available exams</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbFileText className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n\n        <AdminCard className=\"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-orange-100 text-sm font-medium mb-1\">Avg Score</p>\n              <p className=\"text-2xl sm:text-3xl font-bold\">{stats.averageScore}%</p>\n              <p className=\"text-orange-200 text-xs mt-1\">Overall performance</p>\n            </div>\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n              <TbAward className=\"w-6 h-6\" />\n            </div>\n          </div>\n        </AdminCard>\n      </div>\n\n      {/* Quick Actions and Analytics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 sm:mb-8\">\n        {/* Quick Actions */}\n        <AdminCard \n          title=\"Quick Actions\" \n          subtitle=\"Common administrative tasks\"\n        >\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            {quickActions.map((action, index) => {\n              const IconComponent = action.icon;\n              return (\n                <motion.button\n                  key={action.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\n                  onClick={() => navigate(action.path)}\n                  className={`p-4 rounded-xl ${action.color} text-white hover:shadow-lg transition-all duration-300 transform hover:scale-105 text-left`}\n                >\n                  <IconComponent className=\"w-8 h-8 mb-3\" />\n                  <h3 className=\"font-semibold text-sm sm:text-base mb-1\">{action.title}</h3>\n                  <p className=\"text-xs sm:text-sm opacity-90\">{action.description}</p>\n                </motion.button>\n              );\n            })}\n          </div>\n        </AdminCard>\n\n        {/* Performance Analytics */}\n        <AdminCard \n          title=\"Performance Analytics\" \n          subtitle=\"System performance overview\"\n        >\n          <div className=\"space-y-6\">\n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-slate-700\">Completion Rate</span>\n                <span className=\"text-sm font-bold text-slate-900\">{stats.completionRate}%</span>\n              </div>\n              <Progress \n                percent={stats.completionRate} \n                strokeColor={{\n                  '0%': '#3B82F6',\n                  '100%': '#8B5CF6',\n                }}\n                className=\"mb-4\"\n              />\n            </div>\n            \n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-slate-700\">Average Score</span>\n                <span className=\"text-sm font-bold text-slate-900\">{stats.averageScore}%</span>\n              </div>\n              <Progress \n                percent={stats.averageScore} \n                strokeColor={{\n                  '0%': '#10B981',\n                  '100%': '#059669',\n                }}\n                className=\"mb-4\"\n              />\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4 pt-4 border-t border-slate-100\">\n              <div className=\"text-center\">\n                <p className=\"text-2xl font-bold text-slate-900\">{stats.totalReports}</p>\n                <p className=\"text-xs text-slate-600\">Total Attempts</p>\n              </div>\n              <div className=\"text-center\">\n                <p className=\"text-2xl font-bold text-slate-900\">{Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0}%</p>\n                <p className=\"text-xs text-slate-600\">User Activity</p>\n              </div>\n            </div>\n          </div>\n        </AdminCard>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SACEC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,UAAU,EACVC,YAAY,EACZC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,eAAe,QACV,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAOC,SAAS,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B;EAAK,CAAC,GAAG7B,WAAW,CAAE8B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC;IACjCqC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd4C,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChBb,QAAQ,CAACP,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAMsB,aAAa,GAAG,MAAM1B,WAAW,CAAC,CAAC;MACzC,MAAM2B,KAAK,GAAGD,aAAa,CAACE,OAAO,GAAGF,aAAa,CAACC,KAAK,GAAG,EAAE;;MAE9D;MACA,MAAME,aAAa,GAAG,MAAM5B,WAAW,CAAC,CAAC;MACzC,MAAM6B,KAAK,GAAGD,aAAa,CAACD,OAAO,GAAGC,aAAa,CAACE,IAAI,GAAG,EAAE;;MAE7D;MACA,MAAMC,eAAe,GAAG,MAAM9B,aAAa,CAAC;QAAE+B,QAAQ,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAC;MACjG,MAAMC,OAAO,GAAGL,eAAe,CAACJ,OAAO,GAAGI,eAAe,CAACD,IAAI,GAAG,EAAE;;MAEnE;MACA,MAAMd,UAAU,GAAGU,KAAK,CAACW,MAAM;MAC/B,MAAMpB,WAAW,GAAGS,KAAK,CAACY,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,SAAS,CAAC,CAACH,MAAM;MAC1D,MAAMnB,UAAU,GAAGW,KAAK,CAACQ,MAAM;MAC/B,MAAMlB,YAAY,GAAGiB,OAAO,CAACC,MAAM;;MAEnC;MACA,MAAMjB,YAAY,GAAGgB,OAAO,CAACC,MAAM,GAAG,CAAC,GACnCD,OAAO,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,IAAIC,MAAM,CAACC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGR,OAAO,CAACC,MAAM,GACnF,CAAC;;MAEL;MACA,MAAMhB,cAAc,GAAGL,UAAU,GAAG,CAAC,GAAIG,YAAY,GAAGH,UAAU,GAAI,GAAG,GAAG,CAAC;MAE7ED,QAAQ,CAAC;QACPC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,YAAY;QACZC,YAAY,EAAEyB,IAAI,CAACC,KAAK,CAAC1B,YAAY,CAAC;QACtCC,cAAc,EAAEwB,IAAI,CAACC,KAAK,CAACzB,cAAc;MAC3C,CAAC,CAAC;MAEFE,UAAU,CAAC,KAAK,CAAC;MACjBb,QAAQ,CAACR,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDxB,UAAU,CAAC,KAAK,CAAC;MACjBb,QAAQ,CAACR,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAID,MAAM+C,YAAY,GAAG,CACnB;IACEC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,kCAAkC;IAC/CC,IAAI,EAAElE,OAAO;IACbmE,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,EAAEhE,UAAU;IAChBiE,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,2BAA2B;IACxCC,IAAI,EAAEjE,MAAM;IACZkE,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,2BAA2B;IACxCC,IAAI,EAAE/D,UAAU;IAChBgE,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,oBAAoB;IACjCC,IAAI,EAAEvD,MAAM;IACZwD,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAEtD,eAAe;IACrBuD,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,kBAAkB,GAAG,cACzBhD,OAAA,CAAC1B,MAAM,CAAC2E,MAAM;IAEZC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAE;IAC5BC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAC1BE,OAAO,EAAEA,CAAA,KAAMjD,QAAQ,CAAC,cAAc,CAAE;IACxCkD,SAAS,EAAC,sHAAsH;IAAAC,QAAA,gBAEhIvD,OAAA,CAACrB,OAAO;MAAC2E,SAAS,EAAC;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/B3D,OAAA;MAAMsD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,GAPlD,OAAO;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAQE,CAAC,eAChB3D,OAAA,CAAC1B,MAAM,CAAC2E,MAAM;IAEZC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAE;IAC5BC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAC1BE,OAAO,EAAEA,CAAA,KAAMjD,QAAQ,CAAC,kBAAkB,CAAE;IAC5CkD,SAAS,EAAC,wHAAwH;IAAAC,QAAA,gBAElIvD,OAAA,CAACb,MAAM;MAACmE,SAAS,EAAC;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC9B3D,OAAA;MAAMsD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,GAPjD,OAAO;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAQE,CAAC,eAChB3D,OAAA,CAAC1B,MAAM,CAAC2E,MAAM;IAEZC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAE;IAC5BC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAAK,CAAE;IAC1BE,OAAO,EAAEA,CAAA,KAAMjD,QAAQ,CAAC,qBAAqB,CAAE;IAC/CkD,SAAS,EAAC,0HAA0H;IAAAC,QAAA,gBAEpIvD,OAAA,CAACX,OAAO;MAACiE,SAAS,EAAC;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC/B3D,OAAA;MAAMsD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,GAPlD,IAAI;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAQK,CAAC,CACjB;EAED,oBACE3D,OAAA,CAACH,WAAW;IAAC+D,UAAU,EAAE,KAAM;IAAAL,QAAA,gBAE7BvD,OAAA;MAAKsD,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BvD,OAAA;QAAKsD,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FvD,OAAA;UAAKsD,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjFvD,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAIsD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAAC,gBACpC,EAAClD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,IAAI,EAAC,gBAC5B;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3D,OAAA;cAAGsD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClCP;UAAkB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3D,OAAA;MAAKsD,SAAS,EAAC,4EAA4E;MAAAC,QAAA,gBACzFvD,OAAA,CAACF,SAAS;QAACwD,SAAS,EAAC,iEAAiE;QAAAC,QAAA,eACpFvD,OAAA;UAAKsD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvD,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAGsD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxE3D,OAAA;cAAGsD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAEhD,KAAK,CAACE;YAAU;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpE3D,OAAA;cAAGsD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFvD,OAAA,CAACrB,OAAO;cAAC2E,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEZ3D,OAAA,CAACF,SAAS;QAACwD,SAAS,EAAC,mEAAmE;QAAAC,QAAA,eACtFvD,OAAA;UAAKsD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvD,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAGsD,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvE3D,OAAA;cAAGsD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAEhD,KAAK,CAACG;YAAW;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrE3D,OAAA;cAAGsD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFvD,OAAA,CAACjB,YAAY;cAACuE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEZ3D,OAAA,CAACF,SAAS;QAACwD,SAAS,EAAC,qEAAqE;QAAAC,QAAA,eACxFvD,OAAA;UAAKsD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvD,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAGsD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvE3D,OAAA;cAAGsD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAEhD,KAAK,CAACI;YAAU;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpE3D,OAAA;cAAGsD,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFvD,OAAA,CAACnB,UAAU;cAACyE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEZ3D,OAAA,CAACF,SAAS;QAACwD,SAAS,EAAC,qEAAqE;QAAAC,QAAA,eACxFvD,OAAA;UAAKsD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvD,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAGsD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrE3D,OAAA;cAAGsD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,GAAEhD,KAAK,CAACM,YAAY,EAAC,GAAC;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvE3D,OAAA;cAAGsD,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFvD,OAAA,CAACf,OAAO;cAACqE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGN3D,OAAA;MAAKsD,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBAEjEvD,OAAA,CAACF,SAAS;QACR6C,KAAK,EAAC,eAAe;QACrBmB,QAAQ,EAAC,6BAA6B;QAAAP,QAAA,eAEtCvD,OAAA;UAAKsD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDb,YAAY,CAACqB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;YACnC,MAAMC,aAAa,GAAGF,MAAM,CAACnB,IAAI;YACjC,oBACE7C,OAAA,CAAC1B,MAAM,CAAC2E,MAAM;cAEZkB,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAER,KAAK,GAAG;cAAI,CAAE;cAClDZ,OAAO,EAAEA,CAAA,KAAMjD,QAAQ,CAAC4D,MAAM,CAAClB,IAAI,CAAE;cACrCQ,SAAS,EAAG,kBAAiBU,MAAM,CAACjB,KAAM,6FAA6F;cAAAQ,QAAA,gBAEvIvD,OAAA,CAACkE,aAAa;gBAACZ,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1C3D,OAAA;gBAAIsD,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAES,MAAM,CAACrB;cAAK;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3E3D,OAAA;gBAAGsD,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAES,MAAM,CAACpB;cAAW;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAThEK,MAAM,CAACrB,KAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUJ,CAAC;UAEpB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGZ3D,OAAA,CAACF,SAAS;QACR6C,KAAK,EAAC,uBAAuB;QAC7BmB,QAAQ,EAAC,6BAA6B;QAAAP,QAAA,eAEtCvD,OAAA;UAAKsD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvD,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAKsD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDvD,OAAA;gBAAMsD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3E3D,OAAA;gBAAMsD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAEhD,KAAK,CAACO,cAAc,EAAC,GAAC;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACN3D,OAAA,CAACtB,QAAQ;cACPgG,OAAO,EAAEnE,KAAK,CAACO,cAAe;cAC9B6D,WAAW,EAAE;gBACX,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE;cACV,CAAE;cACFrB,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3D,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAKsD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDvD,OAAA;gBAAMsD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzE3D,OAAA;gBAAMsD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAEhD,KAAK,CAACM,YAAY,EAAC,GAAC;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eACN3D,OAAA,CAACtB,QAAQ;cACPgG,OAAO,EAAEnE,KAAK,CAACM,YAAa;cAC5B8D,WAAW,EAAE;gBACX,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE;cACV,CAAE;cACFrB,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3D,OAAA;YAAKsD,SAAS,EAAC,uDAAuD;YAAAC,QAAA,gBACpEvD,OAAA;cAAKsD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvD,OAAA;gBAAGsD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEhD,KAAK,CAACK;cAAY;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzE3D,OAAA;gBAAGsD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACN3D,OAAA;cAAKsD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BvD,OAAA;gBAAGsD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAEjB,IAAI,CAACC,KAAK,CAAEhC,KAAK,CAACG,WAAW,GAAGH,KAAK,CAACE,UAAU,GAAI,GAAG,CAAC,IAAI,CAAC,EAAC,GAAC;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrH3D,OAAA;gBAAGsD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAACzD,EAAA,CA9SID,cAAc;EAAA,QACD1B,WAAW,EACXE,WAAW,EACXD,WAAW;AAAA;AAAAoG,EAAA,GAHxB3E,cAAc;AAgTpB,eAAeA,cAAc;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}