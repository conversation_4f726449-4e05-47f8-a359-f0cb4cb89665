{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\UserReports\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './index.css';\nimport PageTitle from \"../../../components/PageTitle\";\nimport { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbTrophy, TbTarget, TbTrendingUp, TbCalendar, TbClock, TbAward, TbChartBar, TbDownload, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>heckCircle, TbXCircle, TbMedal, Tb<PERSON>lame } from \"react-icons/tb\";\nimport moment from \"moment\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UserReports() {\n  _s();\n  const [reportsData, setReportsData] = React.useState([]);\n  const dispatch = useDispatch();\n  const columns = [{\n    title: \"Exam Name\",\n    dataIndex: \"examName\",\n    render: (text, record) => {\n      var _record$exam;\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: (_record$exam = record.exam) === null || _record$exam === void 0 ? void 0 : _record$exam.name\n      }, void 0, false);\n    }\n  }, {\n    title: \"Date\",\n    dataIndex: \"date\",\n    render: (text, record) => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: moment(record.createdAt).format(\"DD-MM-YYYY hh:mm:ss\")\n    }, void 0, false)\n  }, {\n    title: \"Total Marks\",\n    dataIndex: \"totalQuestions\",\n    render: (text, record) => {\n      var _record$exam2;\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: (_record$exam2 = record.exam) === null || _record$exam2 === void 0 ? void 0 : _record$exam2.totalMarks\n      }, void 0, false);\n    }\n  }, {\n    title: \"Passing Marks\",\n    dataIndex: \"correctAnswers\",\n    render: (text, record) => {\n      var _record$exam3;\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: (_record$exam3 = record.exam) === null || _record$exam3 === void 0 ? void 0 : _record$exam3.passingMarks\n      }, void 0, false);\n    }\n  }, {\n    title: \"Obtained Marks\",\n    dataIndex: \"correctAnswers\",\n    render: (text, record) => {\n      var _record$result;\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: (_record$result = record.result) === null || _record$result === void 0 ? void 0 : _record$result.correctAnswers.length\n      }, void 0, false);\n    }\n  }, {\n    title: \"Verdict\",\n    dataIndex: \"verdict\",\n    render: (text, record) => {\n      var _record$result2;\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: (_record$result2 = record.result) === null || _record$result2 === void 0 ? void 0 : _record$result2.verdict\n      }, void 0, false);\n    }\n  }];\n  const getData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReportsByUser();\n      if (response.success) {\n        setReportsData(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    getData();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"reports-container\",\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      title: \"Reports\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divider\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: reportsData,\n      rowKey: record => record._id,\n      scroll: {\n        x: true\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n}\n_s(UserReports, \"bSbcCfWg8MHrgcvnhAt5x2tc+f0=\", false, function () {\n  return [useDispatch];\n});\n_c = UserReports;\nexport default UserReports;\nvar _c;\n$RefreshReg$(_c, \"UserReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Page<PERSON><PERSON>le", "message", "Card", "Progress", "Statistic", "Select", "DatePicker", "<PERSON><PERSON>", "Empty", "useDispatch", "HideLoading", "ShowLoading", "getAllReportsByUser", "motion", "AnimatePresence", "TbTrophy", "TbTarget", "TbTrendingUp", "TbCalendar", "TbClock", "TbAward", "TbChartBar", "TbDownload", "Tb<PERSON><PERSON>er", "TbEye", "TbCheckCircle", "TbXCircle", "TbMedal", "TbFlame", "moment", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "UserReports", "_s", "reportsData", "setReportsData", "dispatch", "columns", "title", "dataIndex", "render", "text", "record", "_record$exam", "children", "exam", "name", "createdAt", "format", "_record$exam2", "totalMarks", "_record$exam3", "passingMarks", "_record$result", "result", "correctAnswers", "length", "_record$result2", "verdict", "getData", "response", "success", "data", "error", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Table", "dataSource", "<PERSON><PERSON><PERSON>", "_id", "scroll", "x", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/UserReports/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport './index.css';\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  TbTrophy,\r\n  TbTarget,\r\n  TbTrendingUp,\r\n  TbCalendar,\r\n  TbClock,\r\n  TbAward,\r\n  TbChartBar,\r\n  TbDownload,\r\n  TbFilter,\r\n  TbEye,\r\n  TbCheckCircle,\r\n  TbXCircle,\r\n  TbMedal,\r\n  TbFlame\r\n} from \"react-icons/tb\";\r\nimport moment from \"moment\";\r\n\r\nfunction UserReports() {\r\n  const [reportsData, setReportsData] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => <>{record.exam?.name}</>,\r\n    },\r\n    {\r\n      title: \"Date\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <>{moment(record.createdAt).format(\"DD-MM-YYYY hh:mm:ss\")}</>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalQuestions\",\r\n      render: (text, record) => <>{record.exam?.totalMarks}</>,\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.exam?.passingMarks}</>,\r\n    },\r\n    {\r\n      title: \"Obtained Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.result?.correctAnswers.length}</>,\r\n    },\r\n    {\r\n      title: \"Verdict\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => <>{record.result?.verdict}</>,\r\n    },\r\n  ];\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"reports-container\">\r\n      <PageTitle title=\"Reports\" />\r\n      <div className=\"divider\"></div>\r\n      <Table \r\n      columns={columns} \r\n      dataSource={reportsData} \r\n      rowKey={(record) => record._id} \r\n      scroll={{ x: true }} \r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserReports;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAC5F,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,QAAQ,EACRC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,aAAa,EACbC,SAAS,EACTC,OAAO,EACPC,OAAO,QACF,gBAAgB;AACvB,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,QAAA,IAAAC,SAAA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE5B,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,KAAK,CAACC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMwC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,WAAW;IAClBC,SAAS,EAAE,UAAU;IACrBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAC,YAAA;MAAA,oBAAKZ,OAAA,CAAAF,SAAA;QAAAe,QAAA,GAAAD,YAAA,GAAGD,MAAM,CAACG,IAAI,cAAAF,YAAA,uBAAXA,YAAA,CAAaG;MAAI,gBAAG,CAAC;IAAA;EACpD,CAAC,EACD;IACER,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBX,OAAA,CAAAF,SAAA;MAAAe,QAAA,EAAGjB,MAAM,CAACe,MAAM,CAACK,SAAS,CAAC,CAACC,MAAM,CAAC,qBAAqB;IAAC,gBAAG;EAEhE,CAAC,EACD;IACEV,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE,gBAAgB;IAC3BC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAO,aAAA;MAAA,oBAAKlB,OAAA,CAAAF,SAAA;QAAAe,QAAA,GAAAK,aAAA,GAAGP,MAAM,CAACG,IAAI,cAAAI,aAAA,uBAAXA,aAAA,CAAaC;MAAU,gBAAG,CAAC;IAAA;EAC1D,CAAC,EACD;IACEZ,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE,gBAAgB;IAC3BC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAS,aAAA;MAAA,oBAAKpB,OAAA,CAAAF,SAAA;QAAAe,QAAA,GAAAO,aAAA,GAAGT,MAAM,CAACG,IAAI,cAAAM,aAAA,uBAAXA,aAAA,CAAaC;MAAY,gBAAG,CAAC;IAAA;EAC5D,CAAC,EACD;IACEd,KAAK,EAAE,gBAAgB;IACvBC,SAAS,EAAE,gBAAgB;IAC3BC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAW,cAAA;MAAA,oBAAKtB,OAAA,CAAAF,SAAA;QAAAe,QAAA,GAAAS,cAAA,GAAGX,MAAM,CAACY,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,cAAc,CAACC;MAAM,gBAAG,CAAC;IAAA;EACvE,CAAC,EACD;IACElB,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAe,eAAA;MAAA,oBAAK1B,OAAA,CAAAF,SAAA;QAAAe,QAAA,GAAAa,eAAA,GAAGf,MAAM,CAACY,MAAM,cAAAG,eAAA,uBAAbA,eAAA,CAAeC;MAAO,gBAAG,CAAC;IAAA;EACzD,CAAC,CACF;EAED,MAAMC,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACFvB,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMmD,QAAQ,GAAG,MAAMlD,mBAAmB,CAAC,CAAC;MAC5C,IAAIkD,QAAQ,CAACC,OAAO,EAAE;QACpB1B,cAAc,CAACyB,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,MAAM;QACL/D,OAAO,CAACgE,KAAK,CAACH,QAAQ,CAAC7D,OAAO,CAAC;MACjC;MACAqC,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACd3B,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;MACvBT,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDF,SAAS,CAAC,MAAM;IACd8D,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE5B,OAAA;IAAKiC,SAAS,EAAC,mBAAmB;IAAApB,QAAA,gBAChCb,OAAA,CAACjC,SAAS;MAACwC,KAAK,EAAC;IAAS;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC7BrC,OAAA;MAAKiC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC/BrC,OAAA,CAACsC,KAAK;MACNhC,OAAO,EAAEA,OAAQ;MACjBiC,UAAU,EAAEpC,WAAY;MACxBqC,MAAM,EAAG7B,MAAM,IAAKA,MAAM,CAAC8B,GAAI;MAC/BC,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAK;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACnC,EAAA,CAtEQD,WAAW;EAAA,QAEDzB,WAAW;AAAA;AAAAoE,EAAA,GAFrB3C,WAAW;AAwEpB,eAAeA,WAAW;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}