{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport \"./index.css\";\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport OnlineStatusIndicator from \"../../../components/common/OnlineStatusIndicator\";\nimport { addQuestion, addReply, getAllQuestions, deleteQuestion, updateQuestion, updateReplyStatus } from \"../../../apicalls/forum\";\nimport image from \"../../../assets/person.png\";\nimport { FaPencilAlt } from \"react-icons/fa\";\nimport { MdDelete, MdMessage } from \"react-icons/md\";\nimport { FaCheck } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState(\"\");\n  const [questions, setQuestions] = useState([]);\n  const [expandedReplies, setExpandedReplies] = useState({});\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [editQuestion, setEditQuestion] = useState(null);\n  const [form] = Form.useForm();\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\n\n  // Skeleton loader component\n  const ForumSkeleton = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"forum-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"forum-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-8 bg-gray-200 rounded w-48 mb-4 animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-10 bg-blue-100 rounded w-32 animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"forum-content\",\n      children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-card mb-4 p-4 border rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gray-200 rounded-full animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-gray-100 rounded w-1/2 mb-3 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-gray-100 rounded w-full mb-2 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-gray-100 rounded w-2/3 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this)\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n  const [form2] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [limit] = useState(10);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async page => {\n    try {\n      // Check cache first\n      const cacheKey = `forum_questions_${page}_${limit}`;\n      const cachedData = localStorage.getItem(cacheKey);\n      const cacheTime = localStorage.getItem(`${cacheKey}_time`);\n      const now = Date.now();\n\n      // Use cache if less than 5 minutes old\n      if (cachedData && cacheTime && now - parseInt(cacheTime) < 300000) {\n        const cached = JSON.parse(cachedData);\n        setQuestions(cached.questions);\n        setTotalQuestions(cached.totalQuestions);\n        setTotalPages(cached.totalPages);\n        return;\n      }\n      dispatch(ShowLoading());\n      const response = await getAllQuestions({\n        page,\n        limit\n      }); // Pass query params to API call\n      if (response.success) {\n        console.log(response.data);\n        setQuestions(response.data); // No need to reverse as backend will handle order\n        setTotalQuestions(response.totalQuestions);\n        setTotalPages(response.totalPages);\n\n        // Cache the data\n        localStorage.setItem(cacheKey, JSON.stringify({\n          questions: response.data,\n          totalQuestions: response.totalQuestions,\n          totalPages: response.totalPages\n        }));\n        localStorage.setItem(`${cacheKey}_time`, now.toString());\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    fetchQuestions(currentPage).finally(() => {\n      setIsInitialLoad(false);\n    });\n  }, [currentPage, limit]);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n          setUserData(response.data);\n          await fetchQuestions();\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n  const handleEdit = question => {\n    setEditQuestion(question);\n  };\n  const handleDelete = async question => {\n    try {\n      const confirmDelete = window.confirm(\"Are you sure you want to delete this question?\");\n      if (!confirmDelete) {\n        return;\n      }\n      const response = await deleteQuestion(question._id);\n      if (response.success) {\n        message.success(response.message);\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleUpdateQuestion = async values => {\n    try {\n      const response = await updateQuestion(values, editQuestion._id);\n      if (response.success) {\n        message.success(response.message);\n        setEditQuestion(null);\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleCancelUpdate = () => {\n    setEditQuestion(\"\");\n  };\n  const handleCancelAdd = () => {\n    setAskQuestionVisible(false);\n    form.resetFields();\n  };\n  useEffect(() => {\n    if (editQuestion) {\n      form2.setFieldsValue({\n        title: editQuestion.title,\n        body: editQuestion.body\n      });\n    } else {\n      form2.resetFields();\n    }\n  }, [editQuestion]);\n  const handleUpdateStatus = async (questionId, replyId, status) => {\n    try {\n      const response = await updateReplyStatus({\n        replyId,\n        status\n      }, questionId);\n      if (response.success) {\n        message.success(response.message);\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n\n  // Show skeleton on initial load\n  if (isInitialLoad && questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(ForumSkeleton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Forum max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-discuss-line text-2xl text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: [\"Community \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\",\n            children: \"Forum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 23\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-2xl mx-auto mb-8\",\n          children: \"Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setAskQuestionVisible(true),\n          className: \"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-add-line text-xl mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), \"Ask a Question\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-4\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"ri-question-line text-white text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Ask a Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          onFinish: handleAskQuestion,\n          layout: \"vertical\",\n          className: \"modern-form\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"title\",\n            label: \"Question Title\",\n            rules: [{\n              required: true,\n              message: \"Please enter a descriptive title\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"What would you like to know?\",\n              className: \"h-12 text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"body\",\n            label: \"Question Details\",\n            rules: [{\n              required: true,\n              message: \"Please provide more details about your question\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              rows: 6,\n              placeholder: \"Describe your question in detail. The more information you provide, the better answers you'll receive.\",\n              className: \"text-base\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            className: \"mb-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                className: \"h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"ri-send-plane-line mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this), \"Post Question\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleCancelAdd,\n                className: \"h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this), questions.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-loader-4-line text-2xl text-gray-400 animate-spin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Loading discussions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: questions.map(question => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 border-b border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                    user: question.user,\n                    size: \"sm\",\n                    showOnlineStatus: false,\n                    style: {\n                      width: '32px',\n                      height: '32px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      bottom: '-2px',\n                      right: '-2px',\n                      width: '12px',\n                      height: '12px',\n                      backgroundColor: '#22c55e',\n                      borderRadius: '50%',\n                      border: '2px solid #ffffff',\n                      boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                      zIndex: 10\n                    },\n                    title: \"Online\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: question.user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: new Date(question.createdAt).toLocaleDateString('en-US', {\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric',\n                      hour: '2-digit',\n                      minute: '2-digit'\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: (userData._id === question.user._id || userData.isAdmin) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleEdit(question),\n                    className: \"flex items-center px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\",\n                    children: /*#__PURE__*/_jsxDEV(FaPencilAlt, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 432,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDelete(question),\n                    className: \"flex items-center px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\",\n                    children: /*#__PURE__*/_jsxDEV(MdDelete, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-3\",\n              children: question.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 leading-relaxed mb-6\",\n              children: question.body\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between pt-4 border-t border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => toggleReplies(question._id),\n                  className: \"flex items-center px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"ri-eye-line mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 23\n                  }, this), expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleReply(question._id),\n                  className: \"flex items-center px-4 py-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"ri-reply-line mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 23\n                  }, this), \"Reply\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center px-3 py-2 bg-gray-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(MdMessage, {\n                  className: \"w-4 h-4 text-gray-500 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: question.replies.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this), editQuestion && editQuestion._id === question._id && /*#__PURE__*/_jsxDEV(Form, {\n            form: form2,\n            onFinish: handleUpdateQuestion,\n            layout: \"vertical\",\n            initialValues: {\n              title: editQuestion.title,\n              body: editQuestion.body\n            },\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"Title\",\n              rules: [{\n                required: true,\n                message: \"Please enter the title\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                style: {\n                  padding: \"18px 12px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"body\",\n              label: \"Body\",\n              rules: [{\n                required: true,\n                message: \"Please enter the body\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                children: \"Update Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleCancelUpdate,\n                style: {\n                  marginLeft: 10\n                },\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 17\n          }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 space-y-4 bg-gray-50 rounded-xl p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"ri-chat-3-line mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this), \"Replies (\", question.replies.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this), question.replies.map((reply, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `bg-white rounded-lg p-4 shadow-sm border-l-4 ${reply.user.isAdmin ? \"border-purple-500 bg-purple-50\" : reply.isVerified ? \"border-green-500 bg-green-50\" : \"border-gray-300\"}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 relative\",\n                  children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                    user: reply.user,\n                    size: \"xs\",\n                    showOnlineStatus: false,\n                    style: {\n                      width: '24px',\n                      height: '24px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      position: 'absolute',\n                      bottom: '-1px',\n                      right: '-1px',\n                      width: '8px',\n                      height: '8px',\n                      backgroundColor: '#22c55e',\n                      borderRadius: '50%',\n                      border: '1px solid #ffffff',\n                      boxShadow: '0 1px 4px rgba(34, 197, 94, 0.6)',\n                      zIndex: 10\n                    },\n                    title: \"Online\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: reply.user.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 569,\n                        columnNumber: 29\n                      }, this), reply.user.isAdmin && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full\",\n                        children: \"Admin\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 571,\n                        columnNumber: 31\n                      }, this), reply.isVerified && !reply.user.isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                          className: \"w-4 h-4 text-green-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 577,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full\",\n                          children: \"Verified\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 578,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 576,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: new Date(reply.createdAt).toLocaleString(undefined, {\n                        minute: \"numeric\",\n                        hour: \"numeric\",\n                        day: \"numeric\",\n                        month: \"short\",\n                        year: \"numeric\"\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-gray-700 leading-relaxed mb-3\",\n                    children: reply.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 25\n                  }, this), isAdmin && !reply.user.isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleUpdateStatus(question._id, reply._id, !reply.isVerified),\n                      className: `px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${reply.isVerified ? \"bg-red-100 text-red-700 hover:bg-red-200\" : \"bg-green-100 text-green-700 hover:bg-green-200\"}`,\n                      children: reply.isVerified ? \"Disapprove\" : \"Approve\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 603,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 21\n              }, this)\n            }, reply._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: replyRefs[question._id],\n            children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(Form, {\n              form: form,\n              onFinish: handleReplySubmit,\n              layout: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"text\",\n                label: \"Your Reply\",\n                rules: [{\n                  required: true,\n                  message: \"Please enter your reply\"\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                  rows: 4\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  children: \"Submit Reply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: () => setReplyQuestionId(null),\n                  style: {\n                    marginLeft: 10\n                  },\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 13\n          }, this)]\n        }, question._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n        current: currentPage,\n        total: totalQuestions,\n        pageSize: limit,\n        onChange: handlePageChange,\n        style: {\n          marginTop: \"20px\",\n          textAlign: \"center\"\n        },\n        showSizeChanger: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 287,\n    columnNumber: 5\n  }, this);\n};\n_s(Forum, \"Zrv9MCUT7Yzr9Tan4Kbha7tdvHo=\", false, function () {\n  return [Form.useForm, Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Pagination", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "ProfilePicture", "OnlineStatusIndicator", "addQuestion", "addReply", "getAllQuestions", "deleteQuestion", "updateQuestion", "updateReplyStatus", "image", "FaPencilAlt", "MdDelete", "MdMessage", "FaCheck", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "editQuestion", "setEditQuestion", "form", "useForm", "isInitialLoad", "setIsInitialLoad", "ForumSkeleton", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "form2", "dispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "limit", "totalQuestions", "setTotalQuestions", "fetchQuestions", "page", "cache<PERSON>ey", "cachedData", "localStorage", "getItem", "cacheTime", "now", "Date", "parseInt", "cached", "JSON", "parse", "response", "success", "console", "log", "data", "setItem", "stringify", "toString", "error", "finally", "handlePageChange", "getUserData", "toggleReplies", "questionId", "prevExpandedReplies", "handleAskQuestion", "values", "resetFields", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "handleEdit", "question", "handleDelete", "confirmDelete", "window", "confirm", "_id", "handleUpdateQuestion", "handleCancelUpdate", "handleCancelAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "body", "handleUpdateStatus", "replyId", "status", "length", "onClick", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "TextArea", "rows", "type", "htmlType", "user", "size", "showOnlineStatus", "style", "width", "height", "position", "bottom", "right", "backgroundColor", "borderRadius", "border", "boxShadow", "zIndex", "createdAt", "toLocaleDateString", "year", "month", "day", "hour", "minute", "replies", "initialValues", "padding", "marginLeft", "reply", "index", "isVerified", "toLocaleString", "undefined", "ref", "total", "pageSize", "onChange", "marginTop", "textAlign", "showSizeChanger", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport OnlineStatusIndicator from \"../../../components/common/OnlineStatusIndicator\";\r\nimport {\r\n  addQuestion,\r\n  addReply,\r\n  getAllQuestions,\r\n  deleteQuestion,\r\n  updateQuestion,\r\n  updateReplyStatus,\r\n} from \"../../../apicalls/forum\";\r\nimport image from \"../../../assets/person.png\";\r\nimport { FaPencilAlt } from \"react-icons/fa\";\r\nimport { MdDelete, MdMessage } from \"react-icons/md\";\r\nimport { FaCheck } from \"react-icons/fa\";\r\n\r\nconst Forum = () => {\r\n  const [isAdmin, setIsAdmin] = useState(false);\r\n  const [userData, setUserData] = useState(\"\");\r\n  const [questions, setQuestions] = useState([]);\r\n  const [expandedReplies, setExpandedReplies] = useState({});\r\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n  const [editQuestion, setEditQuestion] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\r\n\r\n  // Skeleton loader component\r\n  const ForumSkeleton = () => (\r\n    <div className=\"forum-container\">\r\n      <div className=\"forum-header\">\r\n        <div className=\"h-8 bg-gray-200 rounded w-48 mb-4 animate-pulse\"></div>\r\n        <div className=\"h-10 bg-blue-100 rounded w-32 animate-pulse\"></div>\r\n      </div>\r\n      <div className=\"forum-content\">\r\n        {[...Array(5)].map((_, i) => (\r\n          <div key={i} className=\"question-card mb-4 p-4 border rounded-lg\">\r\n            <div className=\"flex items-start space-x-3\">\r\n              <div className=\"w-10 h-10 bg-gray-200 rounded-full animate-pulse\"></div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-1/2 mb-3 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-full mb-2 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-2/3 animate-pulse\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n  const [form2] = Form.useForm();\r\n  const dispatch = useDispatch();\r\n  const [replyRefs, setReplyRefs] = useState({});\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(10);\r\n  const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n  const fetchQuestions = async (page) => {\r\n    try {\r\n      // Check cache first\r\n      const cacheKey = `forum_questions_${page}_${limit}`;\r\n      const cachedData = localStorage.getItem(cacheKey);\r\n      const cacheTime = localStorage.getItem(`${cacheKey}_time`);\r\n      const now = Date.now();\r\n\r\n      // Use cache if less than 5 minutes old\r\n      if (cachedData && cacheTime && (now - parseInt(cacheTime)) < 300000) {\r\n        const cached = JSON.parse(cachedData);\r\n        setQuestions(cached.questions);\r\n        setTotalQuestions(cached.totalQuestions);\r\n        setTotalPages(cached.totalPages);\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n      const response = await getAllQuestions({ page, limit }); // Pass query params to API call\r\n      if (response.success) {\r\n        console.log(response.data);\r\n        setQuestions(response.data); // No need to reverse as backend will handle order\r\n        setTotalQuestions(response.totalQuestions);\r\n        setTotalPages(response.totalPages);\r\n\r\n        // Cache the data\r\n        localStorage.setItem(cacheKey, JSON.stringify({\r\n          questions: response.data,\r\n          totalQuestions: response.totalQuestions,\r\n          totalPages: response.totalPages\r\n        }));\r\n        localStorage.setItem(`${cacheKey}_time`, now.toString());\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchQuestions(currentPage).finally(() => {\r\n      setIsInitialLoad(false);\r\n    });\r\n  }, [currentPage, limit]);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        if (response.data.isAdmin) {\r\n          setIsAdmin(true);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        } else {\r\n          setIsAdmin(false);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const toggleReplies = (questionId) => {\r\n    setExpandedReplies((prevExpandedReplies) => ({\r\n      ...prevExpandedReplies,\r\n      [questionId]: !prevExpandedReplies[questionId],\r\n    }));\r\n  };\r\n\r\n  const handleAskQuestion = async (values) => {\r\n    try {\r\n      const response = await addQuestion(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setAskQuestionVisible(false);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleReply = (questionId) => {\r\n    setReplyQuestionId(questionId);\r\n  };\r\n\r\n  const handleReplySubmit = async (values) => {\r\n    try {\r\n      const payload = {\r\n        questionId: replyQuestionId,\r\n        text: values.text,\r\n      };\r\n      const response = await addReply(payload);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setReplyQuestionId(null);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n      setReplyRefs((prevRefs) => ({\r\n        ...prevRefs,\r\n        [replyQuestionId]: React.createRef(),\r\n      }));\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n      replyRefs[replyQuestionId].current.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  const handleEdit = (question) => {\r\n    setEditQuestion(question);\r\n  };\r\n\r\n  const handleDelete = async (question) => {\r\n    try {\r\n      const confirmDelete = window.confirm(\r\n        \"Are you sure you want to delete this question?\"\r\n      );\r\n      if (!confirmDelete) {\r\n        return;\r\n      }\r\n      const response = await deleteQuestion(question._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleUpdateQuestion = async (values) => {\r\n    try {\r\n      const response = await updateQuestion(values, editQuestion._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEditQuestion(null);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleCancelUpdate = () => {\r\n    setEditQuestion(\"\");\r\n  };\r\n  const handleCancelAdd = () => {\r\n    setAskQuestionVisible(false);\r\n    form.resetFields();\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editQuestion) {\r\n      form2.setFieldsValue({\r\n        title: editQuestion.title,\r\n        body: editQuestion.body,\r\n      });\r\n    } else {\r\n      form2.resetFields();\r\n    }\r\n  }, [editQuestion]);\r\n\r\n  const handleUpdateStatus = async (questionId, replyId, status) => {\r\n    try {\r\n      const response = await updateReplyStatus({ replyId, status }, questionId);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  // Show skeleton on initial load\r\n  if (isInitialLoad && questions.length === 0) {\r\n    return <ForumSkeleton />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"Forum max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Modern Header Section */}\r\n        <div className=\"text-center mb-12\">\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\">\r\n            <i className=\"ri-discuss-line text-2xl text-white\"></i>\r\n          </div>\r\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\r\n            Community <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Forum</span>\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto mb-8\">\r\n            Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community.\r\n          </p>\r\n\r\n          {/* Ask Question Button */}\r\n          <button\r\n            onClick={() => setAskQuestionVisible(true)}\r\n            className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300\"\r\n          >\r\n            <i className=\"ri-add-line text-xl mr-2\"></i>\r\n            Ask a Question\r\n          </button>\r\n        </div>\r\n\r\n        {/* Modern Ask Question Form */}\r\n        {askQuestionVisible && (\r\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100\">\r\n            <div className=\"flex items-center mb-6\">\r\n              <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-4\">\r\n                <i className=\"ri-question-line text-white text-lg\"></i>\r\n              </div>\r\n              <h2 className=\"text-2xl font-bold text-gray-900\">Ask a Question</h2>\r\n            </div>\r\n\r\n            <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\" className=\"modern-form\">\r\n              <Form.Item\r\n                name=\"title\"\r\n                label=\"Question Title\"\r\n                rules={[{ required: true, message: \"Please enter a descriptive title\" }]}\r\n              >\r\n                <Input\r\n                  placeholder=\"What would you like to know?\"\r\n                  className=\"h-12 text-lg\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"body\"\r\n                label=\"Question Details\"\r\n                rules={[{ required: true, message: \"Please provide more details about your question\" }]}\r\n              >\r\n                <Input.TextArea\r\n                  rows={6}\r\n                  placeholder=\"Describe your question in detail. The more information you provide, the better answers you'll receive.\"\r\n                  className=\"text-base\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item className=\"mb-0\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <Button\r\n                    type=\"primary\"\r\n                    htmlType=\"submit\"\r\n                    className=\"h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base\"\r\n                  >\r\n                    <i className=\"ri-send-plane-line mr-2\"></i>\r\n                    Post Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelAdd}\r\n                    className=\"h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50\"\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </div>\r\n              </Form.Item>\r\n            </Form>\r\n          </div>\r\n        )}\r\n\r\n        {/* Loading State */}\r\n        {questions.length === 0 && (\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4\">\r\n              <i className=\"ri-loader-4-line text-2xl text-gray-400 animate-spin\"></i>\r\n            </div>\r\n            <p className=\"text-gray-500\">Loading discussions...</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Questions Grid */}\r\n        <div className=\"space-y-6\">\r\n          {questions.map((question) => (\r\n            <div key={question._id} className=\"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\">\r\n              {/* Question Header */}\r\n              <div className=\"p-6 border-b border-gray-100\">\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"relative\">\r\n                      <ProfilePicture\r\n                        user={question.user}\r\n                        size=\"sm\"\r\n                        showOnlineStatus={false}\r\n                        style={{\r\n                          width: '32px',\r\n                          height: '32px'\r\n                        }}\r\n                      />\r\n                      {/* Visible Online Status Dot */}\r\n                      <div\r\n                        style={{\r\n                          position: 'absolute',\r\n                          bottom: '-2px',\r\n                          right: '-2px',\r\n                          width: '12px',\r\n                          height: '12px',\r\n                          backgroundColor: '#22c55e',\r\n                          borderRadius: '50%',\r\n                          border: '2px solid #ffffff',\r\n                          boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\r\n                          zIndex: 10\r\n                        }}\r\n                        title=\"Online\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"font-semibold text-gray-900\">{question.user.name}</h4>\r\n                      <p className=\"text-sm text-gray-500\">\r\n                        {new Date(question.createdAt).toLocaleDateString('en-US', {\r\n                          year: 'numeric',\r\n                          month: 'long',\r\n                          day: 'numeric',\r\n                          hour: '2-digit',\r\n                          minute: '2-digit'\r\n                        })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Action Buttons */}\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    {(userData._id === question.user._id || userData.isAdmin) && (\r\n                      <>\r\n                        <button\r\n                          onClick={() => handleEdit(question)}\r\n                          className=\"flex items-center px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <FaPencilAlt className=\"w-4 h-4\" />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(question)}\r\n                          className=\"flex items-center px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <MdDelete className=\"w-4 h-4\" />\r\n                        </button>\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Question Content */}\r\n              <div className=\"p-6\">\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{question.title}</h3>\r\n                <p className=\"text-gray-700 leading-relaxed mb-6\">{question.body}</p>\r\n\r\n                {/* Action Bar */}\r\n                <div className=\"flex items-center justify-between pt-4 border-t border-gray-100\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <button\r\n                      onClick={() => toggleReplies(question._id)}\r\n                      className=\"flex items-center px-4 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\"\r\n                    >\r\n                      <i className=\"ri-eye-line mr-2\"></i>\r\n                      {expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"}\r\n                    </button>\r\n                    <button\r\n                      onClick={() => handleReply(question._id)}\r\n                      className=\"flex items-center px-4 py-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200\"\r\n                    >\r\n                      <i className=\"ri-reply-line mr-2\"></i>\r\n                      Reply\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center px-3 py-2 bg-gray-50 rounded-lg\">\r\n                    <MdMessage className=\"w-4 h-4 text-gray-500 mr-2\" />\r\n                    <span className=\"text-sm font-medium text-gray-700\">{question.replies.length}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Edit Question Form */}\r\n              {editQuestion && editQuestion._id === question._id && (\r\n                <Form\r\n                form={form2}\r\n                onFinish={handleUpdateQuestion}\r\n                layout=\"vertical\"\r\n                initialValues={{\r\n                  title: editQuestion.title,\r\n                  body: editQuestion.body,\r\n                }}\r\n              >\r\n                <Form.Item\r\n                  name=\"title\"\r\n                  label=\"Title\"\r\n                  rules={[\r\n                    { required: true, message: \"Please enter the title\" },\r\n                  ]}\r\n                >\r\n                  <Input style={{ padding: \"18px 12px\" }} />\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"body\"\r\n                  label=\"Body\"\r\n                  rules={[{ required: true, message: \"Please enter the body\" }]}\r\n                >\r\n                  <Input.TextArea />\r\n                </Form.Item>\r\n                <Form.Item>\r\n                  <Button type=\"primary\" htmlType=\"submit\">\r\n                    Update Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelUpdate}\r\n                    style={{ marginLeft: 10 }}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </Form.Item>\r\n              </Form>\r\n              )}\r\n            {expandedReplies[question._id] && (\r\n              <div className=\"mt-6 space-y-4 bg-gray-50 rounded-xl p-4\">\r\n                <h4 className=\"text-lg font-semibold text-gray-800 mb-4 flex items-center\">\r\n                  <i className=\"ri-chat-3-line mr-2 text-blue-600\"></i>\r\n                  Replies ({question.replies.length})\r\n                </h4>\r\n                {question.replies.map((reply, index) => (\r\n                  <div\r\n                    key={reply._id}\r\n                    className={`bg-white rounded-lg p-4 shadow-sm border-l-4 ${\r\n                      reply.user.isAdmin\r\n                        ? \"border-purple-500 bg-purple-50\"\r\n                        : reply.isVerified\r\n                        ? \"border-green-500 bg-green-50\"\r\n                        : \"border-gray-300\"\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start space-x-3\">\r\n                      {/* Avatar with Online Status */}\r\n                      <div className=\"flex-shrink-0 relative\">\r\n                        <ProfilePicture\r\n                          user={reply.user}\r\n                          size=\"xs\"\r\n                          showOnlineStatus={false}\r\n                          style={{\r\n                            width: '24px',\r\n                            height: '24px'\r\n                          }}\r\n                        />\r\n                        {/* Visible Online Status Dot */}\r\n                        <div\r\n                          style={{\r\n                            position: 'absolute',\r\n                            bottom: '-1px',\r\n                            right: '-1px',\r\n                            width: '8px',\r\n                            height: '8px',\r\n                            backgroundColor: '#22c55e',\r\n                            borderRadius: '50%',\r\n                            border: '1px solid #ffffff',\r\n                            boxShadow: '0 1px 4px rgba(34, 197, 94, 0.6)',\r\n                            zIndex: 10\r\n                          }}\r\n                          title=\"Online\"\r\n                        />\r\n                      </div>\r\n\r\n                      {/* Reply Content */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        {/* Header */}\r\n                        <div className=\"flex items-center justify-between mb-2\">\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <h5 className=\"font-semibold text-gray-900\">{reply.user.name}</h5>\r\n                            {reply.user.isAdmin && (\r\n                              <span className=\"px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full\">\r\n                                Admin\r\n                              </span>\r\n                            )}\r\n                            {reply.isVerified && !reply.user.isAdmin && (\r\n                              <div className=\"flex items-center space-x-1\">\r\n                                <FaCheck className=\"w-4 h-4 text-green-600\" />\r\n                                <span className=\"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full\">\r\n                                  Verified\r\n                                </span>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          <span className=\"text-sm text-gray-500\">\r\n                            {new Date(reply.createdAt).toLocaleString(undefined, {\r\n                              minute: \"numeric\",\r\n                              hour: \"numeric\",\r\n                              day: \"numeric\",\r\n                              month: \"short\",\r\n                              year: \"numeric\",\r\n                            })}\r\n                          </span>\r\n                        </div>\r\n\r\n                        {/* Reply Text */}\r\n                        <div className=\"text-gray-700 leading-relaxed mb-3\">\r\n                          {reply.text}\r\n                        </div>\r\n\r\n                        {/* Admin Actions */}\r\n                        {isAdmin && !reply.user.isAdmin && (\r\n                          <div className=\"flex justify-end\">\r\n                            <button\r\n                              onClick={() =>\r\n                                handleUpdateStatus(\r\n                                  question._id,\r\n                                  reply._id,\r\n                                  !reply.isVerified\r\n                                )\r\n                              }\r\n                              className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${\r\n                                reply.isVerified\r\n                                  ? \"bg-red-100 text-red-700 hover:bg-red-200\"\r\n                                  : \"bg-green-100 text-green-700 hover:bg-green-200\"\r\n                              }`}\r\n                            >\r\n                              {reply.isVerified ? \"Disapprove\" : \"Approve\"}\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <div ref={replyRefs[question._id]}>\r\n              {replyQuestionId === question._id && (\r\n                <Form\r\n                  form={form}\r\n                  onFinish={handleReplySubmit}\r\n                  layout=\"vertical\"\r\n                >\r\n                  <Form.Item\r\n                    name=\"text\"\r\n                    label=\"Your Reply\"\r\n                    rules={[\r\n                      { required: true, message: \"Please enter your reply\" },\r\n                    ]}\r\n                  >\r\n                    <Input.TextArea rows={4} />\r\n                  </Form.Item>\r\n                  <Form.Item>\r\n                    <Button type=\"primary\" htmlType=\"submit\">\r\n                      Submit Reply\r\n                    </Button>\r\n                    <Button\r\n                      onClick={() => setReplyQuestionId(null)}\r\n                      style={{ marginLeft: 10 }}\r\n                    >\r\n                      Cancel\r\n                    </Button>\r\n                  </Form.Item>\r\n                </Form>\r\n              )}\r\n            </div>\r\n            </div>\r\n          ))}\r\n\r\n        </div>\r\n\r\n        <Pagination\r\n          current={currentPage}\r\n          total={totalQuestions}\r\n          pageSize={limit}\r\n          onChange={handlePageChange}\r\n          style={{ marginTop: \"20px\", textAlign: \"center\" }}\r\n          showSizeChanger={false}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACvE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,qBAAqB,MAAM,kDAAkD;AACpF,SACEC,WAAW,EACXC,QAAQ,EACRC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,iBAAiB,QACZ,yBAAyB;AAChC,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACyC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+C,IAAI,CAAC,GAAGxC,IAAI,CAACyC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAMmD,aAAa,GAAGA,CAAA,kBACpBvB,OAAA;IAAKwB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BzB,OAAA;MAAKwB,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BzB,OAAA;QAAKwB,SAAS,EAAC;MAAiD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvE7B,OAAA;QAAKwB,SAAS,EAAC;MAA6C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eACN7B,OAAA;MAAKwB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3B,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBjC,OAAA;QAAawB,SAAS,EAAC,0CAA0C;QAAAC,QAAA,eAC/DzB,OAAA;UAAKwB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCzB,OAAA;YAAKwB,SAAS,EAAC;UAAkD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxE7B,OAAA;YAAKwB,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBzB,OAAA;cAAKwB,SAAS,EAAC;YAAkD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxE7B,OAAA;cAAKwB,SAAS,EAAC;YAAkD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxE7B,OAAA;cAAKwB,SAAS,EAAC;YAAmD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzE7B,OAAA;cAAKwB,SAAS,EAAC;YAA6C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GATEI,CAAC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUN,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EACD,MAAM,CAACK,KAAK,CAAC,GAAGvD,IAAI,CAACyC,OAAO,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGpD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACsE,KAAK,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAC5B,MAAM,CAACuE,cAAc,EAAEC,iBAAiB,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAMyE,cAAc,GAAG,MAAOC,IAAI,IAAK;IACrC,IAAI;MACF;MACA,MAAMC,QAAQ,GAAI,mBAAkBD,IAAK,IAAGJ,KAAM,EAAC;MACnD,MAAMM,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACH,QAAQ,CAAC;MACjD,MAAMI,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAE,GAAEH,QAAS,OAAM,CAAC;MAC1D,MAAMK,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIJ,UAAU,IAAIG,SAAS,IAAKC,GAAG,GAAGE,QAAQ,CAACH,SAAS,CAAC,GAAI,MAAM,EAAE;QACnE,MAAMI,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACT,UAAU,CAAC;QACrCtC,YAAY,CAAC6C,MAAM,CAAC9C,SAAS,CAAC;QAC9BmC,iBAAiB,CAACW,MAAM,CAACZ,cAAc,CAAC;QACxCF,aAAa,CAACc,MAAM,CAACf,UAAU,CAAC;QAChC;MACF;MAEAL,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMyE,QAAQ,GAAG,MAAMpE,eAAe,CAAC;QAAEwD,IAAI;QAAEJ;MAAM,CAAC,CAAC,CAAC,CAAC;MACzD,IAAIgB,QAAQ,CAACC,OAAO,EAAE;QACpBC,OAAO,CAACC,GAAG,CAACH,QAAQ,CAACI,IAAI,CAAC;QAC1BpD,YAAY,CAACgD,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC;QAC7BlB,iBAAiB,CAACc,QAAQ,CAACf,cAAc,CAAC;QAC1CF,aAAa,CAACiB,QAAQ,CAAClB,UAAU,CAAC;;QAElC;QACAS,YAAY,CAACc,OAAO,CAAChB,QAAQ,EAAES,IAAI,CAACQ,SAAS,CAAC;UAC5CvD,SAAS,EAAEiD,QAAQ,CAACI,IAAI;UACxBnB,cAAc,EAAEe,QAAQ,CAACf,cAAc;UACvCH,UAAU,EAAEkB,QAAQ,CAAClB;QACvB,CAAC,CAAC,CAAC;QACHS,YAAY,CAACc,OAAO,CAAE,GAAEhB,QAAS,OAAM,EAAEK,GAAG,CAACa,QAAQ,CAAC,CAAC,CAAC;MAC1D,CAAC,MAAM;QACLzF,OAAO,CAAC0F,KAAK,CAACR,QAAQ,CAAClF,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO0F,KAAK,EAAE;MACd1F,OAAO,CAAC0F,KAAK,CAACA,KAAK,CAAC1F,OAAO,CAAC;IAC9B,CAAC,SAAS;MACR2D,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDX,SAAS,CAAC,MAAM;IACdwE,cAAc,CAACP,WAAW,CAAC,CAAC6B,OAAO,CAAC,MAAM;MACxC7C,gBAAgB,CAAC,KAAK,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACgB,WAAW,EAAEI,KAAK,CAAC,CAAC;EAExB,MAAM0B,gBAAgB,GAAItB,IAAI,IAAK;IACjCP,cAAc,CAACO,IAAI,CAAC;EACtB,CAAC;EAED,MAAMuB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BlC,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMyE,QAAQ,GAAG,MAAMnF,WAAW,CAAC,CAAC;MACpC,IAAImF,QAAQ,CAACC,OAAO,EAAE;QACpB,IAAID,QAAQ,CAACI,IAAI,CAACzD,OAAO,EAAE;UACzBC,UAAU,CAAC,IAAI,CAAC;UAChBE,WAAW,CAACkD,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMjB,cAAc,CAAC,CAAC;QACxB,CAAC,MAAM;UACLvC,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACkD,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMjB,cAAc,CAAC,CAAC;QACxB;MACF,CAAC,MAAM;QACLrE,OAAO,CAAC0F,KAAK,CAACR,QAAQ,CAAClF,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO0F,KAAK,EAAE;MACd1F,OAAO,CAAC0F,KAAK,CAACA,KAAK,CAAC1F,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDH,SAAS,CAAC,MAAM;IACd,IAAI4E,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCmB,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,aAAa,GAAIC,UAAU,IAAK;IACpC3D,kBAAkB,CAAE4D,mBAAmB,KAAM;MAC3C,GAAGA,mBAAmB;MACtB,CAACD,UAAU,GAAG,CAACC,mBAAmB,CAACD,UAAU;IAC/C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IAC1C,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMtE,WAAW,CAACsF,MAAM,CAAC;MAC1C,IAAIhB,QAAQ,CAACC,OAAO,EAAE;QACpBnF,OAAO,CAACmF,OAAO,CAACD,QAAQ,CAAClF,OAAO,CAAC;QACjCsC,qBAAqB,CAAC,KAAK,CAAC;QAC5BK,IAAI,CAACwD,WAAW,CAAC,CAAC;QAClB,MAAM9B,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLrE,OAAO,CAAC0F,KAAK,CAACR,QAAQ,CAAClF,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO0F,KAAK,EAAE;MACd1F,OAAO,CAAC0F,KAAK,CAACA,KAAK,CAAC1F,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMoG,WAAW,GAAIL,UAAU,IAAK;IAClCvD,kBAAkB,CAACuD,UAAU,CAAC;EAChC,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IAC1C,IAAI;MACF,MAAMI,OAAO,GAAG;QACdP,UAAU,EAAExD,eAAe;QAC3BgE,IAAI,EAAEL,MAAM,CAACK;MACf,CAAC;MACD,MAAMrB,QAAQ,GAAG,MAAMrE,QAAQ,CAACyF,OAAO,CAAC;MACxC,IAAIpB,QAAQ,CAACC,OAAO,EAAE;QACpBnF,OAAO,CAACmF,OAAO,CAACD,QAAQ,CAAClF,OAAO,CAAC;QACjCwC,kBAAkB,CAAC,IAAI,CAAC;QACxBG,IAAI,CAACwD,WAAW,CAAC,CAAC;QAClB,MAAM9B,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLrE,OAAO,CAAC0F,KAAK,CAACR,QAAQ,CAAClF,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO0F,KAAK,EAAE;MACd1F,OAAO,CAAC0F,KAAK,CAACA,KAAK,CAAC1F,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDH,SAAS,CAAC,MAAM;IACd,IAAI0C,eAAe,IAAI,CAACqB,SAAS,CAACrB,eAAe,CAAC,EAAE;MAClDsB,YAAY,CAAE2C,QAAQ,KAAM;QAC1B,GAAGA,QAAQ;QACX,CAACjE,eAAe,gBAAG5C,KAAK,CAAC8G,SAAS,CAAC;MACrC,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAAClE,eAAe,EAAEqB,SAAS,CAAC,CAAC;EAEhC/D,SAAS,CAAC,MAAM;IACd,IAAI0C,eAAe,IAAIqB,SAAS,CAACrB,eAAe,CAAC,EAAE;MACjDqB,SAAS,CAACrB,eAAe,CAAC,CAACmE,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAACrE,eAAe,EAAEqB,SAAS,CAAC,CAAC;EAEhC,MAAMiD,UAAU,GAAIC,QAAQ,IAAK;IAC/BpE,eAAe,CAACoE,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOD,QAAQ,IAAK;IACvC,IAAI;MACF,MAAME,aAAa,GAAGC,MAAM,CAACC,OAAO,CAClC,gDACF,CAAC;MACD,IAAI,CAACF,aAAa,EAAE;QAClB;MACF;MACA,MAAM9B,QAAQ,GAAG,MAAMnE,cAAc,CAAC+F,QAAQ,CAACK,GAAG,CAAC;MACnD,IAAIjC,QAAQ,CAACC,OAAO,EAAE;QACpBnF,OAAO,CAACmF,OAAO,CAACD,QAAQ,CAAClF,OAAO,CAAC;QACjC,MAAMqE,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLrE,OAAO,CAAC0F,KAAK,CAACR,QAAQ,CAAClF,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO0F,KAAK,EAAE;MACd1F,OAAO,CAAC0F,KAAK,CAACA,KAAK,CAAC1F,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMoH,oBAAoB,GAAG,MAAOlB,MAAM,IAAK;IAC7C,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMlE,cAAc,CAACkF,MAAM,EAAEzD,YAAY,CAAC0E,GAAG,CAAC;MAC/D,IAAIjC,QAAQ,CAACC,OAAO,EAAE;QACpBnF,OAAO,CAACmF,OAAO,CAACD,QAAQ,CAAClF,OAAO,CAAC;QACjC0C,eAAe,CAAC,IAAI,CAAC;QACrB,MAAM2B,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLrE,OAAO,CAAC0F,KAAK,CAACR,QAAQ,CAAClF,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO0F,KAAK,EAAE;MACd1F,OAAO,CAAC0F,KAAK,CAACA,KAAK,CAAC1F,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMqH,kBAAkB,GAAGA,CAAA,KAAM;IAC/B3E,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAM4E,eAAe,GAAGA,CAAA,KAAM;IAC5BhF,qBAAqB,CAAC,KAAK,CAAC;IAC5BK,IAAI,CAACwD,WAAW,CAAC,CAAC;EACpB,CAAC;EAEDtG,SAAS,CAAC,MAAM;IACd,IAAI4C,YAAY,EAAE;MAChBiB,KAAK,CAAC6D,cAAc,CAAC;QACnBC,KAAK,EAAE/E,YAAY,CAAC+E,KAAK;QACzBC,IAAI,EAAEhF,YAAY,CAACgF;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL/D,KAAK,CAACyC,WAAW,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAC1D,YAAY,CAAC,CAAC;EAElB,MAAMiF,kBAAkB,GAAG,MAAAA,CAAO3B,UAAU,EAAE4B,OAAO,EAAEC,MAAM,KAAK;IAChE,IAAI;MACF,MAAM1C,QAAQ,GAAG,MAAMjE,iBAAiB,CAAC;QAAE0G,OAAO;QAAEC;MAAO,CAAC,EAAE7B,UAAU,CAAC;MACzE,IAAIb,QAAQ,CAACC,OAAO,EAAE;QACpBnF,OAAO,CAACmF,OAAO,CAACD,QAAQ,CAAClF,OAAO,CAAC;QACjC,MAAMqE,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLrE,OAAO,CAAC0F,KAAK,CAACR,QAAQ,CAAClF,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO0F,KAAK,EAAE;MACd1F,OAAO,CAAC0F,KAAK,CAACA,KAAK,CAAC1F,OAAO,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,IAAI6C,aAAa,IAAIZ,SAAS,CAAC4F,MAAM,KAAK,CAAC,EAAE;IAC3C,oBAAOrG,OAAA,CAACuB,aAAa;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1B;EAEA,oBACE7B,OAAA;IAAKwB,SAAS,EAAC,oEAAoE;IAAAC,QAAA,eACjFzB,OAAA;MAAKwB,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAEhEzB,OAAA;QAAKwB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzB,OAAA;UAAKwB,SAAS,EAAC,2HAA2H;UAAAC,QAAA,eACxIzB,OAAA;YAAGwB,SAAS,EAAC;UAAqC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACN7B,OAAA;UAAIwB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GAAC,YAC1C,eAAAzB,OAAA;YAAMwB,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjH,CAAC,eACL7B,OAAA;UAAGwB,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGJ7B,OAAA;UACEsG,OAAO,EAAEA,CAAA,KAAMxF,qBAAqB,CAAC,IAAI,CAAE;UAC3CU,SAAS,EAAC,qMAAqM;UAAAC,QAAA,gBAE/MzB,OAAA;YAAGwB,SAAS,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLhB,kBAAkB,iBACjBb,OAAA;QAAKwB,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7EzB,OAAA;UAAKwB,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCzB,OAAA;YAAKwB,SAAS,EAAC,yGAAyG;YAAAC,QAAA,eACtHzB,OAAA;cAAGwB,SAAS,EAAC;YAAqC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACN7B,OAAA;YAAIwB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAEN7B,OAAA,CAACrB,IAAI;UAACwC,IAAI,EAAEA,IAAK;UAACoF,QAAQ,EAAE9B,iBAAkB;UAAC+B,MAAM,EAAC,UAAU;UAAChF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACtFzB,OAAA,CAACrB,IAAI,CAAC8H,IAAI;YACRC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,gBAAgB;YACtBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAErI,OAAO,EAAE;YAAmC,CAAC,CAAE;YAAAiD,QAAA,eAEzEzB,OAAA,CAACtB,KAAK;cACJoI,WAAW,EAAC,8BAA8B;cAC1CtF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZ7B,OAAA,CAACrB,IAAI,CAAC8H,IAAI;YACRC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAC,kBAAkB;YACxBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAErI,OAAO,EAAE;YAAkD,CAAC,CAAE;YAAAiD,QAAA,eAExFzB,OAAA,CAACtB,KAAK,CAACqI,QAAQ;cACbC,IAAI,EAAE,CAAE;cACRF,WAAW,EAAC,wGAAwG;cACpHtF,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZ7B,OAAA,CAACrB,IAAI,CAAC8H,IAAI;YAACjF,SAAS,EAAC,MAAM;YAAAC,QAAA,eACzBzB,OAAA;cAAKwB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CzB,OAAA,CAACvB,MAAM;gBACLwI,IAAI,EAAC,SAAS;gBACdC,QAAQ,EAAC,QAAQ;gBACjB1F,SAAS,EAAC,uGAAuG;gBAAAC,QAAA,gBAEjHzB,OAAA;kBAAGwB,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,iBAE7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7B,OAAA,CAACvB,MAAM;gBACL6H,OAAO,EAAER,eAAgB;gBACzBtE,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,EAC1F;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGApB,SAAS,CAAC4F,MAAM,KAAK,CAAC,iBACrBrG,OAAA;QAAKwB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzB,OAAA;UAAKwB,SAAS,EAAC,iFAAiF;UAAAC,QAAA,eAC9FzB,OAAA;YAAGwB,SAAS,EAAC;UAAsD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACN7B,OAAA;UAAGwB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACN,eAGD7B,OAAA;QAAKwB,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBhB,SAAS,CAACsB,GAAG,CAAEuD,QAAQ,iBACtBtF,OAAA;UAAwBwB,SAAS,EAAC,mHAAmH;UAAAC,QAAA,gBAEnJzB,OAAA;YAAKwB,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CzB,OAAA;cAAKwB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CzB,OAAA;gBAAKwB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CzB,OAAA;kBAAKwB,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBzB,OAAA,CAACd,cAAc;oBACbiI,IAAI,EAAE7B,QAAQ,CAAC6B,IAAK;oBACpBC,IAAI,EAAC,IAAI;oBACTC,gBAAgB,EAAE,KAAM;oBACxBC,KAAK,EAAE;sBACLC,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE;oBACV;kBAAE;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEF7B,OAAA;oBACEsH,KAAK,EAAE;sBACLG,QAAQ,EAAE,UAAU;sBACpBC,MAAM,EAAE,MAAM;sBACdC,KAAK,EAAE,MAAM;sBACbJ,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdI,eAAe,EAAE,SAAS;sBAC1BC,YAAY,EAAE,KAAK;sBACnBC,MAAM,EAAE,mBAAmB;sBAC3BC,SAAS,EAAE,kCAAkC;sBAC7CC,MAAM,EAAE;oBACV,CAAE;oBACFhC,KAAK,EAAC;kBAAQ;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN7B,OAAA;kBAAAyB,QAAA,gBACEzB,OAAA;oBAAIwB,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAE6D,QAAQ,CAAC6B,IAAI,CAACT;kBAAI;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrE7B,OAAA;oBAAGwB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjC,IAAI4B,IAAI,CAACiC,QAAQ,CAAC2C,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;sBACxDC,IAAI,EAAE,SAAS;sBACfC,KAAK,EAAE,MAAM;sBACbC,GAAG,EAAE,SAAS;sBACdC,IAAI,EAAE,SAAS;sBACfC,MAAM,EAAE;oBACV,CAAC;kBAAC;oBAAA7G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN7B,OAAA;gBAAKwB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACzC,CAAClB,QAAQ,CAACoF,GAAG,KAAKL,QAAQ,CAAC6B,IAAI,CAACxB,GAAG,IAAIpF,QAAQ,CAACF,OAAO,kBACtDL,OAAA,CAAAE,SAAA;kBAAAuB,QAAA,gBACEzB,OAAA;oBACEsG,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACC,QAAQ,CAAE;oBACpC9D,SAAS,EAAC,sGAAsG;oBAAAC,QAAA,eAEhHzB,OAAA,CAACL,WAAW;sBAAC6B,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACT7B,OAAA;oBACEsG,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAACD,QAAQ,CAAE;oBACtC9D,SAAS,EAAC,oGAAoG;oBAAAC,QAAA,eAE9GzB,OAAA,CAACJ,QAAQ;sBAAC4B,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA,eACT;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7B,OAAA;YAAKwB,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBzB,OAAA;cAAIwB,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAE6D,QAAQ,CAACU;YAAK;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1E7B,OAAA;cAAGwB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAE6D,QAAQ,CAACW;YAAI;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAGrE7B,OAAA;cAAKwB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EzB,OAAA;gBAAKwB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CzB,OAAA;kBACEsG,OAAO,EAAEA,CAAA,KAAMhC,aAAa,CAACgB,QAAQ,CAACK,GAAG,CAAE;kBAC3CnE,SAAS,EAAC,0HAA0H;kBAAAC,QAAA,gBAEpIzB,OAAA;oBAAGwB,SAAS,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACnClB,eAAe,CAAC2E,QAAQ,CAACK,GAAG,CAAC,GAAG,cAAc,GAAG,cAAc;gBAAA;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACT7B,OAAA;kBACEsG,OAAO,EAAEA,CAAA,KAAM1B,WAAW,CAACU,QAAQ,CAACK,GAAG,CAAE;kBACzCnE,SAAS,EAAC,4HAA4H;kBAAAC,QAAA,gBAEtIzB,OAAA;oBAAGwB,SAAS,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,SAExC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN7B,OAAA;gBAAKwB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChEzB,OAAA,CAACH,SAAS;kBAAC2B,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpD7B,OAAA;kBAAMwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE6D,QAAQ,CAACkD,OAAO,CAACnC;gBAAM;kBAAA3E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLZ,YAAY,IAAIA,YAAY,CAAC0E,GAAG,KAAKL,QAAQ,CAACK,GAAG,iBAChD3F,OAAA,CAACrB,IAAI;YACLwC,IAAI,EAAEe,KAAM;YACZqE,QAAQ,EAAEX,oBAAqB;YAC/BY,MAAM,EAAC,UAAU;YACjBiC,aAAa,EAAE;cACbzC,KAAK,EAAE/E,YAAY,CAAC+E,KAAK;cACzBC,IAAI,EAAEhF,YAAY,CAACgF;YACrB,CAAE;YAAAxE,QAAA,gBAEFzB,OAAA,CAACrB,IAAI,CAAC8H,IAAI;cACRC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,OAAO;cACbC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAErI,OAAO,EAAE;cAAyB,CAAC,CACrD;cAAAiD,QAAA,eAEFzB,OAAA,CAACtB,KAAK;gBAAC4I,KAAK,EAAE;kBAAEoB,OAAO,EAAE;gBAAY;cAAE;gBAAAhH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACZ7B,OAAA,CAACrB,IAAI,CAAC8H,IAAI;cACRC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,MAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAErI,OAAO,EAAE;cAAwB,CAAC,CAAE;cAAAiD,QAAA,eAE9DzB,OAAA,CAACtB,KAAK,CAACqI,QAAQ;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACZ7B,OAAA,CAACrB,IAAI,CAAC8H,IAAI;cAAAhF,QAAA,gBACRzB,OAAA,CAACvB,MAAM;gBAACwI,IAAI,EAAC,SAAS;gBAACC,QAAQ,EAAC,QAAQ;gBAAAzF,QAAA,EAAC;cAEzC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7B,OAAA,CAACvB,MAAM;gBACL6H,OAAO,EAAET,kBAAmB;gBAC5ByB,KAAK,EAAE;kBAAEqB,UAAU,EAAE;gBAAG,CAAE;gBAAAlH,QAAA,EAC3B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACL,EACFlB,eAAe,CAAC2E,QAAQ,CAACK,GAAG,CAAC,iBAC5B3F,OAAA;YAAKwB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDzB,OAAA;cAAIwB,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxEzB,OAAA;gBAAGwB,SAAS,EAAC;cAAmC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,aAC5C,EAACyD,QAAQ,CAACkD,OAAO,CAACnC,MAAM,EAAC,GACpC;YAAA;cAAA3E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACJyD,QAAQ,CAACkD,OAAO,CAACzG,GAAG,CAAC,CAAC6G,KAAK,EAAEC,KAAK,kBACjC7I,OAAA;cAEEwB,SAAS,EAAG,gDACVoH,KAAK,CAACzB,IAAI,CAAC9G,OAAO,GACd,gCAAgC,GAChCuI,KAAK,CAACE,UAAU,GAChB,8BAA8B,GAC9B,iBACL,EAAE;cAAArH,QAAA,eAEHzB,OAAA;gBAAKwB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBAEzCzB,OAAA;kBAAKwB,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCzB,OAAA,CAACd,cAAc;oBACbiI,IAAI,EAAEyB,KAAK,CAACzB,IAAK;oBACjBC,IAAI,EAAC,IAAI;oBACTC,gBAAgB,EAAE,KAAM;oBACxBC,KAAK,EAAE;sBACLC,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE;oBACV;kBAAE;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEF7B,OAAA;oBACEsH,KAAK,EAAE;sBACLG,QAAQ,EAAE,UAAU;sBACpBC,MAAM,EAAE,MAAM;sBACdC,KAAK,EAAE,MAAM;sBACbJ,KAAK,EAAE,KAAK;sBACZC,MAAM,EAAE,KAAK;sBACbI,eAAe,EAAE,SAAS;sBAC1BC,YAAY,EAAE,KAAK;sBACnBC,MAAM,EAAE,mBAAmB;sBAC3BC,SAAS,EAAE,kCAAkC;sBAC7CC,MAAM,EAAE;oBACV,CAAE;oBACFhC,KAAK,EAAC;kBAAQ;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGN7B,OAAA;kBAAKwB,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAE7BzB,OAAA;oBAAKwB,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDzB,OAAA;sBAAKwB,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBAC1CzB,OAAA;wBAAIwB,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAEmH,KAAK,CAACzB,IAAI,CAACT;sBAAI;wBAAAhF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,EACjE+G,KAAK,CAACzB,IAAI,CAAC9G,OAAO,iBACjBL,OAAA;wBAAMwB,SAAS,EAAC,0EAA0E;wBAAAC,QAAA,EAAC;sBAE3F;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP,EACA+G,KAAK,CAACE,UAAU,IAAI,CAACF,KAAK,CAACzB,IAAI,CAAC9G,OAAO,iBACtCL,OAAA;wBAAKwB,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1CzB,OAAA,CAACF,OAAO;0BAAC0B,SAAS,EAAC;wBAAwB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC9C7B,OAAA;0BAAMwB,SAAS,EAAC,wEAAwE;0BAAAC,QAAA,EAAC;wBAEzF;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN7B,OAAA;sBAAMwB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpC,IAAI4B,IAAI,CAACuF,KAAK,CAACX,SAAS,CAAC,CAACc,cAAc,CAACC,SAAS,EAAE;wBACnDT,MAAM,EAAE,SAAS;wBACjBD,IAAI,EAAE,SAAS;wBACfD,GAAG,EAAE,SAAS;wBACdD,KAAK,EAAE,OAAO;wBACdD,IAAI,EAAE;sBACR,CAAC;oBAAC;sBAAAzG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAGN7B,OAAA;oBAAKwB,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAChDmH,KAAK,CAAC7D;kBAAI;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,EAGLxB,OAAO,IAAI,CAACuI,KAAK,CAACzB,IAAI,CAAC9G,OAAO,iBAC7BL,OAAA;oBAAKwB,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC/BzB,OAAA;sBACEsG,OAAO,EAAEA,CAAA,KACPJ,kBAAkB,CAChBZ,QAAQ,CAACK,GAAG,EACZiD,KAAK,CAACjD,GAAG,EACT,CAACiD,KAAK,CAACE,UACT,CACD;sBACDtH,SAAS,EAAG,2EACVoH,KAAK,CAACE,UAAU,GACZ,0CAA0C,GAC1C,gDACL,EAAE;sBAAArH,QAAA,EAEFmH,KAAK,CAACE,UAAU,GAAG,YAAY,GAAG;oBAAS;sBAAApH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAjGD+G,KAAK,CAACjD,GAAG;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkGX,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eACD7B,OAAA;YAAKiJ,GAAG,EAAE7G,SAAS,CAACkD,QAAQ,CAACK,GAAG,CAAE;YAAAlE,QAAA,EAC/BV,eAAe,KAAKuE,QAAQ,CAACK,GAAG,iBAC/B3F,OAAA,CAACrB,IAAI;cACHwC,IAAI,EAAEA,IAAK;cACXoF,QAAQ,EAAE1B,iBAAkB;cAC5B2B,MAAM,EAAC,UAAU;cAAA/E,QAAA,gBAEjBzB,OAAA,CAACrB,IAAI,CAAC8H,IAAI;gBACRC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAC,YAAY;gBAClBC,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAErI,OAAO,EAAE;gBAA0B,CAAC,CACtD;gBAAAiD,QAAA,eAEFzB,OAAA,CAACtB,KAAK,CAACqI,QAAQ;kBAACC,IAAI,EAAE;gBAAE;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACZ7B,OAAA,CAACrB,IAAI,CAAC8H,IAAI;gBAAAhF,QAAA,gBACRzB,OAAA,CAACvB,MAAM;kBAACwI,IAAI,EAAC,SAAS;kBAACC,QAAQ,EAAC,QAAQ;kBAAAzF,QAAA,EAAC;gBAEzC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT7B,OAAA,CAACvB,MAAM;kBACL6H,OAAO,EAAEA,CAAA,KAAMtF,kBAAkB,CAAC,IAAI,CAAE;kBACxCsG,KAAK,EAAE;oBAAEqB,UAAU,EAAE;kBAAG,CAAE;kBAAAlH,QAAA,EAC3B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACP;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAtRIyD,QAAQ,CAACK,GAAG;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuRjB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC,CAAC,eAEN7B,OAAA,CAACnB,UAAU;QACTqG,OAAO,EAAE5C,WAAY;QACrB4G,KAAK,EAAEvG,cAAe;QACtBwG,QAAQ,EAAEzG,KAAM;QAChB0G,QAAQ,EAAEhF,gBAAiB;QAC3BkD,KAAK,EAAE;UAAE+B,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAClDC,eAAe,EAAE;MAAM;QAAA7H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzB,EAAA,CA1oBID,KAAK;EAAA,QAQMxB,IAAI,CAACyC,OAAO,EA2BXzC,IAAI,CAACyC,OAAO,EACXrC,WAAW;AAAA;AAAAyK,EAAA,GApCxBrJ,KAAK;AA4oBX,eAAeA,KAAK;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}