{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BrainwaveAI = () => {\n  _s();\n  const [messages, setMessages] = useState([{\n    role: \"assistant\",\n    content: \"Hello! I'm Brainwave AI. How can I help you?\"\n  }]);\n  const [inputText, setInputText] = useState(\"\");\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputText.trim() && !selectedImage) return;\n    const userMessage = inputText.trim();\n    const imageFile = selectedImage;\n\n    // Clear input immediately\n    setInputText(\"\");\n    removeImage();\n    setIsLoading(true);\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        if (uploadResult !== null && uploadResult !== void 0 && uploadResult.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: userMessage\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: userMessage\n      };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response\n      const response = await chatWithChatGPT([...messages, newUserMessage]);\n      if (response !== null && response !== void 0 && response.success && response !== null && response !== void 0 && response.data) {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: response.data\n        }]);\n      } else {\n        setMessages(prev => [...prev, {\n          role: \"assistant\",\n          content: \"Sorry, I couldn't process your request. Please try again.\"\n        }]);\n      }\n    } catch (error) {\n      console.error(\"Chat error:\", error);\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: \"An error occurred. Please try again.\"\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Render message content\n  const renderMessageContent = message => {\n    if (typeof message.content === 'string') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"whitespace-pre-wrap\",\n        children: message.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 14\n      }, this);\n    }\n    if (Array.isArray(message.content)) {\n      return message.content.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"whitespace-pre-wrap mb-2\",\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 36\n        }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: item.image_url.url,\n          alt: \"User upload\",\n          className: \"max-w-full h-auto rounded-lg shadow-md\",\n          style: {\n            maxHeight: '300px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Invalid message format\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderBottom: '1px solid #e5e7eb',\n        padding: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxWidth: '1024px',\n          margin: '0 auto',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '40px',\n            height: '40px',\n            background: '#3b82f6',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: \"\\uD83E\\uDD16\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '20px',\n              fontWeight: 'bold',\n              color: '#1f2937',\n              margin: 0\n            },\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '14px',\n              color: '#6b7280',\n              margin: 0\n            },\n            children: \"Your intelligent study assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '100%',\n          maxWidth: '1024px',\n          margin: '0 auto',\n          padding: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '100%',\n            overflowY: 'auto',\n            paddingBottom: '16px'\n          },\n          children: [messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px',\n              marginBottom: '16px',\n              justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start'\n            },\n            children: [message.role === 'assistant' && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '32px',\n                height: '32px',\n                background: '#3b82f6',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                flexShrink: 0\n              },\n              children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxWidth: '80%'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  borderRadius: '16px',\n                  padding: '16px',\n                  background: message.role === 'user' ? '#3b82f6' : 'white',\n                  color: message.role === 'user' ? 'white' : '#1f2937',\n                  border: message.role === 'assistant' ? '1px solid #e5e7eb' : 'none',\n                  boxShadow: message.role === 'assistant' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none'\n                },\n                children: typeof message.content === 'string' ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    whiteSpace: 'pre-wrap'\n                  },\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 23\n                }, this) : Array.isArray(message.content) ? message.content.map((item, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      whiteSpace: 'pre-wrap',\n                      marginBottom: '8px'\n                    },\n                    children: item.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 52\n                  }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.image_url.url,\n                    alt: \"Upload\",\n                    style: {\n                      maxWidth: '100%',\n                      height: 'auto',\n                      borderRadius: '8px',\n                      maxHeight: '300px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 29\n                  }, this)]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 25\n                }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Invalid message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), message.role === 'user' && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '32px',\n                height: '32px',\n                background: '#10b981',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                flexShrink: 0\n              },\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px',\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '32px',\n                height: '32px',\n                background: '#3b82f6',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: \"\\uD83E\\uDD16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'white',\n                border: '1px solid #e5e7eb',\n                borderRadius: '16px',\n                padding: '16px',\n                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    background: '#9ca3af',\n                    borderRadius: '50%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    background: '#9ca3af',\n                    borderRadius: '50%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '8px',\n                    height: '8px',\n                    background: '#9ca3af',\n                    borderRadius: '50%'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-gray-50 rounded-xl border border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\",\n                className: \"w-16 h-16 object-cover rounded-lg border border-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: removeImage,\n                className: \"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Image attached\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500\",\n                children: selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-end gap-3 bg-white rounded-2xl border border-gray-300 p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            className: \"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors\",\n            title: \"Attach image\",\n            children: /*#__PURE__*/_jsxDEV(TbPaperclip, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: inputText,\n            onChange: e => setInputText(e.target.value),\n            placeholder: \"Ask me anything... (Enter for new line)\",\n            className: \"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 max-h-32 min-h-[24px]\",\n            rows: 1,\n            onInput: e => {\n              e.target.style.height = 'auto';\n              e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSendMessage,\n            disabled: isLoading || !inputText.trim() && !selectedImage,\n            className: `p-2 rounded-lg transition-all ${isLoading || !inputText.trim() && !selectedImage ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"}`,\n            children: /*#__PURE__*/_jsxDEV(TbSend, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 mt-2 text-center\",\n          children: \"Click send button to send \\u2022 Enter for new line \\u2022 Upload images for analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(BrainwaveAI, \"lIjsTBlFazwoJrwcDyz7AKelNP8=\");\n_c = BrainwaveAI;\nexport default BrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"BrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "chatWithChatGPT", "uploadImg", "jsxDEV", "_jsxDEV", "BrainwaveAI", "_s", "messages", "setMessages", "role", "content", "inputText", "setInputText", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "isLoading", "setIsLoading", "messagesEndRef", "fileInputRef", "current", "scrollIntoView", "behavior", "handleImageSelect", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "removeImage", "value", "handleSendMessage", "trim", "userMessage", "imageFile", "imageUrl", "formData", "FormData", "append", "uploadResult", "success", "url", "newUserMessage", "text", "image_url", "prev", "response", "data", "error", "console", "renderMessageContent", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "isArray", "map", "item", "index", "src", "alt", "style", "maxHeight", "height", "background", "display", "flexDirection", "borderBottom", "padding", "max<PERSON><PERSON><PERSON>", "margin", "alignItems", "gap", "width", "borderRadius", "justifyContent", "fontSize", "fontWeight", "color", "flex", "overflow", "overflowY", "paddingBottom", "marginBottom", "flexShrink", "border", "boxShadow", "whiteSpace", "idx", "ref", "onClick", "TbX", "name", "_fileInputRef$current", "click", "title", "TbPaperclip", "onChange", "placeholder", "rows", "onInput", "Math", "min", "scrollHeight", "disabled", "TbSend", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\n\nconst BrainwaveAI = () => {\n  const [messages, setMessages] = useState([\n    { role: \"assistant\", content: \"Hello! I'm Brainwave AI. How can I help you?\" }\n  ]);\n  const [inputText, setInputText] = useState(\"\");\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n    }\n  }, [messages]);\n\n  // Handle image selection\n  const handleImageSelect = (event) => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = (e) => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputText.trim() && !selectedImage) return;\n\n    const userMessage = inputText.trim();\n    const imageFile = selectedImage;\n\n    // Clear input immediately\n    setInputText(\"\");\n    removeImage();\n    setIsLoading(true);\n\n    try {\n      let imageUrl = null;\n\n      // Upload image if selected\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        \n        if (uploadResult?.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n\n      // Create user message\n      const newUserMessage = imageUrl\n        ? {\n            role: \"user\",\n            content: [\n              { type: \"text\", text: userMessage },\n              { type: \"image_url\", image_url: { url: imageUrl } }\n            ]\n          }\n        : { role: \"user\", content: userMessage };\n\n      // Add user message to chat\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Get AI response\n      const response = await chatWithChatGPT([...messages, newUserMessage]);\n      \n      if (response?.success && response?.data) {\n        setMessages(prev => [...prev, { role: \"assistant\", content: response.data }]);\n      } else {\n        setMessages(prev => [...prev, { role: \"assistant\", content: \"Sorry, I couldn't process your request. Please try again.\" }]);\n      }\n\n    } catch (error) {\n      console.error(\"Chat error:\", error);\n      setMessages(prev => [...prev, { role: \"assistant\", content: \"An error occurred. Please try again.\" }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Render message content\n  const renderMessageContent = (message) => {\n    if (typeof message.content === 'string') {\n      return <div className=\"whitespace-pre-wrap\">{message.content}</div>;\n    }\n    \n    if (Array.isArray(message.content)) {\n      return message.content.map((item, index) => (\n        <div key={index}>\n          {item.type === 'text' && <div className=\"whitespace-pre-wrap mb-2\">{item.text}</div>}\n          {item.type === 'image_url' && (\n            <img \n              src={item.image_url.url} \n              alt=\"User upload\" \n              className=\"max-w-full h-auto rounded-lg shadow-md\"\n              style={{ maxHeight: '300px' }}\n            />\n          )}\n        </div>\n      ));\n    }\n    \n    return <div>Invalid message format</div>;\n  };\n\n  return (\n    <div style={{ height: '100vh', background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <div style={{ background: 'white', borderBottom: '1px solid #e5e7eb', padding: '16px' }}>\n        <div style={{ maxWidth: '1024px', margin: '0 auto', display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div style={{ width: '40px', height: '40px', background: '#3b82f6', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            🤖\n          </div>\n          <div>\n            <h1 style={{ fontSize: '20px', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>Brainwave AI</h1>\n            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>Your intelligent study assistant</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div style={{ flex: 1, overflow: 'hidden' }}>\n        <div style={{ height: '100%', maxWidth: '1024px', margin: '0 auto', padding: '16px' }}>\n          <div style={{ height: '100%', overflowY: 'auto', paddingBottom: '16px' }}>\n            {messages.map((message, index) => (\n              <div\n                key={index}\n                style={{\n                  display: 'flex',\n                  gap: '12px',\n                  marginBottom: '16px',\n                  justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start'\n                }}\n              >\n                {message.role === 'assistant' && (\n                  <div style={{ width: '32px', height: '32px', background: '#3b82f6', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', flexShrink: 0 }}>\n                    🤖\n                  </div>\n                )}\n\n                <div style={{ maxWidth: '80%' }}>\n                  <div\n                    style={{\n                      borderRadius: '16px',\n                      padding: '16px',\n                      background: message.role === 'user' ? '#3b82f6' : 'white',\n                      color: message.role === 'user' ? 'white' : '#1f2937',\n                      border: message.role === 'assistant' ? '1px solid #e5e7eb' : 'none',\n                      boxShadow: message.role === 'assistant' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none'\n                    }}\n                  >\n                    {typeof message.content === 'string' ? (\n                      <div style={{ whiteSpace: 'pre-wrap' }}>{message.content}</div>\n                    ) : Array.isArray(message.content) ? (\n                      message.content.map((item, idx) => (\n                        <div key={idx}>\n                          {item.type === 'text' && <div style={{ whiteSpace: 'pre-wrap', marginBottom: '8px' }}>{item.text}</div>}\n                          {item.type === 'image_url' && (\n                            <img\n                              src={item.image_url.url}\n                              alt=\"Upload\"\n                              style={{ maxWidth: '100%', height: 'auto', borderRadius: '8px', maxHeight: '300px' }}\n                            />\n                          )}\n                        </div>\n                      ))\n                    ) : (\n                      <div>Invalid message</div>\n                    )}\n                  </div>\n                </div>\n\n                {message.role === 'user' && (\n                  <div style={{ width: '32px', height: '32px', background: '#10b981', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', flexShrink: 0 }}>\n                    👤\n                  </div>\n                )}\n              </div>\n            ))}\n\n            {isLoading && (\n              <div style={{ display: 'flex', gap: '12px', marginBottom: '16px' }}>\n                <div style={{ width: '32px', height: '32px', background: '#3b82f6', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                  🤖\n                </div>\n                <div style={{ background: 'white', border: '1px solid #e5e7eb', borderRadius: '16px', padding: '16px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>\n                  <div style={{ display: 'flex', gap: '4px' }}>\n                    <div style={{ width: '8px', height: '8px', background: '#9ca3af', borderRadius: '50%' }}></div>\n                    <div style={{ width: '8px', height: '8px', background: '#9ca3af', borderRadius: '50%' }}></div>\n                    <div style={{ width: '8px', height: '8px', background: '#9ca3af', borderRadius: '50%' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </div>\n        </div>\n      </div>\n\n      {/* Input Section */}\n      <div className=\"bg-white border-t p-4\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Image Preview */}\n          {imagePreview && (\n            <div className=\"mb-4 p-3 bg-gray-50 rounded-xl border border-gray-200\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"relative\">\n                  <img\n                    src={imagePreview}\n                    alt=\"Preview\"\n                    className=\"w-16 h-16 object-cover rounded-lg border border-gray-300\"\n                  />\n                  <button\n                    onClick={removeImage}\n                    className=\"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\"\n                  >\n                    <TbX className=\"w-4 h-4\" />\n                  </button>\n                </div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-700\">Image attached</p>\n                  <p className=\"text-xs text-gray-500\">{selectedImage?.name}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Input Area */}\n          <div className=\"flex items-end gap-3 bg-white rounded-2xl border border-gray-300 p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\">\n            {/* Attachment Button */}\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              className=\"p-2 bg-blue-500 text-white hover:bg-blue-600 rounded-lg transition-colors\"\n              title=\"Attach image\"\n            >\n              <TbPaperclip className=\"w-5 h-5\" />\n            </button>\n\n            {/* Text Input */}\n            <textarea\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              placeholder=\"Ask me anything... (Enter for new line)\"\n              className=\"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 max-h-32 min-h-[24px]\"\n              rows={1}\n              onInput={(e) => {\n                e.target.style.height = 'auto';\n                e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';\n              }}\n            />\n\n            {/* Send Button */}\n            <button\n              onClick={handleSendMessage}\n              disabled={isLoading || (!inputText.trim() && !selectedImage)}\n              className={`p-2 rounded-lg transition-all ${\n                isLoading || (!inputText.trim() && !selectedImage)\n                  ? \"bg-gray-200 text-gray-400 cursor-not-allowed\"\n                  : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"\n              }`}\n            >\n              <TbSend className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* Hidden File Input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={handleImageSelect}\n            style={{ display: 'none' }}\n          />\n\n          {/* Helper Text */}\n          <p className=\"text-xs text-gray-500 mt-2 text-center\">\n            Click send button to send • Enter for new line • Upload images for analysis\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BrainwaveAI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,eAAe,EAAEC,SAAS,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,CACvC;IAAEW,IAAI,EAAE,WAAW;IAAEC,OAAO,EAAE;EAA+C,CAAC,CAC/E,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMqB,cAAc,GAAGpB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMqB,YAAY,GAAGrB,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAC,SAAS,CAAC,MAAM;IACd,IAAImB,cAAc,CAACE,OAAO,EAAE;MAC1BF,cAAc,CAACE,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1ChB,gBAAgB,CAACY,IAAI,CAAC;MACtB,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAKlB,eAAe,CAACkB,CAAC,CAACP,MAAM,CAACQ,MAAM,CAAC;MACvDJ,MAAM,CAACK,aAAa,CAACV,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMW,WAAW,GAAGA,CAAA,KAAM;IACxBvB,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAII,YAAY,CAACC,OAAO,EAAE;MACxBD,YAAY,CAACC,OAAO,CAACiB,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC5B,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAa,EAAE;IAEzC,MAAM4B,WAAW,GAAG9B,SAAS,CAAC6B,IAAI,CAAC,CAAC;IACpC,MAAME,SAAS,GAAG7B,aAAa;;IAE/B;IACAD,YAAY,CAAC,EAAE,CAAC;IAChByB,WAAW,CAAC,CAAC;IACbnB,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,IAAIyB,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAID,SAAS,EAAE;QACb,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,SAAS,CAAC;QACnC,MAAMK,YAAY,GAAG,MAAM7C,SAAS,CAAC0C,QAAQ,CAAC;QAE9C,IAAIG,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,OAAO,EAAE;UACzBL,QAAQ,GAAGI,YAAY,CAACE,GAAG;QAC7B;MACF;;MAEA;MACA,MAAMC,cAAc,GAAGP,QAAQ,GAC3B;QACElC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEmB,IAAI,EAAE,MAAM;UAAEsB,IAAI,EAAEV;QAAY,CAAC,EACnC;UAAEZ,IAAI,EAAE,WAAW;UAAEuB,SAAS,EAAE;YAAEH,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACD;QAAElC,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAE+B;MAAY,CAAC;;MAE1C;MACAjC,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,cAAc,CAAC,CAAC;;MAE9C;MACA,MAAMI,QAAQ,GAAG,MAAMrD,eAAe,CAAC,CAAC,GAAGM,QAAQ,EAAE2C,cAAc,CAAC,CAAC;MAErE,IAAII,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEN,OAAO,IAAIM,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,IAAI,EAAE;QACvC/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAE5C,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAE4C,QAAQ,CAACC;QAAK,CAAC,CAAC,CAAC;MAC/E,CAAC,MAAM;QACL/C,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAAE5C,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAE;QAA4D,CAAC,CAAC,CAAC;MAC7H;IAEF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnChD,WAAW,CAAC6C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE5C,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAuC,CAAC,CAAC,CAAC;IACxG,CAAC,SAAS;MACRQ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMwC,oBAAoB,GAAIC,OAAO,IAAK;IACxC,IAAI,OAAOA,OAAO,CAACjD,OAAO,KAAK,QAAQ,EAAE;MACvC,oBAAON,OAAA;QAAKwD,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAEF,OAAO,CAACjD;MAAO;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACrE;IAEA,IAAIC,KAAK,CAACC,OAAO,CAACR,OAAO,CAACjD,OAAO,CAAC,EAAE;MAClC,OAAOiD,OAAO,CAACjD,OAAO,CAAC0D,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrClE,OAAA;QAAAyD,QAAA,GACGQ,IAAI,CAACxC,IAAI,KAAK,MAAM,iBAAIzB,OAAA;UAAKwD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EAAEQ,IAAI,CAAClB;QAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACnFI,IAAI,CAACxC,IAAI,KAAK,WAAW,iBACxBzB,OAAA;UACEmE,GAAG,EAAEF,IAAI,CAACjB,SAAS,CAACH,GAAI;UACxBuB,GAAG,EAAC,aAAa;UACjBZ,SAAS,EAAC,wCAAwC;UAClDa,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAQ;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CACF;MAAA,GATOK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUV,CACN,CAAC;IACJ;IAEA,oBAAO7D,OAAA;MAAAyD,QAAA,EAAK;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1C,CAAC;EAED,oBACE7D,OAAA;IAAKqE,KAAK,EAAE;MAAEE,MAAM,EAAE,OAAO;MAAEC,UAAU,EAAE,mDAAmD;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAjB,QAAA,gBAEzIzD,OAAA;MAAKqE,KAAK,EAAE;QAAEG,UAAU,EAAE,OAAO;QAAEG,YAAY,EAAE,mBAAmB;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAnB,QAAA,eACtFzD,OAAA;QAAKqE,KAAK,EAAE;UAAEQ,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE,QAAQ;UAAEL,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAvB,QAAA,gBACvGzD,OAAA;UAAKqE,KAAK,EAAE;YAAEY,KAAK,EAAE,MAAM;YAAEV,MAAM,EAAE,MAAM;YAAEC,UAAU,EAAE,SAAS;YAAEU,YAAY,EAAE,KAAK;YAAET,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE,QAAQ;YAAEI,cAAc,EAAE;UAAS,CAAE;UAAA1B,QAAA,EAAC;QAE5J;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7D,OAAA;UAAAyD,QAAA,gBACEzD,OAAA;YAAIqE,KAAK,EAAE;cAAEe,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE,SAAS;cAAER,MAAM,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnG7D,OAAA;YAAGqE,KAAK,EAAE;cAAEe,QAAQ,EAAE,MAAM;cAAEE,KAAK,EAAE,SAAS;cAAER,MAAM,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKqE,KAAK,EAAE;QAAEkB,IAAI,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAE;MAAA/B,QAAA,eAC1CzD,OAAA;QAAKqE,KAAK,EAAE;UAAEE,MAAM,EAAE,MAAM;UAAEM,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE,QAAQ;UAAEF,OAAO,EAAE;QAAO,CAAE;QAAAnB,QAAA,eACpFzD,OAAA;UAAKqE,KAAK,EAAE;YAAEE,MAAM,EAAE,MAAM;YAAEkB,SAAS,EAAE,MAAM;YAAEC,aAAa,EAAE;UAAO,CAAE;UAAAjC,QAAA,GACtEtD,QAAQ,CAAC6D,GAAG,CAAC,CAACT,OAAO,EAAEW,KAAK,kBAC3BlE,OAAA;YAEEqE,KAAK,EAAE;cACLI,OAAO,EAAE,MAAM;cACfO,GAAG,EAAE,MAAM;cACXW,YAAY,EAAE,MAAM;cACpBR,cAAc,EAAE5B,OAAO,CAAClD,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG;YACzD,CAAE;YAAAoD,QAAA,GAEDF,OAAO,CAAClD,IAAI,KAAK,WAAW,iBAC3BL,OAAA;cAAKqE,KAAK,EAAE;gBAAEY,KAAK,EAAE,MAAM;gBAAEV,MAAM,EAAE,MAAM;gBAAEC,UAAU,EAAE,SAAS;gBAAEU,YAAY,EAAE,KAAK;gBAAET,OAAO,EAAE,MAAM;gBAAEM,UAAU,EAAE,QAAQ;gBAAEI,cAAc,EAAE,QAAQ;gBAAES,UAAU,EAAE;cAAE,CAAE;cAAAnC,QAAA,EAAC;YAE3K;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,eAED7D,OAAA;cAAKqE,KAAK,EAAE;gBAAEQ,QAAQ,EAAE;cAAM,CAAE;cAAApB,QAAA,eAC9BzD,OAAA;gBACEqE,KAAK,EAAE;kBACLa,YAAY,EAAE,MAAM;kBACpBN,OAAO,EAAE,MAAM;kBACfJ,UAAU,EAAEjB,OAAO,CAAClD,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,OAAO;kBACzDiF,KAAK,EAAE/B,OAAO,CAAClD,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,SAAS;kBACpDwF,MAAM,EAAEtC,OAAO,CAAClD,IAAI,KAAK,WAAW,GAAG,mBAAmB,GAAG,MAAM;kBACnEyF,SAAS,EAAEvC,OAAO,CAAClD,IAAI,KAAK,WAAW,GAAG,2BAA2B,GAAG;gBAC1E,CAAE;gBAAAoD,QAAA,EAED,OAAOF,OAAO,CAACjD,OAAO,KAAK,QAAQ,gBAClCN,OAAA;kBAAKqE,KAAK,EAAE;oBAAE0B,UAAU,EAAE;kBAAW,CAAE;kBAAAtC,QAAA,EAAEF,OAAO,CAACjD;gBAAO;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,GAC7DC,KAAK,CAACC,OAAO,CAACR,OAAO,CAACjD,OAAO,CAAC,GAChCiD,OAAO,CAACjD,OAAO,CAAC0D,GAAG,CAAC,CAACC,IAAI,EAAE+B,GAAG,kBAC5BhG,OAAA;kBAAAyD,QAAA,GACGQ,IAAI,CAACxC,IAAI,KAAK,MAAM,iBAAIzB,OAAA;oBAAKqE,KAAK,EAAE;sBAAE0B,UAAU,EAAE,UAAU;sBAAEJ,YAAY,EAAE;oBAAM,CAAE;oBAAAlC,QAAA,EAAEQ,IAAI,CAAClB;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACtGI,IAAI,CAACxC,IAAI,KAAK,WAAW,iBACxBzB,OAAA;oBACEmE,GAAG,EAAEF,IAAI,CAACjB,SAAS,CAACH,GAAI;oBACxBuB,GAAG,EAAC,QAAQ;oBACZC,KAAK,EAAE;sBAAEQ,QAAQ,EAAE,MAAM;sBAAEN,MAAM,EAAE,MAAM;sBAAEW,YAAY,EAAE,KAAK;sBAAEZ,SAAS,EAAE;oBAAQ;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF,CACF;gBAAA,GAROmC,GAAG;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASR,CACN,CAAC,gBAEF7D,OAAA;kBAAAyD,QAAA,EAAK;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAC1B;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELN,OAAO,CAAClD,IAAI,KAAK,MAAM,iBACtBL,OAAA;cAAKqE,KAAK,EAAE;gBAAEY,KAAK,EAAE,MAAM;gBAAEV,MAAM,EAAE,MAAM;gBAAEC,UAAU,EAAE,SAAS;gBAAEU,YAAY,EAAE,KAAK;gBAAET,OAAO,EAAE,MAAM;gBAAEM,UAAU,EAAE,QAAQ;gBAAEI,cAAc,EAAE,QAAQ;gBAAES,UAAU,EAAE;cAAE,CAAE;cAAAnC,QAAA,EAAC;YAE3K;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,GAlDIK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmDP,CACN,CAAC,EAEDhD,SAAS,iBACRb,OAAA;YAAKqE,KAAK,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEO,GAAG,EAAE,MAAM;cAAEW,YAAY,EAAE;YAAO,CAAE;YAAAlC,QAAA,gBACjEzD,OAAA;cAAKqE,KAAK,EAAE;gBAAEY,KAAK,EAAE,MAAM;gBAAEV,MAAM,EAAE,MAAM;gBAAEC,UAAU,EAAE,SAAS;gBAAEU,YAAY,EAAE,KAAK;gBAAET,OAAO,EAAE,MAAM;gBAAEM,UAAU,EAAE,QAAQ;gBAAEI,cAAc,EAAE;cAAS,CAAE;cAAA1B,QAAA,EAAC;YAE5J;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN7D,OAAA;cAAKqE,KAAK,EAAE;gBAAEG,UAAU,EAAE,OAAO;gBAAEqB,MAAM,EAAE,mBAAmB;gBAAEX,YAAY,EAAE,MAAM;gBAAEN,OAAO,EAAE,MAAM;gBAAEkB,SAAS,EAAE;cAA4B,CAAE;cAAArC,QAAA,eAC9IzD,OAAA;gBAAKqE,KAAK,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEO,GAAG,EAAE;gBAAM,CAAE;gBAAAvB,QAAA,gBAC1CzD,OAAA;kBAAKqE,KAAK,EAAE;oBAAEY,KAAK,EAAE,KAAK;oBAAEV,MAAM,EAAE,KAAK;oBAAEC,UAAU,EAAE,SAAS;oBAAEU,YAAY,EAAE;kBAAM;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/F7D,OAAA;kBAAKqE,KAAK,EAAE;oBAAEY,KAAK,EAAE,KAAK;oBAAEV,MAAM,EAAE,KAAK;oBAAEC,UAAU,EAAE,SAAS;oBAAEU,YAAY,EAAE;kBAAM;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/F7D,OAAA;kBAAKqE,KAAK,EAAE;oBAAEY,KAAK,EAAE,KAAK;oBAAEV,MAAM,EAAE,KAAK;oBAAEC,UAAU,EAAE,SAAS;oBAAEU,YAAY,EAAE;kBAAM;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED7D,OAAA;YAAKiG,GAAG,EAAElF;UAAe;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA;MAAKwD,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCzD,OAAA;QAAKwD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAE/B9C,YAAY,iBACXX,OAAA;UAAKwD,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eACpEzD,OAAA;YAAKwD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCzD,OAAA;cAAKwD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBzD,OAAA;gBACEmE,GAAG,EAAExD,YAAa;gBAClByD,GAAG,EAAC,SAAS;gBACbZ,SAAS,EAAC;cAA0D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACF7D,OAAA;gBACEkG,OAAO,EAAEjE,WAAY;gBACrBuB,SAAS,EAAC,yIAAyI;gBAAAC,QAAA,eAEnJzD,OAAA,CAACmG,GAAG;kBAAC3C,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7D,OAAA;cAAKwD,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBzD,OAAA;gBAAGwD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnE7D,OAAA;gBAAGwD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEhD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2F;cAAI;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD7D,OAAA;UAAKwD,SAAS,EAAC,+JAA+J;UAAAC,QAAA,gBAE5KzD,OAAA;YACEkG,OAAO,EAAEA,CAAA;cAAA,IAAAG,qBAAA;cAAA,QAAAA,qBAAA,GAAMrF,YAAY,CAACC,OAAO,cAAAoF,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7C9C,SAAS,EAAC,2EAA2E;YACrF+C,KAAK,EAAC,cAAc;YAAA9C,QAAA,eAEpBzD,OAAA,CAACwG,WAAW;cAAChD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAGT7D,OAAA;YACEkC,KAAK,EAAE3B,SAAU;YACjBkG,QAAQ,EAAG3E,CAAC,IAAKtB,YAAY,CAACsB,CAAC,CAACP,MAAM,CAACW,KAAK,CAAE;YAC9CwE,WAAW,EAAC,yCAAyC;YACrDlD,SAAS,EAAC,qHAAqH;YAC/HmD,IAAI,EAAE,CAAE;YACRC,OAAO,EAAG9E,CAAC,IAAK;cACdA,CAAC,CAACP,MAAM,CAAC8C,KAAK,CAACE,MAAM,GAAG,MAAM;cAC9BzC,CAAC,CAACP,MAAM,CAAC8C,KAAK,CAACE,MAAM,GAAGsC,IAAI,CAACC,GAAG,CAAChF,CAAC,CAACP,MAAM,CAACwF,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;YACrE;UAAE;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGF7D,OAAA;YACEkG,OAAO,EAAE/D,iBAAkB;YAC3B6E,QAAQ,EAAEnG,SAAS,IAAK,CAACN,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAe;YAC7D+C,SAAS,EAAG,iCACV3C,SAAS,IAAK,CAACN,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,aAAc,GAC9C,8CAA8C,GAC9C,oEACL,EAAE;YAAAgD,QAAA,eAEHzD,OAAA,CAACiH,MAAM;cAACzD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN7D,OAAA;UACEiG,GAAG,EAAEjF,YAAa;UAClBS,IAAI,EAAC,MAAM;UACXyF,MAAM,EAAC,SAAS;UAChBT,QAAQ,EAAErF,iBAAkB;UAC5BiD,KAAK,EAAE;YAAEI,OAAO,EAAE;UAAO;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAGF7D,OAAA;UAAGwD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA3SID,WAAW;AAAAkH,EAAA,GAAXlH,WAAW;AA6SjB,eAAeA,WAAW;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}