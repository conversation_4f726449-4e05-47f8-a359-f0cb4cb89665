{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbSend, TbPaperclip, TbX, TbRobot, Tb<PERSON>ser, TbPhoto, TbLoader } from \"react-icons/tb\";\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\nimport ContentRenderer from \"../../../components/ContentRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ChatGPTIntegration() {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [prompt, setPrompt] = useState(\"\");\n  const [imageFile, setImageFile] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const textareaRef = useRef(null);\n\n  // Initialize chat with welcome message\n  React.useEffect(() => {\n    // Load cached messages\n    const cachedMessages = localStorage.getItem('chat_messages');\n    if (cachedMessages) {\n      setMessages(JSON.parse(cachedMessages));\n    } else {\n      // Set welcome message\n      setMessages([{\n        role: \"assistant\",\n        content: \"Hello! I'm your AI study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\n      }]);\n    }\n    setIsInitialized(true);\n  }, []);\n\n  // Save messages to cache\n  React.useEffect(() => {\n    if (isInitialized && messages.length > 0) {\n      localStorage.setItem('chat_messages', JSON.stringify(messages));\n    }\n  }, [messages, isInitialized]);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [messages]);\n\n  // Handle image file selection\n  const handleImageSelect = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setImageFile(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n\n  // Remove selected image\n  const removeImage = () => {\n    setImageFile(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Handle Enter key press\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleChat();\n    }\n  };\n  const handleChat = async () => {\n    if (!prompt.trim() && !imageFile) return;\n    setIsLoading(true);\n    setIsTyping(true);\n    try {\n      let imageUrl = null;\n\n      // Step 1: Upload the image to the server (if an image is selected)\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const data = await uploadImg(formData);\n        if (data !== null && data !== void 0 && data.success) {\n          imageUrl = data.url; // Extract the S3 URL\n          console.log(\"Image URL: \", imageUrl);\n        } else {\n          throw new Error(\"Image upload failed\");\n        }\n      }\n\n      // Step 2: Construct the ChatGPT message payload\n      const userMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: prompt\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: prompt\n      };\n      const updatedMessages = [...messages, userMessage];\n      setMessages(updatedMessages);\n      setPrompt(\"\");\n      removeImage();\n\n      // Step 3: Send the payload to ChatGPT\n      const chatPayload = {\n        messages: updatedMessages\n      };\n      const chatRes = await chatWithChatGPT(chatPayload);\n      const apiResponse = chatRes === null || chatRes === void 0 ? void 0 : chatRes.data;\n      console.log(\"API Response: \", apiResponse);\n\n      // Step 4: Append the assistant's response to the conversation\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: apiResponse\n      }]);\n    } catch (error) {\n      console.error(\"Error during chat:\", error);\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: \"Sorry, I encountered an error. Please try again.\"\n      }]);\n    } finally {\n      setIsLoading(false);\n      setIsTyping(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(TbRobot, {\n            className: \"w-6 h-6 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-lg sm:text-xl font-bold text-gray-800\",\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs sm:text-sm text-gray-600\",\n            children: \"Ask questions, upload images, get instant help\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full overflow-y-auto space-y-3 sm:space-y-6 pb-4\",\n          children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: messages.map((msg, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              className: `flex gap-2 sm:gap-4 ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n              children: [msg.role === \"assistant\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                  className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `max-w-[85%] sm:max-w-[80%] ${msg.role === \"user\" ? \"order-1\" : \"\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `rounded-xl sm:rounded-2xl p-3 sm:p-4 ${msg.role === \"user\" ? \"bg-blue-500 text-white ml-auto\" : \"bg-white border border-gray-200 shadow-sm\"}`,\n                  children: msg.role === \"assistant\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-gray-800\",\n                    children: msg !== null && msg !== void 0 && msg.content ? /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                      text: msg.content\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-red-500\",\n                      children: \"Unable to get a response from AI\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: typeof msg.content === \"string\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"whitespace-pre-wrap\",\n                      children: msg.content\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 29\n                    }, this) : msg.content.map((item, idx) => item.type === \"text\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"whitespace-pre-wrap mb-2\",\n                      children: item.text\n                    }, idx, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 33\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: item.image_url.url,\n                        alt: \"User uploaded content\",\n                        className: \"rounded-lg max-w-full h-auto shadow-md\",\n                        style: {\n                          maxHeight: window.innerWidth <= 768 ? \"200px\" : \"300px\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 35\n                      }, this)\n                    }, idx, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 33\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), msg.role === \"user\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(TbUser, {\n                  className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n            children: isTyping && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              exit: {\n                opacity: 0,\n                y: -20\n              },\n              className: \"flex gap-2 sm:gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(TbRobot, {\n                  className: \"w-3 h-3 sm:w-5 sm:h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white border border-gray-200 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      scale: [1, 1.2, 1]\n                    },\n                    transition: {\n                      duration: 0.6,\n                      repeat: Infinity,\n                      delay: 0\n                    },\n                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      scale: [1, 1.2, 1]\n                    },\n                    transition: {\n                      duration: 0.6,\n                      repeat: Infinity,\n                      delay: 0.2\n                    },\n                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    animate: {\n                      scale: [1, 1.2, 1]\n                    },\n                    transition: {\n                      duration: 0.6,\n                      repeat: Infinity,\n                      delay: 0.4\n                    },\n                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/80 backdrop-blur-lg border-t border-gray-200 p-2 sm:p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: imagePreview && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              height: 0\n            },\n            animate: {\n              opacity: 1,\n              height: \"auto\"\n            },\n            exit: {\n              opacity: 0,\n              height: 0\n            },\n            className: \"mb-4 p-3 bg-gray-50 rounded-xl border border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: imagePreview,\n                  alt: \"Preview\",\n                  className: \"w-16 h-16 object-cover rounded-lg border border-gray-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: removeImage,\n                  className: \"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(TbX, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"Image attached\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: imageFile === null || imageFile === void 0 ? void 0 : imageFile.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end gap-3 bg-white rounded-2xl border border-gray-300 p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _fileInputRef$current;\n                return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n              },\n              className: \"p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors\",\n              title: \"Attach image\",\n              children: /*#__PURE__*/_jsxDEV(TbPaperclip, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              ref: textareaRef,\n              value: prompt,\n              onChange: e => setPrompt(e.target.value),\n              onKeyPress: handleKeyPress,\n              placeholder: \"Ask me anything... (Shift+Enter for new line)\",\n              className: \"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 max-h-32 min-h-[24px]\",\n              rows: 1,\n              style: {\n                height: 'auto',\n                minHeight: '24px',\n                maxHeight: '128px'\n              },\n              onInput: e => {\n                e.target.style.height = 'auto';\n                e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: handleChat,\n              disabled: isLoading || !prompt.trim() && !imageFile,\n              className: `p-2 rounded-lg transition-all ${isLoading || !prompt.trim() && !imageFile ? \"bg-gray-200 text-gray-400 cursor-not-allowed\" : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"}`,\n              children: isLoading ? /*#__PURE__*/_jsxDEV(TbLoader, {\n                className: \"w-5 h-5 animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(TbSend, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            ref: fileInputRef,\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: handleImageSelect,\n            className: \"hidden\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 mt-2 text-center\",\n          children: \"Press Enter to send \\u2022 Shift+Enter for new line \\u2022 Upload images for analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatGPTIntegration, \"VSMuF9EUH1ObLJ2Bkv+9tWC7cOk=\");\n_c = ChatGPTIntegration;\nexport default ChatGPTIntegration;\nvar _c;\n$RefreshReg$(_c, \"ChatGPTIntegration\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "TbSend", "TbPaperclip", "TbX", "TbRobot", "TbUser", "TbPhoto", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chatWithChatGPT", "uploadImg", "Content<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ChatGPTIntegration", "_s", "messages", "setMessages", "prompt", "setPrompt", "imageFile", "setImageFile", "imagePreview", "setImagePreview", "isLoading", "setIsLoading", "isInitialized", "setIsInitialized", "isTyping", "setIsTyping", "messagesEndRef", "fileInputRef", "textareaRef", "cachedMessages", "localStorage", "getItem", "JSON", "parse", "role", "content", "length", "setItem", "stringify", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleImageSelect", "e", "file", "target", "files", "reader", "FileReader", "onload", "result", "readAsDataURL", "removeImage", "value", "handleKeyPress", "key", "shift<PERSON>ey", "preventDefault", "handleChat", "trim", "imageUrl", "formData", "FormData", "append", "data", "success", "url", "console", "log", "Error", "userMessage", "type", "text", "image_url", "updatedMessages", "chatPayload", "chatRes", "apiResponse", "prev", "error", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "msg", "index", "item", "idx", "src", "alt", "style", "maxHeight", "window", "innerWidth", "exit", "scale", "transition", "duration", "repeat", "Infinity", "delay", "ref", "height", "onClick", "name", "_fileInputRef$current", "click", "title", "onChange", "onKeyPress", "placeholder", "rows", "minHeight", "onInput", "Math", "min", "scrollHeight", "button", "whileHover", "whileTap", "disabled", "accept", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { TbSend, TbPaperclip, TbX, Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON>b<PERSON>oa<PERSON> } from \"react-icons/tb\";\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nfunction ChatGPTIntegration() {\r\n  const [messages, setMessages] = useState([]);\r\n  const [prompt, setPrompt] = useState(\"\");\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [isTyping, setIsTyping] = useState(false);\r\n\r\n  const messagesEndRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const textareaRef = useRef(null);\r\n\r\n  // Initialize chat with welcome message\r\n  React.useEffect(() => {\r\n    // Load cached messages\r\n    const cachedMessages = localStorage.getItem('chat_messages');\r\n    if (cachedMessages) {\r\n      setMessages(JSON.parse(cachedMessages));\r\n    } else {\r\n      // Set welcome message\r\n      setMessages([{\r\n        role: \"assistant\",\r\n        content: \"Hello! I'm your AI study assistant. I can help you with homework, explain concepts, solve problems, and answer questions. You can also upload images of your textbook pages or worksheets for help. What would you like to learn about today?\"\r\n      }]);\r\n    }\r\n    setIsInitialized(true);\r\n  }, []);\r\n\r\n  // Save messages to cache\r\n  React.useEffect(() => {\r\n    if (isInitialized && messages.length > 0) {\r\n      localStorage.setItem('chat_messages', JSON.stringify(messages));\r\n    }\r\n  }, [messages, isInitialized]);\r\n\r\n  // Auto-scroll to bottom when new messages arrive\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [messages]);\r\n\r\n  // Handle image file selection\r\n  const handleImageSelect = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setImageFile(file);\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImagePreview(e.target.result);\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  // Remove selected image\r\n  const removeImage = () => {\r\n    setImageFile(null);\r\n    setImagePreview(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Handle Enter key press\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleChat();\r\n    }\r\n  };\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n    setIsTyping(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: prompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: prompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      setMessages(updatedMessages);\r\n      setPrompt(\"\");\r\n      removeImage();\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: apiResponse },\r\n      ]);\r\n\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: \"Sorry, I encountered an error. Please try again.\" },\r\n      ]);\r\n    } finally {\r\n      setIsLoading(false);\r\n      setIsTyping(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col\">\r\n      {/* Header */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: -20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        className=\"bg-white/80 backdrop-blur-lg border-b border-gray-200 p-4 shadow-sm\"\r\n      >\r\n        <div className=\"max-w-4xl mx-auto flex items-center gap-3\">\r\n          <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n            <TbRobot className=\"w-6 h-6 text-white\" />\r\n          </div>\r\n          <div>\r\n            <h1 className=\"text-lg sm:text-xl font-bold text-gray-800\">Brainwave AI</h1>\r\n            <p className=\"text-xs sm:text-sm text-gray-600\">Ask questions, upload images, get instant help</p>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Messages Container - Responsive */}\r\n      <div className=\"flex-1 overflow-hidden\">\r\n        <div className=\"h-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4\">\r\n          <div className=\"h-full overflow-y-auto space-y-3 sm:space-y-6 pb-4\">\r\n            <AnimatePresence>\r\n              {messages.map((msg, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  className={`flex gap-2 sm:gap-4 ${msg.role === \"user\" ? \"justify-end\" : \"justify-start\"}`}\r\n                >\r\n                  {msg.role === \"assistant\" && (\r\n                    <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                      <TbRobot className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className={`max-w-[85%] sm:max-w-[80%] ${msg.role === \"user\" ? \"order-1\" : \"\"}`}>\r\n                    <div\r\n                      className={`rounded-xl sm:rounded-2xl p-3 sm:p-4 ${\r\n                        msg.role === \"user\"\r\n                          ? \"bg-blue-500 text-white ml-auto\"\r\n                          : \"bg-white border border-gray-200 shadow-sm\"\r\n                      }`}\r\n                    >\r\n                      {msg.role === \"assistant\" ? (\r\n                        <div className=\"text-gray-800\">\r\n                          {msg?.content ? (\r\n                            <ContentRenderer text={msg.content} />\r\n                          ) : (\r\n                            <p className=\"text-red-500\">Unable to get a response from AI</p>\r\n                          )}\r\n                        </div>\r\n                      ) : (\r\n                        <div>\r\n                          {typeof msg.content === \"string\" ? (\r\n                            <p className=\"whitespace-pre-wrap\">{msg.content}</p>\r\n                          ) : (\r\n                            msg.content.map((item, idx) =>\r\n                              item.type === \"text\" ? (\r\n                                <p key={idx} className=\"whitespace-pre-wrap mb-2\">{item.text}</p>\r\n                              ) : (\r\n                                <div key={idx} className=\"mt-2\">\r\n                                  <img\r\n                                    src={item.image_url.url}\r\n                                    alt=\"User uploaded content\"\r\n                                    className=\"rounded-lg max-w-full h-auto shadow-md\"\r\n                                    style={{ maxHeight: window.innerWidth <= 768 ? \"200px\" : \"300px\" }}\r\n                                  />\r\n                                </div>\r\n                              )\r\n                            )\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {msg.role === \"user\" && (\r\n                    <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0\">\r\n                      <TbUser className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                    </div>\r\n                  )}\r\n                </motion.div>\r\n              ))}\r\n            </AnimatePresence>\r\n\r\n            {/* Typing Indicator */}\r\n            <AnimatePresence>\r\n              {isTyping && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  exit={{ opacity: 0, y: -20 }}\r\n                  className=\"flex gap-2 sm:gap-4\"\r\n                >\r\n                  <div className=\"w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\r\n                    <TbRobot className=\"w-3 h-3 sm:w-5 sm:h-5 text-white\" />\r\n                  </div>\r\n                  <div className=\"bg-white border border-gray-200 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-sm\">\r\n                    <div className=\"flex gap-1\">\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                      <motion.div\r\n                        animate={{ scale: [1, 1.2, 1] }}\r\n                        transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}\r\n                        className=\"w-2 h-2 bg-gray-400 rounded-full\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </AnimatePresence>\r\n\r\n            <div ref={messagesEndRef} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Input Section - Responsive */}\r\n      <div className=\"bg-white/80 backdrop-blur-lg border-t border-gray-200 p-2 sm:p-4\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Image Preview */}\r\n          <AnimatePresence>\r\n            {imagePreview && (\r\n              <motion.div\r\n                initial={{ opacity: 0, height: 0 }}\r\n                animate={{ opacity: 1, height: \"auto\" }}\r\n                exit={{ opacity: 0, height: 0 }}\r\n                className=\"mb-4 p-3 bg-gray-50 rounded-xl border border-gray-200\"\r\n              >\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"relative\">\r\n                    <img\r\n                      src={imagePreview}\r\n                      alt=\"Preview\"\r\n                      className=\"w-16 h-16 object-cover rounded-lg border border-gray-300\"\r\n                    />\r\n                    <button\r\n                      onClick={removeImage}\r\n                      className=\"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\"\r\n                    >\r\n                      <TbX className=\"w-4 h-4\" />\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-700\">Image attached</p>\r\n                    <p className=\"text-xs text-gray-500\">{imageFile?.name}</p>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n\r\n          {/* Input Area */}\r\n          <div className=\"relative\">\r\n            <div className=\"flex items-end gap-3 bg-white rounded-2xl border border-gray-300 p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20\">\r\n              {/* Attachment Button */}\r\n              <button\r\n                onClick={() => fileInputRef.current?.click()}\r\n                className=\"p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors\"\r\n                title=\"Attach image\"\r\n              >\r\n                <TbPaperclip className=\"w-5 h-5\" />\r\n              </button>\r\n\r\n              {/* Text Input */}\r\n              <textarea\r\n                ref={textareaRef}\r\n                value={prompt}\r\n                onChange={(e) => setPrompt(e.target.value)}\r\n                onKeyPress={handleKeyPress}\r\n                placeholder=\"Ask me anything... (Shift+Enter for new line)\"\r\n                className=\"flex-1 resize-none border-none outline-none bg-transparent text-gray-800 placeholder-gray-500 max-h-32 min-h-[24px]\"\r\n                rows={1}\r\n                style={{\r\n                  height: 'auto',\r\n                  minHeight: '24px',\r\n                  maxHeight: '128px'\r\n                }}\r\n                onInput={(e) => {\r\n                  e.target.style.height = 'auto';\r\n                  e.target.style.height = Math.min(e.target.scrollHeight, 128) + 'px';\r\n                }}\r\n              />\r\n\r\n              {/* Send Button */}\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={handleChat}\r\n                disabled={isLoading || (!prompt.trim() && !imageFile)}\r\n                className={`p-2 rounded-lg transition-all ${\r\n                  isLoading || (!prompt.trim() && !imageFile)\r\n                    ? \"bg-gray-200 text-gray-400 cursor-not-allowed\"\r\n                    : \"bg-blue-500 text-white hover:bg-blue-600 shadow-md hover:shadow-lg\"\r\n                }`}\r\n              >\r\n                {isLoading ? (\r\n                  <TbLoader className=\"w-5 h-5 animate-spin\" />\r\n                ) : (\r\n                  <TbSend className=\"w-5 h-5\" />\r\n                )}\r\n              </motion.button>\r\n            </div>\r\n\r\n            {/* Hidden File Input */}\r\n            <input\r\n              ref={fileInputRef}\r\n              type=\"file\"\r\n              accept=\"image/*\"\r\n              onChange={handleImageSelect}\r\n              className=\"hidden\"\r\n            />\r\n          </div>\r\n\r\n          {/* Helper Text */}\r\n          <p className=\"text-xs text-gray-500 mt-2 text-center\">\r\n            Press Enter to send • Shift+Enter for new line • Upload images for analysis\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,MAAM,EAAEC,WAAW,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC7F,SAASC,eAAe,EAAEC,SAAS,QAAQ,wBAAwB;AACnE,OAAOC,eAAe,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMiC,cAAc,GAAGhC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMiC,YAAY,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkC,WAAW,GAAGlC,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACAF,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB;IACA,MAAMkC,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IAC5D,IAAIF,cAAc,EAAE;MAClBhB,WAAW,CAACmB,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC,CAAC;IACzC,CAAC,MAAM;MACL;MACAhB,WAAW,CAAC,CAAC;QACXqB,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL;IACAZ,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/B,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,IAAI2B,aAAa,IAAIV,QAAQ,CAACwB,MAAM,GAAG,CAAC,EAAE;MACxCN,YAAY,CAACO,OAAO,CAAC,eAAe,EAAEL,IAAI,CAACM,SAAS,CAAC1B,QAAQ,CAAC,CAAC;IACjE;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEU,aAAa,CAAC,CAAC;;EAE7B;EACA3B,SAAS,CAAC,MAAM;IAAA,IAAA4C,qBAAA;IACd,CAAAA,qBAAA,GAAAb,cAAc,CAACc,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAAC9B,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM+B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR5B,YAAY,CAAC4B,IAAI,CAAC;MAClB,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIN,CAAC,IAAKzB,eAAe,CAACyB,CAAC,CAACE,MAAM,CAACK,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxBpC,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAIQ,YAAY,CAACa,OAAO,EAAE;MACxBb,YAAY,CAACa,OAAO,CAACc,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIX,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACY,GAAG,KAAK,OAAO,IAAI,CAACZ,CAAC,CAACa,QAAQ,EAAE;MACpCb,CAAC,CAACc,cAAc,CAAC,CAAC;MAClBC,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC7C,MAAM,CAAC8C,IAAI,CAAC,CAAC,IAAI,CAAC5C,SAAS,EAAE;IAElCK,YAAY,CAAC,IAAI,CAAC;IAClBI,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,IAAIoC,QAAQ,GAAG,IAAI;;MAEnB;MACA,IAAI7C,SAAS,EAAE;QACb,MAAM8C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEhD,SAAS,CAAC;QAEnC,MAAMiD,IAAI,GAAG,MAAM3D,SAAS,CAACwD,QAAQ,CAAC;QAEtC,IAAIG,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,OAAO,EAAE;UACjBL,QAAQ,GAAGI,IAAI,CAACE,GAAG,CAAC,CAAC;UACrBC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAER,QAAQ,CAAC;QACtC,CAAC,MAAM;UACL,MAAM,IAAIS,KAAK,CAAC,qBAAqB,CAAC;QACxC;MACF;;MAEA;MACA,MAAMC,WAAW,GAAGV,QAAQ,GACxB;QACA3B,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEqC,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE3D;QAAO,CAAC,EAC9B;UAAE0D,IAAI,EAAE,WAAW;UAAEE,SAAS,EAAE;YAAEP,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACC;QAAE3B,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAErB;MAAO,CAAC;MAErC,MAAM6D,eAAe,GAAG,CAAC,GAAG/D,QAAQ,EAAE2D,WAAW,CAAC;MAClD1D,WAAW,CAAC8D,eAAe,CAAC;MAC5B5D,SAAS,CAAC,EAAE,CAAC;MACbsC,WAAW,CAAC,CAAC;;MAEb;MACA,MAAMuB,WAAW,GAAG;QAAEhE,QAAQ,EAAE+D;MAAgB,CAAC;MAEjD,MAAME,OAAO,GAAG,MAAMxE,eAAe,CAACuE,WAAW,CAAC;MAElD,MAAME,WAAW,GAAGD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEZ,IAAI;MACjCG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAES,WAAW,CAAC;;MAE1C;MACAjE,WAAW,CAAEkE,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAE7C,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE2C;MAAY,CAAC,CAC5C,CAAC;IAEJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CnE,WAAW,CAAEkE,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAE7C,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAmD,CAAC,CACnF,CAAC;IACJ,CAAC,SAAS;MACRd,YAAY,CAAC,KAAK,CAAC;MACnBI,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKwE,SAAS,EAAC,uEAAuE;IAAAC,QAAA,gBAEpFzE,OAAA,CAACb,MAAM,CAACuF,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAE/EzE,OAAA;QAAKwE,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDzE,OAAA;UAAKwE,SAAS,EAAC,sGAAsG;UAAAC,QAAA,eACnHzE,OAAA,CAACR,OAAO;YAACgF,SAAS,EAAC;UAAoB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACNlF,OAAA;UAAAyE,QAAA,gBACEzE,OAAA;YAAIwE,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5ElF,OAAA;YAAGwE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAA8C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGblF,OAAA;MAAKwE,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCzE,OAAA;QAAKwE,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eACjEzE,OAAA;UAAKwE,SAAS,EAAC,oDAAoD;UAAAC,QAAA,gBACjEzE,OAAA,CAACZ,eAAe;YAAAqF,QAAA,EACbtE,QAAQ,CAACgF,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBrF,OAAA,CAACb,MAAM,CAACuF,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BL,SAAS,EAAG,uBAAsBY,GAAG,CAAC3D,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAgB,EAAE;cAAAgD,QAAA,GAEzFW,GAAG,CAAC3D,IAAI,KAAK,WAAW,iBACvBzB,OAAA;gBAAKwE,SAAS,EAAC,gIAAgI;gBAAAC,QAAA,eAC7IzE,OAAA,CAACR,OAAO;kBAACgF,SAAS,EAAC;gBAAkC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CACN,eAEDlF,OAAA;gBAAKwE,SAAS,EAAG,8BAA6BY,GAAG,CAAC3D,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,EAAG,EAAE;gBAAAgD,QAAA,eACnFzE,OAAA;kBACEwE,SAAS,EAAG,wCACVY,GAAG,CAAC3D,IAAI,KAAK,MAAM,GACf,gCAAgC,GAChC,2CACL,EAAE;kBAAAgD,QAAA,EAEFW,GAAG,CAAC3D,IAAI,KAAK,WAAW,gBACvBzB,OAAA;oBAAKwE,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC3BW,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAE1D,OAAO,gBACX1B,OAAA,CAACF,eAAe;sBAACkE,IAAI,EAAEoB,GAAG,CAAC1D;oBAAQ;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEtClF,OAAA;sBAAGwE,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAgC;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAChE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAENlF,OAAA;oBAAAyE,QAAA,EACG,OAAOW,GAAG,CAAC1D,OAAO,KAAK,QAAQ,gBAC9B1B,OAAA;sBAAGwE,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,EAAEW,GAAG,CAAC1D;oBAAO;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,GAEpDE,GAAG,CAAC1D,OAAO,CAACyD,GAAG,CAAC,CAACG,IAAI,EAAEC,GAAG,KACxBD,IAAI,CAACvB,IAAI,KAAK,MAAM,gBAClB/D,OAAA;sBAAawE,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAEa,IAAI,CAACtB;oBAAI,GAApDuB,GAAG;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAqD,CAAC,gBAEjElF,OAAA;sBAAewE,SAAS,EAAC,MAAM;sBAAAC,QAAA,eAC7BzE,OAAA;wBACEwF,GAAG,EAAEF,IAAI,CAACrB,SAAS,CAACP,GAAI;wBACxB+B,GAAG,EAAC,uBAAuB;wBAC3BjB,SAAS,EAAC,wCAAwC;wBAClDkB,KAAK,EAAE;0BAAEC,SAAS,EAAEC,MAAM,CAACC,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG;wBAAQ;sBAAE;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpE;oBAAC,GANMK,GAAG;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAOR,CAET;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELE,GAAG,CAAC3D,IAAI,KAAK,MAAM,iBAClBzB,OAAA;gBAAKwE,SAAS,EAAC,+HAA+H;gBAAAC,QAAA,eAC5IzE,OAAA,CAACP,MAAM;kBAAC+E,SAAS,EAAC;gBAAkC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CACN;YAAA,GAxDIG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyDA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa,CAAC,eAGlBlF,OAAA,CAACZ,eAAe;YAAAqF,QAAA,EACb1D,QAAQ,iBACPf,OAAA,CAACb,MAAM,CAACuF,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BiB,IAAI,EAAE;gBAAElB,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE,CAAC;cAAG,CAAE;cAC7BL,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAE/BzE,OAAA;gBAAKwE,SAAS,EAAC,kHAAkH;gBAAAC,QAAA,eAC/HzE,OAAA,CAACR,OAAO;kBAACgF,SAAS,EAAC;gBAAkC;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACNlF,OAAA;gBAAKwE,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,eAC7FzE,OAAA;kBAAKwE,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBzE,OAAA,CAACb,MAAM,CAACuF,GAAG;oBACTI,OAAO,EAAE;sBAAEiB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBAAE,CAAE;oBAChCC,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,KAAK,EAAE;oBAAE,CAAE;oBAC1D5B,SAAS,EAAC;kBAAkC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACFlF,OAAA,CAACb,MAAM,CAACuF,GAAG;oBACTI,OAAO,EAAE;sBAAEiB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBAAE,CAAE;oBAChCC,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,KAAK,EAAE;oBAAI,CAAE;oBAC5D5B,SAAS,EAAC;kBAAkC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACFlF,OAAA,CAACb,MAAM,CAACuF,GAAG;oBACTI,OAAO,EAAE;sBAAEiB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;oBAAE,CAAE;oBAChCC,UAAU,EAAE;sBAAEC,QAAQ,EAAE,GAAG;sBAAEC,MAAM,EAAEC,QAAQ;sBAAEC,KAAK,EAAE;oBAAI,CAAE;oBAC5D5B,SAAS,EAAC;kBAAkC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC,eAElBlF,OAAA;YAAKqG,GAAG,EAAEpF;UAAe;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlF,OAAA;MAAKwE,SAAS,EAAC,kEAAkE;MAAAC,QAAA,eAC/EzE,OAAA;QAAKwE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhCzE,OAAA,CAACZ,eAAe;UAAAqF,QAAA,EACbhE,YAAY,iBACXT,OAAA,CAACb,MAAM,CAACuF,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAE0B,MAAM,EAAE;YAAE,CAAE;YACnCxB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAE0B,MAAM,EAAE;YAAO,CAAE;YACxCR,IAAI,EAAE;cAAElB,OAAO,EAAE,CAAC;cAAE0B,MAAM,EAAE;YAAE,CAAE;YAChC9B,SAAS,EAAC,uDAAuD;YAAAC,QAAA,eAEjEzE,OAAA;cAAKwE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCzE,OAAA;gBAAKwE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBzE,OAAA;kBACEwF,GAAG,EAAE/E,YAAa;kBAClBgF,GAAG,EAAC,SAAS;kBACbjB,SAAS,EAAC;gBAA0D;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACFlF,OAAA;kBACEuG,OAAO,EAAE3D,WAAY;kBACrB4B,SAAS,EAAC,yIAAyI;kBAAAC,QAAA,eAEnJzE,OAAA,CAACT,GAAG;oBAACiF,SAAS,EAAC;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNlF,OAAA;gBAAKwE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBzE,OAAA;kBAAGwE,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACnElF,OAAA;kBAAGwE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAElE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEiG;gBAAI;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAGlBlF,OAAA;UAAKwE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBzE,OAAA;YAAKwE,SAAS,EAAC,+JAA+J;YAAAC,QAAA,gBAE5KzE,OAAA;cACEuG,OAAO,EAAEA,CAAA;gBAAA,IAAAE,qBAAA;gBAAA,QAAAA,qBAAA,GAAMvF,YAAY,CAACa,OAAO,cAAA0E,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;cAAA,CAAC;cAC7ClC,SAAS,EAAC,qFAAqF;cAC/FmC,KAAK,EAAC,cAAc;cAAAlC,QAAA,eAEpBzE,OAAA,CAACV,WAAW;gBAACkF,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eAGTlF,OAAA;cACEqG,GAAG,EAAElF,WAAY;cACjB0B,KAAK,EAAExC,MAAO;cACduG,QAAQ,EAAGzE,CAAC,IAAK7B,SAAS,CAAC6B,CAAC,CAACE,MAAM,CAACQ,KAAK,CAAE;cAC3CgE,UAAU,EAAE/D,cAAe;cAC3BgE,WAAW,EAAC,+CAA+C;cAC3DtC,SAAS,EAAC,qHAAqH;cAC/HuC,IAAI,EAAE,CAAE;cACRrB,KAAK,EAAE;gBACLY,MAAM,EAAE,MAAM;gBACdU,SAAS,EAAE,MAAM;gBACjBrB,SAAS,EAAE;cACb,CAAE;cACFsB,OAAO,EAAG9E,CAAC,IAAK;gBACdA,CAAC,CAACE,MAAM,CAACqD,KAAK,CAACY,MAAM,GAAG,MAAM;gBAC9BnE,CAAC,CAACE,MAAM,CAACqD,KAAK,CAACY,MAAM,GAAGY,IAAI,CAACC,GAAG,CAAChF,CAAC,CAACE,MAAM,CAAC+E,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;cACrE;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGFlF,OAAA,CAACb,MAAM,CAACkI,MAAM;cACZC,UAAU,EAAE;gBAAEvB,KAAK,EAAE;cAAK,CAAE;cAC5BwB,QAAQ,EAAE;gBAAExB,KAAK,EAAE;cAAK,CAAE;cAC1BQ,OAAO,EAAErD,UAAW;cACpBsE,QAAQ,EAAE7G,SAAS,IAAK,CAACN,MAAM,CAAC8C,IAAI,CAAC,CAAC,IAAI,CAAC5C,SAAW;cACtDiE,SAAS,EAAG,iCACV7D,SAAS,IAAK,CAACN,MAAM,CAAC8C,IAAI,CAAC,CAAC,IAAI,CAAC5C,SAAU,GACvC,8CAA8C,GAC9C,oEACL,EAAE;cAAAkE,QAAA,EAEF9D,SAAS,gBACRX,OAAA,CAACL,QAAQ;gBAAC6E,SAAS,EAAC;cAAsB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE7ClF,OAAA,CAACX,MAAM;gBAACmF,SAAS,EAAC;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC9B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAGNlF,OAAA;YACEqG,GAAG,EAAEnF,YAAa;YAClB6C,IAAI,EAAC,MAAM;YACX0D,MAAM,EAAC,SAAS;YAChBb,QAAQ,EAAE1E,iBAAkB;YAC5BsC,SAAS,EAAC;UAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlF,OAAA;UAAGwE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEtD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChF,EAAA,CA/WQD,kBAAkB;AAAAyH,EAAA,GAAlBzH,kBAAkB;AAiX3B,eAAeA,kBAAkB;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}