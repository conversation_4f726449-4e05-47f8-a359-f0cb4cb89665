{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    id\n  } = useParams();\n  const {\n    user\n  } = useSelector(state => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n  const [isFlashing, setIsFlashing] = useState(false);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n    // Play enhanced sound effects and trigger flash animation\n    const playSound = () => {\n      // Trigger premium flash animation\n      setIsFlashing(true);\n      setTimeout(() => setIsFlashing(false), 3200); // Flash for 3.2 seconds (2 cycles of 0.8s each)\n\n      try {\n        if (isPassed) {\n          // Success sound with clapping\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration, type = 'sine') => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = type;\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Create clapping sound effect\n          const createClap = startTime => {\n            const noise = audioContext.createBufferSource();\n            const buffer = audioContext.createBuffer(1, audioContext.sampleRate * 0.1, audioContext.sampleRate);\n            const data = buffer.getChannelData(0);\n\n            // Generate white noise for clap\n            for (let i = 0; i < data.length; i++) {\n              data[i] = Math.random() * 2 - 1;\n            }\n            noise.buffer = buffer;\n            const filter = audioContext.createBiquadFilter();\n            filter.type = 'highpass';\n            filter.frequency.setValueAtTime(1000, startTime);\n            const gainNode = audioContext.createGain();\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);\n            noise.connect(filter);\n            filter.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            noise.start(startTime);\n            noise.stop(startTime + 0.1);\n          };\n          const now = audioContext.currentTime;\n\n          // Play success melody: C-E-G-C (major chord progression)\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n          // Add clapping sounds\n          createClap(now + 0.5);\n          createClap(now + 0.7);\n          createClap(now + 0.9);\n          createClap(now + 1.1);\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate premium animations based on pass/fail\n    if (isPassed) {\n      // Premium confetti explosion\n      const premiumConfetti = [];\n\n      // Main confetti burst (200 pieces)\n      for (let i = 0; i < 200; i++) {\n        const colors = ['#FFD700', '#FFA500', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FF69B4', '#32CD32', '#FF4500', '#9370DB', '#00CED1', '#FF1493', '#00FF7F', '#FF8C00', '#DA70D6'];\n        premiumConfetti.push({\n          id: `confetti_${i}`,\n          left: 20 + Math.random() * 60,\n          // More centered\n          delay: Math.random() * 2,\n          duration: 4 + Math.random() * 3,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],\n          size: 3 + Math.random() * 6,\n          type: 'confetti',\n          randomX: (Math.random() - 0.5) * 200 // For burst effect\n        });\n      }\n\n      // Premium sparkles (50 pieces)\n      for (let i = 0; i < 50; i++) {\n        premiumConfetti.push({\n          id: `sparkle_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 2 + Math.random() * 2,\n          color: ['#FFD700', '#FFFFFF', '#FFF700', '#FFFF00'][Math.floor(Math.random() * 4)],\n          size: 1 + Math.random() * 3,\n          type: 'sparkle'\n        });\n      }\n\n      // Burst confetti (100 pieces from center)\n      for (let i = 0; i < 100; i++) {\n        premiumConfetti.push({\n          id: `burst_${i}`,\n          left: 45 + Math.random() * 10,\n          // Center burst\n          delay: Math.random() * 0.5,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#FF69B4'][Math.floor(Math.random() * 4)],\n          shape: 'circle',\n          size: 2 + Math.random() * 4,\n          type: 'burst',\n          randomX: (Math.random() - 0.5) * 300\n        });\n      }\n      setConfetti(premiumConfetti);\n\n      // Remove all animations after 10 seconds\n      setTimeout(() => setConfetti([]), 10000);\n    } else {\n      // Premium motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡', '⭐', '✨', '🔥', '💎'];\n      for (let i = 0; i < 30; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 5 + Math.random() * 3,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true,\n          size: 2 + Math.random() * 2\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    }\n    playSound();\n  }, [isPassed]);\n\n  // Function to create animated letters\n  const createAnimatedText = (text, className = '') => {\n    return text.split('').map((letter, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `letter-animate ${className}`,\n      style: {\n        animationDelay: `${index * 0.1}s`\n      },\n      children: letter === ' ' ? '\\u00A0' : letter\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this));\n  };\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n    try {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: true\n      }));\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: false\n      }));\n    }\n  };\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen flex items-center justify-center p-4 relative overflow-hidden ${isPassed ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100' : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'} ${isFlashing ? isPassed ? 'flash-green' : 'flash-red' : ''}`,\n    children: [confetti.map(piece => {\n      if (piece.isMotivational) {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute opacity-90\",\n          style: {\n            left: `${piece.left}%`,\n            top: `${20 + Math.random() * 60}%`,\n            fontSize: `${piece.size || 2}rem`,\n            animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n            zIndex: 100\n          },\n          children: piece.emoji\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this);\n      }\n      if (piece.type === 'sparkle') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute\",\n          style: {\n            left: `${piece.left}%`,\n            width: `${piece.size}px`,\n            height: `${piece.size}px`,\n            background: `radial-gradient(circle, ${piece.color}, transparent)`,\n            borderRadius: '50%',\n            animation: `premium-sparkle ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n            top: `${Math.random() * 100}%`,\n            boxShadow: `0 0 ${piece.size * 2}px ${piece.color}`,\n            zIndex: 100\n          }\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this);\n      }\n      if (piece.type === 'burst') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute opacity-90\",\n          style: {\n            left: `${piece.left}%`,\n            width: `${piece.size}px`,\n            height: `${piece.size}px`,\n            backgroundColor: piece.color,\n            borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n            clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n            animation: `confetti-burst ${piece.duration}s ease-out ${piece.delay}s forwards`,\n            top: '40%',\n            '--random-x': `${piece.randomX}px`,\n            boxShadow: `0 0 ${piece.size}px ${piece.color}40`,\n            zIndex: 100\n          }\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this);\n      }\n\n      // Regular premium confetti\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute opacity-90\",\n        style: {\n          left: `${piece.left}%`,\n          width: `${piece.size}px`,\n          height: `${piece.size}px`,\n          backgroundColor: piece.color,\n          borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n          clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n          animation: `confetti-fall ${piece.duration}s ease-out ${piece.delay}s forwards`,\n          top: '-20px',\n          boxShadow: `0 0 ${piece.size}px ${piece.color}60`,\n          border: `1px solid ${piece.color}`,\n          background: `linear-gradient(45deg, ${piece.color}, ${piece.color}80)`,\n          zIndex: 100\n        }\n      }, piece.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-20px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n            transform: translateY(0px) rotate(36deg) scale(1);\n          }\n          100% {\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\n            opacity: 0;\n          }\n        }\n\n        @keyframes confetti-burst {\n          0% {\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          15% {\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\n            opacity: 0;\n          }\n        }\n\n        @keyframes premium-sparkle {\n          0%, 100% {\n            transform: scale(0) rotate(0deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.5) rotate(180deg);\n            opacity: 1;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes text-bounce {\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n          40% { transform: translateY(-20px) scale(1.1); }\n          60% { transform: translateY(-10px) scale(1.05); }\n        }\n\n        @keyframes text-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\n            transform: scale(1);\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\n            transform: scale(1.05);\n          }\n        }\n\n        @keyframes rainbow-text {\n          0% { color: #FF6B6B; }\n          16% { color: #FFD93D; }\n          33% { color: #6BCF7F; }\n          50% { color: #4D96FF; }\n          66% { color: #9B59B6; }\n          83% { color: #FF69B4; }\n          100% { color: #FF6B6B; }\n        }\n\n        @keyframes shake-celebrate {\n          0%, 100% { transform: translateX(0) rotate(0deg); }\n          10% { transform: translateX(-5px) rotate(-1deg); }\n          20% { transform: translateX(5px) rotate(1deg); }\n          30% { transform: translateX(-5px) rotate(-1deg); }\n          40% { transform: translateX(5px) rotate(1deg); }\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\n          60% { transform: translateX(3px) rotate(0.5deg); }\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\n          80% { transform: translateX(2px) rotate(0.5deg); }\n          90% { transform: translateX(-1px) rotate(0deg); }\n        }\n\n        @keyframes zoom-in-out {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.2); }\n        }\n\n        .animate-text-bounce {\n          animation: text-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-text-glow {\n          animation: text-glow 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-shake-celebrate {\n          animation: shake-celebrate 1s ease-in-out infinite;\n        }\n\n        .animate-zoom {\n          animation: zoom-in-out 2s ease-in-out infinite;\n        }\n\n        @keyframes elegant-scale {\n          0%, 100% {\n            transform: scale(1) rotateY(0deg);\n            text-shadow: 0 0 20px currentColor;\n          }\n          50% {\n            transform: scale(1.1) rotateY(5deg);\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\n          }\n        }\n\n        @keyframes premium-pulse {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 1;\n          }\n          50% {\n            transform: scale(1.05);\n            opacity: 0.9;\n          }\n        }\n\n        @keyframes smooth-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;\n            filter: brightness(1);\n          }\n          50% {\n            text-shadow: 0 0 30px currentColor, 0 0 50px currentColor, 0 0 70px currentColor;\n            filter: brightness(1.2);\n          }\n        }\n\n        @keyframes celebration-bounce {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0) scale(1);\n          }\n          40% {\n            transform: translateY(-15px) scale(1.05);\n          }\n          60% {\n            transform: translateY(-8px) scale(1.02);\n          }\n        }\n\n        .animate-elegant {\n          animation: elegant-scale 3s ease-in-out infinite;\n        }\n\n        .animate-premium-pulse {\n          animation: premium-pulse 2s ease-in-out infinite;\n        }\n\n        .animate-smooth-glow {\n          animation: smooth-glow 2.5s ease-in-out infinite;\n        }\n\n        .animate-celebration {\n          animation: celebration-bounce 2s ease-in-out infinite;\n        }\n\n        @keyframes premium-green-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        @keyframes premium-red-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        .flash-green {\n          animation: premium-green-flash 0.8s ease-in-out 2;\n        }\n\n        .flash-red {\n          animation: premium-red-flash 0.8s ease-in-out 2;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), isFlashing && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 pointer-events-none\",\n      style: {\n        background: isPassed ? 'radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)' : 'radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)',\n        animation: isPassed ? 'premium-green-flash 0.8s ease-in-out infinite' : 'premium-red-flash 0.8s ease-in-out infinite',\n        zIndex: 5\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white rounded-2xl shadow-2xl border-2 p-8 max-w-2xl w-full relative ${isPassed ? 'border-green-400 shadow-green-200' : 'border-red-400 shadow-red-200'} ${isFlashing ? 'shadow-3xl' : ''}`,\n      style: {\n        background: isFlashing ? isPassed ? 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))' : 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))' : 'white',\n        boxShadow: isFlashing ? isPassed ? '0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)' : '0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)' : '0 25px 50px rgba(0,0,0,0.15)',\n        zIndex: 10\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'}`,\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: `w-12 h-12 ${isPassed ? 'text-green-600' : 'text-red-600'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `text-6xl font-bold mb-4 ${isPassed ? 'text-green-600 animate-text-glow animate-text-bounce' : 'text-red-600 animate-shake-celebrate'}`,\n          children: isPassed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-3 animate-zoom\",\n            children: [\"\\uD83C\\uDF89 \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-rainbow\",\n              children: createAnimatedText('Congratulations!')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 20\n            }, this), \" \\uD83C\\uDF89\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-3\",\n            children: [\"\\uD83D\\uDCAA \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-text-glow\",\n              children: createAnimatedText('Keep Going!')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 20\n            }, this), \" \\uD83D\\uDCAA\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-2xl font-semibold mb-2 ${isPassed ? 'text-green-700 animate-text-bounce' : 'text-red-700 animate-zoom'}`,\n          children: isPassed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-rainbow\",\n            children: createAnimatedText('You Passed!')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-text-glow\",\n            children: createAnimatedText('You Can Do It!')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2 text-lg\",\n          children: [\"\\uD83D\\uDCDA \", quizName, \" - \", quizSubject]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-block px-8 py-4 rounded-2xl ${isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-5xl font-bold mb-2 ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-600\",\n            children: \"Your Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-green-600\",\n            children: correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-green-600 font-medium\",\n            children: \"\\u2705 Correct\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-red-600\",\n            children: totalQuestions - correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-600 font-medium\",\n            children: \"\\u274C Wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\uD83D\\uDCCA Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-blue-600\",\n            children: [Math.floor(timeTaken / 60), \":\", (timeTaken % 60).toString().padStart(2, '0')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\u23F1\\uFE0F Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 9\n      }, this), xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-purple-600 font-bold text-xl\",\n                children: [\"+\", xpData.xpAwarded || 0, \" XP\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Earned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (((user === null || user === void 0 ? void 0 : user.totalXP) || 0) + (xpData.xpAwarded || 0)).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", (user === null || user === void 0 ? void 0 : user.currentLevel) || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 11\n      }, this), !xpData && user && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Your Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (user.totalXP || 0).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", user.currentLevel || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCDA Learning Summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 800,\n        columnNumber: 9\n      }, this), resultDetails && resultDetails.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCCB Question by Question Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 max-h-96 overflow-y-auto\",\n          children: resultDetails.map((detail, index) => {\n            // Debug: Log question data to see what's available\n            console.log(`Question ${index + 1} data:`, detail);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl ${detail.isCorrect ? 'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300' : 'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'}`,\n              style: {\n                boxShadow: detail.isCorrect ? '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)' : '0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 ${detail.isCorrect ? 'bg-green-300 border-b-4 border-green-500' : 'bg-red-300 border-b-4 border-red-500'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-10 h-10 rounded-full flex items-center justify-center font-bold ${detail.isCorrect ? 'bg-green-500 text-white shadow-lg' : 'bg-red-500 text-white shadow-lg'}`,\n                    children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 851,\n                      columnNumber: 45\n                    }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 851,\n                      columnNumber: 79\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-bold text-gray-900 text-lg\",\n                      children: [\"Question \", index + 1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 855,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-3 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 859,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 858,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 854,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 bg-white p-3 rounded-lg border\",\n                    children: detail.questionText || detail.questionName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 874,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 21\n                }, this), (detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-lg border-2 ${detail.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-semibold text-gray-700\",\n                        children: \"\\uD83D\\uDCF7 Question Image:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 888,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 889,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 887,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-2 rounded-lg border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: detail.questionImage || detail.image || detail.imageUrl,\n                        alt: \"Question Image\",\n                        className: \"max-w-full h-auto rounded-lg shadow-sm mx-auto block\",\n                        style: {\n                          maxHeight: '300px'\n                        },\n                        onError: e => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'block';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 898,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\",\n                        style: {\n                          display: 'none'\n                        },\n                        children: \"\\uD83D\\uDCF7 Image could not be loaded\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 908,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 897,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-4 rounded-lg border-4 ${detail.isCorrect ? 'bg-green-50 border-green-500' : 'bg-red-50 border-red-500'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-6 h-6 rounded-full flex items-center justify-center ${detail.isCorrect ? 'bg-green-500' : 'bg-red-500'}`,\n                        children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 931,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 933,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 927,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Your Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 936,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 926,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `p-3 rounded-lg font-bold text-lg ${detail.isCorrect ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'}`,\n                      children: detail.userAnswer || 'No answer provided'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 921,\n                    columnNumber: 23\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-50 p-4 rounded-lg border-4 border-green-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 951,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 950,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Correct Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 953,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 949,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\",\n                      children: detail.correctAnswer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 955,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 25\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${loadingExplanations[`question_${index}`] ? 'bg-gray-400 cursor-not-allowed' : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'}`,\n                      onClick: () => fetchExplanation(index, detail),\n                      disabled: loadingExplanations[`question_${index}`],\n                      children: loadingExplanations[`question_${index}`] ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 975,\n                          columnNumber: 33\n                        }, this), \"Getting Explanation...\"]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 980,\n                          columnNumber: 33\n                        }, this), \"Get Explanation\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 964,\n                      columnNumber: 27\n                    }, this), explanations[`question_${index}`] && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-2 mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5 text-blue-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 990,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-bold text-blue-800\",\n                          children: \"\\uD83D\\uDCA1 Explanation:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 991,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 989,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-blue-700 leading-relaxed whitespace-pre-wrap\",\n                        children: explanations[`question_${index}`]\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 993,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 988,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 963,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 920,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 19\n              }, this)]\n            }, detail.questionId || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 More Quizzes button clicked!');\n            handleBackToQuizzes();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbHome, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 13\n          }, this), \"More Quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1013,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 Retake Quiz button clicked!');\n            handleRetakeQuiz();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1035,\n            columnNumber: 13\n          }, this), \"Retake Quiz\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1012,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 316,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"Ts/BUI5rSPpEvy+1LV+0+/PKtNw=\", false, function () {\n  return [useNavigate, useLocation, useParams, useSelector];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "startTransition", "useEffect", "useState", "useNavigate", "useLocation", "useParams", "useSelector", "TbTrophy", "TbCheck", "TbX", "TbHome", "TbStar", "TbBrain", "TbChartBar", "chatWithChatGPTToExplainAns", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "navigate", "location", "id", "user", "state", "resultData", "percentage", "correctAnswers", "totalQuestions", "timeTaken", "resultDetails", "xpData", "quizName", "quizSubject", "passingPercentage", "verdict", "isPassed", "confetti", "set<PERSON>on<PERSON>tti", "explanations", "setExplanations", "loadingExplanations", "setLoadingExplanations", "isFlashing", "setIsFlashing", "playSound", "setTimeout", "audioContext", "window", "AudioContext", "webkitAudioContext", "playTone", "frequency", "startTime", "duration", "type", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "createClap", "noise", "createBufferSource", "buffer", "createBuffer", "sampleRate", "data", "getChannelData", "i", "length", "Math", "random", "filter", "createBiquadFilter", "now", "currentTime", "error", "console", "log", "premiumConfetti", "colors", "push", "left", "delay", "color", "floor", "shape", "size", "randomX", "motivationalElements", "motivationalEmojis", "emoji", "isMotivational", "createAnimatedText", "text", "className", "split", "map", "letter", "index", "style", "animationDelay", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fetchExplanation", "questionIndex", "detail", "<PERSON><PERSON><PERSON>", "prev", "response", "question", "questionText", "questionName", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "userAnswer", "imageUrl", "questionImage", "image", "success", "explanation", "handleBackToQuizzes", "handleRetakeQuiz", "piece", "top", "fontSize", "animation", "zIndex", "width", "height", "background", "borderRadius", "boxShadow", "backgroundColor", "clipPath", "border", "jsx", "toString", "padStart", "xpAwarded", "totalXP", "toLocaleString", "currentLevel", "isCorrect", "questionType", "src", "alt", "maxHeight", "onError", "e", "target", "display", "nextS<PERSON>ling", "onClick", "disabled", "questionId", "preventDefault", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\n\nconst QuizResult = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { id } = useParams();\n  const { user } = useSelector((state) => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n  const [isFlashing, setIsFlashing] = useState(false);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n\n    // Play enhanced sound effects and trigger flash animation\n    const playSound = () => {\n      // Trigger premium flash animation\n      setIsFlashing(true);\n      setTimeout(() => setIsFlashing(false), 3200); // Flash for 3.2 seconds (2 cycles of 0.8s each)\n\n      try {\n        if (isPassed) {\n          // Success sound with clapping\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration, type = 'sine') => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = type;\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Create clapping sound effect\n          const createClap = (startTime) => {\n            const noise = audioContext.createBufferSource();\n            const buffer = audioContext.createBuffer(1, audioContext.sampleRate * 0.1, audioContext.sampleRate);\n            const data = buffer.getChannelData(0);\n\n            // Generate white noise for clap\n            for (let i = 0; i < data.length; i++) {\n              data[i] = Math.random() * 2 - 1;\n            }\n\n            noise.buffer = buffer;\n\n            const filter = audioContext.createBiquadFilter();\n            filter.type = 'highpass';\n            filter.frequency.setValueAtTime(1000, startTime);\n\n            const gainNode = audioContext.createGain();\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);\n\n            noise.connect(filter);\n            filter.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            noise.start(startTime);\n            noise.stop(startTime + 0.1);\n          };\n\n          const now = audioContext.currentTime;\n\n          // Play success melody: C-E-G-C (major chord progression)\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n          // Add clapping sounds\n          createClap(now + 0.5);\n          createClap(now + 0.7);\n          createClap(now + 0.9);\n          createClap(now + 1.1);\n\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate premium animations based on pass/fail\n    if (isPassed) {\n      // Premium confetti explosion\n      const premiumConfetti = [];\n\n      // Main confetti burst (200 pieces)\n      for (let i = 0; i < 200; i++) {\n        const colors = [\n          '#FFD700', '#FFA500', '#FF6B6B', '#4ECDC4', '#45B7D1',\n          '#96CEB4', '#FF69B4', '#32CD32', '#FF4500', '#9370DB',\n          '#00CED1', '#FF1493', '#00FF7F', '#FF8C00', '#DA70D6'\n        ];\n\n        premiumConfetti.push({\n          id: `confetti_${i}`,\n          left: 20 + Math.random() * 60, // More centered\n          delay: Math.random() * 2,\n          duration: 4 + Math.random() * 3,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],\n          size: 3 + Math.random() * 6,\n          type: 'confetti',\n          randomX: (Math.random() - 0.5) * 200 // For burst effect\n        });\n      }\n\n      // Premium sparkles (50 pieces)\n      for (let i = 0; i < 50; i++) {\n        premiumConfetti.push({\n          id: `sparkle_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 2 + Math.random() * 2,\n          color: ['#FFD700', '#FFFFFF', '#FFF700', '#FFFF00'][Math.floor(Math.random() * 4)],\n          size: 1 + Math.random() * 3,\n          type: 'sparkle'\n        });\n      }\n\n      // Burst confetti (100 pieces from center)\n      for (let i = 0; i < 100; i++) {\n        premiumConfetti.push({\n          id: `burst_${i}`,\n          left: 45 + Math.random() * 10, // Center burst\n          delay: Math.random() * 0.5,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#FF69B4'][Math.floor(Math.random() * 4)],\n          shape: 'circle',\n          size: 2 + Math.random() * 4,\n          type: 'burst',\n          randomX: (Math.random() - 0.5) * 300\n        });\n      }\n\n      setConfetti(premiumConfetti);\n\n      // Remove all animations after 10 seconds\n      setTimeout(() => setConfetti([]), 10000);\n    } else {\n      // Premium motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡', '⭐', '✨', '🔥', '💎'];\n\n      for (let i = 0; i < 30; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 5 + Math.random() * 3,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true,\n          size: 2 + Math.random() * 2\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    }\n\n    playSound();\n  }, [isPassed]);\n\n  // Function to create animated letters\n  const createAnimatedText = (text, className = '') => {\n    return text.split('').map((letter, index) => (\n      <span\n        key={index}\n        className={`letter-animate ${className}`}\n        style={{ animationDelay: `${index * 0.1}s` }}\n      >\n        {letter === ' ' ? '\\u00A0' : letter}\n      </span>\n    ));\n  };\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n\n    try {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: true }));\n\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: false }));\n    }\n  };\n\n\n\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center p-4 relative overflow-hidden ${\n      isPassed\n        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100'\n        : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'\n    } ${isFlashing ? (isPassed ? 'flash-green' : 'flash-red') : ''}`}>\n\n      {/* Premium Confetti System */}\n      {confetti.map((piece) => {\n        if (piece.isMotivational) {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                top: `${20 + Math.random() * 60}%`,\n                fontSize: `${piece.size || 2}rem`,\n                animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                zIndex: 100\n              }}\n            >\n              {piece.emoji}\n            </div>\n          );\n        }\n\n        if (piece.type === 'sparkle') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                background: `radial-gradient(circle, ${piece.color}, transparent)`,\n                borderRadius: '50%',\n                animation: `premium-sparkle ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                top: `${Math.random() * 100}%`,\n                boxShadow: `0 0 ${piece.size * 2}px ${piece.color}`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        if (piece.type === 'burst') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                backgroundColor: piece.color,\n                borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n                clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n                animation: `confetti-burst ${piece.duration}s ease-out ${piece.delay}s forwards`,\n                top: '40%',\n                '--random-x': `${piece.randomX}px`,\n                boxShadow: `0 0 ${piece.size}px ${piece.color}40`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        // Regular premium confetti\n        return (\n          <div\n            key={piece.id}\n            className=\"absolute opacity-90\"\n            style={{\n              left: `${piece.left}%`,\n              width: `${piece.size}px`,\n              height: `${piece.size}px`,\n              backgroundColor: piece.color,\n              borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n              clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n              animation: `confetti-fall ${piece.duration}s ease-out ${piece.delay}s forwards`,\n              top: '-20px',\n              boxShadow: `0 0 ${piece.size}px ${piece.color}60`,\n              border: `1px solid ${piece.color}`,\n              background: `linear-gradient(45deg, ${piece.color}, ${piece.color}80)`,\n              zIndex: 100\n            }}\n          />\n        );\n      })}\n\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-20px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n            transform: translateY(0px) rotate(36deg) scale(1);\n          }\n          100% {\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\n            opacity: 0;\n          }\n        }\n\n        @keyframes confetti-burst {\n          0% {\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          15% {\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\n            opacity: 0;\n          }\n        }\n\n        @keyframes premium-sparkle {\n          0%, 100% {\n            transform: scale(0) rotate(0deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.5) rotate(180deg);\n            opacity: 1;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes text-bounce {\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n          40% { transform: translateY(-20px) scale(1.1); }\n          60% { transform: translateY(-10px) scale(1.05); }\n        }\n\n        @keyframes text-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\n            transform: scale(1);\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\n            transform: scale(1.05);\n          }\n        }\n\n        @keyframes rainbow-text {\n          0% { color: #FF6B6B; }\n          16% { color: #FFD93D; }\n          33% { color: #6BCF7F; }\n          50% { color: #4D96FF; }\n          66% { color: #9B59B6; }\n          83% { color: #FF69B4; }\n          100% { color: #FF6B6B; }\n        }\n\n        @keyframes shake-celebrate {\n          0%, 100% { transform: translateX(0) rotate(0deg); }\n          10% { transform: translateX(-5px) rotate(-1deg); }\n          20% { transform: translateX(5px) rotate(1deg); }\n          30% { transform: translateX(-5px) rotate(-1deg); }\n          40% { transform: translateX(5px) rotate(1deg); }\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\n          60% { transform: translateX(3px) rotate(0.5deg); }\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\n          80% { transform: translateX(2px) rotate(0.5deg); }\n          90% { transform: translateX(-1px) rotate(0deg); }\n        }\n\n        @keyframes zoom-in-out {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.2); }\n        }\n\n        .animate-text-bounce {\n          animation: text-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-text-glow {\n          animation: text-glow 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-shake-celebrate {\n          animation: shake-celebrate 1s ease-in-out infinite;\n        }\n\n        .animate-zoom {\n          animation: zoom-in-out 2s ease-in-out infinite;\n        }\n\n        @keyframes elegant-scale {\n          0%, 100% {\n            transform: scale(1) rotateY(0deg);\n            text-shadow: 0 0 20px currentColor;\n          }\n          50% {\n            transform: scale(1.1) rotateY(5deg);\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\n          }\n        }\n\n        @keyframes premium-pulse {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 1;\n          }\n          50% {\n            transform: scale(1.05);\n            opacity: 0.9;\n          }\n        }\n\n        @keyframes smooth-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor;\n            filter: brightness(1);\n          }\n          50% {\n            text-shadow: 0 0 30px currentColor, 0 0 50px currentColor, 0 0 70px currentColor;\n            filter: brightness(1.2);\n          }\n        }\n\n        @keyframes celebration-bounce {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0) scale(1);\n          }\n          40% {\n            transform: translateY(-15px) scale(1.05);\n          }\n          60% {\n            transform: translateY(-8px) scale(1.02);\n          }\n        }\n\n        .animate-elegant {\n          animation: elegant-scale 3s ease-in-out infinite;\n        }\n\n        .animate-premium-pulse {\n          animation: premium-pulse 2s ease-in-out infinite;\n        }\n\n        .animate-smooth-glow {\n          animation: smooth-glow 2.5s ease-in-out infinite;\n        }\n\n        .animate-celebration {\n          animation: celebration-bounce 2s ease-in-out infinite;\n        }\n\n        @keyframes premium-green-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        @keyframes premium-red-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        .flash-green {\n          animation: premium-green-flash 0.8s ease-in-out 2;\n        }\n\n        .flash-red {\n          animation: premium-red-flash 0.8s ease-in-out 2;\n        }\n      `}</style>\n\n      {/* Premium Overlay Effect */}\n      {isFlashing && (\n        <div\n          className=\"fixed inset-0 pointer-events-none\"\n          style={{\n            background: isPassed\n              ? 'radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)'\n              : 'radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)',\n            animation: isPassed ? 'premium-green-flash 0.8s ease-in-out infinite' : 'premium-red-flash 0.8s ease-in-out infinite',\n            zIndex: 5\n          }}\n        />\n      )}\n\n      <div className={`bg-white rounded-2xl shadow-2xl border-2 p-8 max-w-2xl w-full relative ${\n        isPassed ? 'border-green-400 shadow-green-200' : 'border-red-400 shadow-red-200'\n      } ${isFlashing ? 'shadow-3xl' : ''}`}\n      style={{\n        background: isFlashing\n          ? (isPassed\n              ? 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))'\n              : 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))')\n          : 'white',\n        boxShadow: isFlashing\n          ? (isPassed\n              ? '0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)'\n              : '0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)')\n          : '0 25px 50px rgba(0,0,0,0.15)',\n        zIndex: 10\n      }}>\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${\n            isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'\n          }`}>\n\n            <TbTrophy className={`w-12 h-12 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`} />\n          </div>\n          \n          <h1 className={`text-6xl font-bold mb-4 ${\n            isPassed\n              ? 'text-green-600 animate-text-glow animate-text-bounce'\n              : 'text-red-600 animate-shake-celebrate'\n          }`}>\n            {isPassed ? (\n              <span className=\"flex items-center justify-center gap-3 animate-zoom\">\n                🎉 <span className=\"animate-rainbow\">{createAnimatedText('Congratulations!')}</span> 🎉\n              </span>\n            ) : (\n              <span className=\"flex items-center justify-center gap-3\">\n                💪 <span className=\"animate-text-glow\">{createAnimatedText('Keep Going!')}</span> 💪\n              </span>\n            )}\n          </h1>\n\n          <div className={`text-2xl font-semibold mb-2 ${\n            isPassed\n              ? 'text-green-700 animate-text-bounce'\n              : 'text-red-700 animate-zoom'\n          }`}>\n            {isPassed ? (\n              <span className=\"animate-rainbow\">{createAnimatedText('You Passed!')}</span>\n            ) : (\n              <span className=\"animate-text-glow\">{createAnimatedText('You Can Do It!')}</span>\n            )}\n          </div>\n\n          <p className=\"text-gray-600 mt-2 text-lg\">\n            📚 {quizName} - {quizSubject}\n          </p>\n        </div>\n\n        {/* Score Display */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-block px-8 py-4 rounded-2xl ${\n            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'\n          }`}>\n            <div className={`text-5xl font-bold mb-2 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {percentage}%\n            </div>\n            <div className=\"text-gray-600\">\n              Your Score\n            </div>\n          </div>\n        </div>\n\n\n\n        {/* Streamlined Results Cards */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-green-600\">{correctAnswers}</div>\n            <div className=\"text-sm text-green-600 font-medium\">✅ Correct</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-red-600\">{totalQuestions - correctAnswers}</div>\n            <div className=\"text-sm text-red-600 font-medium\">❌ Wrong</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className={`text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`}>{percentage}%</div>\n            <div className=\"text-sm text-gray-600 font-medium\">📊 Score</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-blue-600\">\n              {Math.floor(timeTaken / 60)}:{(timeTaken % 60).toString().padStart(2, '0')}\n            </div>\n            <div className=\"text-sm text-gray-600 font-medium\">⏱️ Time</div>\n          </div>\n        </div>\n\n        {/* Streamlined XP Section */}\n        {xpData && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-purple-600 font-bold text-xl\">+{xpData.xpAwarded || 0} XP</div>\n                  <div className=\"text-sm text-gray-600\">Earned</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {((user?.totalXP || 0) + (xpData.xpAwarded || 0)).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user?.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Streamlined XP Section (for users without XP data) */}\n        {!xpData && user && (\n          <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-sm text-gray-600\">Your Progress</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {(user.totalXP || 0).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Summary Section */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n              <TbBrain className=\"w-6 h-6 text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">📚 Learning Summary</h3>\n          </div>\n\n          {/* Simplified Learning Summary - Only Question Review */}\n        </div>\n\n        {/* Detailed Question Breakdown */}\n        {resultDetails && resultDetails.length > 0 && (\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\">\n                <TbChartBar className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">📋 Question by Question Review</h3>\n            </div>\n\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              {resultDetails.map((detail, index) => {\n                // Debug: Log question data to see what's available\n                console.log(`Question ${index + 1} data:`, detail);\n                return (\n                <div\n                  key={detail.questionId || index}\n                  className={`rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl ${\n                    detail.isCorrect\n                      ? 'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300'\n                      : 'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'\n                  }`}\n                  style={{\n                    boxShadow: detail.isCorrect\n                      ? '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)'\n                      : '0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'\n                  }}\n                >\n                  {/* Question Header */}\n                  <div className={`p-4 ${\n                    detail.isCorrect\n                      ? 'bg-green-300 border-b-4 border-green-500'\n                      : 'bg-red-300 border-b-4 border-red-500'\n                  }`}>\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${\n                        detail.isCorrect\n                          ? 'bg-green-500 text-white shadow-lg'\n                          : 'bg-red-500 text-white shadow-lg'\n                      }`}>\n                        {detail.isCorrect ? <TbCheck className=\"w-5 h-5\" /> : <TbX className=\"w-5 h-5\" />}\n                      </div>\n\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-bold text-gray-900 text-lg\">\n                          Question {index + 1}\n                        </h4>\n                        <div className=\"flex items-center gap-2 mt-1\">\n                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${\n                            detail.isCorrect\n                              ? 'bg-green-500 text-white'\n                              : 'bg-red-500 text-white'\n                          }`}>\n                            {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4\">\n                    <div className=\"mb-4\">\n                      <p className=\"text-gray-700 bg-white p-3 rounded-lg border\">\n                        {detail.questionText || detail.questionName}\n                      </p>\n                    </div>\n\n                    {/* Show Image for image questions or any question with an image */}\n                    {(detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && (\n                      <div className=\"mb-4\">\n                        <div className={`p-3 rounded-lg border-2 ${\n                          detail.isCorrect\n                            ? 'bg-green-50 border-green-200'\n                            : 'bg-red-50 border-red-200'\n                        }`}>\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <span className=\"text-sm font-semibold text-gray-700\">📷 Question Image:</span>\n                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${\n                              detail.isCorrect\n                                ? 'bg-green-500 text-white'\n                                : 'bg-red-500 text-white'\n                            }`}>\n                              {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                            </span>\n                          </div>\n                          <div className=\"bg-white p-2 rounded-lg border\">\n                            <img\n                              src={detail.questionImage || detail.image || detail.imageUrl}\n                              alt=\"Question Image\"\n                              className=\"max-w-full h-auto rounded-lg shadow-sm mx-auto block\"\n                              style={{ maxHeight: '300px' }}\n                              onError={(e) => {\n                                e.target.style.display = 'none';\n                                e.target.nextSibling.style.display = 'block';\n                              }}\n                            />\n                            <div\n                              className=\"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\"\n                              style={{ display: 'none' }}\n                            >\n                              📷 Image could not be loaded\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Enhanced Answer Section with Color Indicators */}\n                    <div className=\"space-y-3\">\n                      <div className={`p-4 rounded-lg border-4 ${\n                        detail.isCorrect\n                          ? 'bg-green-50 border-green-500'\n                          : 'bg-red-50 border-red-500'\n                      }`}>\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${\n                            detail.isCorrect ? 'bg-green-500' : 'bg-red-500'\n                          }`}>\n                            {detail.isCorrect ? (\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            ) : (\n                              <TbX className=\"w-4 h-4 text-white\" />\n                            )}\n                          </div>\n                          <span className=\"font-semibold text-gray-700\">Your Answer:</span>\n                        </div>\n                        <div className={`p-3 rounded-lg font-bold text-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-100 text-green-700 border border-green-200'\n                            : 'bg-red-100 text-red-700 border border-red-200'\n                        }`}>\n                          {detail.userAnswer || 'No answer provided'}\n                        </div>\n                      </div>\n\n                      {!detail.isCorrect && (\n                        <div className=\"bg-green-50 p-4 rounded-lg border-4 border-green-500\">\n                          <div className=\"flex items-center gap-3 mb-2\">\n                            <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\">\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            </div>\n                            <span className=\"font-semibold text-gray-700\">Correct Answer:</span>\n                          </div>\n                          <div className=\"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\">\n                            {detail.correctAnswer}\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Explanation Button for Wrong Answers */}\n                      {!detail.isCorrect && (\n                        <div className=\"mt-3\">\n                          <button\n                            className={`w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${\n                              loadingExplanations[`question_${index}`]\n                                ? 'bg-gray-400 cursor-not-allowed'\n                                : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'\n                            }`}\n                            onClick={() => fetchExplanation(index, detail)}\n                            disabled={loadingExplanations[`question_${index}`]}\n                          >\n                            {loadingExplanations[`question_${index}`] ? (\n                              <>\n                                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                                Getting Explanation...\n                              </>\n                            ) : (\n                              <>\n                                <TbBrain className=\"w-5 h-5\" />\n                                Get Explanation\n                              </>\n                            )}\n                          </button>\n\n                          {/* Explanation Display */}\n                          {explanations[`question_${index}`] && (\n                            <div className=\"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\">\n                              <div className=\"flex items-center gap-2 mb-3\">\n                                <TbBrain className=\"w-5 h-5 text-blue-600\" />\n                                <h6 className=\"font-bold text-blue-800\">💡 Explanation:</h6>\n                              </div>\n                              <div className=\"text-blue-700 leading-relaxed whitespace-pre-wrap\">\n                                {explanations[`question_${index}`]}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                );\n              })}\n            </div>\n\n\n          </div>\n        )}\n\n        {/* Actions */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 More Quizzes button clicked!');\n              handleBackToQuizzes();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbHome className=\"w-5 h-5\" />\n            More Quizzes\n          </button>\n\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 Retake Quiz button clicked!');\n              handleRetakeQuiz();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbTrophy className=\"w-5 h-5\" />\n            Retake Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACnE,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AAC5F,SAASC,2BAA2B,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB;EAAG,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEmB;EAAK,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD;EACA,MAAME,UAAU,GAAGJ,QAAQ,CAACG,KAAK,IAAI;IACnCE,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,EAAE;IACjBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,SAAS;IACtBC,iBAAiB,EAAE,EAAE;IACrBC,OAAO,EAAE;EACX,CAAC;EAED,MAAM;IACJT,UAAU;IACVC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTE,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,iBAAiB;IACjBC,OAAO;IACPL;EACF,CAAC,GAAGL,UAAU;EACd,MAAMW,QAAQ,GAAGD,OAAO,KAAK,MAAM,IAAIT,UAAU,KAAKQ,iBAAiB,IAAI,EAAE,CAAC;EAE9E,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACwC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClE,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAD,SAAS,CAAC,MAAM;IAEd;IACA,MAAM6C,SAAS,GAAGA,CAAA,KAAM;MACtB;MACAD,aAAa,CAAC,IAAI,CAAC;MACnBE,UAAU,CAAC,MAAMF,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAE9C,IAAI;QACF,IAAIR,QAAQ,EAAE;UACZ;UACA,MAAMW,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;;UAE7E;UACA,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,GAAG,MAAM,KAAK;YAClE,MAAMC,UAAU,GAAGT,YAAY,CAACU,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGX,YAAY,CAACY,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;YAE1CL,UAAU,CAACJ,SAAS,CAACU,cAAc,CAACV,SAAS,EAAEC,SAAS,CAAC;YACzDG,UAAU,CAACD,IAAI,GAAGA,IAAI;YAEtBG,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAET,SAAS,CAAC;YAC1CK,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC5DK,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEE,UAAU,CAACU,KAAK,CAACb,SAAS,CAAC;YAC3BG,UAAU,CAACW,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAMc,UAAU,GAAIf,SAAS,IAAK;YAChC,MAAMgB,KAAK,GAAGtB,YAAY,CAACuB,kBAAkB,CAAC,CAAC;YAC/C,MAAMC,MAAM,GAAGxB,YAAY,CAACyB,YAAY,CAAC,CAAC,EAAEzB,YAAY,CAAC0B,UAAU,GAAG,GAAG,EAAE1B,YAAY,CAAC0B,UAAU,CAAC;YACnG,MAAMC,IAAI,GAAGH,MAAM,CAACI,cAAc,CAAC,CAAC,CAAC;;YAErC;YACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;cACpCF,IAAI,CAACE,CAAC,CAAC,GAAGE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;YACjC;YAEAV,KAAK,CAACE,MAAM,GAAGA,MAAM;YAErB,MAAMS,MAAM,GAAGjC,YAAY,CAACkC,kBAAkB,CAAC,CAAC;YAChDD,MAAM,CAACzB,IAAI,GAAG,UAAU;YACxByB,MAAM,CAAC5B,SAAS,CAACU,cAAc,CAAC,IAAI,EAAET,SAAS,CAAC;YAEhD,MAAMK,QAAQ,GAAGX,YAAY,CAACY,UAAU,CAAC,CAAC;YAC1CD,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAET,SAAS,CAAC;YAC1CK,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC5DK,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAG,GAAG,CAAC;YAEjEgB,KAAK,CAACT,OAAO,CAACoB,MAAM,CAAC;YACrBA,MAAM,CAACpB,OAAO,CAACF,QAAQ,CAAC;YACxBA,QAAQ,CAACE,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;YAE1CQ,KAAK,CAACH,KAAK,CAACb,SAAS,CAAC;YACtBgB,KAAK,CAACF,IAAI,CAACd,SAAS,GAAG,GAAG,CAAC;UAC7B,CAAC;UAED,MAAM6B,GAAG,GAAGnC,YAAY,CAACoC,WAAW;;UAEpC;UACAhC,QAAQ,CAAC,MAAM,EAAE+B,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAC5B/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClC/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClC/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;UAElC;UACAd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;UACrBd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;UACrBd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;UACrBd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;QAEvB,CAAC,MAAM;UACL;UACA,MAAMnC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;UAE7E,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,KAAK;YACnD,MAAME,UAAU,GAAGT,YAAY,CAACU,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGX,YAAY,CAACY,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;YAE1CL,UAAU,CAACJ,SAAS,CAACU,cAAc,CAACV,SAAS,EAAEC,SAAS,CAAC;YACzDG,UAAU,CAACD,IAAI,GAAG,MAAM;YAExBG,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAET,SAAS,CAAC;YAC1CK,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,IAAI,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC7DK,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEE,UAAU,CAACU,KAAK,CAACb,SAAS,CAAC;YAC3BG,UAAU,CAACW,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAM4B,GAAG,GAAGnC,YAAY,CAACoC,WAAW;UACpChC,QAAQ,CAAC,GAAG,EAAE+B,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UACzB/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACpC;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACpC;IACF,CAAC;;IAED;IACA,IAAIlD,QAAQ,EAAE;MACZ;MACA,MAAMmD,eAAe,GAAG,EAAE;;MAE1B;MACA,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5B,MAAMY,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CACtD;QAEDD,eAAe,CAACE,IAAI,CAAC;UACnBnE,EAAE,EAAG,YAAWsD,CAAE,EAAC;UACnBc,IAAI,EAAE,EAAE,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UAAE;UAC/BY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Ba,KAAK,EAAEJ,MAAM,CAACV,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGS,MAAM,CAACX,MAAM,CAAC,CAAC;UACxDiB,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAChB,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACtEgB,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC3BxB,IAAI,EAAE,UAAU;UAChByC,OAAO,EAAE,CAAClB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;QACvC,CAAC,CAAC;MACJ;;MAEA;MACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BW,eAAe,CAACE,IAAI,CAAC;UACnBnE,EAAE,EAAG,WAAUsD,CAAE,EAAC;UAClBc,IAAI,EAAEZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Ba,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACd,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClFgB,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC3BxB,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;;MAEA;MACA,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5BW,eAAe,CAACE,IAAI,CAAC;UACnBnE,EAAE,EAAG,SAAQsD,CAAE,EAAC;UAChBc,IAAI,EAAE,EAAE,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UAAE;UAC/BY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC1BzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Ba,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACd,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClFe,KAAK,EAAE,QAAQ;UACfC,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC3BxB,IAAI,EAAE,OAAO;UACbyC,OAAO,EAAE,CAAClB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;QACnC,CAAC,CAAC;MACJ;MAEAzC,WAAW,CAACiD,eAAe,CAAC;;MAE5B;MACAzC,UAAU,CAAC,MAAMR,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;IAC1C,CAAC,MAAM;MACL;MACA,MAAM2D,oBAAoB,GAAG,EAAE;MAC/B,MAAMC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;MAErF,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BqB,oBAAoB,CAACR,IAAI,CAAC;UACxBnE,EAAE,EAAG,YAAWsD,CAAE,EAAC;UACnBc,IAAI,EAAEZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BoB,KAAK,EAAED,kBAAkB,CAACpB,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGmB,kBAAkB,CAACrB,MAAM,CAAC,CAAC;UAChFuB,cAAc,EAAE,IAAI;UACpBL,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAC5B,CAAC,CAAC;MACJ;MACAzC,WAAW,CAAC2D,oBAAoB,CAAC;;MAEjC;MACAnD,UAAU,CAAC,MAAMR,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACzC;IAEAO,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMiE,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,EAAE,KAAK;IACnD,OAAOD,IAAI,CAACE,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACtC5F,OAAA;MAEEwF,SAAS,EAAG,kBAAiBA,SAAU,EAAE;MACzCK,KAAK,EAAE;QAAEC,cAAc,EAAG,GAAEF,KAAK,GAAG,GAAI;MAAG,CAAE;MAAAG,QAAA,EAE5CJ,MAAM,KAAK,GAAG,GAAG,QAAQ,GAAGA;IAAM,GAJ9BC,KAAK;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKN,CACP,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,MAAM,KAAK;IACxD,MAAMC,WAAW,GAAI,YAAWF,aAAc,EAAC;;IAE/C;IACA,IAAI3E,mBAAmB,CAAC6E,WAAW,CAAC,IAAI/E,YAAY,CAAC+E,WAAW,CAAC,EAAE;MACjE;IACF;IAEA,IAAI;MACF5E,sBAAsB,CAAC6E,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,WAAW,GAAG;MAAK,CAAC,CAAC,CAAC;MAElE,MAAME,QAAQ,GAAG,MAAM3G,2BAA2B,CAAC;QACjD4G,QAAQ,EAAEJ,MAAM,CAACK,YAAY,IAAIL,MAAM,CAACM,YAAY;QACpDC,cAAc,EAAEP,MAAM,CAACQ,aAAa;QACpCC,UAAU,EAAET,MAAM,CAACS,UAAU;QAC7BC,QAAQ,EAAEV,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAQ,IAAI;MACvE,CAAC,CAAC;MAEF,IAAIP,QAAQ,CAACU,OAAO,EAAE;QACpB1F,eAAe,CAAC+E,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACD,WAAW,GAAGE,QAAQ,CAACW;QAC1B,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL9C,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEoC,QAAQ,CAACpC,KAAK,CAAC;QAC7D5C,eAAe,CAAC+E,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACD,WAAW,GAAG;QACjB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD5C,eAAe,CAAC+E,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACD,WAAW,GAAG;MACjB,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACR5E,sBAAsB,CAAC6E,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,WAAW,GAAG;MAAM,CAAC,CAAC,CAAC;IACrE;EACF,CAAC;EAID,MAAMc,mBAAmB,GAAGA,CAAA,KAAM;IAChC/C,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CvF,eAAe,CAAC,MAAM;MACpBqB,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiH,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhE,EAAE,CAAC;IAC5C,IAAIA,EAAE,EAAE;MACNvB,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAE,SAAQE,EAAG,OAAM,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL+D,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DvF,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEL,OAAA;IAAKwF,SAAS,EAAG,8EACfnE,QAAQ,GACJ,4DAA4D,GAC5D,yDACL,IAAGO,UAAU,GAAIP,QAAQ,GAAG,aAAa,GAAG,WAAW,GAAI,EAAG,EAAE;IAAA0E,QAAA,GAG9DzE,QAAQ,CAACoE,GAAG,CAAE6B,KAAK,IAAK;MACvB,IAAIA,KAAK,CAAClC,cAAc,EAAE;QACxB,oBACErF,OAAA;UAEEwF,SAAS,EAAC,qBAAqB;UAC/BK,KAAK,EAAE;YACLlB,IAAI,EAAG,GAAE4C,KAAK,CAAC5C,IAAK,GAAE;YACtB6C,GAAG,EAAG,GAAE,EAAE,GAAGzD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAG,GAAE;YAClCyD,QAAQ,EAAG,GAAEF,KAAK,CAACvC,IAAI,IAAI,CAAE,KAAI;YACjC0C,SAAS,EAAG,cAAaH,KAAK,CAAChF,QAAS,iBAAgBgF,KAAK,CAAC3C,KAAM,YAAW;YAC/E+C,MAAM,EAAE;UACV,CAAE;UAAA5B,QAAA,EAEDwB,KAAK,CAACnC;QAAK,GAVPmC,KAAK,CAAChH,EAAE;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWV,CAAC;MAEV;MAEA,IAAIoB,KAAK,CAAC/E,IAAI,KAAK,SAAS,EAAE;QAC5B,oBACExC,OAAA;UAEEwF,SAAS,EAAC,UAAU;UACpBK,KAAK,EAAE;YACLlB,IAAI,EAAG,GAAE4C,KAAK,CAAC5C,IAAK,GAAE;YACtBiD,KAAK,EAAG,GAAEL,KAAK,CAACvC,IAAK,IAAG;YACxB6C,MAAM,EAAG,GAAEN,KAAK,CAACvC,IAAK,IAAG;YACzB8C,UAAU,EAAG,2BAA0BP,KAAK,CAAC1C,KAAM,gBAAe;YAClEkD,YAAY,EAAE,KAAK;YACnBL,SAAS,EAAG,mBAAkBH,KAAK,CAAChF,QAAS,iBAAgBgF,KAAK,CAAC3C,KAAM,YAAW;YACpF4C,GAAG,EAAG,GAAEzD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC9BgE,SAAS,EAAG,OAAMT,KAAK,CAACvC,IAAI,GAAG,CAAE,MAAKuC,KAAK,CAAC1C,KAAM,EAAC;YACnD8C,MAAM,EAAE;UACV;QAAE,GAZGJ,KAAK,CAAChH,EAAE;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAad,CAAC;MAEN;MAEA,IAAIoB,KAAK,CAAC/E,IAAI,KAAK,OAAO,EAAE;QAC1B,oBACExC,OAAA;UAEEwF,SAAS,EAAC,qBAAqB;UAC/BK,KAAK,EAAE;YACLlB,IAAI,EAAG,GAAE4C,KAAK,CAAC5C,IAAK,GAAE;YACtBiD,KAAK,EAAG,GAAEL,KAAK,CAACvC,IAAK,IAAG;YACxB6C,MAAM,EAAG,GAAEN,KAAK,CAACvC,IAAK,IAAG;YACzBiD,eAAe,EAAEV,KAAK,CAAC1C,KAAK;YAC5BkD,YAAY,EAAER,KAAK,CAACxC,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAGwC,KAAK,CAACxC,KAAK,KAAK,UAAU,GAAG,GAAG,GAAG,IAAI;YACxFmD,QAAQ,EAAEX,KAAK,CAACxC,KAAK,KAAK,UAAU,GAAG,qCAAqC,GAAG,MAAM;YACrF2C,SAAS,EAAG,kBAAiBH,KAAK,CAAChF,QAAS,cAAagF,KAAK,CAAC3C,KAAM,YAAW;YAChF4C,GAAG,EAAE,KAAK;YACV,YAAY,EAAG,GAAED,KAAK,CAACtC,OAAQ,IAAG;YAClC+C,SAAS,EAAG,OAAMT,KAAK,CAACvC,IAAK,MAAKuC,KAAK,CAAC1C,KAAM,IAAG;YACjD8C,MAAM,EAAE;UACV;QAAE,GAdGJ,KAAK,CAAChH,EAAE;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAed,CAAC;MAEN;;MAEA;MACA,oBACEnG,OAAA;QAEEwF,SAAS,EAAC,qBAAqB;QAC/BK,KAAK,EAAE;UACLlB,IAAI,EAAG,GAAE4C,KAAK,CAAC5C,IAAK,GAAE;UACtBiD,KAAK,EAAG,GAAEL,KAAK,CAACvC,IAAK,IAAG;UACxB6C,MAAM,EAAG,GAAEN,KAAK,CAACvC,IAAK,IAAG;UACzBiD,eAAe,EAAEV,KAAK,CAAC1C,KAAK;UAC5BkD,YAAY,EAAER,KAAK,CAACxC,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAGwC,KAAK,CAACxC,KAAK,KAAK,UAAU,GAAG,GAAG,GAAG,IAAI;UACxFmD,QAAQ,EAAEX,KAAK,CAACxC,KAAK,KAAK,UAAU,GAAG,qCAAqC,GAAG,MAAM;UACrF2C,SAAS,EAAG,iBAAgBH,KAAK,CAAChF,QAAS,cAAagF,KAAK,CAAC3C,KAAM,YAAW;UAC/E4C,GAAG,EAAE,OAAO;UACZQ,SAAS,EAAG,OAAMT,KAAK,CAACvC,IAAK,MAAKuC,KAAK,CAAC1C,KAAM,IAAG;UACjDsD,MAAM,EAAG,aAAYZ,KAAK,CAAC1C,KAAM,EAAC;UAClCiD,UAAU,EAAG,0BAAyBP,KAAK,CAAC1C,KAAM,KAAI0C,KAAK,CAAC1C,KAAM,KAAI;UACtE8C,MAAM,EAAE;QACV;MAAE,GAfGJ,KAAK,CAAChH,EAAE;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBd,CAAC;IAEN,CAAC,CAAC,eAGFnG,OAAA;MAAOoI,GAAG;MAAArC,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAGTvE,UAAU,iBACT5B,OAAA;MACEwF,SAAS,EAAC,mCAAmC;MAC7CK,KAAK,EAAE;QACLiC,UAAU,EAAEzG,QAAQ,GAChB,6GAA6G,GAC7G,6GAA6G;QACjHqG,SAAS,EAAErG,QAAQ,GAAG,+CAA+C,GAAG,6CAA6C;QACrHsG,MAAM,EAAE;MACV;IAAE;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,eAEDnG,OAAA;MAAKwF,SAAS,EAAG,0EACfnE,QAAQ,GAAG,mCAAmC,GAAG,+BAClD,IAAGO,UAAU,GAAG,YAAY,GAAG,EAAG,EAAE;MACrCiE,KAAK,EAAE;QACLiC,UAAU,EAAElG,UAAU,GACjBP,QAAQ,GACL,0EAA0E,GAC1E,0EAA0E,GAC9E,OAAO;QACX2G,SAAS,EAAEpG,UAAU,GAChBP,QAAQ,GACL,sEAAsE,GACtE,sEAAsE,GAC1E,8BAA8B;QAClCsG,MAAM,EAAE;MACV,CAAE;MAAA5B,QAAA,gBAEA/F,OAAA;QAAKwF,SAAS,EAAC,kBAAkB;QAAAO,QAAA,gBAC/B/F,OAAA;UAAKwF,SAAS,EAAG,gFACfnE,QAAQ,GAAG,iDAAiD,GAAG,4CAChE,EAAE;UAAA0E,QAAA,eAED/F,OAAA,CAACT,QAAQ;YAACiG,SAAS,EAAG,aACpBnE,QAAQ,GAAG,gBAAgB,GAAG,cAC/B;UAAE;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA;UAAIwF,SAAS,EAAG,2BACdnE,QAAQ,GACJ,sDAAsD,GACtD,sCACL,EAAE;UAAA0E,QAAA,EACA1E,QAAQ,gBACPrB,OAAA;YAAMwF,SAAS,EAAC,qDAAqD;YAAAO,QAAA,GAAC,eACjE,eAAA/F,OAAA;cAAMwF,SAAS,EAAC,iBAAiB;cAAAO,QAAA,EAAET,kBAAkB,CAAC,kBAAkB;YAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,iBACtF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAEPnG,OAAA;YAAMwF,SAAS,EAAC,wCAAwC;YAAAO,QAAA,GAAC,eACpD,eAAA/F,OAAA;cAAMwF,SAAS,EAAC,mBAAmB;cAAAO,QAAA,EAAET,kBAAkB,CAAC,aAAa;YAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,iBACnF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAELnG,OAAA;UAAKwF,SAAS,EAAG,+BACfnE,QAAQ,GACJ,oCAAoC,GACpC,2BACL,EAAE;UAAA0E,QAAA,EACA1E,QAAQ,gBACPrB,OAAA;YAAMwF,SAAS,EAAC,iBAAiB;YAAAO,QAAA,EAAET,kBAAkB,CAAC,aAAa;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE5EnG,OAAA;YAAMwF,SAAS,EAAC,mBAAmB;YAAAO,QAAA,EAAET,kBAAkB,CAAC,gBAAgB;UAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QACjF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENnG,OAAA;UAAGwF,SAAS,EAAC,4BAA4B;UAAAO,QAAA,GAAC,eACrC,EAAC9E,QAAQ,EAAC,KAAG,EAACC,WAAW;QAAA;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNnG,OAAA;QAAKwF,SAAS,EAAC,kBAAkB;QAAAO,QAAA,eAC/B/F,OAAA;UAAKwF,SAAS,EAAG,sCACfnE,QAAQ,GAAG,uCAAuC,GAAG,mCACtD,EAAE;UAAA0E,QAAA,gBACD/F,OAAA;YAAKwF,SAAS,EAAG,2BACfnE,QAAQ,GAAG,gBAAgB,GAAG,cAC/B,EAAE;YAAA0E,QAAA,GACApF,UAAU,EAAC,GACd;UAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnG,OAAA;YAAKwF,SAAS,EAAC,eAAe;YAAAO,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAKNnG,OAAA;QAAKwF,SAAS,EAAC,4CAA4C;QAAAO,QAAA,gBACzD/F,OAAA;UAAKwF,SAAS,EAAC,sEAAsE;UAAAO,QAAA,gBACnF/F,OAAA;YAAKwF,SAAS,EAAC,mCAAmC;YAAAO,QAAA,EAAEnF;UAAc;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzEnG,OAAA;YAAKwF,SAAS,EAAC,oCAAoC;YAAAO,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACNnG,OAAA;UAAKwF,SAAS,EAAC,sEAAsE;UAAAO,QAAA,gBACnF/F,OAAA;YAAKwF,SAAS,EAAC,iCAAiC;YAAAO,QAAA,EAAElF,cAAc,GAAGD;UAAc;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxFnG,OAAA;YAAKwF,SAAS,EAAC,kCAAkC;YAAAO,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eACNnG,OAAA;UAAKwF,SAAS,EAAC,sEAAsE;UAAAO,QAAA,gBACnF/F,OAAA;YAAKwF,SAAS,EAAG,sBAAqBnE,QAAQ,GAAG,gBAAgB,GAAG,cAAe,EAAE;YAAA0E,QAAA,GAAEpF,UAAU,EAAC,GAAC;UAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzGnG,OAAA;YAAKwF,SAAS,EAAC,mCAAmC;YAAAO,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNnG,OAAA;UAAKwF,SAAS,EAAC,sEAAsE;UAAAO,QAAA,gBACnF/F,OAAA;YAAKwF,SAAS,EAAC,kCAAkC;YAAAO,QAAA,GAC9ChC,IAAI,CAACe,KAAK,CAAChE,SAAS,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC,CAACA,SAAS,GAAG,EAAE,EAAEuH,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNnG,OAAA;YAAKwF,SAAS,EAAC,mCAAmC;YAAAO,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLnF,MAAM,iBACLhB,OAAA;QAAKwF,SAAS,EAAC,2FAA2F;QAAAO,QAAA,eACxG/F,OAAA;UAAKwF,SAAS,EAAC,mCAAmC;UAAAO,QAAA,gBAChD/F,OAAA;YAAKwF,SAAS,EAAC,yBAAyB;YAAAO,QAAA,gBACtC/F,OAAA;cAAKwF,SAAS,EAAC,uEAAuE;cAAAO,QAAA,eACpF/F,OAAA,CAACL,MAAM;gBAAC6F,SAAS,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNnG,OAAA;cAAA+F,QAAA,gBACE/F,OAAA;gBAAKwF,SAAS,EAAC,mCAAmC;gBAAAO,QAAA,GAAC,GAAC,EAAC/E,MAAM,CAACuH,SAAS,IAAI,CAAC,EAAC,KAAG;cAAA;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpFnG,OAAA;gBAAKwF,SAAS,EAAC,uBAAuB;gBAAAO,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnG,OAAA;YAAKwF,SAAS,EAAC,YAAY;YAAAO,QAAA,gBACzB/F,OAAA;cAAKwF,SAAS,EAAC,oCAAoC;cAAAO,QAAA,EAChD,CAAC,CAAC,CAAAvF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgI,OAAO,KAAI,CAAC,KAAKxH,MAAM,CAACuH,SAAS,IAAI,CAAC,CAAC,EAAEE,cAAc,CAAC;YAAC;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNnG,OAAA;cAAKwF,SAAS,EAAC,uBAAuB;cAAAO,QAAA,GAAC,wBAAiB,EAAC,CAAAvF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkI,YAAY,KAAI,CAAC;YAAA;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACnF,MAAM,IAAIR,IAAI,iBACdR,OAAA;QAAKwF,SAAS,EAAC,2FAA2F;QAAAO,QAAA,eACxG/F,OAAA;UAAKwF,SAAS,EAAC,mCAAmC;UAAAO,QAAA,gBAChD/F,OAAA;YAAKwF,SAAS,EAAC,yBAAyB;YAAAO,QAAA,gBACtC/F,OAAA;cAAKwF,SAAS,EAAC,uEAAuE;cAAAO,QAAA,eACpF/F,OAAA,CAACL,MAAM;gBAAC6F,SAAS,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNnG,OAAA;cAAA+F,QAAA,eACE/F,OAAA;gBAAKwF,SAAS,EAAC,uBAAuB;gBAAAO,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnG,OAAA;YAAKwF,SAAS,EAAC,YAAY;YAAAO,QAAA,gBACzB/F,OAAA;cAAKwF,SAAS,EAAC,oCAAoC;cAAAO,QAAA,EAChD,CAACvF,IAAI,CAACgI,OAAO,IAAI,CAAC,EAAEC,cAAc,CAAC;YAAC;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNnG,OAAA;cAAKwF,SAAS,EAAC,uBAAuB;cAAAO,QAAA,GAAC,wBAAiB,EAACvF,IAAI,CAACkI,YAAY,IAAI,CAAC;YAAA;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDnG,OAAA;QAAKwF,SAAS,EAAC,qFAAqF;QAAAO,QAAA,eAClG/F,OAAA;UAAKwF,SAAS,EAAC,8BAA8B;UAAAO,QAAA,gBAC3C/F,OAAA;YAAKwF,SAAS,EAAC,qEAAqE;YAAAO,QAAA,eAClF/F,OAAA,CAACJ,OAAO;cAAC4F,SAAS,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNnG,OAAA;YAAIwF,SAAS,EAAC,qCAAqC;YAAAO,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CAAC,EAGLpF,aAAa,IAAIA,aAAa,CAAC+C,MAAM,GAAG,CAAC,iBACxC9D,OAAA;QAAKwF,SAAS,EAAC,sFAAsF;QAAAO,QAAA,gBACnG/F,OAAA;UAAKwF,SAAS,EAAC,8BAA8B;UAAAO,QAAA,gBAC3C/F,OAAA;YAAKwF,SAAS,EAAC,qEAAqE;YAAAO,QAAA,eAClF/F,OAAA,CAACH,UAAU;cAAC2F,SAAS,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNnG,OAAA;YAAIwF,SAAS,EAAC,qCAAqC;YAAAO,QAAA,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAENnG,OAAA;UAAKwF,SAAS,EAAC,oCAAoC;UAAAO,QAAA,EAChDhF,aAAa,CAAC2E,GAAG,CAAC,CAACY,MAAM,EAAEV,KAAK,KAAK;YACpC;YACAtB,OAAO,CAACC,GAAG,CAAE,YAAWqB,KAAK,GAAG,CAAE,QAAO,EAAEU,MAAM,CAAC;YAClD,oBACAtG,OAAA;cAEEwF,SAAS,EAAG,mFACVc,MAAM,CAACqC,SAAS,GACZ,4FAA4F,GAC5F,mFACL,EAAE;cACH9C,KAAK,EAAE;gBACLmC,SAAS,EAAE1B,MAAM,CAACqC,SAAS,GACvB,sEAAsE,GACtE;cACN,CAAE;cAAA5C,QAAA,gBAGF/F,OAAA;gBAAKwF,SAAS,EAAG,OACfc,MAAM,CAACqC,SAAS,GACZ,0CAA0C,GAC1C,sCACL,EAAE;gBAAA5C,QAAA,eACD/F,OAAA;kBAAKwF,SAAS,EAAC,yBAAyB;kBAAAO,QAAA,gBACtC/F,OAAA;oBAAKwF,SAAS,EAAG,qEACfc,MAAM,CAACqC,SAAS,GACZ,mCAAmC,GACnC,iCACL,EAAE;oBAAA5C,QAAA,EACAO,MAAM,CAACqC,SAAS,gBAAG3I,OAAA,CAACR,OAAO;sBAACgG,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGnG,OAAA,CAACP,GAAG;sBAAC+F,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eAENnG,OAAA;oBAAKwF,SAAS,EAAC,QAAQ;oBAAAO,QAAA,gBACrB/F,OAAA;sBAAIwF,SAAS,EAAC,iCAAiC;sBAAAO,QAAA,GAAC,WACrC,EAACH,KAAK,GAAG,CAAC;oBAAA;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACLnG,OAAA;sBAAKwF,SAAS,EAAC,8BAA8B;sBAAAO,QAAA,eAC3C/F,OAAA;wBAAMwF,SAAS,EAAG,4CAChBc,MAAM,CAACqC,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAA5C,QAAA,EACAO,MAAM,CAACqC,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnG,OAAA;gBAAKwF,SAAS,EAAC,KAAK;gBAAAO,QAAA,gBAClB/F,OAAA;kBAAKwF,SAAS,EAAC,MAAM;kBAAAO,QAAA,eACnB/F,OAAA;oBAAGwF,SAAS,EAAC,8CAA8C;oBAAAO,QAAA,EACxDO,MAAM,CAACK,YAAY,IAAIL,MAAM,CAACM;kBAAY;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EAGL,CAACG,MAAM,CAACsC,YAAY,KAAK,OAAO,IAAItC,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAQ,MAAMV,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAQ,CAAC,iBACxJhH,OAAA;kBAAKwF,SAAS,EAAC,MAAM;kBAAAO,QAAA,eACnB/F,OAAA;oBAAKwF,SAAS,EAAG,2BACfc,MAAM,CAACqC,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;oBAAA5C,QAAA,gBACD/F,OAAA;sBAAKwF,SAAS,EAAC,8BAA8B;sBAAAO,QAAA,gBAC3C/F,OAAA;wBAAMwF,SAAS,EAAC,qCAAqC;wBAAAO,QAAA,EAAC;sBAAkB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/EnG,OAAA;wBAAMwF,SAAS,EAAG,4CAChBc,MAAM,CAACqC,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAA5C,QAAA,EACAO,MAAM,CAACqC,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNnG,OAAA;sBAAKwF,SAAS,EAAC,gCAAgC;sBAAAO,QAAA,gBAC7C/F,OAAA;wBACE6I,GAAG,EAAEvC,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAS;wBAC7D8B,GAAG,EAAC,gBAAgB;wBACpBtD,SAAS,EAAC,sDAAsD;wBAChEK,KAAK,EAAE;0BAAEkD,SAAS,EAAE;wBAAQ,CAAE;wBAC9BC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,MAAM,CAACrD,KAAK,CAACsD,OAAO,GAAG,MAAM;0BAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAACvD,KAAK,CAACsD,OAAO,GAAG,OAAO;wBAC9C;sBAAE;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFnG,OAAA;wBACEwF,SAAS,EAAC,8DAA8D;wBACxEK,KAAK,EAAE;0BAAEsD,OAAO,EAAE;wBAAO,CAAE;wBAAApD,QAAA,EAC5B;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGDnG,OAAA;kBAAKwF,SAAS,EAAC,WAAW;kBAAAO,QAAA,gBACxB/F,OAAA;oBAAKwF,SAAS,EAAG,2BACfc,MAAM,CAACqC,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;oBAAA5C,QAAA,gBACD/F,OAAA;sBAAKwF,SAAS,EAAC,8BAA8B;sBAAAO,QAAA,gBAC3C/F,OAAA;wBAAKwF,SAAS,EAAG,yDACfc,MAAM,CAACqC,SAAS,GAAG,cAAc,GAAG,YACrC,EAAE;wBAAA5C,QAAA,EACAO,MAAM,CAACqC,SAAS,gBACf3I,OAAA,CAACR,OAAO;0BAACgG,SAAS,EAAC;wBAAoB;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAE1CnG,OAAA,CAACP,GAAG;0BAAC+F,SAAS,EAAC;wBAAoB;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBACtC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNnG,OAAA;wBAAMwF,SAAS,EAAC,6BAA6B;wBAAAO,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNnG,OAAA;sBAAKwF,SAAS,EAAG,oCACfc,MAAM,CAACqC,SAAS,GACZ,qDAAqD,GACrD,+CACL,EAAE;sBAAA5C,QAAA,EACAO,MAAM,CAACS,UAAU,IAAI;oBAAoB;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,CAACG,MAAM,CAACqC,SAAS,iBAChB3I,OAAA;oBAAKwF,SAAS,EAAC,sDAAsD;oBAAAO,QAAA,gBACnE/F,OAAA;sBAAKwF,SAAS,EAAC,8BAA8B;sBAAAO,QAAA,gBAC3C/F,OAAA;wBAAKwF,SAAS,EAAC,oEAAoE;wBAAAO,QAAA,eACjF/F,OAAA,CAACR,OAAO;0BAACgG,SAAS,EAAC;wBAAoB;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNnG,OAAA;wBAAMwF,SAAS,EAAC,6BAA6B;wBAAAO,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNnG,OAAA;sBAAKwF,SAAS,EAAC,wFAAwF;sBAAAO,QAAA,EACpGO,MAAM,CAACQ;oBAAa;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA,CAACG,MAAM,CAACqC,SAAS,iBAChB3I,OAAA;oBAAKwF,SAAS,EAAC,MAAM;oBAAAO,QAAA,gBACnB/F,OAAA;sBACEwF,SAAS,EAAG,gHACV9D,mBAAmB,CAAE,YAAWkE,KAAM,EAAC,CAAC,GACpC,gCAAgC,GAChC,2HACL,EAAE;sBACHyD,OAAO,EAAEA,CAAA,KAAMjD,gBAAgB,CAACR,KAAK,EAAEU,MAAM,CAAE;sBAC/CgD,QAAQ,EAAE5H,mBAAmB,CAAE,YAAWkE,KAAM,EAAC,CAAE;sBAAAG,QAAA,EAElDrE,mBAAmB,CAAE,YAAWkE,KAAM,EAAC,CAAC,gBACvC5F,OAAA,CAAAE,SAAA;wBAAA6F,QAAA,gBACE/F,OAAA;0BAAKwF,SAAS,EAAC;wBAA8E;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,0BAEtG;sBAAA,eAAE,CAAC,gBAEHnG,OAAA,CAAAE,SAAA;wBAAA6F,QAAA,gBACE/F,OAAA,CAACJ,OAAO;0BAAC4F,SAAS,EAAC;wBAAS;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,mBAEjC;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC,EAGR3E,YAAY,CAAE,YAAWoE,KAAM,EAAC,CAAC,iBAChC5F,OAAA;sBAAKwF,SAAS,EAAC,yFAAyF;sBAAAO,QAAA,gBACtG/F,OAAA;wBAAKwF,SAAS,EAAC,8BAA8B;wBAAAO,QAAA,gBAC3C/F,OAAA,CAACJ,OAAO;0BAAC4F,SAAS,EAAC;wBAAuB;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CnG,OAAA;0BAAIwF,SAAS,EAAC,yBAAyB;0BAAAO,QAAA,EAAC;wBAAe;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eACNnG,OAAA;wBAAKwF,SAAS,EAAC,mDAAmD;wBAAAO,QAAA,EAC/DvE,YAAY,CAAE,YAAWoE,KAAM,EAAC;sBAAC;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA9KDG,MAAM,CAACiD,UAAU,IAAI3D,KAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+K5B,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CACN,eAGDnG,OAAA;QAAKwF,SAAS,EAAC,iCAAiC;QAAAO,QAAA,gBAC9C/F,OAAA;UACEqJ,OAAO,EAAGJ,CAAC,IAAK;YACdA,CAAC,CAACO,cAAc,CAAC,CAAC;YAClBlF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9C8C,mBAAmB,CAAC,CAAC;UACvB,CAAE;UACF7B,SAAS,EAAC,+NAA+N;UACzOhD,IAAI,EAAC,QAAQ;UAAAuD,QAAA,gBAEb/F,OAAA,CAACN,MAAM;YAAC8F,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETnG,OAAA;UACEqJ,OAAO,EAAGJ,CAAC,IAAK;YACdA,CAAC,CAACO,cAAc,CAAC,CAAC;YAClBlF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7C+C,gBAAgB,CAAC,CAAC;UACpB,CAAE;UACF9B,SAAS,EAAC,kOAAkO;UAC5OhD,IAAI,EAAC,QAAQ;UAAAuD,QAAA,gBAEb/F,OAAA,CAACT,QAAQ;YAACiG,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/F,EAAA,CA3gCID,UAAU;EAAA,QACGhB,WAAW,EACXC,WAAW,EACbC,SAAS,EACPC,WAAW;AAAA;AAAAmK,EAAA,GAJxBtJ,UAAU;AA6gChB,eAAeA,UAAU;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}