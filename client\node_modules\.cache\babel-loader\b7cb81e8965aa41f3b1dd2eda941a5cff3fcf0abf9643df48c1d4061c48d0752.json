{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbClock, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    id\n  } = useParams();\n  const {\n    user\n  } = useSelector(state => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n  const [showAnimation, setShowAnimation] = useState(false);\n  const [confetti, setConfetti] = useState([]);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n    // Trigger entrance animation\n    setTimeout(() => setShowAnimation(true), 100);\n\n    // Play sound effect based on pass/fail\n    const playSound = () => {\n      try {\n        if (isPassed) {\n          // Success sound (you can replace with actual audio file)\n          const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');\n          audio.volume = 0.3;\n          audio.play().catch(() => {}); // Ignore errors if audio fails\n        } else {\n          // Fail sound\n          const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');\n          audio.volume = 0.2;\n          audio.play().catch(() => {}); // Ignore errors if audio fails\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate confetti for pass\n    if (isPassed) {\n      const newConfetti = [];\n      for (let i = 0; i < 50; i++) {\n        newConfetti.push({\n          id: i,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'][Math.floor(Math.random() * 5)]\n        });\n      }\n      setConfetti(newConfetti);\n\n      // Remove confetti after animation\n      setTimeout(() => setConfetti([]), 5000);\n    }\n    playSound();\n  }, [isPassed]);\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4 relative overflow-hidden\",\n    children: [confetti.map(piece => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute w-2 h-2 opacity-80\",\n      style: {\n        left: `${piece.left}%`,\n        backgroundColor: piece.color,\n        animation: `confetti-fall ${piece.duration}s linear ${piece.delay}s forwards`,\n        top: '-10px'\n      }\n    }, piece.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-10px) rotate(0deg);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) rotate(720deg);\n            opacity: 0;\n          }\n        }\n\n        @keyframes bounce-in {\n          0% {\n            transform: scale(0.3) rotate(-10deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.1) rotate(5deg);\n          }\n          100% {\n            transform: scale(1) rotate(0deg);\n            opacity: 1;\n          }\n        }\n\n        @keyframes pulse-glow {\n          0%, 100% {\n            box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);\n          }\n          50% {\n            box-shadow: 0 0 40px rgba(34, 197, 94, 0.8);\n          }\n        }\n\n        @keyframes shake {\n          0%, 100% { transform: translateX(0); }\n          10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }\n          20%, 40%, 60%, 80% { transform: translateX(5px); }\n        }\n\n        .result-card {\n          animation: bounce-in 0.8s ease-out forwards;\n        }\n\n        .pass-glow {\n          animation: pulse-glow 2s ease-in-out infinite;\n        }\n\n        .fail-shake {\n          animation: shake 0.5s ease-in-out;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-2xl w-full result-card ${showAnimation ? isPassed ? 'pass-glow' : 'fail-shake' : ''} ${isPassed ? 'border-green-300' : 'border-red-300'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'} ${showAnimation ? 'animate-bounce' : ''}`,\n          children: [isPassed && showAnimation && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 rounded-full border-4 border-green-300 animate-ping\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-2 rounded-full border-2 border-green-400 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: `w-12 h-12 ${isPassed ? 'text-green-600' : 'text-red-600'} ${showAnimation ? 'animate-pulse' : ''}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), isPassed && showAnimation && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-2 -right-2 w-3 h-3 bg-yellow-400 rounded-full animate-ping\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-2 -left-2 w-2 h-2 bg-yellow-300 rounded-full animate-ping\",\n              style: {\n                animationDelay: '0.5s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-0 left-0 w-2 h-2 bg-yellow-500 rounded-full animate-ping\",\n              style: {\n                animationDelay: '1s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `text-4xl font-bold text-gray-900 mb-4 ${showAnimation ? 'animate-pulse' : ''}`,\n          children: \"\\uD83C\\uDFAF Quiz Completed!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-2xl font-bold mb-2 ${showAnimation ? 'animate-bounce' : ''} ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n          children: isPassed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-2\",\n            children: \"\\uD83C\\uDF89 Congratulations! You Passed! \\uD83C\\uDF89\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-2\",\n            children: \"\\uD83D\\uDCAA Keep Practicing! You Can Do It! \\uD83D\\uDCAA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2 text-lg\",\n          children: [\"\\uD83D\\uDCDA \", quizName, \" - \", quizSubject]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-block px-8 py-4 rounded-2xl ${isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-5xl font-bold mb-2 ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-600\",\n            children: \"Your Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-50 rounded-xl p-4 text-center border border-green-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center mb-2\",\n            children: /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-6 h-6 text-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-green-600\",\n            children: correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-green-700 font-medium\",\n            children: \"Correct\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 rounded-xl p-4 text-center border border-red-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center mb-2\",\n            children: /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-6 h-6 text-red-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-red-600\",\n            children: totalQuestions - correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-700 font-medium\",\n            children: \"Wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 rounded-xl p-4 text-center border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center mb-2\",\n            children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-6 h-6 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: totalQuestions\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 rounded-xl p-4 text-center border border-blue-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center mb-2\",\n            children: /*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-6 h-6 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-blue-600\",\n            children: formatTime(timeTaken)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-blue-700 font-medium\",\n            children: \"Time Taken\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-green-600\",\n            children: correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-green-600 font-medium\",\n            children: \"\\u2705 Correct\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-red-600\",\n            children: totalQuestions - correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-600 font-medium\",\n            children: \"\\u274C Wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\uD83D\\uDCCA Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-blue-600\",\n            children: [Math.floor(timeTaken / 60), \":\", (timeTaken % 60).toString().padStart(2, '0')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\u23F1\\uFE0F Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-purple-600 font-bold text-xl\",\n                children: [\"+\", xpData.xpAwarded || 0, \" XP\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Earned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (((user === null || user === void 0 ? void 0 : user.totalXP) || 0) + (xpData.xpAwarded || 0)).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", (user === null || user === void 0 ? void 0 : user.currentLevel) || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this), !xpData && user && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Your Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (user.totalXP || 0).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", user.currentLevel || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCDA Learning Summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), resultDetails && resultDetails.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCCB Question by Question Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 max-h-96 overflow-y-auto\",\n          children: resultDetails.map((detail, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-xl border-2 overflow-hidden transition-all duration-300 hover:shadow-lg ${detail.isCorrect ? 'border-green-300 bg-gradient-to-r from-green-50 to-emerald-50' : 'border-red-300 bg-gradient-to-r from-red-50 to-pink-50'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-4 ${detail.isCorrect ? 'bg-green-100 border-b border-green-200' : 'bg-red-100 border-b border-red-200'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-10 h-10 rounded-full flex items-center justify-center font-bold ${detail.isCorrect ? 'bg-green-500 text-white shadow-lg' : 'bg-red-500 text-white shadow-lg'}`,\n                  children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 45\n                  }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 79\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-bold text-gray-900 text-lg\",\n                    children: [\"Question \", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2 mt-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-3 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                      children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-700 bg-white p-3 rounded-lg border\",\n                  children: detail.questionText || detail.questionName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this), (detail.questionType === 'image' || detail.questionImage || detail.image) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-3 rounded-lg border-2 ${detail.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2 mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-semibold text-gray-700\",\n                      children: \"\\uD83D\\uDCF7 Question Image:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `px-2 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                      children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 444,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white p-2 rounded-lg border\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: detail.questionImage || detail.image,\n                      alt: \"Question Image\",\n                      className: \"max-w-full h-auto rounded-lg shadow-sm mx-auto block\",\n                      style: {\n                        maxHeight: '300px'\n                      },\n                      onError: e => {\n                        e.target.style.display = 'none';\n                        e.target.nextSibling.style.display = 'block';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\",\n                      style: {\n                        display: 'none'\n                      },\n                      children: \"\\uD83D\\uDCF7 Image could not be loaded\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-4 rounded-lg border-2 ${detail.isCorrect ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-6 h-6 rounded-full flex items-center justify-center ${detail.isCorrect ? 'bg-green-500' : 'bg-red-500'}`,\n                      children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 31\n                      }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                        className: \"w-4 h-4 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 488,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold text-gray-700\",\n                      children: \"Your Answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-lg font-bold text-lg ${detail.isCorrect ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'}`,\n                    children: detail.userAnswer || 'No answer provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 23\n                }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 p-4 rounded-lg border-2 border-green-300\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 506,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold text-gray-700\",\n                      children: \"Correct Answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-100 p-3 rounded-lg border border-green-200 font-bold text-lg text-green-700\",\n                    children: detail.correctAnswer\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 25\n                }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2\",\n                    onClick: () => {\n                      // Toggle explanation visibility\n                      const explanationDiv = document.getElementById(`explanation-${index}`);\n                      if (explanationDiv) {\n                        explanationDiv.style.display = explanationDiv.style.display === 'none' ? 'block' : 'none';\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 29\n                    }, this), \"Show Explanation\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    id: `explanation-${index}`,\n                    className: \"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                    style: {\n                      display: 'none'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"font-semibold text-blue-800 mb-2\",\n                      children: \"\\uD83D\\uDCA1 Explanation:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-blue-700\",\n                      children: detail.explanation || `The correct answer is \"${detail.correctAnswer}\". Make sure to review this topic and understand why this is the right answer.`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 19\n            }, this)]\n          }, detail.questionId || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 pt-4 border-t border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-100 rounded-lg p-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-green-600\",\n                children: correctAnswers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-green-700\",\n                children: \"Correct\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-red-100 rounded-lg p-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-red-600\",\n                children: totalQuestions - correctAnswers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-red-700\",\n                children: \"Wrong\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-100 rounded-lg p-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-blue-600\",\n                children: [percentage, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-blue-700\",\n                children: \"Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-100 rounded-lg p-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-purple-600\",\n                children: [Math.round(correctAnswers / totalQuestions * 100), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-purple-700\",\n                children: \"Accuracy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 More Quizzes button clicked!');\n            handleBackToQuizzes();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbHome, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this), \"More Quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 Retake Quiz button clicked!');\n            handleRetakeQuiz();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this), \"Retake Quiz\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"8NPvUhuBSxCkaRZr7qwGV89Zdz0=\", false, function () {\n  return [useNavigate, useLocation, useParams, useSelector];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "startTransition", "useEffect", "useState", "useNavigate", "useLocation", "useParams", "useSelector", "TbTrophy", "TbClock", "TbCheck", "TbX", "TbHome", "TbStar", "TbBrain", "TbChartBar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "navigate", "location", "id", "user", "state", "resultData", "percentage", "correctAnswers", "totalQuestions", "timeTaken", "resultDetails", "xpData", "quizName", "quizSubject", "passingPercentage", "verdict", "isPassed", "showAnimation", "setShowAnimation", "confetti", "set<PERSON>on<PERSON>tti", "setTimeout", "playSound", "audio", "Audio", "volume", "play", "catch", "error", "console", "log", "newConfetti", "i", "push", "left", "Math", "random", "delay", "duration", "color", "floor", "formatTime", "seconds", "minutes", "remainingSeconds", "toString", "padStart", "handleBackToQuizzes", "handleRetakeQuiz", "className", "children", "map", "piece", "style", "backgroundColor", "animation", "top", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "animationDelay", "xpAwarded", "totalXP", "toLocaleString", "currentLevel", "length", "detail", "index", "isCorrect", "questionText", "questionName", "questionType", "questionImage", "image", "src", "alt", "maxHeight", "onError", "e", "target", "display", "nextS<PERSON>ling", "userAnswer", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "explanationDiv", "document", "getElementById", "explanation", "questionId", "round", "preventDefault", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbClock, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\n\nconst QuizResult = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { id } = useParams();\n  const { user } = useSelector((state) => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n\n  const [showAnimation, setShowAnimation] = useState(false);\n  const [confetti, setConfetti] = useState([]);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n    // Trigger entrance animation\n    setTimeout(() => setShowAnimation(true), 100);\n\n    // Play sound effect based on pass/fail\n    const playSound = () => {\n      try {\n        if (isPassed) {\n          // Success sound (you can replace with actual audio file)\n          const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');\n          audio.volume = 0.3;\n          audio.play().catch(() => {}); // Ignore errors if audio fails\n        } else {\n          // Fail sound\n          const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');\n          audio.volume = 0.2;\n          audio.play().catch(() => {}); // Ignore errors if audio fails\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate confetti for pass\n    if (isPassed) {\n      const newConfetti = [];\n      for (let i = 0; i < 50; i++) {\n        newConfetti.push({\n          id: i,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'][Math.floor(Math.random() * 5)]\n        });\n      }\n      setConfetti(newConfetti);\n\n      // Remove confetti after animation\n      setTimeout(() => setConfetti([]), 5000);\n    }\n\n    playSound();\n  }, [isPassed]);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4 relative overflow-hidden\">\n\n      {/* Confetti Animation */}\n      {confetti.map((piece) => (\n        <div\n          key={piece.id}\n          className=\"absolute w-2 h-2 opacity-80\"\n          style={{\n            left: `${piece.left}%`,\n            backgroundColor: piece.color,\n            animation: `confetti-fall ${piece.duration}s linear ${piece.delay}s forwards`,\n            top: '-10px'\n          }}\n        />\n      ))}\n\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-10px) rotate(0deg);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) rotate(720deg);\n            opacity: 0;\n          }\n        }\n\n        @keyframes bounce-in {\n          0% {\n            transform: scale(0.3) rotate(-10deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.1) rotate(5deg);\n          }\n          100% {\n            transform: scale(1) rotate(0deg);\n            opacity: 1;\n          }\n        }\n\n        @keyframes pulse-glow {\n          0%, 100% {\n            box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);\n          }\n          50% {\n            box-shadow: 0 0 40px rgba(34, 197, 94, 0.8);\n          }\n        }\n\n        @keyframes shake {\n          0%, 100% { transform: translateX(0); }\n          10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }\n          20%, 40%, 60%, 80% { transform: translateX(5px); }\n        }\n\n        .result-card {\n          animation: bounce-in 0.8s ease-out forwards;\n        }\n\n        .pass-glow {\n          animation: pulse-glow 2s ease-in-out infinite;\n        }\n\n        .fail-shake {\n          animation: shake 0.5s ease-in-out;\n        }\n      `}</style>\n\n      <div className={`bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-2xl w-full result-card ${\n        showAnimation ? (isPassed ? 'pass-glow' : 'fail-shake') : ''\n      } ${isPassed ? 'border-green-300' : 'border-red-300'}`}>\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${\n            isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'\n          } ${showAnimation ? 'animate-bounce' : ''}`}>\n\n            {/* Animated rings for pass */}\n            {isPassed && showAnimation && (\n              <>\n                <div className=\"absolute inset-0 rounded-full border-4 border-green-300 animate-ping\"></div>\n                <div className=\"absolute inset-2 rounded-full border-2 border-green-400 animate-pulse\"></div>\n              </>\n            )}\n\n            <TbTrophy className={`w-12 h-12 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            } ${showAnimation ? 'animate-pulse' : ''}`} />\n\n            {/* Sparkles for pass */}\n            {isPassed && showAnimation && (\n              <>\n                <div className=\"absolute -top-2 -right-2 w-3 h-3 bg-yellow-400 rounded-full animate-ping\"></div>\n                <div className=\"absolute -bottom-2 -left-2 w-2 h-2 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                <div className=\"absolute top-0 left-0 w-2 h-2 bg-yellow-500 rounded-full animate-ping\" style={{animationDelay: '1s'}}></div>\n              </>\n            )}\n          </div>\n          \n          <h1 className={`text-4xl font-bold text-gray-900 mb-4 ${\n            showAnimation ? 'animate-pulse' : ''\n          }`}>\n            🎯 Quiz Completed!\n          </h1>\n\n          <div className={`text-2xl font-bold mb-2 ${\n            showAnimation ? 'animate-bounce' : ''\n          } ${isPassed ? 'text-green-600' : 'text-red-600'}`}>\n            {isPassed ? (\n              <span className=\"flex items-center justify-center gap-2\">\n                🎉 Congratulations! You Passed! 🎉\n              </span>\n            ) : (\n              <span className=\"flex items-center justify-center gap-2\">\n                💪 Keep Practicing! You Can Do It! 💪\n              </span>\n            )}\n          </div>\n\n          <p className=\"text-gray-600 mt-2 text-lg\">\n            📚 {quizName} - {quizSubject}\n          </p>\n        </div>\n\n        {/* Score Display */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-block px-8 py-4 rounded-2xl ${\n            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'\n          }`}>\n            <div className={`text-5xl font-bold mb-2 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {percentage}%\n            </div>\n            <div className=\"text-gray-600\">\n              Your Score\n            </div>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\">\n          <div className=\"bg-green-50 rounded-xl p-4 text-center border border-green-200\">\n            <div className=\"flex items-center justify-center mb-2\">\n              <TbCheck className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"text-2xl font-bold text-green-600\">{correctAnswers}</div>\n            <div className=\"text-sm text-green-700 font-medium\">Correct</div>\n          </div>\n\n          <div className=\"bg-red-50 rounded-xl p-4 text-center border border-red-200\">\n            <div className=\"flex items-center justify-center mb-2\">\n              <TbX className=\"w-6 h-6 text-red-600\" />\n            </div>\n            <div className=\"text-2xl font-bold text-red-600\">{totalQuestions - correctAnswers}</div>\n            <div className=\"text-sm text-red-700 font-medium\">Wrong</div>\n          </div>\n\n          <div className=\"bg-gray-50 rounded-xl p-4 text-center border border-gray-200\">\n            <div className=\"flex items-center justify-center mb-2\">\n              <TbTrophy className=\"w-6 h-6 text-gray-600\" />\n            </div>\n            <div className=\"text-2xl font-bold text-gray-900\">{totalQuestions}</div>\n            <div className=\"text-sm text-gray-600 font-medium\">Total</div>\n          </div>\n\n          <div className=\"bg-blue-50 rounded-xl p-4 text-center border border-blue-200\">\n            <div className=\"flex items-center justify-center mb-2\">\n              <TbClock className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"text-2xl font-bold text-blue-600\">{formatTime(timeTaken)}</div>\n            <div className=\"text-sm text-blue-700 font-medium\">Time Taken</div>\n          </div>\n        </div>\n\n        {/* Streamlined Results Cards */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-green-600\">{correctAnswers}</div>\n            <div className=\"text-sm text-green-600 font-medium\">✅ Correct</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-red-600\">{totalQuestions - correctAnswers}</div>\n            <div className=\"text-sm text-red-600 font-medium\">❌ Wrong</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className={`text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`}>{percentage}%</div>\n            <div className=\"text-sm text-gray-600 font-medium\">📊 Score</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-blue-600\">\n              {Math.floor(timeTaken / 60)}:{(timeTaken % 60).toString().padStart(2, '0')}\n            </div>\n            <div className=\"text-sm text-gray-600 font-medium\">⏱️ Time</div>\n          </div>\n        </div>\n\n        {/* Streamlined XP Section */}\n        {xpData && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-purple-600 font-bold text-xl\">+{xpData.xpAwarded || 0} XP</div>\n                  <div className=\"text-sm text-gray-600\">Earned</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {((user?.totalXP || 0) + (xpData.xpAwarded || 0)).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user?.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Streamlined XP Section (for users without XP data) */}\n        {!xpData && user && (\n          <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-sm text-gray-600\">Your Progress</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {(user.totalXP || 0).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Summary Section */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n              <TbBrain className=\"w-6 h-6 text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">📚 Learning Summary</h3>\n          </div>\n\n          {/* Simplified Learning Summary - Only Question Review */}\n        </div>\n\n        {/* Detailed Question Breakdown */}\n        {resultDetails && resultDetails.length > 0 && (\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\">\n                <TbChartBar className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">📋 Question by Question Review</h3>\n            </div>\n\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              {resultDetails.map((detail, index) => (\n                <div\n                  key={detail.questionId || index}\n                  className={`rounded-xl border-2 overflow-hidden transition-all duration-300 hover:shadow-lg ${\n                    detail.isCorrect\n                      ? 'border-green-300 bg-gradient-to-r from-green-50 to-emerald-50'\n                      : 'border-red-300 bg-gradient-to-r from-red-50 to-pink-50'\n                  }`}\n                >\n                  {/* Question Header */}\n                  <div className={`p-4 ${\n                    detail.isCorrect\n                      ? 'bg-green-100 border-b border-green-200'\n                      : 'bg-red-100 border-b border-red-200'\n                  }`}>\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${\n                        detail.isCorrect\n                          ? 'bg-green-500 text-white shadow-lg'\n                          : 'bg-red-500 text-white shadow-lg'\n                      }`}>\n                        {detail.isCorrect ? <TbCheck className=\"w-5 h-5\" /> : <TbX className=\"w-5 h-5\" />}\n                      </div>\n\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-bold text-gray-900 text-lg\">\n                          Question {index + 1}\n                        </h4>\n                        <div className=\"flex items-center gap-2 mt-1\">\n                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${\n                            detail.isCorrect\n                              ? 'bg-green-500 text-white'\n                              : 'bg-red-500 text-white'\n                          }`}>\n                            {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4\">\n                    <div className=\"mb-4\">\n                      <p className=\"text-gray-700 bg-white p-3 rounded-lg border\">\n                        {detail.questionText || detail.questionName}\n                      </p>\n                    </div>\n\n                    {/* Show Image for image questions or any question with an image */}\n                    {(detail.questionType === 'image' || detail.questionImage || detail.image) && (\n                      <div className=\"mb-4\">\n                        <div className={`p-3 rounded-lg border-2 ${\n                          detail.isCorrect\n                            ? 'bg-green-50 border-green-200'\n                            : 'bg-red-50 border-red-200'\n                        }`}>\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <span className=\"text-sm font-semibold text-gray-700\">📷 Question Image:</span>\n                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${\n                              detail.isCorrect\n                                ? 'bg-green-500 text-white'\n                                : 'bg-red-500 text-white'\n                            }`}>\n                              {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                            </span>\n                          </div>\n                          <div className=\"bg-white p-2 rounded-lg border\">\n                            <img\n                              src={detail.questionImage || detail.image}\n                              alt=\"Question Image\"\n                              className=\"max-w-full h-auto rounded-lg shadow-sm mx-auto block\"\n                              style={{ maxHeight: '300px' }}\n                              onError={(e) => {\n                                e.target.style.display = 'none';\n                                e.target.nextSibling.style.display = 'block';\n                              }}\n                            />\n                            <div\n                              className=\"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\"\n                              style={{ display: 'none' }}\n                            >\n                              📷 Image could not be loaded\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Enhanced Answer Section with Color Indicators */}\n                    <div className=\"space-y-3\">\n                      <div className={`p-4 rounded-lg border-2 ${\n                        detail.isCorrect\n                          ? 'bg-green-50 border-green-300'\n                          : 'bg-red-50 border-red-300'\n                      }`}>\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${\n                            detail.isCorrect ? 'bg-green-500' : 'bg-red-500'\n                          }`}>\n                            {detail.isCorrect ? (\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            ) : (\n                              <TbX className=\"w-4 h-4 text-white\" />\n                            )}\n                          </div>\n                          <span className=\"font-semibold text-gray-700\">Your Answer:</span>\n                        </div>\n                        <div className={`p-3 rounded-lg font-bold text-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-100 text-green-700 border border-green-200'\n                            : 'bg-red-100 text-red-700 border border-red-200'\n                        }`}>\n                          {detail.userAnswer || 'No answer provided'}\n                        </div>\n                      </div>\n\n                      {!detail.isCorrect && (\n                        <div className=\"bg-green-50 p-4 rounded-lg border-2 border-green-300\">\n                          <div className=\"flex items-center gap-3 mb-2\">\n                            <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\">\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            </div>\n                            <span className=\"font-semibold text-gray-700\">Correct Answer:</span>\n                          </div>\n                          <div className=\"bg-green-100 p-3 rounded-lg border border-green-200 font-bold text-lg text-green-700\">\n                            {detail.correctAnswer}\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Explanation Button for Wrong Answers */}\n                      {!detail.isCorrect && (\n                        <div className=\"mt-3\">\n                          <button\n                            className=\"w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2\"\n                            onClick={() => {\n                              // Toggle explanation visibility\n                              const explanationDiv = document.getElementById(`explanation-${index}`);\n                              if (explanationDiv) {\n                                explanationDiv.style.display = explanationDiv.style.display === 'none' ? 'block' : 'none';\n                              }\n                            }}\n                          >\n                            <TbBrain className=\"w-4 h-4\" />\n                            Show Explanation\n                          </button>\n\n                          <div\n                            id={`explanation-${index}`}\n                            className=\"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\"\n                            style={{ display: 'none' }}\n                          >\n                            <h6 className=\"font-semibold text-blue-800 mb-2\">💡 Explanation:</h6>\n                            <p className=\"text-blue-700\">\n                              {detail.explanation || `The correct answer is \"${detail.correctAnswer}\". Make sure to review this topic and understand why this is the right answer.`}\n                            </p>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Summary Stats */}\n            <div className=\"mt-4 pt-4 border-t border-gray-200\">\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n                <div className=\"bg-green-100 rounded-lg p-3\">\n                  <div className=\"text-2xl font-bold text-green-600\">{correctAnswers}</div>\n                  <div className=\"text-sm text-green-700\">Correct</div>\n                </div>\n                <div className=\"bg-red-100 rounded-lg p-3\">\n                  <div className=\"text-2xl font-bold text-red-600\">{totalQuestions - correctAnswers}</div>\n                  <div className=\"text-sm text-red-700\">Wrong</div>\n                </div>\n                <div className=\"bg-blue-100 rounded-lg p-3\">\n                  <div className=\"text-2xl font-bold text-blue-600\">{percentage}%</div>\n                  <div className=\"text-sm text-blue-700\">Score</div>\n                </div>\n                <div className=\"bg-purple-100 rounded-lg p-3\">\n                  <div className=\"text-2xl font-bold text-purple-600\">{Math.round((correctAnswers / totalQuestions) * 100)}%</div>\n                  <div className=\"text-sm text-purple-700\">Accuracy</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Actions */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 More Quizzes button clicked!');\n              handleBackToQuizzes();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbHome className=\"w-5 h-5\" />\n            More Quizzes\n          </button>\n\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 Retake Quiz button clicked!');\n              handleRetakeQuiz();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbTrophy className=\"w-5 h-5\" />\n            Retake Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACnE,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEtG,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB;EAAG,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEmB;EAAK,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD;EACA,MAAME,UAAU,GAAGJ,QAAQ,CAACG,KAAK,IAAI;IACnCE,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,EAAE;IACjBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,SAAS;IACtBC,iBAAiB,EAAE,EAAE;IACrBC,OAAO,EAAE;EACX,CAAC;EAED,MAAM;IACJT,UAAU;IACVC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTE,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,iBAAiB;IACjBC,OAAO;IACPL;EACF,CAAC,GAAGL,UAAU;EACd,MAAMW,QAAQ,GAAGD,OAAO,KAAK,MAAM,IAAIT,UAAU,KAAKQ,iBAAiB,IAAI,EAAE,CAAC;EAE9E,MAAM,CAACG,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACAD,SAAS,CAAC,MAAM;IACd;IACAyC,UAAU,CAAC,MAAMH,gBAAgB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;;IAE7C;IACA,MAAMI,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,IAAIN,QAAQ,EAAE;UACZ;UACA,MAAMO,KAAK,GAAG,IAAIC,KAAK,CAAC,yQAAyQ,CAAC;UAClSD,KAAK,CAACE,MAAM,GAAG,GAAG;UAClBF,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,MAAM;UACL;UACA,MAAMJ,KAAK,GAAG,IAAIC,KAAK,CAAC,yQAAyQ,CAAC;UAClSD,KAAK,CAACE,MAAM,GAAG,GAAG;UAClBF,KAAK,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACpC;IACF,CAAC;;IAED;IACA,IAAId,QAAQ,EAAE;MACZ,MAAMe,WAAW,GAAG,EAAE;MACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BD,WAAW,CAACE,IAAI,CAAC;UACf/B,EAAE,EAAE8B,CAAC;UACLE,IAAI,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBC,KAAK,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBE,QAAQ,EAAE,CAAC,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BG,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACJ,IAAI,CAACK,KAAK,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9F,CAAC,CAAC;MACJ;MACAhB,WAAW,CAACW,WAAW,CAAC;;MAExB;MACAV,UAAU,CAAC,MAAMD,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACzC;IAEAE,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EAEd,MAAMyB,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGR,IAAI,CAACK,KAAK,CAACE,OAAO,GAAG,EAAE,CAAC;IACxC,MAAME,gBAAgB,GAAGF,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChClB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CnD,eAAe,CAAC,MAAM;MACpBqB,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BnB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE5B,EAAE,CAAC;IAC5C,IAAIA,EAAE,EAAE;MACNvB,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAE,SAAQE,EAAG,OAAM,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL2B,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DnD,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEL,OAAA;IAAKsD,SAAS,EAAC,yHAAyH;IAAAC,QAAA,GAGrI/B,QAAQ,CAACgC,GAAG,CAAEC,KAAK,iBAClBzD,OAAA;MAEEsD,SAAS,EAAC,6BAA6B;MACvCI,KAAK,EAAE;QACLnB,IAAI,EAAG,GAAEkB,KAAK,CAAClB,IAAK,GAAE;QACtBoB,eAAe,EAAEF,KAAK,CAACb,KAAK;QAC5BgB,SAAS,EAAG,iBAAgBH,KAAK,CAACd,QAAS,YAAWc,KAAK,CAACf,KAAM,YAAW;QAC7EmB,GAAG,EAAE;MACP;IAAE,GAPGJ,KAAK,CAAClD,EAAE;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQd,CACF,CAAC,eAGFjE,OAAA;MAAOkE,GAAG;MAAAX,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEVjE,OAAA;MAAKsD,SAAS,EAAG,0FACfhC,aAAa,GAAID,QAAQ,GAAG,WAAW,GAAG,YAAY,GAAI,EAC3D,IAAGA,QAAQ,GAAG,kBAAkB,GAAG,gBAAiB,EAAE;MAAAkC,QAAA,gBAErDvD,OAAA;QAAKsD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BvD,OAAA;UAAKsD,SAAS,EAAG,gFACfjC,QAAQ,GAAG,iDAAiD,GAAG,4CAChE,IAAGC,aAAa,GAAG,gBAAgB,GAAG,EAAG,EAAE;UAAAiC,QAAA,GAGzClC,QAAQ,IAAIC,aAAa,iBACxBtB,OAAA,CAAAE,SAAA;YAAAqD,QAAA,gBACEvD,OAAA;cAAKsD,SAAS,EAAC;YAAsE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5FjE,OAAA;cAAKsD,SAAS,EAAC;YAAuE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eAC7F,CACH,eAEDjE,OAAA,CAACT,QAAQ;YAAC+D,SAAS,EAAG,aACpBjC,QAAQ,GAAG,gBAAgB,GAAG,cAC/B,IAAGC,aAAa,GAAG,eAAe,GAAG,EAAG;UAAE;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAG7C5C,QAAQ,IAAIC,aAAa,iBACxBtB,OAAA,CAAAE,SAAA;YAAAqD,QAAA,gBACEvD,OAAA;cAAKsD,SAAS,EAAC;YAA0E;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChGjE,OAAA;cAAKsD,SAAS,EAAC,4EAA4E;cAACI,KAAK,EAAE;gBAACS,cAAc,EAAE;cAAM;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnIjE,OAAA;cAAKsD,SAAS,EAAC,uEAAuE;cAACI,KAAK,EAAE;gBAACS,cAAc,EAAE;cAAI;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eAC5H,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENjE,OAAA;UAAIsD,SAAS,EAAG,yCACdhC,aAAa,GAAG,eAAe,GAAG,EACnC,EAAE;UAAAiC,QAAA,EAAC;QAEJ;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELjE,OAAA;UAAKsD,SAAS,EAAG,2BACfhC,aAAa,GAAG,gBAAgB,GAAG,EACpC,IAAGD,QAAQ,GAAG,gBAAgB,GAAG,cAAe,EAAE;UAAAkC,QAAA,EAChDlC,QAAQ,gBACPrB,OAAA;YAAMsD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEzD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAEPjE,OAAA;YAAMsD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEzD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENjE,OAAA;UAAGsD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,eACrC,EAACtC,QAAQ,EAAC,KAAG,EAACC,WAAW;QAAA;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNjE,OAAA;QAAKsD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BvD,OAAA;UAAKsD,SAAS,EAAG,sCACfjC,QAAQ,GAAG,uCAAuC,GAAG,mCACtD,EAAE;UAAAkC,QAAA,gBACDvD,OAAA;YAAKsD,SAAS,EAAG,2BACfjC,QAAQ,GAAG,gBAAgB,GAAG,cAC/B,EAAE;YAAAkC,QAAA,GACA5C,UAAU,EAAC,GACd;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNjE,OAAA;YAAKsD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjE,OAAA;QAAKsD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDvD,OAAA;UAAKsD,SAAS,EAAC,gEAAgE;UAAAC,QAAA,gBAC7EvD,OAAA;YAAKsD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDvD,OAAA,CAACP,OAAO;cAAC6D,SAAS,EAAC;YAAwB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNjE,OAAA;YAAKsD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAE3C;UAAc;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzEjE,OAAA;YAAKsD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAO;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAENjE,OAAA;UAAKsD,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACzEvD,OAAA;YAAKsD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDvD,OAAA,CAACN,GAAG;cAAC4D,SAAS,EAAC;YAAsB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACNjE,OAAA;YAAKsD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAE1C,cAAc,GAAGD;UAAc;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxFjE,OAAA;YAAKsD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAENjE,OAAA;UAAKsD,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3EvD,OAAA;YAAKsD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDvD,OAAA,CAACT,QAAQ;cAAC+D,SAAS,EAAC;YAAuB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNjE,OAAA;YAAKsD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE1C;UAAc;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxEjE,OAAA;YAAKsD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAENjE,OAAA;UAAKsD,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3EvD,OAAA;YAAKsD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDvD,OAAA,CAACR,OAAO;cAAC8D,SAAS,EAAC;YAAuB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNjE,OAAA;YAAKsD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAET,UAAU,CAAChC,SAAS;UAAC;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/EjE,OAAA;YAAKsD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAU;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjE,OAAA;QAAKsD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDvD,OAAA;UAAKsD,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFvD,OAAA;YAAKsD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAE3C;UAAc;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzEjE,OAAA;YAAKsD,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACNjE,OAAA;UAAKsD,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFvD,OAAA;YAAKsD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAE1C,cAAc,GAAGD;UAAc;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxFjE,OAAA;YAAKsD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAO;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eACNjE,OAAA;UAAKsD,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFvD,OAAA;YAAKsD,SAAS,EAAG,sBAAqBjC,QAAQ,GAAG,gBAAgB,GAAG,cAAe,EAAE;YAAAkC,QAAA,GAAE5C,UAAU,EAAC,GAAC;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzGjE,OAAA;YAAKsD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNjE,OAAA;UAAKsD,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFvD,OAAA;YAAKsD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAC9Cf,IAAI,CAACK,KAAK,CAAC/B,SAAS,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC,CAACA,SAAS,GAAG,EAAE,EAAEoC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNjE,OAAA;YAAKsD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLjD,MAAM,iBACLhB,OAAA;QAAKsD,SAAS,EAAC,2FAA2F;QAAAC,QAAA,eACxGvD,OAAA;UAAKsD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvD,OAAA;YAAKsD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCvD,OAAA;cAAKsD,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eACpFvD,OAAA,CAACJ,MAAM;gBAAC0D,SAAS,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNjE,OAAA;cAAAuD,QAAA,gBACEvD,OAAA;gBAAKsD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,GAAC,EAACvC,MAAM,CAACoD,SAAS,IAAI,CAAC,EAAC,KAAG;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpFjE,OAAA;gBAAKsD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjE,OAAA;YAAKsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvD,OAAA;cAAKsD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD,CAAC,CAAC,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,OAAO,KAAI,CAAC,KAAKrD,MAAM,CAACoD,SAAS,IAAI,CAAC,CAAC,EAAEE,cAAc,CAAC;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNjE,OAAA;cAAKsD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBAAiB,EAAC,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,YAAY,KAAI,CAAC;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACjD,MAAM,IAAIR,IAAI,iBACdR,OAAA;QAAKsD,SAAS,EAAC,2FAA2F;QAAAC,QAAA,eACxGvD,OAAA;UAAKsD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvD,OAAA;YAAKsD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCvD,OAAA;cAAKsD,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eACpFvD,OAAA,CAACJ,MAAM;gBAAC0D,SAAS,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNjE,OAAA;cAAAuD,QAAA,eACEvD,OAAA;gBAAKsD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjE,OAAA;YAAKsD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvD,OAAA;cAAKsD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD,CAAC/C,IAAI,CAAC6D,OAAO,IAAI,CAAC,EAAEC,cAAc,CAAC;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNjE,OAAA;cAAKsD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBAAiB,EAAC/C,IAAI,CAAC+D,YAAY,IAAI,CAAC;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDjE,OAAA;QAAKsD,SAAS,EAAC,qFAAqF;QAAAC,QAAA,eAClGvD,OAAA;UAAKsD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CvD,OAAA;YAAKsD,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClFvD,OAAA,CAACH,OAAO;cAACyD,SAAS,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNjE,OAAA;YAAIsD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAmB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CAAC,EAGLlD,aAAa,IAAIA,aAAa,CAACyD,MAAM,GAAG,CAAC,iBACxCxE,OAAA;QAAKsD,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBACnGvD,OAAA;UAAKsD,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CvD,OAAA;YAAKsD,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClFvD,OAAA,CAACF,UAAU;cAACwD,SAAS,EAAC;YAAoB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNjE,OAAA;YAAIsD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAA8B;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAENjE,OAAA;UAAKsD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChDxC,aAAa,CAACyC,GAAG,CAAC,CAACiB,MAAM,EAAEC,KAAK,kBAC/B1E,OAAA;YAEEsD,SAAS,EAAG,mFACVmB,MAAM,CAACE,SAAS,GACZ,+DAA+D,GAC/D,wDACL,EAAE;YAAApB,QAAA,gBAGHvD,OAAA;cAAKsD,SAAS,EAAG,OACfmB,MAAM,CAACE,SAAS,GACZ,wCAAwC,GACxC,oCACL,EAAE;cAAApB,QAAA,eACDvD,OAAA;gBAAKsD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCvD,OAAA;kBAAKsD,SAAS,EAAG,qEACfmB,MAAM,CAACE,SAAS,GACZ,mCAAmC,GACnC,iCACL,EAAE;kBAAApB,QAAA,EACAkB,MAAM,CAACE,SAAS,gBAAG3E,OAAA,CAACP,OAAO;oBAAC6D,SAAS,EAAC;kBAAS;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACN,GAAG;oBAAC4D,SAAS,EAAC;kBAAS;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eAENjE,OAAA;kBAAKsD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBvD,OAAA;oBAAIsD,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAAC,WACrC,EAACmB,KAAK,GAAG,CAAC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACLjE,OAAA;oBAAKsD,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,eAC3CvD,OAAA;sBAAMsD,SAAS,EAAG,4CAChBmB,MAAM,CAACE,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;sBAAApB,QAAA,EACAkB,MAAM,CAACE,SAAS,GAAG,WAAW,GAAG;oBAAS;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNjE,OAAA;cAAKsD,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBvD,OAAA;gBAAKsD,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBvD,OAAA;kBAAGsD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EACxDkB,MAAM,CAACG,YAAY,IAAIH,MAAM,CAACI;gBAAY;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EAGL,CAACQ,MAAM,CAACK,YAAY,KAAK,OAAO,IAAIL,MAAM,CAACM,aAAa,IAAIN,MAAM,CAACO,KAAK,kBACvEhF,OAAA;gBAAKsD,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBvD,OAAA;kBAAKsD,SAAS,EAAG,2BACfmB,MAAM,CAACE,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;kBAAApB,QAAA,gBACDvD,OAAA;oBAAKsD,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,gBAC3CvD,OAAA;sBAAMsD,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/EjE,OAAA;sBAAMsD,SAAS,EAAG,4CAChBmB,MAAM,CAACE,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;sBAAApB,QAAA,EACAkB,MAAM,CAACE,SAAS,GAAG,WAAW,GAAG;oBAAS;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNjE,OAAA;oBAAKsD,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7CvD,OAAA;sBACEiF,GAAG,EAAER,MAAM,CAACM,aAAa,IAAIN,MAAM,CAACO,KAAM;sBAC1CE,GAAG,EAAC,gBAAgB;sBACpB5B,SAAS,EAAC,sDAAsD;sBAChEI,KAAK,EAAE;wBAAEyB,SAAS,EAAE;sBAAQ,CAAE;sBAC9BC,OAAO,EAAGC,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAAC5B,KAAK,CAAC6B,OAAO,GAAG,MAAM;wBAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAAC9B,KAAK,CAAC6B,OAAO,GAAG,OAAO;sBAC9C;oBAAE;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFjE,OAAA;sBACEsD,SAAS,EAAC,8DAA8D;sBACxEI,KAAK,EAAE;wBAAE6B,OAAO,EAAE;sBAAO,CAAE;sBAAAhC,QAAA,EAC5B;oBAED;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAGDjE,OAAA;gBAAKsD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBvD,OAAA;kBAAKsD,SAAS,EAAG,2BACfmB,MAAM,CAACE,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;kBAAApB,QAAA,gBACDvD,OAAA;oBAAKsD,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,gBAC3CvD,OAAA;sBAAKsD,SAAS,EAAG,yDACfmB,MAAM,CAACE,SAAS,GAAG,cAAc,GAAG,YACrC,EAAE;sBAAApB,QAAA,EACAkB,MAAM,CAACE,SAAS,gBACf3E,OAAA,CAACP,OAAO;wBAAC6D,SAAS,EAAC;sBAAoB;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAE1CjE,OAAA,CAACN,GAAG;wBAAC4D,SAAS,EAAC;sBAAoB;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBACtC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACNjE,OAAA;sBAAMsD,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAY;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACNjE,OAAA;oBAAKsD,SAAS,EAAG,oCACfmB,MAAM,CAACE,SAAS,GACZ,qDAAqD,GACrD,+CACL,EAAE;oBAAApB,QAAA,EACAkB,MAAM,CAACgB,UAAU,IAAI;kBAAoB;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAEL,CAACQ,MAAM,CAACE,SAAS,iBAChB3E,OAAA;kBAAKsD,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,gBACnEvD,OAAA;oBAAKsD,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,gBAC3CvD,OAAA;sBAAKsD,SAAS,EAAC,oEAAoE;sBAAAC,QAAA,eACjFvD,OAAA,CAACP,OAAO;wBAAC6D,SAAS,EAAC;sBAAoB;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNjE,OAAA;sBAAMsD,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACNjE,OAAA;oBAAKsD,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,EAClGkB,MAAM,CAACiB;kBAAa;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAGA,CAACQ,MAAM,CAACE,SAAS,iBAChB3E,OAAA;kBAAKsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBvD,OAAA;oBACEsD,SAAS,EAAC,0JAA0J;oBACpKqC,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACA,MAAMC,cAAc,GAAGC,QAAQ,CAACC,cAAc,CAAE,eAAcpB,KAAM,EAAC,CAAC;sBACtE,IAAIkB,cAAc,EAAE;wBAClBA,cAAc,CAAClC,KAAK,CAAC6B,OAAO,GAAGK,cAAc,CAAClC,KAAK,CAAC6B,OAAO,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;sBAC3F;oBACF,CAAE;oBAAAhC,QAAA,gBAEFvD,OAAA,CAACH,OAAO;sBAACyD,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,oBAEjC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAETjE,OAAA;oBACEO,EAAE,EAAG,eAAcmE,KAAM,EAAE;oBAC3BpB,SAAS,EAAC,uDAAuD;oBACjEI,KAAK,EAAE;sBAAE6B,OAAO,EAAE;oBAAO,CAAE;oBAAAhC,QAAA,gBAE3BvD,OAAA;sBAAIsD,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEjE,OAAA;sBAAGsD,SAAS,EAAC,eAAe;sBAAAC,QAAA,EACzBkB,MAAM,CAACsB,WAAW,IAAK,0BAAyBtB,MAAM,CAACiB,aAAc;oBAA+E;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA/JDQ,MAAM,CAACuB,UAAU,IAAItB,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgK5B,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjE,OAAA;UAAKsD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDvD,OAAA;YAAKsD,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChEvD,OAAA;cAAKsD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CvD,OAAA;gBAAKsD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAE3C;cAAc;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzEjE,OAAA;gBAAKsD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAO;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNjE,OAAA;cAAKsD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCvD,OAAA;gBAAKsD,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAE1C,cAAc,GAAGD;cAAc;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxFjE,OAAA;gBAAKsD,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNjE,OAAA;cAAKsD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCvD,OAAA;gBAAKsD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAE5C,UAAU,EAAC,GAAC;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrEjE,OAAA;gBAAKsD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACNjE,OAAA;cAAKsD,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBAC3CvD,OAAA;gBAAKsD,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAAEf,IAAI,CAACyD,KAAK,CAAErF,cAAc,GAAGC,cAAc,GAAI,GAAG,CAAC,EAAC,GAAC;cAAA;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChHjE,OAAA;gBAAKsD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDjE,OAAA;QAAKsD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CvD,OAAA;UACE2F,OAAO,EAAGN,CAAC,IAAK;YACdA,CAAC,CAACa,cAAc,CAAC,CAAC;YAClBhE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9CiB,mBAAmB,CAAC,CAAC;UACvB,CAAE;UACFE,SAAS,EAAC,+NAA+N;UACzO6C,IAAI,EAAC,QAAQ;UAAA5C,QAAA,gBAEbvD,OAAA,CAACL,MAAM;YAAC2D,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETjE,OAAA;UACE2F,OAAO,EAAGN,CAAC,IAAK;YACdA,CAAC,CAACa,cAAc,CAAC,CAAC;YAClBhE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7CkB,gBAAgB,CAAC,CAAC;UACpB,CAAE;UACFC,SAAS,EAAC,kOAAkO;UAC5O6C,IAAI,EAAC,QAAQ;UAAA5C,QAAA,gBAEbvD,OAAA,CAACT,QAAQ;YAAC+D,SAAS,EAAC;UAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAxlBID,UAAU;EAAA,QACGhB,WAAW,EACXC,WAAW,EACbC,SAAS,EACPC,WAAW;AAAA;AAAA8G,EAAA,GAJxBjG,UAAU;AA0lBhB,eAAeA,UAAU;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}