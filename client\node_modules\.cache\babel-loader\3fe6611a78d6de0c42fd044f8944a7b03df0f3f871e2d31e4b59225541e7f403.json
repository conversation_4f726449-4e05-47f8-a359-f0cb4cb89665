{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Chat\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction BrainwaveAI() {\n  _s();\n  const [messages, setMessages] = useState([{\n    role: \"assistant\",\n    content: \"Hello! I'm Brainwave AI. How can I help you?\"\n  }]);\n  const [input, setInput] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const sendMessage = () => {\n    if (!input.trim()) return;\n    const userMsg = input.trim();\n    setInput(\"\");\n    setLoading(true);\n    setMessages(prev => [...prev, {\n      role: \"user\",\n      content: userMsg\n    }]);\n    setTimeout(() => {\n      setMessages(prev => [...prev, {\n        role: \"assistant\",\n        content: \"I received your message: \" + userMsg\n      }]);\n      setLoading(false);\n    }, 1000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      background: '#f0f9ff'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        padding: '20px',\n        borderBottom: '1px solid #e5e7eb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          margin: 0,\n          color: '#1f2937',\n          fontSize: '24px',\n          fontWeight: 'bold'\n        },\n        children: \"Brainwave AI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          color: '#6b7280',\n          fontSize: '14px'\n        },\n        children: \"Your intelligent study assistant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        padding: '20px',\n        overflow: 'auto',\n        maxWidth: '800px',\n        margin: '0 auto',\n        width: '100%'\n      },\n      children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px',\n          display: 'flex',\n          justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: msg.role === 'user' ? '#3b82f6' : 'white',\n            color: msg.role === 'user' ? 'white' : '#1f2937',\n            padding: '16px',\n            borderRadius: '16px',\n            maxWidth: '70%',\n            border: msg.role === 'assistant' ? '1px solid #e5e7eb' : 'none',\n            boxShadow: msg.role === 'assistant' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none',\n            wordWrap: 'break-word'\n          },\n          children: msg.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this)), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px',\n          display: 'flex',\n          justifyContent: 'flex-start'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            padding: '16px',\n            borderRadius: '16px',\n            maxWidth: '70%',\n            border: '1px solid #e5e7eb',\n            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'\n          },\n          children: \"Typing...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        padding: '20px',\n        borderTop: '1px solid #e5e7eb'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxWidth: '800px',\n          margin: '0 auto',\n          display: 'flex',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: input,\n          onChange: e => setInput(e.target.value),\n          onKeyPress: e => e.key === 'Enter' && sendMessage(),\n          placeholder: \"Ask me anything...\",\n          style: {\n            flex: 1,\n            padding: '12px 16px',\n            border: '1px solid #d1d5db',\n            borderRadius: '12px',\n            outline: 'none',\n            fontSize: '16px',\n            fontFamily: 'inherit'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: sendMessage,\n          disabled: loading || !input.trim(),\n          style: {\n            padding: '12px 24px',\n            background: loading || !input.trim() ? '#e5e7eb' : '#3b82f6',\n            color: loading || !input.trim() ? '#9ca3af' : 'white',\n            border: 'none',\n            borderRadius: '12px',\n            cursor: loading || !input.trim() ? 'not-allowed' : 'pointer',\n            fontSize: '16px',\n            fontWeight: '500',\n            fontFamily: 'inherit'\n          },\n          children: loading ? 'Sending...' : 'Send'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          textAlign: 'center',\n          margin: '12px 0 0 0',\n          fontSize: '12px',\n          color: '#6b7280'\n        },\n        children: \"Press Enter to send your message\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n}\n_s(BrainwaveAI, \"Rt8BLK5g7uciv/MC/sjAvd94lfg=\");\n_c = BrainwaveAI;\nexport default BrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"BrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "BrainwaveAI", "_s", "messages", "setMessages", "role", "content", "input", "setInput", "loading", "setLoading", "sendMessage", "trim", "userMsg", "prev", "setTimeout", "style", "height", "display", "flexDirection", "background", "children", "padding", "borderBottom", "margin", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "overflow", "max<PERSON><PERSON><PERSON>", "width", "map", "msg", "index", "marginBottom", "justifyContent", "borderRadius", "border", "boxShadow", "wordWrap", "borderTop", "gap", "type", "value", "onChange", "e", "target", "onKeyPress", "key", "placeholder", "outline", "fontFamily", "onClick", "disabled", "cursor", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Chat/index.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\n\r\nfunction BrainwaveAI() {\r\n  const [messages, setMessages] = useState([\r\n    { role: \"assistant\", content: \"Hello! I'm Brainwave AI. How can I help you?\" }\r\n  ]);\r\n  const [input, setInput] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const sendMessage = () => {\r\n    if (!input.trim()) return;\r\n\r\n    const userMsg = input.trim();\r\n    setInput(\"\");\r\n    setLoading(true);\r\n\r\n    setMessages(prev => [...prev, { role: \"user\", content: userMsg }]);\r\n\r\n    setTimeout(() => {\r\n      setMessages(prev => [...prev, { role: \"assistant\", content: \"I received your message: \" + userMsg }]);\r\n      setLoading(false);\r\n    }, 1000);\r\n  };\r\n\r\n  return (\r\n    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column', background: '#f0f9ff' }}>\r\n      <div style={{ background: 'white', padding: '20px', borderBottom: '1px solid #e5e7eb' }}>\r\n        <h1 style={{ margin: 0, color: '#1f2937', fontSize: '24px', fontWeight: 'bold' }}>Brainwave AI</h1>\r\n        <p style={{ margin: 0, color: '#6b7280', fontSize: '14px' }}>Your intelligent study assistant</p>\r\n      </div>\r\n\r\n      <div style={{ flex: 1, padding: '20px', overflow: 'auto', maxWidth: '800px', margin: '0 auto', width: '100%' }}>\r\n        {messages.map((msg, index) => (\r\n          <div key={index} style={{ marginBottom: '20px', display: 'flex', justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start' }}>\r\n            <div style={{\r\n              background: msg.role === 'user' ? '#3b82f6' : 'white',\r\n              color: msg.role === 'user' ? 'white' : '#1f2937',\r\n              padding: '16px',\r\n              borderRadius: '16px',\r\n              maxWidth: '70%',\r\n              border: msg.role === 'assistant' ? '1px solid #e5e7eb' : 'none',\r\n              boxShadow: msg.role === 'assistant' ? '0 1px 3px rgba(0,0,0,0.1)' : 'none',\r\n              wordWrap: 'break-word'\r\n            }}>\r\n              {msg.content}\r\n            </div>\r\n          </div>\r\n        ))}\r\n\r\n        {loading && (\r\n          <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'flex-start' }}>\r\n            <div style={{\r\n              background: 'white',\r\n              padding: '16px',\r\n              borderRadius: '16px',\r\n              maxWidth: '70%',\r\n              border: '1px solid #e5e7eb',\r\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'\r\n            }}>\r\n              Typing...\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div style={{ background: 'white', padding: '20px', borderTop: '1px solid #e5e7eb' }}>\r\n        <div style={{ maxWidth: '800px', margin: '0 auto', display: 'flex', gap: '12px' }}>\r\n          <input\r\n            type=\"text\"\r\n            value={input}\r\n            onChange={(e) => setInput(e.target.value)}\r\n            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}\r\n            placeholder=\"Ask me anything...\"\r\n            style={{\r\n              flex: 1,\r\n              padding: '12px 16px',\r\n              border: '1px solid #d1d5db',\r\n              borderRadius: '12px',\r\n              outline: 'none',\r\n              fontSize: '16px',\r\n              fontFamily: 'inherit'\r\n            }}\r\n          />\r\n          <button\r\n            onClick={sendMessage}\r\n            disabled={loading || !input.trim()}\r\n            style={{\r\n              padding: '12px 24px',\r\n              background: loading || !input.trim() ? '#e5e7eb' : '#3b82f6',\r\n              color: loading || !input.trim() ? '#9ca3af' : 'white',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              cursor: loading || !input.trim() ? 'not-allowed' : 'pointer',\r\n              fontSize: '16px',\r\n              fontWeight: '500',\r\n              fontFamily: 'inherit'\r\n            }}\r\n          >\r\n            {loading ? 'Sending...' : 'Send'}\r\n          </button>\r\n        </div>\r\n        <p style={{\r\n          textAlign: 'center',\r\n          margin: '12px 0 0 0',\r\n          fontSize: '12px',\r\n          color: '#6b7280'\r\n        }}>\r\n          Press Enter to send your message\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default BrainwaveAI;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGN,QAAQ,CAAC,CACvC;IAAEO,IAAI,EAAE,WAAW;IAAEC,OAAO,EAAE;EAA+C,CAAC,CAC/E,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMa,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACJ,KAAK,CAACK,IAAI,CAAC,CAAC,EAAE;IAEnB,MAAMC,OAAO,GAAGN,KAAK,CAACK,IAAI,CAAC,CAAC;IAC5BJ,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,IAAI,CAAC;IAEhBN,WAAW,CAACU,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAET,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEO;IAAQ,CAAC,CAAC,CAAC;IAElEE,UAAU,CAAC,MAAM;MACfX,WAAW,CAACU,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAET,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE,2BAA2B,GAAGO;MAAQ,CAAC,CAAC,CAAC;MACrGH,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACEV,OAAA;IAAKgB,KAAK,EAAE;MAAEC,MAAM,EAAE,OAAO;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAU,CAAE;IAAAC,QAAA,gBAC/FrB,OAAA;MAAKgB,KAAK,EAAE;QAAEI,UAAU,EAAE,OAAO;QAAEE,OAAO,EAAE,MAAM;QAAEC,YAAY,EAAE;MAAoB,CAAE;MAAAF,QAAA,gBACtFrB,OAAA;QAAIgB,KAAK,EAAE;UAAEQ,MAAM,EAAE,CAAC;UAAEC,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAN,QAAA,EAAC;MAAY;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnG/B,OAAA;QAAGgB,KAAK,EAAE;UAAEQ,MAAM,EAAE,CAAC;UAAEC,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAL,QAAA,EAAC;MAAgC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC,eAEN/B,OAAA;MAAKgB,KAAK,EAAE;QAAEgB,IAAI,EAAE,CAAC;QAAEV,OAAO,EAAE,MAAM;QAAEW,QAAQ,EAAE,MAAM;QAAEC,QAAQ,EAAE,OAAO;QAAEV,MAAM,EAAE,QAAQ;QAAEW,KAAK,EAAE;MAAO,CAAE;MAAAd,QAAA,GAC5GlB,QAAQ,CAACiC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBtC,OAAA;QAAiBgB,KAAK,EAAE;UAAEuB,YAAY,EAAE,MAAM;UAAErB,OAAO,EAAE,MAAM;UAAEsB,cAAc,EAAEH,GAAG,CAAChC,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG;QAAa,CAAE;QAAAgB,QAAA,eACjIrB,OAAA;UAAKgB,KAAK,EAAE;YACVI,UAAU,EAAEiB,GAAG,CAAChC,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,OAAO;YACrDoB,KAAK,EAAEY,GAAG,CAAChC,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,SAAS;YAChDiB,OAAO,EAAE,MAAM;YACfmB,YAAY,EAAE,MAAM;YACpBP,QAAQ,EAAE,KAAK;YACfQ,MAAM,EAAEL,GAAG,CAAChC,IAAI,KAAK,WAAW,GAAG,mBAAmB,GAAG,MAAM;YAC/DsC,SAAS,EAAEN,GAAG,CAAChC,IAAI,KAAK,WAAW,GAAG,2BAA2B,GAAG,MAAM;YAC1EuC,QAAQ,EAAE;UACZ,CAAE;UAAAvB,QAAA,EACCgB,GAAG,CAAC/B;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC,GAZEO,KAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaV,CACN,CAAC,EAEDtB,OAAO,iBACNT,OAAA;QAAKgB,KAAK,EAAE;UAAEuB,YAAY,EAAE,MAAM;UAAErB,OAAO,EAAE,MAAM;UAAEsB,cAAc,EAAE;QAAa,CAAE;QAAAnB,QAAA,eAClFrB,OAAA;UAAKgB,KAAK,EAAE;YACVI,UAAU,EAAE,OAAO;YACnBE,OAAO,EAAE,MAAM;YACfmB,YAAY,EAAE,MAAM;YACpBP,QAAQ,EAAE,KAAK;YACfQ,MAAM,EAAE,mBAAmB;YAC3BC,SAAS,EAAE;UACb,CAAE;UAAAtB,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN/B,OAAA;MAAKgB,KAAK,EAAE;QAAEI,UAAU,EAAE,OAAO;QAAEE,OAAO,EAAE,MAAM;QAAEuB,SAAS,EAAE;MAAoB,CAAE;MAAAxB,QAAA,gBACnFrB,OAAA;QAAKgB,KAAK,EAAE;UAAEkB,QAAQ,EAAE,OAAO;UAAEV,MAAM,EAAE,QAAQ;UAAEN,OAAO,EAAE,MAAM;UAAE4B,GAAG,EAAE;QAAO,CAAE;QAAAzB,QAAA,gBAChFrB,OAAA;UACE+C,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEzC,KAAM;UACb0C,QAAQ,EAAGC,CAAC,IAAK1C,QAAQ,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC1CI,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAI1C,WAAW,CAAC,CAAE;UACtD2C,WAAW,EAAC,oBAAoB;UAChCtC,KAAK,EAAE;YACLgB,IAAI,EAAE,CAAC;YACPV,OAAO,EAAE,WAAW;YACpBoB,MAAM,EAAE,mBAAmB;YAC3BD,YAAY,EAAE,MAAM;YACpBc,OAAO,EAAE,MAAM;YACf7B,QAAQ,EAAE,MAAM;YAChB8B,UAAU,EAAE;UACd;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF/B,OAAA;UACEyD,OAAO,EAAE9C,WAAY;UACrB+C,QAAQ,EAAEjD,OAAO,IAAI,CAACF,KAAK,CAACK,IAAI,CAAC,CAAE;UACnCI,KAAK,EAAE;YACLM,OAAO,EAAE,WAAW;YACpBF,UAAU,EAAEX,OAAO,IAAI,CAACF,KAAK,CAACK,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS;YAC5Da,KAAK,EAAEhB,OAAO,IAAI,CAACF,KAAK,CAACK,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO;YACrD8B,MAAM,EAAE,MAAM;YACdD,YAAY,EAAE,MAAM;YACpBkB,MAAM,EAAElD,OAAO,IAAI,CAACF,KAAK,CAACK,IAAI,CAAC,CAAC,GAAG,aAAa,GAAG,SAAS;YAC5Dc,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjB6B,UAAU,EAAE;UACd,CAAE;UAAAnC,QAAA,EAEDZ,OAAO,GAAG,YAAY,GAAG;QAAM;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN/B,OAAA;QAAGgB,KAAK,EAAE;UACR4C,SAAS,EAAE,QAAQ;UACnBpC,MAAM,EAAE,YAAY;UACpBE,QAAQ,EAAE,MAAM;UAChBD,KAAK,EAAE;QACT,CAAE;QAAAJ,QAAA,EAAC;MAEH;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7B,EAAA,CA9GQD,WAAW;AAAA4D,EAAA,GAAX5D,WAAW;AAgHpB,eAAeA,WAAW;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}