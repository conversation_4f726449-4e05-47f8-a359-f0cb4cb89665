/* Modern Reports Styles */
.reports-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #eef2ff 100%);
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Card hover effects */
.ant-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  overflow: hidden;
}

.ant-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Progress bar customization */
.ant-progress-bg {
  border-radius: 8px;
}

.ant-progress-inner {
  border-radius: 8px;
  background: #f3f4f6;
}

/* Button customization */
.ant-btn-primary {
  border-radius: 12px;
  font-weight: 600;
  height: auto;
  padding: 12px 24px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

/* Select and DatePicker customization */
.ant-select-selector,
.ant-picker {
  border-radius: 12px !important;
  border: 2px solid #e5e7eb !important;
  transition: all 0.3s ease !important;
}

.ant-select-focused .ant-select-selector,
.ant-picker-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Text truncation utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive design */
@media only screen and (max-width: 768px) {
  .reports-container {
    padding: 16px;
  }

  .ant-card {
    margin-bottom: 16px;
  }

  .ant-statistic-title {
    font-size: 12px;
  }

  .ant-statistic-content {
    font-size: 18px;
  }
}

@media only screen and (max-width: 640px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .reports-container {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  }

  .ant-card {
    background: #1e293b;
    border-color: #334155;
  }

  .ant-card .ant-card-body {
    color: #f1f5f9;
  }
}