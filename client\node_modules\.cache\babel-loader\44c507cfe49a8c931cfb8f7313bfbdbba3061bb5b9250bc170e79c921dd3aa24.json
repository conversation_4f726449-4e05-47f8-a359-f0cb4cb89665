{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\UserReports\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './index.css';\nimport PageTitle from \"../../../components/PageTitle\";\nimport { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty, Table, Tag } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbTrophy, TbTarget, TbTrendingUp, TbCalendar, TbClock, TbAward, TbChartBar, TbDownload, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>lame } from \"react-icons/tb\";\nimport moment from \"moment\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nfunction UserReports() {\n  _s();\n  const [reportsData, setReportsData] = useState([]);\n  const [filteredData, setFilteredData] = useState([]);\n  const [filterSubject, setFilterSubject] = useState('all');\n  const [filterVerdict, setFilterVerdict] = useState('all');\n  const [dateRange, setDateRange] = useState(null);\n  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'\n  const [stats, setStats] = useState({\n    totalExams: 0,\n    passedExams: 0,\n    averageScore: 0,\n    streak: 0,\n    bestScore: 0\n  });\n  const dispatch = useDispatch();\n  const calculateStats = data => {\n    if (!data || data.length === 0) {\n      setStats({\n        totalExams: 0,\n        passedExams: 0,\n        averageScore: 0,\n        streak: 0,\n        bestScore: 0\n      });\n      return;\n    }\n    const totalExams = data.length;\n    const passedExams = data.filter(report => {\n      var _report$result;\n      return ((_report$result = report.result) === null || _report$result === void 0 ? void 0 : _report$result.verdict) === 'Pass';\n    }).length;\n    const scores = data.map(report => {\n      var _report$result2, _report$result2$corre, _report$exam;\n      const obtained = ((_report$result2 = report.result) === null || _report$result2 === void 0 ? void 0 : (_report$result2$corre = _report$result2.correctAnswers) === null || _report$result2$corre === void 0 ? void 0 : _report$result2$corre.length) || 0;\n      const total = ((_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam.totalMarks) || 1;\n      return obtained / total * 100;\n    });\n    const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalExams;\n    const bestScore = Math.max(...scores);\n\n    // Calculate streak (consecutive passes)\n    let currentStreak = 0;\n    let maxStreak = 0;\n    for (let i = data.length - 1; i >= 0; i--) {\n      var _data$i$result;\n      if (((_data$i$result = data[i].result) === null || _data$i$result === void 0 ? void 0 : _data$i$result.verdict) === 'Pass') {\n        currentStreak++;\n        maxStreak = Math.max(maxStreak, currentStreak);\n      } else {\n        currentStreak = 0;\n      }\n    }\n    setStats({\n      totalExams,\n      passedExams,\n      averageScore: Math.round(averageScore),\n      streak: maxStreak,\n      bestScore: Math.round(bestScore)\n    });\n  };\n  const getData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReportsByUser();\n      if (response.success) {\n        setReportsData(response.data);\n        setFilteredData(response.data);\n        calculateStats(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const applyFilters = () => {\n    let filtered = [...reportsData];\n    if (filterSubject !== 'all') {\n      filtered = filtered.filter(report => {\n        var _report$exam2, _report$exam2$subject;\n        return (_report$exam2 = report.exam) === null || _report$exam2 === void 0 ? void 0 : (_report$exam2$subject = _report$exam2.subject) === null || _report$exam2$subject === void 0 ? void 0 : _report$exam2$subject.toLowerCase().includes(filterSubject.toLowerCase());\n      });\n    }\n    if (filterVerdict !== 'all') {\n      filtered = filtered.filter(report => {\n        var _report$result3;\n        return ((_report$result3 = report.result) === null || _report$result3 === void 0 ? void 0 : _report$result3.verdict) === filterVerdict;\n      });\n    }\n    if (dateRange && dateRange.length === 2) {\n      filtered = filtered.filter(report => {\n        const reportDate = moment(report.createdAt);\n        return reportDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');\n      });\n    }\n    setFilteredData(filtered);\n    calculateStats(filtered);\n  };\n  useEffect(() => {\n    getData();\n  }, []);\n  useEffect(() => {\n    applyFilters();\n  }, [filterSubject, filterVerdict, dateRange, reportsData]);\n  const getScoreColor = score => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-blue-600';\n    if (score >= 40) return 'text-orange-600';\n    return 'text-red-600';\n  };\n  const getVerdictIcon = verdict => {\n    return verdict === 'Pass' ? /*#__PURE__*/_jsxDEV(TbCheck, {\n      className: \"w-5 h-5 text-green-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n      className: \"w-5 h-5 text-red-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this);\n  };\n  const getVerdictColor = verdict => {\n    return verdict === 'Pass' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';\n  };\n  const formatTime = seconds => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    if (hours > 0) {\n      return `${hours}h ${minutes}m`;\n    }\n    return `${minutes}m`;\n  };\n  const getUniqueSubjects = () => {\n    const subjects = reportsData.map(report => {\n      var _report$exam3;\n      return (_report$exam3 = report.exam) === null || _report$exam3 === void 0 ? void 0 : _report$exam3.subject;\n    }).filter(Boolean);\n    return [...new Set(subjects)];\n  };\n  const columns = [{\n    title: 'Exam Name',\n    dataIndex: 'examName',\n    key: 'examName',\n    render: (text, record) => {\n      var _record$exam, _record$exam2;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"font-semibold text-gray-900\",\n          children: ((_record$exam = record.exam) === null || _record$exam === void 0 ? void 0 : _record$exam.name) || 'Unnamed Exam'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: ((_record$exam2 = record.exam) === null || _record$exam2 === void 0 ? void 0 : _record$exam2.subject) || 'General'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this);\n    },\n    width: 250\n  }, {\n    title: 'Date',\n    dataIndex: 'createdAt',\n    key: 'date',\n    render: date => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: moment(date).format(\"MMM DD, YYYY\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-500\",\n        children: moment(date).format(\"HH:mm\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 9\n    }, this),\n    width: 120\n  }, {\n    title: 'Score',\n    dataIndex: 'score',\n    key: 'score',\n    render: (text, record) => {\n      var _record$result, _record$result$correc, _record$exam3;\n      const obtained = ((_record$result = record.result) === null || _record$result === void 0 ? void 0 : (_record$result$correc = _record$result.correctAnswers) === null || _record$result$correc === void 0 ? void 0 : _record$result$correc.length) || 0;\n      const total = ((_record$exam3 = record.exam) === null || _record$exam3 === void 0 ? void 0 : _record$exam3.totalMarks) || 1;\n      const percentage = Math.round(obtained / total * 100);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg font-bold text-gray-900\",\n          children: [obtained, \"/\", total]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Progress, {\n          percent: percentage,\n          size: \"small\",\n          strokeColor: percentage >= 60 ? '#10b981' : '#ef4444',\n          showInfo: false,\n          className: \"mb-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-sm font-medium ${getScoreColor(percentage)}`,\n          children: [percentage, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 11\n      }, this);\n    },\n    width: 120,\n    sorter: (a, b) => {\n      var _a$result, _a$result$correctAnsw, _a$exam, _b$result, _b$result$correctAnsw, _b$exam;\n      const scoreA = Math.round((((_a$result = a.result) === null || _a$result === void 0 ? void 0 : (_a$result$correctAnsw = _a$result.correctAnswers) === null || _a$result$correctAnsw === void 0 ? void 0 : _a$result$correctAnsw.length) || 0) / (((_a$exam = a.exam) === null || _a$exam === void 0 ? void 0 : _a$exam.totalMarks) || 1) * 100);\n      const scoreB = Math.round((((_b$result = b.result) === null || _b$result === void 0 ? void 0 : (_b$result$correctAnsw = _b$result.correctAnswers) === null || _b$result$correctAnsw === void 0 ? void 0 : _b$result$correctAnsw.length) || 0) / (((_b$exam = b.exam) === null || _b$exam === void 0 ? void 0 : _b$exam.totalMarks) || 1) * 100);\n      return scoreA - scoreB;\n    }\n  }, {\n    title: 'Result',\n    dataIndex: 'verdict',\n    key: 'verdict',\n    render: (text, record) => {\n      var _record$result2;\n      const verdict = (_record$result2 = record.result) === null || _record$result2 === void 0 ? void 0 : _record$result2.verdict;\n      const isPassed = verdict === 'Pass';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        icon: getVerdictIcon(verdict),\n        color: isPassed ? 'success' : 'error',\n        className: \"font-medium\",\n        children: verdict || 'N/A'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this);\n    },\n    width: 100,\n    filters: [{\n      text: 'Pass',\n      value: 'Pass'\n    }, {\n      text: 'Fail',\n      value: 'Fail'\n    }],\n    onFilter: (value, record) => {\n      var _record$result3;\n      return ((_record$result3 = record.result) === null || _record$result3 === void 0 ? void 0 : _record$result3.verdict) === value;\n    }\n  }, {\n    title: 'Actions',\n    key: 'actions',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(TbEye, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 17\n      }, this),\n      className: \"bg-blue-500 hover:bg-blue-600\",\n      children: \"View\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 9\n    }, this),\n    width: 80\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      title: \"Performance Reports\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n            className: \"w-8 h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: [\"Your \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 18\n          }, this), \" Journey\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n          children: \"Track your progress, analyze your performance, and celebrate your achievements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Total Exams\",\n              value: stats.totalExams,\n              valueStyle: {\n                color: '#1e40af',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Passed\",\n              value: stats.passedExams,\n              valueStyle: {\n                color: '#059669',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbTrendingUp, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Average Score\",\n              value: stats.averageScore,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#7c3aed',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Best Score\",\n              value: stats.bestScore,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#ea580c',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbFlame, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Best Streak\",\n              value: stats.streak,\n              valueStyle: {\n                color: '#db2777',\n                fontSize: '24px',\n                fontWeight: 'bold'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbFilter, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Filter Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Subject\",\n              value: filterSubject,\n              onChange: setFilterSubject,\n              className: \"w-full sm:w-48\",\n              size: \"large\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), getUniqueSubjects().map(subject => /*#__PURE__*/_jsxDEV(Option, {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select Result\",\n              value: filterVerdict,\n              onChange: setFilterVerdict,\n              className: \"w-full sm:w-48\",\n              size: \"large\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"all\",\n                children: \"All Results\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Pass\",\n                children: \"Passed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"Fail\",\n                children: \"Failed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n              value: dateRange,\n              onChange: setDateRange,\n              className: \"w-full sm:w-64\",\n              size: \"large\",\n              placeholder: ['Start Date', 'End Date']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                setFilterSubject('all');\n                setFilterVerdict('all');\n                setDateRange(null);\n              },\n              size: \"large\",\n              className: \"w-full sm:w-auto\",\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          columns: columns,\n          dataSource: filteredData,\n          rowKey: record => record._id,\n          pagination: {\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} results`,\n            className: \"px-6 py-4\"\n          },\n          scroll: {\n            x: 800\n          },\n          className: \"modern-table\",\n          size: \"large\",\n          locale: {\n            emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n              image: Empty.PRESENTED_IMAGE_SIMPLE,\n              description: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-medium text-gray-900 mb-2\",\n                  children: \"No exam results found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500\",\n                  children: \"Try adjusting your filters or take some exams to see your results here.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 5\n  }, this);\n}\n_s(UserReports, \"LAvY+fW8DCnRnM0Lc/pXJyt5XxQ=\", false, function () {\n  return [useDispatch];\n});\n_c = UserReports;\nexport default UserReports;\nvar _c;\n$RefreshReg$(_c, \"UserReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Page<PERSON><PERSON>le", "message", "Card", "Progress", "Statistic", "Select", "DatePicker", "<PERSON><PERSON>", "Empty", "Table", "Tag", "useDispatch", "HideLoading", "ShowLoading", "getAllReportsByUser", "motion", "AnimatePresence", "TbTrophy", "TbTarget", "TbTrendingUp", "TbCalendar", "TbClock", "TbAward", "TbChartBar", "TbDownload", "Tb<PERSON><PERSON>er", "TbEye", "TbCheck", "TbX", "TbMedal", "TbFlame", "moment", "jsxDEV", "_jsxDEV", "Option", "RangePicker", "UserReports", "_s", "reportsData", "setReportsData", "filteredData", "setFilteredData", "filterSubject", "setFilterSubject", "filterVerdict", "setFilterVerdict", "date<PERSON><PERSON><PERSON>", "setDateRange", "viewMode", "setViewMode", "stats", "setStats", "totalExams", "passedExams", "averageScore", "streak", "bestScore", "dispatch", "calculateStats", "data", "length", "filter", "report", "_report$result", "result", "verdict", "scores", "map", "_report$result2", "_report$result2$corre", "_report$exam", "obtained", "correctAnswers", "total", "exam", "totalMarks", "reduce", "sum", "score", "Math", "max", "currentStreak", "maxStreak", "i", "_data$i$result", "round", "getData", "response", "success", "error", "applyFilters", "filtered", "_report$exam2", "_report$exam2$subject", "subject", "toLowerCase", "includes", "_report$result3", "reportDate", "createdAt", "isBetween", "getScoreColor", "getVerdictIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getVerdictColor", "formatTime", "seconds", "hours", "floor", "minutes", "getUniqueSubjects", "subjects", "_report$exam3", "Boolean", "Set", "columns", "title", "dataIndex", "key", "render", "text", "record", "_record$exam", "_record$exam2", "children", "name", "width", "date", "format", "_record$result", "_record$result$correc", "_record$exam3", "percentage", "percent", "size", "strokeColor", "showInfo", "sorter", "a", "b", "_a$result", "_a$result$correctAnsw", "_a$exam", "_b$result", "_b$result$correctAnsw", "_b$exam", "scoreA", "scoreB", "_record$result2", "isPassed", "icon", "color", "filters", "value", "onFilter", "_record$result3", "type", "div", "initial", "opacity", "y", "animate", "transition", "delay", "valueStyle", "fontSize", "fontWeight", "suffix", "placeholder", "onChange", "onClick", "dataSource", "<PERSON><PERSON><PERSON>", "_id", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "scroll", "x", "locale", "emptyText", "image", "PRESENTED_IMAGE_SIMPLE", "description", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/UserReports/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport './index.css';\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty, Table, Tag } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  TbTrophy,\r\n  TbTarget,\r\n  TbTrendingUp,\r\n  TbCalendar,\r\n  TbClock,\r\n  TbAward,\r\n  TbChartBar,\r\n  TbDownload,\r\n  TbFilter,\r\n  TbEye,\r\n  TbCheck,\r\n  TbX,\r\n  TbMedal,\r\n  TbFlame\r\n} from \"react-icons/tb\";\r\nimport moment from \"moment\";\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\n\r\nfunction UserReports() {\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [filteredData, setFilteredData] = useState([]);\r\n  const [filterSubject, setFilterSubject] = useState('all');\r\n  const [filterVerdict, setFilterVerdict] = useState('all');\r\n  const [dateRange, setDateRange] = useState(null);\r\n  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'\r\n  const [stats, setStats] = useState({\r\n    totalExams: 0,\r\n    passedExams: 0,\r\n    averageScore: 0,\r\n    streak: 0,\r\n    bestScore: 0\r\n  });\r\n  const dispatch = useDispatch();\r\n\r\n  const calculateStats = (data) => {\r\n    if (!data || data.length === 0) {\r\n      setStats({\r\n        totalExams: 0,\r\n        passedExams: 0,\r\n        averageScore: 0,\r\n        streak: 0,\r\n        bestScore: 0\r\n      });\r\n      return;\r\n    }\r\n\r\n    const totalExams = data.length;\r\n    const passedExams = data.filter(report => report.result?.verdict === 'Pass').length;\r\n    const scores = data.map(report => {\r\n      const obtained = report.result?.correctAnswers?.length || 0;\r\n      const total = report.exam?.totalMarks || 1;\r\n      return (obtained / total) * 100;\r\n    });\r\n\r\n    const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalExams;\r\n    const bestScore = Math.max(...scores);\r\n\r\n    // Calculate streak (consecutive passes)\r\n    let currentStreak = 0;\r\n    let maxStreak = 0;\r\n    for (let i = data.length - 1; i >= 0; i--) {\r\n      if (data[i].result?.verdict === 'Pass') {\r\n        currentStreak++;\r\n        maxStreak = Math.max(maxStreak, currentStreak);\r\n      } else {\r\n        currentStreak = 0;\r\n      }\r\n    }\r\n\r\n    setStats({\r\n      totalExams,\r\n      passedExams,\r\n      averageScore: Math.round(averageScore),\r\n      streak: maxStreak,\r\n      bestScore: Math.round(bestScore)\r\n    });\r\n  };\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        setFilteredData(response.data);\r\n        calculateStats(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const applyFilters = () => {\r\n    let filtered = [...reportsData];\r\n\r\n    if (filterSubject !== 'all') {\r\n      filtered = filtered.filter(report =>\r\n        report.exam?.subject?.toLowerCase().includes(filterSubject.toLowerCase())\r\n      );\r\n    }\r\n\r\n    if (filterVerdict !== 'all') {\r\n      filtered = filtered.filter(report => report.result?.verdict === filterVerdict);\r\n    }\r\n\r\n    if (dateRange && dateRange.length === 2) {\r\n      filtered = filtered.filter(report => {\r\n        const reportDate = moment(report.createdAt);\r\n        return reportDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');\r\n      });\r\n    }\r\n\r\n    setFilteredData(filtered);\r\n    calculateStats(filtered);\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    applyFilters();\r\n  }, [filterSubject, filterVerdict, dateRange, reportsData]);\r\n\r\n  const getScoreColor = (score) => {\r\n    if (score >= 80) return 'text-green-600';\r\n    if (score >= 60) return 'text-blue-600';\r\n    if (score >= 40) return 'text-orange-600';\r\n    return 'text-red-600';\r\n  };\r\n\r\n  const getVerdictIcon = (verdict) => {\r\n    return verdict === 'Pass' ?\r\n      <TbCheck className=\"w-5 h-5 text-green-600\" /> :\r\n      <TbX className=\"w-5 h-5 text-red-600\" />;\r\n  };\r\n\r\n  const getVerdictColor = (verdict) => {\r\n    return verdict === 'Pass' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';\r\n  };\r\n\r\n  const formatTime = (seconds) => {\r\n    const hours = Math.floor(seconds / 3600);\r\n    const minutes = Math.floor((seconds % 3600) / 60);\r\n    if (hours > 0) {\r\n      return `${hours}h ${minutes}m`;\r\n    }\r\n    return `${minutes}m`;\r\n  };\r\n\r\n  const getUniqueSubjects = () => {\r\n    const subjects = reportsData.map(report => report.exam?.subject).filter(Boolean);\r\n    return [...new Set(subjects)];\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      title: 'Exam Name',\r\n      dataIndex: 'examName',\r\n      key: 'examName',\r\n      render: (text, record) => (\r\n        <div>\r\n          <div className=\"font-semibold text-gray-900\">{record.exam?.name || 'Unnamed Exam'}</div>\r\n          <div className=\"text-sm text-gray-500\">{record.exam?.subject || 'General'}</div>\r\n        </div>\r\n      ),\r\n      width: 250,\r\n    },\r\n    {\r\n      title: 'Date',\r\n      dataIndex: 'createdAt',\r\n      key: 'date',\r\n      render: (date) => (\r\n        <div className=\"text-sm\">\r\n          <div className=\"font-medium\">{moment(date).format(\"MMM DD, YYYY\")}</div>\r\n          <div className=\"text-gray-500\">{moment(date).format(\"HH:mm\")}</div>\r\n        </div>\r\n      ),\r\n      width: 120,\r\n    },\r\n    {\r\n      title: 'Score',\r\n      dataIndex: 'score',\r\n      key: 'score',\r\n      render: (text, record) => {\r\n        const obtained = record.result?.correctAnswers?.length || 0;\r\n        const total = record.exam?.totalMarks || 1;\r\n        const percentage = Math.round((obtained / total) * 100);\r\n\r\n        return (\r\n          <div className=\"text-center\">\r\n            <div className=\"text-lg font-bold text-gray-900\">{obtained}/{total}</div>\r\n            <Progress\r\n              percent={percentage}\r\n              size=\"small\"\r\n              strokeColor={percentage >= 60 ? '#10b981' : '#ef4444'}\r\n              showInfo={false}\r\n              className=\"mb-1\"\r\n            />\r\n            <div className={`text-sm font-medium ${getScoreColor(percentage)}`}>\r\n              {percentage}%\r\n            </div>\r\n          </div>\r\n        );\r\n      },\r\n      width: 120,\r\n      sorter: (a, b) => {\r\n        const scoreA = Math.round(((a.result?.correctAnswers?.length || 0) / (a.exam?.totalMarks || 1)) * 100);\r\n        const scoreB = Math.round(((b.result?.correctAnswers?.length || 0) / (b.exam?.totalMarks || 1)) * 100);\r\n        return scoreA - scoreB;\r\n      },\r\n    },\r\n    {\r\n      title: 'Result',\r\n      dataIndex: 'verdict',\r\n      key: 'verdict',\r\n      render: (text, record) => {\r\n        const verdict = record.result?.verdict;\r\n        const isPassed = verdict === 'Pass';\r\n\r\n        return (\r\n          <Tag\r\n            icon={getVerdictIcon(verdict)}\r\n            color={isPassed ? 'success' : 'error'}\r\n            className=\"font-medium\"\r\n          >\r\n            {verdict || 'N/A'}\r\n          </Tag>\r\n        );\r\n      },\r\n      width: 100,\r\n      filters: [\r\n        { text: 'Pass', value: 'Pass' },\r\n        { text: 'Fail', value: 'Fail' },\r\n      ],\r\n      onFilter: (value, record) => record.result?.verdict === value,\r\n    },\r\n    {\r\n      title: 'Actions',\r\n      key: 'actions',\r\n      render: (text, record) => (\r\n        <Button\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          icon={<TbEye />}\r\n          className=\"bg-blue-500 hover:bg-blue-600\"\r\n        >\r\n          View\r\n        </Button>\r\n      ),\r\n      width: 80,\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <PageTitle title=\"Performance Reports\" />\r\n\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Header Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"text-center mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg\">\r\n            <TbChartBar className=\"w-8 h-8 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\r\n            Your <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Performance</span> Journey\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\r\n            Track your progress, analyze your performance, and celebrate your achievements\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Stats Cards */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8\"\r\n        >\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbTarget className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Total Exams\"\r\n                value={stats.totalExams}\r\n                valueStyle={{ color: '#1e40af', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbCheck className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Passed\"\r\n                value={stats.passedExams}\r\n                valueStyle={{ color: '#059669', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbTrendingUp className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Average Score\"\r\n                value={stats.averageScore}\r\n                suffix=\"%\"\r\n                valueStyle={{ color: '#7c3aed', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbTrophy className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Best Score\"\r\n                value={stats.bestScore}\r\n                suffix=\"%\"\r\n                valueStyle={{ color: '#ea580c', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100\">\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center mb-3\">\r\n                <TbFlame className=\"w-6 h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Best Streak\"\r\n                value={stats.streak}\r\n                valueStyle={{ color: '#db2777', fontSize: '24px', fontWeight: 'bold' }}\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n\r\n        </motion.div>\r\n\r\n        {/* Filters Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"bg-white rounded-2xl shadow-lg p-6 mb-8 border border-gray-100\"\r\n        >\r\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <TbFilter className=\"w-5 h-5 text-gray-600\" />\r\n              <h3 className=\"text-lg font-semibold text-gray-900\">Filter Results</h3>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <Select\r\n                placeholder=\"Select Subject\"\r\n                value={filterSubject}\r\n                onChange={setFilterSubject}\r\n                className=\"w-full sm:w-48\"\r\n                size=\"large\"\r\n              >\r\n                <Option value=\"all\">All Subjects</Option>\r\n                {getUniqueSubjects().map(subject => (\r\n                  <Option key={subject} value={subject}>{subject}</Option>\r\n                ))}\r\n              </Select>\r\n\r\n              <Select\r\n                placeholder=\"Select Result\"\r\n                value={filterVerdict}\r\n                onChange={setFilterVerdict}\r\n                className=\"w-full sm:w-48\"\r\n                size=\"large\"\r\n              >\r\n                <Option value=\"all\">All Results</Option>\r\n                <Option value=\"Pass\">Passed</Option>\r\n                <Option value=\"Fail\">Failed</Option>\r\n              </Select>\r\n\r\n              <RangePicker\r\n                value={dateRange}\r\n                onChange={setDateRange}\r\n                className=\"w-full sm:w-64\"\r\n                size=\"large\"\r\n                placeholder={['Start Date', 'End Date']}\r\n              />\r\n\r\n              <Button\r\n                onClick={() => {\r\n                  setFilterSubject('all');\r\n                  setFilterVerdict('all');\r\n                  setDateRange(null);\r\n                }}\r\n                size=\"large\"\r\n                className=\"w-full sm:w-auto\"\r\n              >\r\n                Clear Filters\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Exam Results Table */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\"\r\n        >\r\n          <Table\r\n            columns={columns}\r\n            dataSource={filteredData}\r\n            rowKey={(record) => record._id}\r\n            pagination={{\r\n              pageSize: 10,\r\n              showSizeChanger: true,\r\n              showQuickJumper: true,\r\n              showTotal: (total, range) =>\r\n                `${range[0]}-${range[1]} of ${total} results`,\r\n              className: \"px-6 py-4\"\r\n            }}\r\n            scroll={{ x: 800 }}\r\n            className=\"modern-table\"\r\n            size=\"large\"\r\n            locale={{\r\n              emptyText: (\r\n                <Empty\r\n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\r\n                  description={\r\n                    <div>\r\n                      <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No exam results found</h3>\r\n                      <p className=\"text-gray-500\">Try adjusting your filters or take some exams to see your results here.</p>\r\n                    </div>\r\n                  }\r\n                />\r\n              )\r\n            }}\r\n          />\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserReports;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AACxG,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,QAAQ,EACRC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,OAAO,QACF,gBAAgB;AACvB,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAM;EAAEC;AAAO,CAAC,GAAG7B,MAAM;AACzB,MAAM;EAAE8B;AAAY,CAAC,GAAG7B,UAAU;AAElC,SAAS8B,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC;IACjCsD,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAE9B,MAAM+C,cAAc,GAAIC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9BT,QAAQ,CAAC;QACPC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE;MACb,CAAC,CAAC;MACF;IACF;IAEA,MAAMJ,UAAU,GAAGO,IAAI,CAACC,MAAM;IAC9B,MAAMP,WAAW,GAAGM,IAAI,CAACE,MAAM,CAACC,MAAM;MAAA,IAAAC,cAAA;MAAA,OAAI,EAAAA,cAAA,GAAAD,MAAM,CAACE,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,OAAO,MAAK,MAAM;IAAA,EAAC,CAACL,MAAM;IACnF,MAAMM,MAAM,GAAGP,IAAI,CAACQ,GAAG,CAACL,MAAM,IAAI;MAAA,IAAAM,eAAA,EAAAC,qBAAA,EAAAC,YAAA;MAChC,MAAMC,QAAQ,GAAG,EAAAH,eAAA,GAAAN,MAAM,CAACE,MAAM,cAAAI,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAeI,cAAc,cAAAH,qBAAA,uBAA7BA,qBAAA,CAA+BT,MAAM,KAAI,CAAC;MAC3D,MAAMa,KAAK,GAAG,EAAAH,YAAA,GAAAR,MAAM,CAACY,IAAI,cAAAJ,YAAA,uBAAXA,YAAA,CAAaK,UAAU,KAAI,CAAC;MAC1C,OAAQJ,QAAQ,GAAGE,KAAK,GAAI,GAAG;IACjC,CAAC,CAAC;IAEF,MAAMnB,YAAY,GAAGY,MAAM,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAG1B,UAAU;IAC/E,MAAMI,SAAS,GAAGuB,IAAI,CAACC,GAAG,CAAC,GAAGd,MAAM,CAAC;;IAErC;IACA,IAAIe,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAGxB,IAAI,CAACC,MAAM,GAAG,CAAC,EAAEuB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAAA,IAAAC,cAAA;MACzC,IAAI,EAAAA,cAAA,GAAAzB,IAAI,CAACwB,CAAC,CAAC,CAACnB,MAAM,cAAAoB,cAAA,uBAAdA,cAAA,CAAgBnB,OAAO,MAAK,MAAM,EAAE;QACtCgB,aAAa,EAAE;QACfC,SAAS,GAAGH,IAAI,CAACC,GAAG,CAACE,SAAS,EAAED,aAAa,CAAC;MAChD,CAAC,MAAM;QACLA,aAAa,GAAG,CAAC;MACnB;IACF;IAEA9B,QAAQ,CAAC;MACPC,UAAU;MACVC,WAAW;MACXC,YAAY,EAAEyB,IAAI,CAACM,KAAK,CAAC/B,YAAY,CAAC;MACtCC,MAAM,EAAE2B,SAAS;MACjB1B,SAAS,EAAEuB,IAAI,CAACM,KAAK,CAAC7B,SAAS;IACjC,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8B,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF7B,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM0E,QAAQ,GAAG,MAAMzE,mBAAmB,CAAC,CAAC;MAC5C,IAAIyE,QAAQ,CAACC,OAAO,EAAE;QACpBjD,cAAc,CAACgD,QAAQ,CAAC5B,IAAI,CAAC;QAC7BlB,eAAe,CAAC8C,QAAQ,CAAC5B,IAAI,CAAC;QAC9BD,cAAc,CAAC6B,QAAQ,CAAC5B,IAAI,CAAC;MAC/B,CAAC,MAAM;QACL1D,OAAO,CAACwF,KAAK,CAACF,QAAQ,CAACtF,OAAO,CAAC;MACjC;MACAwD,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO6E,KAAK,EAAE;MACdhC,QAAQ,CAAC7C,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACwF,KAAK,CAACA,KAAK,CAACxF,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMyF,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,QAAQ,GAAG,CAAC,GAAGrD,WAAW,CAAC;IAE/B,IAAII,aAAa,KAAK,KAAK,EAAE;MAC3BiD,QAAQ,GAAGA,QAAQ,CAAC9B,MAAM,CAACC,MAAM;QAAA,IAAA8B,aAAA,EAAAC,qBAAA;QAAA,QAAAD,aAAA,GAC/B9B,MAAM,CAACY,IAAI,cAAAkB,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAaE,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtD,aAAa,CAACqD,WAAW,CAAC,CAAC,CAAC;MAAA,CAC3E,CAAC;IACH;IAEA,IAAInD,aAAa,KAAK,KAAK,EAAE;MAC3B+C,QAAQ,GAAGA,QAAQ,CAAC9B,MAAM,CAACC,MAAM;QAAA,IAAAmC,eAAA;QAAA,OAAI,EAAAA,eAAA,GAAAnC,MAAM,CAACE,MAAM,cAAAiC,eAAA,uBAAbA,eAAA,CAAehC,OAAO,MAAKrB,aAAa;MAAA,EAAC;IAChF;IAEA,IAAIE,SAAS,IAAIA,SAAS,CAACc,MAAM,KAAK,CAAC,EAAE;MACvC+B,QAAQ,GAAGA,QAAQ,CAAC9B,MAAM,CAACC,MAAM,IAAI;QACnC,MAAMoC,UAAU,GAAGnE,MAAM,CAAC+B,MAAM,CAACqC,SAAS,CAAC;QAC3C,OAAOD,UAAU,CAACE,SAAS,CAACtD,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACtE,CAAC,CAAC;IACJ;IAEAL,eAAe,CAACkD,QAAQ,CAAC;IACzBjC,cAAc,CAACiC,QAAQ,CAAC;EAC1B,CAAC;EAED5F,SAAS,CAAC,MAAM;IACduF,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EAENvF,SAAS,CAAC,MAAM;IACd2F,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAChD,aAAa,EAAEE,aAAa,EAAEE,SAAS,EAAER,WAAW,CAAC,CAAC;EAE1D,MAAM+D,aAAa,GAAIvB,KAAK,IAAK;IAC/B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;IACzC,OAAO,cAAc;EACvB,CAAC;EAED,MAAMwB,cAAc,GAAIrC,OAAO,IAAK;IAClC,OAAOA,OAAO,KAAK,MAAM,gBACvBhC,OAAA,CAACN,OAAO;MAAC4E,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAC9C1E,OAAA,CAACL,GAAG;MAAC2E,SAAS,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5C,CAAC;EAED,MAAMC,eAAe,GAAI3C,OAAO,IAAK;IACnC,OAAOA,OAAO,KAAK,MAAM,GAAG,8BAA8B,GAAG,0BAA0B;EACzF,CAAC;EAED,MAAM4C,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,KAAK,GAAGhC,IAAI,CAACiC,KAAK,CAACF,OAAO,GAAG,IAAI,CAAC;IACxC,MAAMG,OAAO,GAAGlC,IAAI,CAACiC,KAAK,CAAEF,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;IACjD,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAQ,GAAEA,KAAM,KAAIE,OAAQ,GAAE;IAChC;IACA,OAAQ,GAAEA,OAAQ,GAAE;EACtB,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,QAAQ,GAAG7E,WAAW,CAAC6B,GAAG,CAACL,MAAM;MAAA,IAAAsD,aAAA;MAAA,QAAAA,aAAA,GAAItD,MAAM,CAACY,IAAI,cAAA0C,aAAA,uBAAXA,aAAA,CAAatB,OAAO;IAAA,EAAC,CAACjC,MAAM,CAACwD,OAAO,CAAC;IAChF,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMI,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,WAAW;IAClBC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAC,YAAA,EAAAC,aAAA;MAAA,oBACnB9F,OAAA;QAAA+F,QAAA,gBACE/F,OAAA;UAAKsE,SAAS,EAAC,6BAA6B;UAAAyB,QAAA,EAAE,EAAAF,YAAA,GAAAD,MAAM,CAACnD,IAAI,cAAAoD,YAAA,uBAAXA,YAAA,CAAaG,IAAI,KAAI;QAAc;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxF1E,OAAA;UAAKsE,SAAS,EAAC,uBAAuB;UAAAyB,QAAA,EAAE,EAAAD,aAAA,GAAAF,MAAM,CAACnD,IAAI,cAAAqD,aAAA,uBAAXA,aAAA,CAAajC,OAAO,KAAI;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC;IAAA,CACP;IACDuB,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGQ,IAAI,iBACXlG,OAAA;MAAKsE,SAAS,EAAC,SAAS;MAAAyB,QAAA,gBACtB/F,OAAA;QAAKsE,SAAS,EAAC,aAAa;QAAAyB,QAAA,EAAEjG,MAAM,CAACoG,IAAI,CAAC,CAACC,MAAM,CAAC,cAAc;MAAC;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxE1E,OAAA;QAAKsE,SAAS,EAAC,eAAe;QAAAyB,QAAA,EAAEjG,MAAM,CAACoG,IAAI,CAAC,CAACC,MAAM,CAAC,OAAO;MAAC;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CACN;IACDuB,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MAAA,IAAAQ,cAAA,EAAAC,qBAAA,EAAAC,aAAA;MACxB,MAAMhE,QAAQ,GAAG,EAAA8D,cAAA,GAAAR,MAAM,CAAC7D,MAAM,cAAAqE,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAe7D,cAAc,cAAA8D,qBAAA,uBAA7BA,qBAAA,CAA+B1E,MAAM,KAAI,CAAC;MAC3D,MAAMa,KAAK,GAAG,EAAA8D,aAAA,GAAAV,MAAM,CAACnD,IAAI,cAAA6D,aAAA,uBAAXA,aAAA,CAAa5D,UAAU,KAAI,CAAC;MAC1C,MAAM6D,UAAU,GAAGzD,IAAI,CAACM,KAAK,CAAEd,QAAQ,GAAGE,KAAK,GAAI,GAAG,CAAC;MAEvD,oBACExC,OAAA;QAAKsE,SAAS,EAAC,aAAa;QAAAyB,QAAA,gBAC1B/F,OAAA;UAAKsE,SAAS,EAAC,iCAAiC;UAAAyB,QAAA,GAAEzD,QAAQ,EAAC,GAAC,EAACE,KAAK;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzE1E,OAAA,CAAC9B,QAAQ;UACPsI,OAAO,EAAED,UAAW;UACpBE,IAAI,EAAC,OAAO;UACZC,WAAW,EAAEH,UAAU,IAAI,EAAE,GAAG,SAAS,GAAG,SAAU;UACtDI,QAAQ,EAAE,KAAM;UAChBrC,SAAS,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACF1E,OAAA;UAAKsE,SAAS,EAAG,uBAAsBF,aAAa,CAACmC,UAAU,CAAE,EAAE;UAAAR,QAAA,GAChEQ,UAAU,EAAC,GACd;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV,CAAC;IACDuB,KAAK,EAAE,GAAG;IACVW,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK;MAAA,IAAAC,SAAA,EAAAC,qBAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,qBAAA,EAAAC,OAAA;MAChB,MAAMC,MAAM,GAAGvE,IAAI,CAACM,KAAK,CAAE,CAAC,EAAA2D,SAAA,GAAAF,CAAC,CAAC9E,MAAM,cAAAgF,SAAA,wBAAAC,qBAAA,GAARD,SAAA,CAAUxE,cAAc,cAAAyE,qBAAA,uBAAxBA,qBAAA,CAA0BrF,MAAM,KAAI,CAAC,KAAK,EAAAsF,OAAA,GAAAJ,CAAC,CAACpE,IAAI,cAAAwE,OAAA,uBAANA,OAAA,CAAQvE,UAAU,KAAI,CAAC,CAAC,GAAI,GAAG,CAAC;MACtG,MAAM4E,MAAM,GAAGxE,IAAI,CAACM,KAAK,CAAE,CAAC,EAAA8D,SAAA,GAAAJ,CAAC,CAAC/E,MAAM,cAAAmF,SAAA,wBAAAC,qBAAA,GAARD,SAAA,CAAU3E,cAAc,cAAA4E,qBAAA,uBAAxBA,qBAAA,CAA0BxF,MAAM,KAAI,CAAC,KAAK,EAAAyF,OAAA,GAAAN,CAAC,CAACrE,IAAI,cAAA2E,OAAA,uBAANA,OAAA,CAAQ1E,UAAU,KAAI,CAAC,CAAC,GAAI,GAAG,CAAC;MACtG,OAAO2E,MAAM,GAAGC,MAAM;IACxB;EACF,CAAC,EACD;IACE/B,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MAAA,IAAA2B,eAAA;MACxB,MAAMvF,OAAO,IAAAuF,eAAA,GAAG3B,MAAM,CAAC7D,MAAM,cAAAwF,eAAA,uBAAbA,eAAA,CAAevF,OAAO;MACtC,MAAMwF,QAAQ,GAAGxF,OAAO,KAAK,MAAM;MAEnC,oBACEhC,OAAA,CAACvB,GAAG;QACFgJ,IAAI,EAAEpD,cAAc,CAACrC,OAAO,CAAE;QAC9B0F,KAAK,EAAEF,QAAQ,GAAG,SAAS,GAAG,OAAQ;QACtClD,SAAS,EAAC,aAAa;QAAAyB,QAAA,EAEtB/D,OAAO,IAAI;MAAK;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAEV,CAAC;IACDuB,KAAK,EAAE,GAAG;IACV0B,OAAO,EAAE,CACP;MAAEhC,IAAI,EAAE,MAAM;MAAEiC,KAAK,EAAE;IAAO,CAAC,EAC/B;MAAEjC,IAAI,EAAE,MAAM;MAAEiC,KAAK,EAAE;IAAO,CAAC,CAChC;IACDC,QAAQ,EAAEA,CAACD,KAAK,EAAEhC,MAAM;MAAA,IAAAkC,eAAA;MAAA,OAAK,EAAAA,eAAA,GAAAlC,MAAM,CAAC7D,MAAM,cAAA+F,eAAA,uBAAbA,eAAA,CAAe9F,OAAO,MAAK4F,KAAK;IAAA;EAC/D,CAAC,EACD;IACErC,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB5F,OAAA,CAAC1B,MAAM;MACLyJ,IAAI,EAAC,SAAS;MACdtB,IAAI,EAAC,OAAO;MACZgB,IAAI,eAAEzH,OAAA,CAACP,KAAK;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAChBJ,SAAS,EAAC,+BAA+B;MAAAyB,QAAA,EAC1C;IAED;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;IACDuB,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEjG,OAAA;IAAKsE,SAAS,EAAC,oEAAoE;IAAAyB,QAAA,gBACjF/F,OAAA,CAACjC,SAAS;MAACwH,KAAK,EAAC;IAAqB;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEzC1E,OAAA;MAAKsE,SAAS,EAAC,6CAA6C;MAAAyB,QAAA,gBAE1D/F,OAAA,CAAClB,MAAM,CAACkJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9B7D,SAAS,EAAC,mBAAmB;QAAAyB,QAAA,gBAE7B/F,OAAA;UAAKsE,SAAS,EAAC,2HAA2H;UAAAyB,QAAA,eACxI/F,OAAA,CAACV,UAAU;YAACgF,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACN1E,OAAA;UAAIsE,SAAS,EAAC,uCAAuC;UAAAyB,QAAA,GAAC,OAC/C,eAAA/F,OAAA;YAAMsE,SAAS,EAAC,4EAA4E;YAAAyB,QAAA,EAAC;UAAW;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,YACtH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1E,OAAA;UAAGsE,SAAS,EAAC,yCAAyC;UAAAyB,QAAA,EAAC;QAEvD;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb1E,OAAA,CAAClB,MAAM,CAACkJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BhE,SAAS,EAAC,0EAA0E;QAAAyB,QAAA,gBAEpF/F,OAAA,CAAC/B,IAAI;UAACqG,SAAS,EAAC,6GAA6G;UAAAyB,QAAA,eAC3H/F,OAAA;YAAKsE,SAAS,EAAC,4BAA4B;YAAAyB,QAAA,gBACzC/F,OAAA;cAAKsE,SAAS,EAAC,0EAA0E;cAAAyB,QAAA,eACvF/F,OAAA,CAACf,QAAQ;gBAACqF,SAAS,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN1E,OAAA,CAAC7B,SAAS;cACRoH,KAAK,EAAC,aAAa;cACnBqC,KAAK,EAAE3G,KAAK,CAACE,UAAW;cACxBoH,UAAU,EAAE;gBAAEb,KAAK,EAAE,SAAS;gBAAEc,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP1E,OAAA,CAAC/B,IAAI;UAACqG,SAAS,EAAC,+GAA+G;UAAAyB,QAAA,eAC7H/F,OAAA;YAAKsE,SAAS,EAAC,4BAA4B;YAAAyB,QAAA,gBACzC/F,OAAA;cAAKsE,SAAS,EAAC,2EAA2E;cAAAyB,QAAA,eACxF/F,OAAA,CAACN,OAAO;gBAAC4E,SAAS,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN1E,OAAA,CAAC7B,SAAS;cACRoH,KAAK,EAAC,QAAQ;cACdqC,KAAK,EAAE3G,KAAK,CAACG,WAAY;cACzBmH,UAAU,EAAE;gBAAEb,KAAK,EAAE,SAAS;gBAAEc,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP1E,OAAA,CAAC/B,IAAI;UAACqG,SAAS,EAAC,iHAAiH;UAAAyB,QAAA,eAC/H/F,OAAA;YAAKsE,SAAS,EAAC,4BAA4B;YAAAyB,QAAA,gBACzC/F,OAAA;cAAKsE,SAAS,EAAC,4EAA4E;cAAAyB,QAAA,eACzF/F,OAAA,CAACd,YAAY;gBAACoF,SAAS,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN1E,OAAA,CAAC7B,SAAS;cACRoH,KAAK,EAAC,eAAe;cACrBqC,KAAK,EAAE3G,KAAK,CAACI,YAAa;cAC1BqH,MAAM,EAAC,GAAG;cACVH,UAAU,EAAE;gBAAEb,KAAK,EAAE,SAAS;gBAAEc,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP1E,OAAA,CAAC/B,IAAI;UAACqG,SAAS,EAAC,iHAAiH;UAAAyB,QAAA,eAC/H/F,OAAA;YAAKsE,SAAS,EAAC,4BAA4B;YAAAyB,QAAA,gBACzC/F,OAAA;cAAKsE,SAAS,EAAC,4EAA4E;cAAAyB,QAAA,eACzF/F,OAAA,CAAChB,QAAQ;gBAACsF,SAAS,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN1E,OAAA,CAAC7B,SAAS;cACRoH,KAAK,EAAC,YAAY;cAClBqC,KAAK,EAAE3G,KAAK,CAACM,SAAU;cACvBmH,MAAM,EAAC,GAAG;cACVH,UAAU,EAAE;gBAAEb,KAAK,EAAE,SAAS;gBAAEc,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP1E,OAAA,CAAC/B,IAAI;UAACqG,SAAS,EAAC,6GAA6G;UAAAyB,QAAA,eAC3H/F,OAAA;YAAKsE,SAAS,EAAC,4BAA4B;YAAAyB,QAAA,gBACzC/F,OAAA;cAAKsE,SAAS,EAAC,0EAA0E;cAAAyB,QAAA,eACvF/F,OAAA,CAACH,OAAO;gBAACyE,SAAS,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN1E,OAAA,CAAC7B,SAAS;cACRoH,KAAK,EAAC,aAAa;cACnBqC,KAAK,EAAE3G,KAAK,CAACK,MAAO;cACpBiH,UAAU,EAAE;gBAAEb,KAAK,EAAE,SAAS;gBAAEc,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO;YAAE;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGG,CAAC,eAGb1E,OAAA,CAAClB,MAAM,CAACkJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BhE,SAAS,EAAC,gEAAgE;QAAAyB,QAAA,eAE1E/F,OAAA;UAAKsE,SAAS,EAAC,oEAAoE;UAAAyB,QAAA,gBACjF/F,OAAA;YAAKsE,SAAS,EAAC,yBAAyB;YAAAyB,QAAA,gBACtC/F,OAAA,CAACR,QAAQ;cAAC8E,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C1E,OAAA;cAAIsE,SAAS,EAAC,qCAAqC;cAAAyB,QAAA,EAAC;YAAc;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEN1E,OAAA;YAAKsE,SAAS,EAAC,iCAAiC;YAAAyB,QAAA,gBAC9C/F,OAAA,CAAC5B,MAAM;cACLuK,WAAW,EAAC,gBAAgB;cAC5Bf,KAAK,EAAEnH,aAAc;cACrBmI,QAAQ,EAAElI,gBAAiB;cAC3B4D,SAAS,EAAC,gBAAgB;cAC1BmC,IAAI,EAAC,OAAO;cAAAV,QAAA,gBAEZ/F,OAAA,CAACC,MAAM;gBAAC2H,KAAK,EAAC,KAAK;gBAAA7B,QAAA,EAAC;cAAY;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCO,iBAAiB,CAAC,CAAC,CAAC/C,GAAG,CAAC2B,OAAO,iBAC9B7D,OAAA,CAACC,MAAM;gBAAe2H,KAAK,EAAE/D,OAAQ;gBAAAkC,QAAA,EAAElC;cAAO,GAAjCA,OAAO;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmC,CACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAET1E,OAAA,CAAC5B,MAAM;cACLuK,WAAW,EAAC,eAAe;cAC3Bf,KAAK,EAAEjH,aAAc;cACrBiI,QAAQ,EAAEhI,gBAAiB;cAC3B0D,SAAS,EAAC,gBAAgB;cAC1BmC,IAAI,EAAC,OAAO;cAAAV,QAAA,gBAEZ/F,OAAA,CAACC,MAAM;gBAAC2H,KAAK,EAAC,KAAK;gBAAA7B,QAAA,EAAC;cAAW;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1E,OAAA,CAACC,MAAM;gBAAC2H,KAAK,EAAC,MAAM;gBAAA7B,QAAA,EAAC;cAAM;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC1E,OAAA,CAACC,MAAM;gBAAC2H,KAAK,EAAC,MAAM;gBAAA7B,QAAA,EAAC;cAAM;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAET1E,OAAA,CAACE,WAAW;cACV0H,KAAK,EAAE/G,SAAU;cACjB+H,QAAQ,EAAE9H,YAAa;cACvBwD,SAAS,EAAC,gBAAgB;cAC1BmC,IAAI,EAAC,OAAO;cACZkC,WAAW,EAAE,CAAC,YAAY,EAAE,UAAU;YAAE;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eAEF1E,OAAA,CAAC1B,MAAM;cACLuK,OAAO,EAAEA,CAAA,KAAM;gBACbnI,gBAAgB,CAAC,KAAK,CAAC;gBACvBE,gBAAgB,CAAC,KAAK,CAAC;gBACvBE,YAAY,CAAC,IAAI,CAAC;cACpB,CAAE;cACF2F,IAAI,EAAC,OAAO;cACZnC,SAAS,EAAC,kBAAkB;cAAAyB,QAAA,EAC7B;YAED;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb1E,OAAA,CAAClB,MAAM,CAACkJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BhE,SAAS,EAAC,uEAAuE;QAAAyB,QAAA,eAEjF/F,OAAA,CAACxB,KAAK;UACJ8G,OAAO,EAAEA,OAAQ;UACjBwD,UAAU,EAAEvI,YAAa;UACzBwI,MAAM,EAAGnD,MAAM,IAAKA,MAAM,CAACoD,GAAI;UAC/BC,UAAU,EAAE;YACVC,QAAQ,EAAE,EAAE;YACZC,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE,IAAI;YACrBC,SAAS,EAAEA,CAAC7G,KAAK,EAAE8G,KAAK,KACrB,GAAEA,KAAK,CAAC,CAAC,CAAE,IAAGA,KAAK,CAAC,CAAC,CAAE,OAAM9G,KAAM,UAAS;YAC/C8B,SAAS,EAAE;UACb,CAAE;UACFiF,MAAM,EAAE;YAAEC,CAAC,EAAE;UAAI,CAAE;UACnBlF,SAAS,EAAC,cAAc;UACxBmC,IAAI,EAAC,OAAO;UACZgD,MAAM,EAAE;YACNC,SAAS,eACP1J,OAAA,CAACzB,KAAK;cACJoL,KAAK,EAAEpL,KAAK,CAACqL,sBAAuB;cACpCC,WAAW,eACT7J,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBAAIsE,SAAS,EAAC,wCAAwC;kBAAAyB,QAAA,EAAC;gBAAqB;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjF1E,OAAA;kBAAGsE,SAAS,EAAC,eAAe;kBAAAyB,QAAA,EAAC;gBAAuE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrG;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAEL;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtE,EAAA,CAzbQD,WAAW;EAAA,QAcDzB,WAAW;AAAA;AAAAoL,EAAA,GAdrB3J,WAAW;AA2bpB,eAAeA,WAAW;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}