[{"C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js": "5", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js": "6", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js": "7", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js": "8", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js": "9", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js": "10", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js": "11", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js": "12", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx": "13", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js": "14", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js": "15", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js": "16", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js": "17", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js": "18", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js": "19", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js": "20", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js": "22", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js": "23", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js": "24", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js": "25", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx": "28", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js": "29", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js": "30", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js": "31", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js": "32", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js": "33", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js": "34", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js": "35", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js": "36", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx": "37", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js": "38", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "39", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js": "40", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "41", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js": "42", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js": "43", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx": "44", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx": "45", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js": "46", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js": "47", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "48", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "49", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js": "50", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "51", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "52", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js": "53", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js": "54", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js": "55", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js": "56", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js": "57", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js": "58", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js": "59", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js": "60", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js": "61", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js": "62", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js": "63", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js": "64", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js": "65", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js": "66", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js": "67", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js": "68", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js": "69", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js": "70", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js": "71", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js": "72", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizErrorBoundary.js": "73", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js": "74", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js": "75", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js": "76", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js": "77", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js": "78", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js": "79", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js": "80", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js": "81", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js": "82", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js": "83", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js": "84", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js": "85", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ErrorBoundary.js": "86", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizDashboard.js": "87", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ModernQuizCard.js": "88", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\quiz.js": "89", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js": "90", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js": "91", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js": "92", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js": "93", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js": "94", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx": "95", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js": "96", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx": "97", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js": "98", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js": "99", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js": "100", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js": "101", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js": "102", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js": "103", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js": "104", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js": "105", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js": "106", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js": "107", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js": "108", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js": "109", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js": "110", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js": "111", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js": "112"}, {"size": 395, "mtime": 1696247250000, "results": "113", "hashOfConfig": "114"}, {"size": 11542, "mtime": 1751869431304, "results": "115", "hashOfConfig": "114"}, {"size": 362, "mtime": 1696247250000, "results": "116", "hashOfConfig": "114"}, {"size": 430, "mtime": 1736735017645, "results": "117", "hashOfConfig": "114"}, {"size": 180, "mtime": 1696247250000, "results": "118", "hashOfConfig": "114"}, {"size": 21249, "mtime": 1751864501272, "results": "119", "hashOfConfig": "114"}, {"size": 334, "mtime": 1696247250000, "results": "120", "hashOfConfig": "114"}, {"size": 416, "mtime": 1696247250000, "results": "121", "hashOfConfig": "114"}, {"size": 404, "mtime": 1736731932223, "results": "122", "hashOfConfig": "114"}, {"size": 449, "mtime": 1736732007232, "results": "123", "hashOfConfig": "114"}, {"size": 15139, "mtime": 1751582824879, "results": "124", "hashOfConfig": "114"}, {"size": 3744, "mtime": 1751585502662, "results": "125", "hashOfConfig": "114"}, {"size": 7653, "mtime": 1750329453221, "results": "126", "hashOfConfig": "114"}, {"size": 48075, "mtime": 1751478351350, "results": "127", "hashOfConfig": "114"}, {"size": 44652, "mtime": 1751260519262, "results": "128", "hashOfConfig": "114"}, {"size": 21978, "mtime": 1751872498069, "results": "129", "hashOfConfig": "114"}, {"size": 29701, "mtime": 1751852936137, "results": "130", "hashOfConfig": "114"}, {"size": 134107, "mtime": 1751858663325, "results": "131", "hashOfConfig": "114"}, {"size": 1327, "mtime": 1709427669270, "results": "132", "hashOfConfig": "114"}, {"size": 8089, "mtime": 1740446459586, "results": "133", "hashOfConfig": "114"}, {"size": 16063, "mtime": 1751870755172, "results": "134", "hashOfConfig": "114"}, {"size": 22154, "mtime": 1751585459342, "results": "135", "hashOfConfig": "114"}, {"size": 7528, "mtime": 1751289392869, "results": "136", "hashOfConfig": "114"}, {"size": 3565, "mtime": 1751575843323, "results": "137", "hashOfConfig": "114"}, {"size": 29503, "mtime": 1751869262900, "results": "138", "hashOfConfig": "114"}, {"size": 18152, "mtime": 1751482578847, "results": "139", "hashOfConfig": "114"}, {"size": 44361, "mtime": 1751693026439, "results": "140", "hashOfConfig": "114"}, {"size": 4187, "mtime": 1751864088314, "results": "141", "hashOfConfig": "114"}, {"size": 522, "mtime": 1736735708590, "results": "142", "hashOfConfig": "114"}, {"size": 2578, "mtime": 1740446459580, "results": "143", "hashOfConfig": "114"}, {"size": 2455, "mtime": 1751479784424, "results": "144", "hashOfConfig": "114"}, {"size": 388, "mtime": 1703845955779, "results": "145", "hashOfConfig": "114"}, {"size": 279, "mtime": 1736719733927, "results": "146", "hashOfConfig": "114"}, {"size": 1104, "mtime": 1749936905424, "results": "147", "hashOfConfig": "114"}, {"size": 3391, "mtime": 1751304153158, "results": "148", "hashOfConfig": "114"}, {"size": 5595, "mtime": 1751164672302, "results": "149", "hashOfConfig": "114"}, {"size": 944, "mtime": 1750970590507, "results": "150", "hashOfConfig": "114"}, {"size": 6669, "mtime": 1750999504134, "results": "151", "hashOfConfig": "114"}, {"size": 12864, "mtime": 1751134045332, "results": "152", "hashOfConfig": "114"}, {"size": 3835, "mtime": 1751478376207, "results": "153", "hashOfConfig": "114"}, {"size": 8101, "mtime": 1750963515173, "results": "154", "hashOfConfig": "114"}, {"size": 578, "mtime": 1705434185826, "results": "155", "hashOfConfig": "114"}, {"size": 1787, "mtime": 1734985908268, "results": "156", "hashOfConfig": "114"}, {"size": 2748, "mtime": 1736737718411, "results": "157", "hashOfConfig": "114"}, {"size": 2421, "mtime": 1737107445778, "results": "158", "hashOfConfig": "114"}, {"size": 3672, "mtime": 1751651464536, "results": "159", "hashOfConfig": "114"}, {"size": 8883, "mtime": 1751585554937, "results": "160", "hashOfConfig": "114"}, {"size": 29072, "mtime": 1750992761364, "results": "161", "hashOfConfig": "114"}, {"size": 9494, "mtime": 1750995979612, "results": "162", "hashOfConfig": "114"}, {"size": 1524, "mtime": 1750994293078, "results": "163", "hashOfConfig": "114"}, {"size": 17375, "mtime": 1751000106093, "results": "164", "hashOfConfig": "114"}, {"size": 11161, "mtime": 1750999560542, "results": "165", "hashOfConfig": "114"}, {"size": 6337, "mtime": 1751558223480, "results": "166", "hashOfConfig": "114"}, {"size": 5089, "mtime": 1751261831682, "results": "167", "hashOfConfig": "114"}, {"size": 5991, "mtime": 1751088070022, "results": "168", "hashOfConfig": "114"}, {"size": 5741, "mtime": 1751088101803, "results": "169", "hashOfConfig": "114"}, {"size": 3690, "mtime": 1751088038266, "results": "170", "hashOfConfig": "114"}, {"size": 15176, "mtime": 1751864547725, "results": "171", "hashOfConfig": "114"}, {"size": 1410, "mtime": 1751140352157, "results": "172", "hashOfConfig": "114"}, {"size": 1140, "mtime": 1751426583568, "results": "173", "hashOfConfig": "114"}, {"size": 2324, "mtime": 1751140401815, "results": "174", "hashOfConfig": "114"}, {"size": 2913, "mtime": 1751140370241, "results": "175", "hashOfConfig": "114"}, {"size": 1857, "mtime": 1751140385464, "results": "176", "hashOfConfig": "114"}, {"size": 3119, "mtime": 1751164996340, "results": "177", "hashOfConfig": "114"}, {"size": 15468, "mtime": 1751850428292, "results": "178", "hashOfConfig": "114"}, {"size": 13299, "mtime": 1751249005755, "results": "179", "hashOfConfig": "114"}, {"size": 7171, "mtime": 1751229742199, "results": "180", "hashOfConfig": "114"}, {"size": 2576, "mtime": 1751143230244, "results": "181", "hashOfConfig": "114"}, {"size": 3904, "mtime": 1751143777976, "results": "182", "hashOfConfig": "114"}, {"size": 5088, "mtime": 1751143254906, "results": "183", "hashOfConfig": "114"}, {"size": 4989, "mtime": 1751143312418, "results": "184", "hashOfConfig": "114"}, {"size": 6304, "mtime": 1751188593099, "results": "185", "hashOfConfig": "114"}, {"size": 3061, "mtime": 1751208387152, "results": "186", "hashOfConfig": "114"}, {"size": 12711, "mtime": 1751260271529, "results": "187", "hashOfConfig": "114"}, {"size": 18256, "mtime": 1751482855935, "results": "188", "hashOfConfig": "114"}, {"size": 29607, "mtime": 1751260312633, "results": "189", "hashOfConfig": "114"}, {"size": 11901, "mtime": 1751236424130, "results": "190", "hashOfConfig": "114"}, {"size": 8429, "mtime": 1751244672688, "results": "191", "hashOfConfig": "114"}, {"size": 7685, "mtime": 1751244700154, "results": "192", "hashOfConfig": "114"}, {"size": 10081, "mtime": 1751244608756, "results": "193", "hashOfConfig": "114"}, {"size": 16372, "mtime": 1751479340474, "results": "194", "hashOfConfig": "114"}, {"size": 3109, "mtime": 1751260973778, "results": "195", "hashOfConfig": "114"}, {"size": 15500, "mtime": 1751869483167, "results": "196", "hashOfConfig": "114"}, {"size": 1601, "mtime": 1751575879221, "results": "197", "hashOfConfig": "114"}, {"size": 11632, "mtime": 1751864568172, "results": "198", "hashOfConfig": "114"}, {"size": 3103, "mtime": 1751464185780, "results": "199", "hashOfConfig": "114"}, {"size": 9366, "mtime": 1751467242668, "results": "200", "hashOfConfig": "114"}, {"size": 6704, "mtime": 1751478229862, "results": "201", "hashOfConfig": "114"}, {"size": 3908, "mtime": 1751475102949, "results": "202", "hashOfConfig": "114"}, {"size": 9114, "mtime": 1751691985112, "results": "203", "hashOfConfig": "114"}, {"size": 3632, "mtime": 1751487806125, "results": "204", "hashOfConfig": "114"}, {"size": 3307, "mtime": 1751855844189, "results": "205", "hashOfConfig": "114"}, {"size": 11208, "mtime": 1751856738542, "results": "206", "hashOfConfig": "114"}, {"size": 6259, "mtime": 1751857621778, "results": "207", "hashOfConfig": "114"}, {"size": 10989, "mtime": 1751586090664, "results": "208", "hashOfConfig": "114"}, {"size": 7315, "mtime": 1751495843287, "results": "209", "hashOfConfig": "114"}, {"size": 9770, "mtime": 1751495320007, "results": "210", "hashOfConfig": "114"}, {"size": 1717, "mtime": 1751561083661, "results": "211", "hashOfConfig": "114"}, {"size": 2200, "mtime": 1751563008113, "results": "212", "hashOfConfig": "114"}, {"size": 12035, "mtime": 1751869525840, "results": "213", "hashOfConfig": "114"}, {"size": 6449, "mtime": 1751574434238, "results": "214", "hashOfConfig": "114"}, {"size": 6016, "mtime": 1751638273351, "results": "215", "hashOfConfig": "114"}, {"size": 8772, "mtime": 1751646679501, "results": "216", "hashOfConfig": "114"}, {"size": 24601, "mtime": 1751649435580, "results": "217", "hashOfConfig": "114"}, {"size": 10009, "mtime": 1751649332583, "results": "218", "hashOfConfig": "114"}, {"size": 10040, "mtime": 1751638250072, "results": "219", "hashOfConfig": "114"}, {"size": 18033, "mtime": 1751647770785, "results": "220", "hashOfConfig": "114"}, {"size": 1024, "mtime": 1751637471453, "results": "221", "hashOfConfig": "114"}, {"size": 38195, "mtime": 1751855234917, "results": "222", "hashOfConfig": "114"}, {"size": 47410, "mtime": 1751853593302, "results": "223", "hashOfConfig": "114"}, {"size": 22499, "mtime": 1751868278014, "results": "224", "hashOfConfig": "114"}, {"size": 12434, "mtime": 1751869385895, "results": "225", "hashOfConfig": "114"}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, "1ymk59w", {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "260", "usedDeprecatedRules": "229"}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "264", "usedDeprecatedRules": "229"}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "268", "usedDeprecatedRules": "229"}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "272", "usedDeprecatedRules": "229"}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "276", "usedDeprecatedRules": "229"}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "289", "usedDeprecatedRules": "229"}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "293", "usedDeprecatedRules": "229"}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "300", "usedDeprecatedRules": "229"}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "313", "usedDeprecatedRules": "229"}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "344", "usedDeprecatedRules": "229"}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "357", "usedDeprecatedRules": "229"}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "370", "usedDeprecatedRules": "229"}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "395", "usedDeprecatedRules": "229"}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "402"}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "406", "usedDeprecatedRules": "402"}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "410", "usedDeprecatedRules": "402"}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "402"}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "441", "usedDeprecatedRules": "229"}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "445", "usedDeprecatedRules": "229"}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "452", "usedDeprecatedRules": "229"}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "459", "usedDeprecatedRules": "229"}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "463", "usedDeprecatedRules": "229"}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "479", "usedDeprecatedRules": "229"}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "483", "usedDeprecatedRules": "229"}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "493", "usedDeprecatedRules": "229"}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "512", "usedDeprecatedRules": "229"}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "549", "usedDeprecatedRules": "229"}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "556", "usedDeprecatedRules": "229"}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "563", "usedDeprecatedRules": "229"}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "567", "usedDeprecatedRules": "229"}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "571", "usedDeprecatedRules": "229"}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "575", "usedDeprecatedRules": "229"}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "579", "usedDeprecatedRules": "229"}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "229"}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js", ["595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js", ["613", "614"], [], "import { Col, Form, message, Row, Select, Table } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport {\r\n  addExam,\r\n  deleteQuestionById,\r\n  editExamById,\r\n  getExamById,\r\n} from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Tabs } from \"antd\";\r\nimport AddEditQuestion from \"./AddEditQuestion\";\r\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\r\nconst { TabPane } = Tabs;\r\n\r\nfunction AddEditExam() {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [examData, setExamData] = useState(null);\r\n  const [level, setLevel] = useState('');\r\n  const [showAddEditQuestionModal, setShowAddEditQuestionModal] = useState(false);\r\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n  const [classValue, setClassValue] = useState('');\r\n  const params = useParams();\r\n\r\n\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      let response;\r\n\r\n      if (params.id) {\r\n        response = await editExamById({\r\n          ...values,\r\n          examId: params.id,\r\n        });\r\n      } else {\r\n        response = await addExam(values);\r\n      }\r\n      if (response.success) {\r\n        message.success(response.message);\r\n\r\n        // Dispatch event to notify other components about new exam creation\r\n        if (!params.id) { // Only for new exams, not edits\r\n          window.dispatchEvent(new CustomEvent('newExamCreated', {\r\n            detail: {\r\n              examName: values.name,\r\n              level: values.level,\r\n              timestamp: Date.now()\r\n            }\r\n          }));\r\n\r\n          // For new exams, navigate to edit mode so user can add questions\r\n          const newExamId = response.data?._id || response.data?.id;\r\n          if (newExamId) {\r\n            dispatch(HideLoading()); // Hide loading before navigation\r\n            navigate(`/admin/exams/edit/${newExamId}`);\r\n            return; // Don't continue with the rest of the function\r\n          }\r\n        }\r\n\r\n        // For edits, stay on the same page and refresh data\r\n        if (params.id) {\r\n          getExamData(); // Refresh the exam data\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const getExamData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Get user data from localStorage for the API call\r\n      const user = JSON.parse(localStorage.getItem(\"user\"));\r\n\r\n      const response = await getExamById({\r\n        examId: params.id,\r\n        userId: user?._id, // Add userId for backend validation\r\n      });\r\n\r\n      setClassValue(response?.data?.class);\r\n      setLevel(response?.data?.level);\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExamData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, []);\r\n\r\n  const deleteQuestion = async (questionId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteQuestionById({\r\n        questionId,\r\n        examId: params.id\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const questionsColumns = [\r\n    {\r\n      title: \"Question\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Options\",\r\n      dataIndex: \"options\",\r\n      render: (text, record) => {\r\n        if (record?.options && typeof record.options === 'object' && Object.keys(record.options).length > 0) {\r\n          return Object.keys(record.options).map((key) => (\r\n            <div key={key}>\r\n              {key}: {record.options[key]}\r\n            </div>\r\n          ));\r\n        } else {\r\n          return <div>No options available for this question.</div>;\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Correct Answer\",\r\n      dataIndex: \"correctAnswer\",\r\n      render: (text, record) => {\r\n        // Handle both old (correctOption) and new (correctAnswer) formats\r\n        const correctAnswer = record.correctAnswer || record.correctOption;\r\n\r\n        if (record.answerType === \"Free Text\" || record.type === \"fill\" || record.type === \"text\") {\r\n          return <div>{correctAnswer}</div>;\r\n        } else {\r\n          return (\r\n            <div>\r\n              {correctAnswer}: {record.options && record.options[correctAnswer] ? record.options[correctAnswer] : correctAnswer}\r\n            </div>\r\n          );\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Source\",\r\n      dataIndex: \"source\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center gap-1\">\r\n          {record?.isAIGenerated ? (\r\n            <span className=\"flex items-center gap-1 text-blue-600 text-sm\">\r\n              🤖 AI\r\n            </span>\r\n          ) : (\r\n            <span className=\"text-gray-600 text-sm\">Manual</span>\r\n          )}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span title=\"Has Image\">🖼️</span>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2 items-center\">\r\n          {/* Edit Button */}\r\n          <i\r\n            className=\"ri-pencil-line cursor-pointer text-blue-600 hover:text-blue-800\"\r\n            title=\"Edit Question\"\r\n            onClick={() => {\r\n              setSelectedQuestion(record);\r\n              setShowAddEditQuestionModal(true);\r\n            }}\r\n          ></i>\r\n\r\n          {/* Add Image Button for AI-generated questions without images */}\r\n          {record?.isAIGenerated && !record?.image && !record?.imageUrl && (\r\n            <i\r\n              className=\"ri-image-add-line cursor-pointer text-green-600 hover:text-green-800\"\r\n              title=\"Add Image to AI Question\"\r\n              onClick={() => {\r\n                setSelectedQuestion(record);\r\n                setShowAddEditQuestionModal(true);\r\n              }}\r\n            ></i>\r\n          )}\r\n\r\n          {/* AI Generated Indicator */}\r\n          {record?.isAIGenerated && (\r\n            <span\r\n              className=\"text-blue-500 text-sm\"\r\n              title=\"AI Generated Question\"\r\n            >\r\n              🤖\r\n            </span>\r\n          )}\r\n\r\n          {/* Image Indicator */}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span\r\n              className=\"text-green-500 text-sm\"\r\n              title=\"Has Image\"\r\n            >\r\n              🖼️\r\n            </span>\r\n          )}\r\n\r\n          {/* Delete Button */}\r\n          <i\r\n            className=\"ri-delete-bin-line cursor-pointer text-red-600 hover:text-red-800\"\r\n            title=\"Delete Question\"\r\n            onClick={() => {\r\n              deleteQuestion(record._id);\r\n            }}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const handleLevelChange = (e) => {\r\n    setLevel(e.target.value);\r\n    setClassValue(\"\"); // Reset class\r\n  };\r\n\r\n  console.log(classValue, \"classValue\")\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <PageTitle title={params.id ? \"Edit Exam\" : \"Add Exam\"} />\r\n      <div className=\"divider\"></div>\r\n\r\n      {(examData || !params.id) && (\r\n        <Form layout=\"vertical\" onFinish={onFinish} initialValues={examData}>\r\n          <Tabs defaultActiveKey=\"1\">\r\n            <TabPane tab=\"Exam Details\" key=\"1\">\r\n              <Row gutter={[10, 10]}>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Name\" name=\"name\">\r\n                    <input type=\"text\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Topic\" name=\"topic\">\r\n                    <input type=\"text\" placeholder=\"Enter quiz topic (e.g., Algebra, Cell Biology)\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Duration (Seconds)\" name=\"duration\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n\r\n\r\n                <Col span={8}>\r\n                  <Form.Item name=\"level\" label=\"Level\" initialValue=\"\">\r\n                    <select value={level} onChange={handleLevelChange}   >\r\n                      <option value=\"\" disabled >\r\n                        Select Level\r\n                      </option>\r\n                      <option value=\"Primary\">Primary</option>\r\n                      <option value=\"Secondary\">Secondary</option>\r\n                      <option value=\"Advance\">Advance</option>\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Category\" name=\"category\">\r\n                    <select name=\"\" id=\"\">\r\n                      <option value=\"\">Select Category</option>\r\n                      {level.toLowerCase() === \"primary\" && (\r\n                        <>\r\n                          {primarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"secondary\" && (\r\n                        <>\r\n                          {secondarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"advance\" && (\r\n                        <>\r\n                          {advanceSubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n\r\n                  <Form.Item name=\"class\" label=\"Class\" initialValue=\"\" required>\r\n                    <select value={classValue} onChange={(e) => setClassValue(e.target.value)}>\r\n                      <option value=\"\"  >\r\n                        Select Class\r\n                      </option>\r\n                      {level.toLowerCase() === \"primary\" && (\r\n                        <>\r\n                          <option value=\"1\">1</option>\r\n                          <option value=\"2\">2</option>\r\n                          <option value=\"3\">3</option>\r\n                          <option value=\"4\">4</option>\r\n                          <option value=\"5\">5</option>\r\n                          <option value=\"6\">6</option>\r\n                          <option value=\"7\">7</option>\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"secondary\" && (\r\n                        <>\r\n                          <option value=\"Form-1\">Form-1</option>\r\n                          <option value=\"Form-2\">Form-2</option>\r\n                          <option value=\"Form-3\">Form-3</option>\r\n                          <option value=\"Form-4\">Form-4</option>\r\n                        </>\r\n                      )}\r\n                      {level.toLowerCase() === \"advance\" && (\r\n                        <>\r\n                          <option value=\"Form-5\">Form-5</option>\r\n                          <option value=\"Form-6\">Form-6</option>\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Total Marks\" name=\"totalMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Passing Marks\" name=\"passingMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n              <div className=\"flex justify-end gap-2\">\r\n                <button\r\n                  className=\"primary-outlined-btn\"\r\n                  type=\"button\"\r\n                  onClick={() => navigate(\"/admin/exams\")}\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button className=\"primary-contained-btn\" type=\"submit\">\r\n                  Save\r\n                </button>\r\n              </div>\r\n            </TabPane>\r\n            {params.id && (\r\n              <TabPane tab=\"Questions\" key=\"2\">\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold\">Exam Questions</h3>\r\n                    <p className=\"text-gray-600\">Add and manage questions for this exam</p>\r\n                  </div>\r\n                  <button\r\n                    className=\"primary-contained-btn\"\r\n                    type=\"button\"\r\n                    onClick={() => setShowAddEditQuestionModal(true)}\r\n                  >\r\n                    Add Question\r\n                  </button>\r\n                </div>\r\n\r\n                <Table\r\n                  columns={questionsColumns}\r\n                  dataSource={examData?.questions || []}\r\n                  pagination={{\r\n                    pageSize: 10,\r\n                    showSizeChanger: true,\r\n                    showQuickJumper: true,\r\n                  }}\r\n                  locale={{\r\n                    emptyText: examData?.questions?.length === 0 ?\r\n                      'No questions added yet. Click \"Add Question\" to add questions.' :\r\n                      'Loading questions...'\r\n                  }}\r\n                />\r\n              </TabPane>\r\n            )}\r\n          </Tabs>\r\n        </Form>\r\n      )}\r\n\r\n      {showAddEditQuestionModal && (\r\n        <AddEditQuestion\r\n          setShowAddEditQuestionModal={setShowAddEditQuestionModal}\r\n          showAddEditQuestionModal={showAddEditQuestionModal}\r\n          examId={params.id}\r\n          refreshData={getExamData}\r\n          selectedQuestion={selectedQuestion}\r\n          setSelectedQuestion={setSelectedQuestion}\r\n        />\r\n      )}\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AddEditExam;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js", ["615"], [], "import { message, Table } from \"antd\";\r\nimport React, { useEffect } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport { TbDashboard, TbPlus } from \"react-icons/tb\";\r\nimport { deleteExamById, getAllExams } from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Exams() {\r\n  const navigate = useNavigate();\r\n  const [exams, setExams] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n\r\n  const getExamsData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExams(response.data.reverse());\r\n        console.log(response, \"exam\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteExam = async (examId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteExamById({\r\n        examId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamsData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Duration\",\r\n      dataIndex: \"duration\",\r\n      render: (duration) => `${Math.round(duration / 60)} min`,\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n    },\r\n    {\r\n      title: \"Category\",\r\n      dataIndex: \"category\",\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalMarks\",\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"passingMarks\",\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2\">\r\n          <i\r\n            className=\"ri-pencil-line\"\r\n            onClick={() => navigate(`/admin/exams/edit/${record._id}`)}\r\n          ></i>\r\n          <i\r\n            className=\"ri-delete-bin-line\"\r\n            onClick={() => deleteExam(record._id)}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getExamsData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <div className=\"flex items-center gap-4\">\r\n          {/* Dashboard Shortcut */}\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={() => navigate('/admin/dashboard')}\r\n            className=\"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\"\r\n          >\r\n            <TbDashboard className=\"w-4 h-4\" />\r\n            <span className=\"hidden sm:inline text-sm font-medium\">Dashboard</span>\r\n          </motion.button>\r\n\r\n          <PageTitle title=\"Exams\" />\r\n        </div>\r\n\r\n        <motion.button\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          className=\"primary-outlined-btn flex items-center gap-2\"\r\n          onClick={() => navigate(\"/admin/exams/add\")}\r\n        >\r\n          <TbPlus className=\"w-4 h-4\" />\r\n          Add Exam\r\n        </motion.button>\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      <Table columns={columns} dataSource={exams} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Exams;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx", ["616"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { getPlans } from \"../../../apicalls/plans\";\r\nimport \"./Plans.css\";\r\nimport ConfirmModal from \"./components/ConfirmModal\";\r\nimport WaitingModal from \"./components/WaitingModal\";\r\nimport { addPayment } from \"../../../apicalls/payment\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { setPaymentVerificationNeeded } from \"../../../redux/paymentSlice\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nconst Plans = () => {\r\n    const [plans, setPlans] = useState([]);\r\n    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);\r\n    const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);\r\n    const [paymentInProgress, setPaymentInProgress] = useState(false);\r\n    const [selectedPlan, setSelectedPlan] = useState(null);\r\n    const { user } = useSelector((state) => state.user);\r\n    const { subscriptionData } = useSelector((state) => state.subscription);\r\n    const dispatch = useDispatch()\r\n\r\n    useEffect(() => {\r\n        const fetchPlans = async () => {\r\n            try {\r\n                const response = await getPlans();\r\n                setPlans(response);\r\n            } catch (error) {\r\n                console.error(\"Error fetching plans:\", error);\r\n            }\r\n        };\r\n\r\n        fetchPlans();\r\n    }, []);\r\n\r\n    const transactionDetails = {\r\n        amount: selectedPlan?.discountedPrice || 'N/A',\r\n        currency: \"TZS\",\r\n        destination: \"brainwave.zone\",\r\n    };\r\n\r\n\r\n    const handlePaymentStart = async (plan) => {\r\n        setSelectedPlan(plan);\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await addPayment({ plan });\r\n            localStorage.setItem(\"order_id\", response.order_id);\r\n            setWaitingModalOpen(true);\r\n            setPaymentInProgress(true);\r\n            dispatch(setPaymentVerificationNeeded(true));\r\n        } catch (error) {\r\n            console.error(\"Error processing payment:\", error);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        console.log(\"subscription Data in Plans\", subscriptionData)\r\n        if (user?.paymentRequired === true && subscriptionData?.paymentStatus === \"paid\" && paymentInProgress) {\r\n            setWaitingModalOpen(false);\r\n            setConfirmModalOpen(true);\r\n            setPaymentInProgress(false);\r\n        }\r\n    }, [user, subscriptionData]);\r\n\r\n    return (\r\n        <div>\r\n            {!user ?\r\n                <>\r\n                </>\r\n                :\r\n                !user.paymentRequired ?\r\n                    <div className=\"no-plan-required\">\r\n                        <div className=\"no-plan-content\">\r\n                            <h2>No Plan Required</h2>\r\n                            <p>You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!</p>\r\n                        </div>\r\n                    </div>\r\n                    :\r\n                    subscriptionData?.paymentStatus !== \"paid\" ?\r\n                        <div className=\"plans-container\">\r\n                            {plans.map((plan) => (\r\n                                <div\r\n                                    key={plan._id}\r\n                                    className={`plan-card ${plan.title === \"Standard Membership\" ? \"basic\" : \"\"}`}\r\n                                >\r\n                                    {plan.title === \"Standard Membership\" && (\r\n                                        <div className=\"most-popular-label\">MOST POPULAR</div>\r\n                                    )}\r\n\r\n                                    <h2 className=\"plan-title\">{plan.title}</h2>\r\n                                    <p className=\"plan-actual-price\">\r\n                                        {plan.actualPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <p className=\"plan-discounted-price\">\r\n                                        {plan.discountedPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <span className=\"plan-discount-tag\">\r\n                                        {plan.discountPercentage}% OFF\r\n                                    </span>\r\n                                    <p className=\"plan-renewal-info\">\r\n                                        For {plan?.features[0]}\r\n                                    </p>\r\n                                    <button className=\"plan-button\"\r\n                                        // onClick={() => setConfirmModalOpen(true)}\r\n                                        onClick={() => handlePaymentStart(plan)}\r\n                                    >Choose Plan</button>\r\n                                    <ul className=\"plan-features\">\r\n                                        {plan.features.map((feature, index) => (\r\n                                            <li key={index} className=\"plan-feature\">\r\n                                                <span className=\"plan-feature-icon\">✔</span>\r\n                                                {feature}\r\n                                            </li>\r\n                                        ))}\r\n                                    </ul>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        :\r\n                        <div className=\"subscription-details\">\r\n                            <h1 className=\"plan-title\">{subscriptionData.plan.title}</h1>\r\n\r\n                            <svg\r\n                                width=\"64px\"\r\n                                height=\"64px\"\r\n                                viewBox=\"-3.2 -3.2 38.40 38.40\"\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"#10B981\"\r\n                                stroke=\"#253864\"\r\n                                transform=\"matrix(1, 0, 0, 1, 0, 0)\"\r\n                            >\r\n                                <g id=\"SVGRepo_bgCarrier\" strokeWidth=\"0\"></g>\r\n                                <g id=\"SVGRepo_tracerCarrier\" strokeLinecap=\"round\" strokeLinejoin=\"round\" stroke=\"#CCCCCC\" strokeWidth=\"0.064\"></g>\r\n                                <g id=\"SVGRepo_iconCarrier\">\r\n                                    <path\r\n                                        d=\"m16 0c8.836556 0 16 7.163444 16 16s-7.163444 16-16 16-16-7.163444-16-16 7.163444-16 16-16zm5.7279221 11-7.0710679 7.0710678-4.2426406-4.2426407-1.4142136 1.4142136 5.6568542 5.6568542 8.4852814-8.4852813z\"\r\n                                        fill=\"#202327\"\r\n                                        fillRule=\"evenodd\"\r\n                                    ></path>\r\n                                </g>\r\n                            </svg>\r\n\r\n                            <p className=\"plan-description\">{subscriptionData?.plan?.subscriptionData}</p>\r\n                            <p className=\"plan-dates\">Start Date: {subscriptionData.startDate}</p>\r\n                            <p className=\"plan-dates\">End Date: {subscriptionData.endDate}</p>\r\n                        </div>\r\n            }\r\n\r\n            <WaitingModal\r\n                isOpen={isWaitingModalOpen}\r\n                onClose={() => setWaitingModalOpen(false)}\r\n            />\r\n\r\n            <ConfirmModal\r\n                isOpen={isConfirmModalOpen}\r\n                onClose={() => setConfirmModalOpen(false)}\r\n                transaction={transactionDetails}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Plans;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js", ["617", "618", "619"], [], "import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\r\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\r\nimport { extractUserResultData, safeNumber } from \"../../../utils/quizDataUtils\";\r\n\r\n// Minimal Safe Quiz Component\r\nconst MinimalQuizRenderer = ({ question, questionIndex, totalQuestions, selectedAnswer, onAnswerSelect, onNext, onPrevious, timeLeft, examTitle }) => {\r\n  // Safety checks\r\n  if (!question) {\r\n    return <div>Loading question...</div>;\r\n  }\r\n\r\n  // Convert everything to safe strings\r\n  const questionText = question.name ? String(question.name) : 'Question text not available';\r\n  const answerType = question.answerType ? String(question.answerType) : 'Options';\r\n\r\n  // Process options safely\r\n  let options = [];\r\n  if (question.options) {\r\n    if (Array.isArray(question.options)) {\r\n      options = question.options.map(opt => String(opt || ''));\r\n    } else if (typeof question.options === 'object') {\r\n      options = Object.values(question.options).map(opt => String(opt || ''));\r\n    }\r\n  }\r\n\r\n  const formatTime = (seconds) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return mins + ':' + (secs < 10 ? '0' : '') + secs;\r\n  };\r\n\r\n  return (\r\n    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)' }}>\r\n      {/* Simple Header */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderBottom: '1px solid #e5e7eb',\r\n        padding: '16px',\r\n        position: 'sticky',\r\n        top: 0,\r\n        zIndex: 50\r\n      }}>\r\n        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          <div>\r\n            <h1 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>\r\n              {examTitle ? String(examTitle) : 'Quiz'}\r\n            </h1>\r\n            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>\r\n              Question {questionIndex + 1} of {totalQuestions}\r\n            </p>\r\n          </div>\r\n\r\n          <div style={{\r\n            background: timeLeft <= 60 ? '#dc2626' : '#2563eb',\r\n            color: 'white',\r\n            padding: '12px 24px',\r\n            borderRadius: '12px',\r\n            fontFamily: 'monospace',\r\n            fontWeight: 'bold'\r\n          }}>\r\n            TIME: {formatTime(timeLeft)}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Question Content */}\r\n      <div style={{ maxWidth: '800px', margin: '0 auto', padding: '32px 16px' }}>\r\n        <div style={{\r\n          background: 'white',\r\n          borderRadius: '16px',\r\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\r\n          padding: '32px',\r\n          marginBottom: '24px'\r\n        }}>\r\n          {/* Question Number Badge */}\r\n          <div style={{ marginBottom: '24px' }}>\r\n            <span style={{\r\n              background: '#dbeafe',\r\n              color: '#1e40af',\r\n              padding: '8px 16px',\r\n              borderRadius: '20px',\r\n              fontSize: '14px',\r\n              fontWeight: '600'\r\n            }}>\r\n              Question {questionIndex + 1} of {totalQuestions}\r\n            </span>\r\n          </div>\r\n\r\n          {/* Question Text */}\r\n          <div style={{ marginBottom: '32px' }}>\r\n            <h2 style={{\r\n              fontSize: '20px',\r\n              fontWeight: '500',\r\n              color: '#111827',\r\n              lineHeight: '1.6',\r\n              margin: 0\r\n            }}>\r\n              {questionText}\r\n            </h2>\r\n          </div>\r\n\r\n          {/* Image */}\r\n          {(question.image || question.imageUrl) && (\r\n            <div style={{ marginBottom: '32px', textAlign: 'center' }}>\r\n              <img\r\n                src={question.image || question.imageUrl}\r\n                alt=\"Question\"\r\n                style={{\r\n                  maxWidth: '100%',\r\n                  maxHeight: '400px',\r\n                  objectFit: 'contain',\r\n                  borderRadius: '12px',\r\n                  border: '1px solid #e5e7eb'\r\n                }}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {/* Answer Options */}\r\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>\r\n            {answerType === \"Options\" && options.length > 0 ? (\r\n              options.map((option, index) => {\r\n                const letter = String.fromCharCode(65 + index);\r\n                const isSelected = selectedAnswer === index;\r\n\r\n                return (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => onAnswerSelect(index)}\r\n                    style={{\r\n                      width: '100%',\r\n                      textAlign: 'left',\r\n                      padding: '16px',\r\n                      borderRadius: '12px',\r\n                      border: isSelected ? '2px solid #2563eb' : '2px solid #e5e7eb',\r\n                      background: isSelected ? '#eff6ff' : 'white',\r\n                      color: isSelected ? '#1e40af' : '#111827',\r\n                      cursor: 'pointer',\r\n                      transition: 'all 0.3s',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '16px'\r\n                    }}\r\n                  >\r\n                    <div style={{\r\n                      width: '32px',\r\n                      height: '32px',\r\n                      borderRadius: '50%',\r\n                      background: isSelected ? '#2563eb' : '#f3f4f6',\r\n                      color: isSelected ? 'white' : '#6b7280',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      fontWeight: 'bold',\r\n                      fontSize: '14px'\r\n                    }}>\r\n                      {letter}\r\n                    </div>\r\n                    <span style={{ flex: 1, fontWeight: '500' }}>{option}</span>\r\n                    {isSelected && (\r\n                      <div style={{\r\n                        width: '24px',\r\n                        height: '24px',\r\n                        borderRadius: '50%',\r\n                        background: '#2563eb',\r\n                        color: 'white',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}>\r\n                        ✓\r\n                      </div>\r\n                    )}\r\n                  </button>\r\n                );\r\n              })\r\n            ) : (\r\n              <div>\r\n                <label style={{\r\n                  display: 'block',\r\n                  fontSize: '14px',\r\n                  fontWeight: '500',\r\n                  color: '#374151',\r\n                  marginBottom: '8px'\r\n                }}>\r\n                  Your Answer:\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={selectedAnswer || ''}\r\n                  onChange={(e) => onAnswerSelect(e.target.value)}\r\n                  placeholder=\"Type your answer here...\"\r\n                  style={{\r\n                    width: '100%',\r\n                    padding: '16px',\r\n                    border: '2px solid #e5e7eb',\r\n                    borderRadius: '12px',\r\n                    fontSize: '16px',\r\n                    outline: 'none'\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          <button\r\n            onClick={onPrevious}\r\n            disabled={questionIndex === 0}\r\n            style={{\r\n              padding: '12px 24px',\r\n              borderRadius: '12px',\r\n              fontWeight: '600',\r\n              border: 'none',\r\n              cursor: questionIndex === 0 ? 'not-allowed' : 'pointer',\r\n              background: questionIndex === 0 ? '#e5e7eb' : '#4b5563',\r\n              color: questionIndex === 0 ? '#9ca3af' : 'white'\r\n            }}\r\n          >\r\n            ← Previous\r\n          </button>\r\n\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px' }}>Progress</div>\r\n            <div style={{\r\n              width: '200px',\r\n              height: '8px',\r\n              background: '#e5e7eb',\r\n              borderRadius: '4px',\r\n              overflow: 'hidden'\r\n            }}>\r\n              <div\r\n                style={{\r\n                  height: '100%',\r\n                  background: '#2563eb',\r\n                  borderRadius: '4px',\r\n                  width: ((questionIndex + 1) / totalQuestions) * 100 + '%',\r\n                  transition: 'width 0.3s'\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <button\r\n            onClick={onNext}\r\n            style={{\r\n              padding: '12px 24px',\r\n              borderRadius: '12px',\r\n              fontWeight: '600',\r\n              border: 'none',\r\n              cursor: 'pointer',\r\n              background: '#2563eb',\r\n              color: 'white'\r\n            }}\r\n          >\r\n            {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next →'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Minimal Safe Review Component\r\nconst MinimalReviewRenderer = ({\r\n  questions,\r\n  selectedOptions,\r\n  explanations,\r\n  fetchExplanation,\r\n  setView,\r\n  examData,\r\n  setSelectedQuestionIndex,\r\n  setSelectedOptions,\r\n  setResult,\r\n  setTimeUp,\r\n  setSecondsLeft,\r\n  setExplanations\r\n}) => {\r\n  if (!questions || !Array.isArray(questions)) {\r\n    return <div>No questions to review</div>;\r\n  }\r\n\r\n  return (\r\n    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)', padding: '24px' }}>\r\n      <div style={{ maxWidth: '800px', margin: '0 auto' }}>\r\n        {/* Header */}\r\n        <div style={{ textAlign: 'center', marginBottom: '24px' }}>\r\n          <div style={{\r\n            background: 'white',\r\n            borderRadius: '16px',\r\n            padding: '24px',\r\n            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',\r\n            border: '1px solid #e2e8f0'\r\n          }}>\r\n            <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#2563eb', margin: '0 0 8px 0' }}>\r\n              Answer Review\r\n            </h2>\r\n            <p style={{ color: '#64748b', margin: 0 }}>Review your answers and get explanations</p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Questions */}\r\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', marginBottom: '24px' }}>\r\n          {questions.map((question, index) => {\r\n            if (!question) return null;\r\n\r\n            const questionText = question.name ? String(question.name) : 'Question not available';\r\n            const answerType = question.answerType ? String(question.answerType) : 'Options';\r\n            const userAnswer = selectedOptions[index];\r\n\r\n            let isCorrect = false;\r\n            let correctAnswerText = 'Unknown';\r\n            let userAnswerText = 'Not answered';\r\n\r\n            if (answerType === \"Options\") {\r\n              isCorrect = question.correctOption === userAnswer;\r\n\r\n              if (question.options && question.correctOption !== undefined) {\r\n                const correctOpt = question.options[question.correctOption];\r\n                correctAnswerText = correctOpt ? String(correctOpt) : 'Unknown';\r\n              }\r\n\r\n              if (question.options && userAnswer !== undefined) {\r\n                const userOpt = question.options[userAnswer];\r\n                userAnswerText = userOpt ? String(userOpt) : 'Not answered';\r\n              }\r\n            } else {\r\n              isCorrect = question.correctAnswer === userAnswer;\r\n              correctAnswerText = question.correctAnswer ? String(question.correctAnswer) : 'Unknown';\r\n              userAnswerText = userAnswer ? String(userAnswer) : 'Not answered';\r\n            }\r\n\r\n            return (\r\n              <div\r\n                key={question._id || index}\r\n                style={{\r\n                  borderRadius: '12px',\r\n                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\r\n                  border: '2px solid ' + (isCorrect ? '#10b981' : '#ef4444'),\r\n                  padding: '16px',\r\n                  background: isCorrect ? '#f0fdf4' : '#fef2f2'\r\n                }}\r\n              >\r\n                {/* Question */}\r\n                <div style={{ marginBottom: '12px' }}>\r\n                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>\r\n                    <div style={{\r\n                      width: '32px',\r\n                      height: '32px',\r\n                      borderRadius: '8px',\r\n                      background: '#2563eb',\r\n                      color: 'white',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      fontWeight: 'bold',\r\n                      fontSize: '14px',\r\n                      flexShrink: 0,\r\n                      marginTop: '4px'\r\n                    }}>\r\n                      {index + 1}\r\n                    </div>\r\n                    <div style={{ flex: 1 }}>\r\n                      <p style={{\r\n                        color: '#1e293b',\r\n                        fontWeight: '500',\r\n                        lineHeight: '1.6',\r\n                        margin: 0\r\n                      }}>\r\n                        {questionText}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Your Answer */}\r\n                <div style={{ marginBottom: '8px' }}>\r\n                  <span style={{ fontSize: '14px', fontWeight: '600', color: '#64748b' }}>Your Answer: </span>\r\n                  <span style={{\r\n                    fontWeight: '500',\r\n                    color: isCorrect ? '#059669' : '#dc2626'\r\n                  }}>\r\n                    {userAnswerText}\r\n                  </span>\r\n                  <span style={{\r\n                    marginLeft: '12px',\r\n                    fontSize: '18px',\r\n                    color: isCorrect ? '#10b981' : '#ef4444'\r\n                  }}>\r\n                    {isCorrect ? '✓' : '✗'}\r\n                  </span>\r\n                </div>\r\n\r\n                {/* Correct Answer */}\r\n                {!isCorrect && (\r\n                  <div style={{ marginBottom: '8px' }}>\r\n                    <span style={{ fontSize: '14px', fontWeight: '600', color: '#64748b' }}>Correct Answer: </span>\r\n                    <span style={{ fontWeight: '500', color: '#059669' }}>{correctAnswerText}</span>\r\n                    <span style={{ marginLeft: '12px', fontSize: '18px', color: '#10b981' }}>✓</span>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation Button */}\r\n                {!isCorrect && (\r\n                  <div style={{ marginTop: '8px' }}>\r\n                    <button\r\n                      onClick={() => {\r\n                        fetchExplanation(\r\n                          questionText,\r\n                          correctAnswerText,\r\n                          userAnswerText,\r\n                          question.image || question.imageUrl || ''\r\n                        );\r\n                      }}\r\n                      style={{\r\n                        padding: '8px 16px',\r\n                        background: '#2563eb',\r\n                        color: 'white',\r\n                        border: 'none',\r\n                        borderRadius: '8px',\r\n                        fontSize: '14px',\r\n                        fontWeight: '500',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        gap: '8px'\r\n                      }}\r\n                    >\r\n                      <span>💡</span>\r\n                      <span>Get Explanation</span>\r\n                    </button>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation */}\r\n                {explanations[questionText] && (\r\n                  <div style={{\r\n                    marginTop: '12px',\r\n                    padding: '12px',\r\n                    background: 'white',\r\n                    borderRadius: '8px',\r\n                    borderLeft: '4px solid #2563eb',\r\n                    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\r\n                    border: '1px solid #e5e7eb'\r\n                  }}>\r\n                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\r\n                      <span style={{ fontSize: '18px', marginRight: '8px' }}>💡</span>\r\n                      <h6 style={{ fontWeight: 'bold', color: '#1f2937', fontSize: '16px', margin: 0 }}>\r\n                        Explanation\r\n                      </h6>\r\n                    </div>\r\n\r\n                    {/* Image */}\r\n                    {(question.image || question.imageUrl) && (\r\n                      <div style={{\r\n                        marginBottom: '12px',\r\n                        padding: '8px',\r\n                        background: '#f9fafb',\r\n                        borderRadius: '6px',\r\n                        border: '1px solid #e5e7eb'\r\n                      }}>\r\n                        <div style={{ marginBottom: '4px' }}>\r\n                          <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>\r\n                            📊 Reference Diagram:\r\n                          </span>\r\n                        </div>\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <img\r\n                            src={question.image || question.imageUrl}\r\n                            alt=\"Question diagram\"\r\n                            style={{\r\n                              maxWidth: '100%',\r\n                              maxHeight: '200px',\r\n                              objectFit: 'contain',\r\n                              borderRadius: '6px',\r\n                              border: '1px solid #d1d5db'\r\n                            }}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    <div style={{\r\n                      fontSize: '14px',\r\n                      color: '#1f2937',\r\n                      lineHeight: '1.6',\r\n                      background: '#f9fafb',\r\n                      padding: '8px',\r\n                      borderRadius: '6px'\r\n                    }}>\r\n                      {explanations[questionText] ? String(explanations[questionText]) : ''}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div style={{\r\n          display: 'flex',\r\n          flexDirection: window.innerWidth < 640 ? 'column' : 'row',\r\n          gap: '16px',\r\n          justifyContent: 'center',\r\n          alignItems: 'center'\r\n        }}>\r\n          <button\r\n            onClick={() => setView(\"result\")}\r\n            style={{\r\n              padding: '16px 32px',\r\n              background: '#4b5563',\r\n              color: 'white',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              fontWeight: 'bold',\r\n              cursor: 'pointer',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\r\n            }}\r\n          >\r\n            ← Back to Results\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => {\r\n              setView(\"instructions\");\r\n              setSelectedQuestionIndex(0);\r\n              setSelectedOptions({});\r\n              setResult({});\r\n              setTimeUp(false);\r\n              setSecondsLeft(examData?.duration || 0);\r\n              setExplanations({});\r\n            }}\r\n            style={{\r\n              padding: '16px 32px',\r\n              background: '#059669',\r\n              color: 'white',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              fontWeight: 'bold',\r\n              cursor: 'pointer',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\r\n            }}\r\n          >\r\n            🔄 Retake Quiz\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction WriteExam() {\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [startTime, setStartTime] = useState(null);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      dispatch(ShowLoading());\r\n      console.log(\"Fetching exam data for ID:\", params.id);\r\n\r\n      const response = await getExamById({ examId: params.id });\r\n      console.log(\"Exam API Response:\", response);\r\n\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n\r\n      if (response.success) {\r\n        const examData = response.data;\r\n\r\n        // Check different possible question locations\r\n        let questions = [];\r\n        if (examData?.questions && Array.isArray(examData.questions)) {\r\n          questions = examData.questions;\r\n        } else if (examData?.question && Array.isArray(examData.question)) {\r\n          questions = examData.question;\r\n        } else if (examData && Array.isArray(examData)) {\r\n          questions = examData;\r\n        }\r\n\r\n        console.log(\"Exam Data:\", examData);\r\n        console.log(\"Questions found:\", questions.length);\r\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\r\n\r\n        setQuestions(questions);\r\n        setExamData(examData);\r\n        setSecondsLeft(examData?.duration || 0);\r\n\r\n        if (questions.length === 0) {\r\n          console.warn(\"No questions found in exam data\");\r\n          console.log(\"Full response for debugging:\", response);\r\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\r\n        }\r\n      } else {\r\n        console.error(\"API Error:\", response.message);\r\n        console.log(\"Full error response:\", response);\r\n        message.error(response.message || \"Failed to load exam data\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n      console.error(\"Exception in getExamData:\", error);\r\n      message.error(error.message || \"Failed to load exam. Please try again.\");\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = (q.options && q.options[correctKey]) || correctKey;\r\n          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      // Calculate time spent\r\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\r\n      const totalTimeAllowed = (examData?.duration || 0) * 60; // Convert minutes to seconds\r\n\r\n      // Calculate score and points\r\n      const totalQuestions = questions.length;\r\n      const correctCount = correctAnswers.length;\r\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\r\n      const points = correctCount * 10; // 10 points per correct answer\r\n\r\n      // Determine pass/fail based on percentage\r\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\r\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\r\n\r\n      const tempResult = {\r\n        correctAnswers: correctAnswers || [],\r\n        wrongAnswers: wrongAnswers || [],\r\n        verdict: verdict || \"Fail\",\r\n        score: scorePercentage,\r\n        points: points,\r\n        totalQuestions: totalQuestions,\r\n        timeSpent: timeSpent,\r\n        totalTimeAllowed: totalTimeAllowed\r\n      };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        // Include XP data in the result\r\n        const resultWithXP = {\r\n          ...tempResult,\r\n          xpData: response.xpData\r\n        };\r\n        setResult(resultWithXP);\r\n\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0; // Duration is already in seconds\r\n    setSecondsLeft(totalSeconds);\r\n    setStartTime(Date.now()); // Record start time for XP calculation\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\r\n    if (params.id) {\r\n      getExamData();\r\n    } else {\r\n      console.error(\"No exam ID provided in URL parameters\");\r\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\r\n      navigate('/user/quiz');\r\n    }\r\n  }, [params.id, getExamData, navigate]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Add fullscreen class for all quiz views (instructions, questions, results)\r\n  useEffect(() => {\r\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\r\n      document.body.classList.add(\"quiz-fullscreen\");\r\n    } else {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    };\r\n  }, [view]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n          questions={questions}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        isLoading ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\">\r\n                <svg className=\"w-12 h-12 text-white animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-blue-800 mb-4\">Loading Quiz...</h3>\r\n              <p className=\"text-blue-600 text-lg\">\r\n                Please wait while we prepare your questions.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-6 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. This could be due to:\r\n              </p>\r\n              <ul className=\"text-left text-amber-700 mb-8 space-y-2\">\r\n                <li>• Questions not properly linked to this exam</li>\r\n                <li>• Database connection issues</li>\r\n                <li>• Exam configuration problems</li>\r\n              </ul>\r\n              <div className=\"space-y-3\">\r\n                <button\r\n                  onClick={repairExamQuestions}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔧 Repair Questions\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    console.log(\"Retrying exam data fetch...\");\r\n                    getExamData();\r\n                  }}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔄 Retry Loading\r\n                </button>\r\n                <button\r\n                  onClick={() => navigate('/user/quiz')}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  ← Back to Quiz List\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <MinimalQuizRenderer\r\n            question={questions[selectedQuestionIndex]}\r\n            questionIndex={selectedQuestionIndex}\r\n            totalQuestions={questions.length}\r\n            selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n            onAnswerSelect={(answer) => setSelectedOptions({...selectedOptions, [selectedQuestionIndex]: answer})}\r\n            onNext={() => {\r\n              if (selectedQuestionIndex === questions.length - 1) {\r\n                calculateResult();\r\n              } else {\r\n                setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n              }\r\n            }}\r\n            onPrevious={() => {\r\n              if (selectedQuestionIndex > 0) {\r\n                setSelectedQuestionIndex(selectedQuestionIndex - 1);\r\n              }\r\n            }}\r\n            timeLeft={secondsLeft}\r\n            examTitle={examData?.name || \"Quiz\"}\r\n          />\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\">\r\n              {/* Modern Header */}\r\n              <div className={`px-8 py-10 text-center relative ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\"\r\n                  : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"\r\n              }`}>\r\n                <div className=\"relative\">\r\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500 to-green-600\"\r\n                      : \"bg-gradient-to-br from-amber-500 to-orange-600\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-12 h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${\r\n                    result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"}\r\n                  </h1>\r\n                  <p className=\"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've mastered this exam with flying colors!\"\r\n                      : \"Every challenge makes you stronger. Try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modern Statistics Cards */}\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                  {/* Score Card */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-blue-600 mb-2 tracking-tight\">\r\n                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-blue-700/80 uppercase tracking-wider\">Your Score</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-emerald-600 mb-2 tracking-tight\">\r\n                        {Array.isArray(result.correctAnswers) ? result.correctAnswers.length : 0}/{questions.length}\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\">Correct</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\"\r\n                      : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"\r\n                  }`}>\r\n                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${\r\n                      result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"\r\n                    } to-transparent`}></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className={`text-4xl font-black mb-2 tracking-tight ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                      </div>\r\n                      <div className={`text-sm font-bold uppercase tracking-wider ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Progress Visualization */}\r\n                <div className=\"mb-8\">\r\n                  <div className=\"relative bg-slate-100 rounded-2xl p-6\">\r\n                    <div className=\"text-center mb-4\">\r\n                      <h3 className=\"text-lg font-bold text-slate-700 mb-1\">Performance Overview</h3>\r\n                      <p className=\"text-sm text-slate-500\">Your achievement level</p>\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\">\r\n                        <div\r\n                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${\r\n                            result.verdict === \"Pass\"\r\n                              ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\"\r\n                              : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"\r\n                          }`}\r\n                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                        >\r\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"></div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center mt-3\">\r\n                        <span className=\"text-xs font-medium text-slate-500\">0%</span>\r\n                        <span className={`text-lg font-black tracking-tight ${\r\n                          result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                        }`}>\r\n                          {Math.round(((Array.isArray(result.correctAnswers) ? result.correctAnswers.length : 0) / questions.length) * 100)}%\r\n                        </span>\r\n                        <span className=\"text-xs font-medium text-slate-500\">100%</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* XP Display */}\r\n                {result.xpData && (\r\n                  <div className=\"mb-8\">\r\n                    <XPResultDisplay xpData={result.xpData} />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Modern Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Review Answers</span>\r\n                  </button>\r\n\r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <MinimalReviewRenderer\r\n          questions={questions}\r\n          selectedOptions={selectedOptions}\r\n          explanations={explanations}\r\n          fetchExplanation={fetchExplanation}\r\n          setView={setView}\r\n          examData={examData}\r\n          setSelectedQuestionIndex={setSelectedQuestionIndex}\r\n          setSelectedOptions={setSelectedOptions}\r\n          setResult={setResult}\r\n          setTimeUp={setTimeUp}\r\n          setSecondsLeft={setSecondsLeft}\r\n          setExplanations={setExplanations}\r\n        />\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js", ["620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636"], [], "import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport {\n  FaPlayCircle,\n  FaBook,\n  FaVideo,\n  FaFileAlt,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbFileText,\n  TbBook as TbBookIcon,\n  TbScho<PERSON>,\n  Tb<PERSON><PERSON><PERSON>,\n  <PERSON>b<PERSON><PERSON><PERSON>,\n  Tb<PERSON>ortAscending,\n  Tb<PERSON><PERSON>,\n  TbD<PERSON>load,\n  Tb<PERSON><PERSON>,\n  TbCalendar,\n  Tb<PERSON>ser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  TbAlertTriangle,\n  TbInfoCircle,\n  TbCheck,\n  TbSubtitles,\n  TbBooks,\n  TbCertificate\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction StudyMaterial() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = user?.level || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary'\n    ? primarySubjects\n    : userLevelLower === 'secondary'\n      ? secondarySubjects\n      : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary'\n    ? ['1', '2', '3', '4', '5', '6', '7']\n    : userLevelLower === 'secondary'\n      ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']\n      : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"videos\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = user?.class || user?.className;\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n  const [selectedSubtitle, setSelectedSubtitle] = useState('off');\n  const [videoRef, setVideoRef] = useState(null);\n\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = user?.class || user?.className;\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user?.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user?.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" :\n        selectedClass.toString().replace(\"Form-\", \"\");\n\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject, // This can be \"all\" or a specific subject\n      };\n      if (userLevel) {\n        data.level = userLevel;\n      }\n\n      const res = await getStudyMaterial(data);\n\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = (tab) => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n\n  const handleSubjectChange = (subject) => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n\n  const handleClassChange = (className) => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material =>\n        material.title.toLowerCase().includes(searchLower) ||\n        material.subject.toLowerCase().includes(searchLower) ||\n        (material.year && material.year.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n        // For videos (no year field), sort by creation date or reverse order\n        else if (activeTab === \"videos\") {\n          // Since videos are fetched in reverse order from server, maintain that for \"newest\"\n          return 0; // Keep original order (newest first from server)\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n        // For videos, reverse the order\n        else if (activeTab === \"videos\") {\n          return 0; // Will be reversed after sort\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n    // For videos with \"oldest\" sort, reverse the array\n    if (activeTab === \"videos\" && sortBy === \"oldest\") {\n      filtered.reverse();\n    }\n\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = (documentUrl) => {\n    // Use proxy endpoint to handle CORS issues\n    const proxyUrl = `${process.env.REACT_APP_SERVER_DOMAIN}/api/study/document-proxy?url=${encodeURIComponent(documentUrl)}`;\n\n    fetch(proxyUrl, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('token')}`,\n      }\n    })\n      .then((response) => {\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        return response.blob();\n      })\n      .then((blob) => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = documentUrl.split(\"/\").pop();\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      })\n      .catch((error) => {\n        console.error(\"Error downloading the file:\", error);\n        // Fallback to direct download if proxy fails\n        window.open(documentUrl, '_blank');\n      });\n  };\n\n  const handleDocumentPreview = (documentUrl) => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedMaterials[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    setSelectedSubtitle('off');\n    setVideoRef(null);\n  };\n\n  // Handle subtitle selection\n  const handleSubtitleChange = (language) => {\n    setSelectedSubtitle(language);\n\n    if (videoRef) {\n      const tracks = videoRef.textTracks;\n\n      // Disable all tracks first\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'disabled';\n      }\n\n      // Enable selected track\n      if (language !== 'off') {\n        for (let i = 0; i < tracks.length; i++) {\n          if (tracks[i].language === language) {\n            tracks[i].mode = 'showing';\n            break;\n          }\n        }\n      }\n    }\n  };\n\n  const handleExpandVideo = () => {\n    setIsVideoExpanded(true);\n  };\n\n  const handleCollapseVideo = () => {\n    setIsVideoExpanded(false);\n  };\n\n\n\n\n\n\n\n\n\n  // Note: Auto-refresh removed since videos are now uploaded synchronously\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          credentials: 'include'\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.warn('⚠️ Failed to get signed URL, using original:', error.message);\n        return videoUrl;\n      }\n    }\n\n    // For other URLs or if signed URL fails, return as-is\n    return videoUrl;\n  };\n\n\n\n\n\n  // Get appropriate thumbnail URL for video\n  const getThumbnailUrl = (material) => {\n    // If we have a custom thumbnail, use it\n    if (material.thumbnail && material.thumbnail !== \"\" && material.thumbnail !== \"processing\") {\n      return material.thumbnail;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n      // Extract YouTube video ID if it's a full URL\n      let videoId = material.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n\n    // For uploaded videos without thumbnails, use a default placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n  };\n\n  // Keyboard support for video modal\n  useEffect(() => {\n    const handleKeyPress = (event) => {\n      if (showVideoIndices.length > 0) {\n        switch (event.key) {\n          case 'Escape':\n            handleHideVideo();\n            break;\n          case 'f':\n          case 'F':\n            if (!isVideoExpanded) {\n              handleExpandVideo();\n            } else {\n              handleCollapseVideo();\n            }\n            break;\n          default:\n            break;\n        }\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => {\n      document.removeEventListener('keydown', handleKeyPress);\n    };\n  }, [showVideoIndices, isVideoExpanded]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\n      {/* Modern Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-r from-primary-600 to-blue-600 text-white\"\n      >\n        <div className=\"container-modern py-12\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n                <TbBooks className=\"w-8 h-8 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-4xl font-bold mb-2\">Study Materials</h1>\n                <p className=\"text-xl text-blue-100\">\n                  Access comprehensive learning resources for {userLevel} education\n                </p>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3\">\n                <div className=\"text-sm text-blue-100 mb-1\">Current Level</div>\n                <div className=\"text-lg font-bold\">{userLevel?.toUpperCase()}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      <div className=\"container-modern py-8\">\n        {/* Study Material Tabs */}\n        <div className=\"mb-6\">\n          <div className=\"study-tabs\">\n            {[\n              { key: 'videos', label: 'Videos', icon: TbVideo },\n              { key: 'study-notes', label: 'Notes', icon: TbFileText },\n              { key: 'past-papers', label: 'Past Papers', icon: TbCertificate },\n              { key: 'books', label: 'Books', icon: TbBookIcon }\n            ].map((tab) => (\n              <button\n                key={tab.key}\n                className={`study-tab ${activeTab === tab.key ? 'active' : ''}`}\n                onClick={() => handleTabChange(tab.key)}\n              >\n                <tab.icon />\n                <span>{tab.label}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Modern Filters Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <div className=\"card p-6\">\n            <div className=\"flex flex-col lg:flex-row gap-6 items-end\">\n              {/* Search */}\n              <div className=\"flex-1\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Search Materials\n                </label>\n                <input\n                  placeholder={`Search ${activeTab.replace('-', ' ')}...`}\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"form-input\"\n                />\n              </div>\n\n              {/* Class Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Class\n                  {userCurrentClass && (\n                    <span className=\"ml-2 text-xs text-primary-600 font-medium\">\n                      (Your class: {userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`})\n                    </span>\n                  )}\n                </label>\n                <div className=\"relative\">\n                  <button\n                    onClick={toggleClassSelector}\n                    className=\"w-full input-modern flex items-center justify-between\"\n                  >\n                    <span className=\"flex items-center space-x-2\">\n                      <TbSchool className=\"w-4 h-4 text-gray-400\" />\n                      <span>\n                        {selectedClass === 'all' ? 'All Classes' :\n                          userLevelLower === 'primary'\n                            ? `Class ${selectedClass}`\n                            : `Form ${selectedClass}`\n                        }\n                      </span>\n                      {selectedClass === userCurrentClass && (\n                        <span className=\"badge-primary text-xs\">Current</span>\n                      )}\n                    </span>\n                    <TbChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  <AnimatePresence>\n                    {showClassSelector && (\n                      <motion.div\n                        initial={{ opacity: 0, y: -10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        exit={{ opacity: 0, y: -10 }}\n                        className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\"\n                      >\n                        <button\n                          className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${\n                            selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                          }`}\n                          onClick={() => handleClassChange('all')}\n                        >\n                          All Classes\n                        </button>\n                        {availableClasses.map((className, index) => (\n                          <button\n                            key={index}\n                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${\n                              selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                            }`}\n                            onClick={() => handleClassChange(className)}\n                          >\n                            <span>\n                              {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}\n                            </span>\n                            {className === userCurrentClass && (\n                              <span className=\"badge-success text-xs\">Your Class</span>\n                            )}\n                          </button>\n                        ))}\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              </div>\n\n              {/* Subject Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Subject\n                </label>\n                <select\n                  value={selectedSubject}\n                  onChange={(e) => handleSubjectChange(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"all\">All Subjects</option>\n                  {subjectsList.map((subject, index) => (\n                    <option key={index} value={subject}>\n                      {subject}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Sort */}\n              <div className=\"w-full lg:w-48\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Sort by\n                </label>\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                  <option value=\"title\">By Title</option>\n                </select>\n              </div>\n\n              {/* Clear Filters */}\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => {\n                  setSearchTerm(\"\");\n                  setSelectedClass(\"all\");\n                  setSelectedSubject(\"all\");\n                  setSortBy(\"newest\");\n                }}\n              >\n                Clear Filters\n              </button>\n            </div>\n\n            {/* Results Count */}\n            {(searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && (\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                <span className=\"text-sm text-gray-600\">\n                  Showing {filteredAndSortedMaterials.length} of {materials.length} {activeTab.replace('-', ' ')}\n                </span>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n      {/* Materials Display */}\n      <div className=\"materials-section\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading materials...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <FaTimes className=\"error-icon\" />\n            <h3>Error Loading Materials</h3>\n            <p>{error}</p>\n            <button\n              className=\"retry-btn\"\n              onClick={() => {\n                setError(null);\n                fetchMaterials();\n              }}\n            >\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedMaterials.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {filteredAndSortedMaterials.map((material, index) => (\n              <div key={index} className=\"study-card\">\n                <div className=\"study-card-header\">\n                  <div className=\"study-card-meta\">\n                    {activeTab === 'videos' && <FaVideo />}\n                    {activeTab === 'study-notes' && <FaFileAlt />}\n                    {activeTab === 'past-papers' && <FaFileAlt />}\n                    {activeTab === 'books' && <FaBook />}\n                    <span>\n                      {activeTab === 'study-notes' ? 'Note' :\n                       activeTab === 'past-papers' ? 'Past Paper' :\n                       activeTab === 'videos' ? 'Video' : 'Book'}\n                    </span>\n                  </div>\n\n                  <div className=\"study-card-title\">\n                    {material.title}\n                  </div>\n\n                  {/* Class tags for videos */}\n                  {activeTab === 'videos' && material.coreClass && (\n                    <div className=\"d-flex gap-2 mt-2\">\n                      {material.isCore ? (\n                        <span className=\"badge badge-primary\">\n                          Core Class {userLevelLower === 'primary' ? material.coreClass : `Form ${material.coreClass}`}\n                        </span>\n                      ) : material.sharedFromClass && (\n                        <span className=\"badge badge-secondary\">\n                          Shared from {userLevelLower === 'primary' ? `Class ${material.sharedFromClass}` : `Form ${material.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  )}\n                  {material.year && (\n                    <span className=\"badge badge-secondary mt-2\">{material.year}</span>\n                  )}\n                </div>\n\n                {/* Video Thumbnail for videos */}\n                {activeTab === 'videos' && (material.videoUrl || material.videoID) && (\n                  <div className=\"video-thumbnail-container\" onClick={() => handleShowVideo(index)}>\n                    <img\n                      src={getThumbnailUrl(material)}\n                      alt={material.title}\n                      className=\"video-thumbnail\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = material.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          if (!e.target.src.includes('youtube.com')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n                          } else if (e.target.src.includes('maxresdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;\n                          } else if (e.target.src.includes('mqdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n                          } else {\n                            // Final fallback to default placeholder\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                          }\n                        } else {\n                          // For uploaded videos without thumbnails, use default placeholder\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                      <span className=\"play-text\">Watch Now</span>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"card-content\">\n                  <h3 className=\"material-title\">{material.title}</h3>\n                  <div className=\"material-meta\">\n                    <span className=\"material-subject\">{material.subject}</span>\n                    {material.className && (\n                      <span className=\"material-class\">\n                        {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"card-actions\">\n                  {activeTab === 'videos' && (material.videoUrl || material.videoID) ? (\n                    <div className=\"video-info-text\">\n                      <span className=\"video-duration\">Click thumbnail to play</span>\n                    </div>\n                  ) : material.documentUrl ? (\n                    <>\n                      <button\n                        className=\"action-btn secondary\"\n                        onClick={() => handleDocumentPreview(material.documentUrl)}\n                      >\n                        <FaEye /> View\n                      </button>\n                      <button\n                        className=\"action-btn primary\"\n                        onClick={() => handleDocumentDownload(material.documentUrl)}\n                      >\n                        <FaDownload /> Download\n                      </button>\n                    </>\n                  ) : (\n                    <span className=\"unavailable\">Not available</span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Materials Found</h3>\n            <p>No study materials are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedMaterials[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <>\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3>{video.title || 'Untitled Video'}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject || 'Unknown Subject'}</span>\n                        <span className=\"video-class\">Class {video.className || 'N/A'}</span>\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      {!isVideoExpanded ? (\n                        <button\n                          className=\"control-btn expand-btn\"\n                          onClick={handleExpandVideo}\n                          title=\"Expand to fullscreen\"\n                        >\n                          <FaExpand />\n                        </button>\n                      ) : (\n                        <button\n                          className=\"control-btn collapse-btn\"\n                          onClick={handleCollapseVideo}\n                          title=\"Exit fullscreen\"\n                        >\n                          <FaCompress />\n                        </button>\n                      )}\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadedMetadata={() => {\n                              // Auto-enable first subtitle if available and none selected\n                              if (video.subtitles && video.subtitles.length > 0 && selectedSubtitle === 'off') {\n                                const defaultSubtitle = video.subtitles.find(sub => sub.isDefault) || video.subtitles[0];\n                                handleSubtitleChange(defaultSubtitle.language);\n                              }\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            <p style={{color: 'white', textAlign: 'center', padding: '20px'}}>\n                              Your browser does not support the video tag.\n                              <br />\n                              <a href={video.signedVideoUrl || video.videoUrl} target=\"_blank\" rel=\"noopener noreferrer\" style={{color: '#4fc3f7'}}>\n                                Click here to open video in new tab\n                              </a>\n                            </p>\n                          </video>\n\n                          {/* Custom Subtitle Controls */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div style={{\n                              padding: '12px 15px',\n                              background: 'rgba(0,0,0,0.8)',\n                              borderRadius: '0 0 8px 8px',\n                              borderTop: '1px solid #333'\n                            }}>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '12px',\n                                flexWrap: 'wrap'\n                              }}>\n                                <span style={{\n                                  color: '#fff',\n                                  fontSize: '14px',\n                                  fontWeight: '500',\n                                  minWidth: 'fit-content'\n                                }}>\n                                  📝 Choose Language:\n                                </span>\n\n                                <div style={{\n                                  display: 'flex',\n                                  gap: '8px',\n                                  flexWrap: 'wrap',\n                                  flex: 1\n                                }}>\n                                  {/* Off Button */}\n                                  <button\n                                    onClick={() => handleSubtitleChange('off')}\n                                    style={{\n                                      padding: '6px 12px',\n                                      borderRadius: '20px',\n                                      border: 'none',\n                                      fontSize: '12px',\n                                      fontWeight: '500',\n                                      cursor: 'pointer',\n                                      transition: 'all 0.2s ease',\n                                      backgroundColor: selectedSubtitle === 'off' ? '#ff4757' : '#2f3542',\n                                      color: '#fff'\n                                    }}\n                                  >\n                                    OFF\n                                  </button>\n\n                                  {/* Language Buttons */}\n                                  {video.subtitles.map((subtitle) => (\n                                    <button\n                                      key={subtitle.language}\n                                      onClick={() => handleSubtitleChange(subtitle.language)}\n                                      style={{\n                                        padding: '6px 12px',\n                                        borderRadius: '20px',\n                                        border: 'none',\n                                        fontSize: '12px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s ease',\n                                        backgroundColor: selectedSubtitle === subtitle.language ? '#2ed573' : '#57606f',\n                                        color: '#fff',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '4px'\n                                      }}\n                                    >\n                                      <span>{subtitle.languageName}</span>\n                                      {subtitle.isAutoGenerated && (\n                                        <span style={{\n                                          fontSize: '10px',\n                                          opacity: 0.8,\n                                          backgroundColor: 'rgba(255,255,255,0.2)',\n                                          padding: '1px 4px',\n                                          borderRadius: '8px'\n                                        }}>\n                                          AI\n                                        </span>\n                                      )}\n                                    </button>\n                                  ))}\n                                </div>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  {!isVideoExpanded && (\n                    <div className=\"video-footer\">\n                      <div className=\"video-description\">\n                        <p>Watch this educational video to learn more about {video.subject}.</p>\n                        {video.subtitleGenerationStatus === 'processing' && (\n                          <div className=\"subtitle-status\" style={{\n                            marginTop: '10px',\n                            fontSize: '0.9em',\n                            color: '#2196F3',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px'\n                          }}>\n                            <div style={{\n                              width: '16px',\n                              height: '16px',\n                              border: '2px solid #2196F3',\n                              borderTop: '2px solid transparent',\n                              borderRadius: '50%',\n                              animation: 'spin 1s linear infinite'\n                            }}></div>\n                            🤖 Generating subtitles...\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n\n      {/* PDF Modal */}\n      <PDFModal\n        modalIsOpen={modalIsOpen}\n        closeModal={() => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        }}\n        documentUrl={documentUrl}\n      />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterial;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js", ["637", "638", "639", "640", "641", "642", "643", "644", "645", "646"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js", ["647", "648", "649", "650", "651", "652"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js", ["653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js", ["670", "671"], [], "import React, { useEffect, useState, Suspense } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\n\r\nimport { message } from \"antd\";\r\nconst Test = () => {\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n\r\n\r\n    useEffect(() => {\r\n        const getUserData = async () => {\r\n            try {\r\n                const response = await getUserInfo();\r\n                if (response.success) {\r\n                    if (response.data.isAdmin) {\r\n                        setIsAdmin(true);\r\n                    } else {\r\n                        setIsAdmin(false);\r\n                        setUserData(response.data);\r\n                    }\r\n                } else {\r\n                    message.error(response.message);\r\n                }\r\n            } catch (error) {\r\n                message.error(error.message);\r\n            }\r\n        };\r\n        if (localStorage.getItem(\"token\")) {\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        // <Suspense fallback={<div>Loading...</div>}>\r\n        <div className=\"\">\r\n            <div>{userData.name}</div>\r\n            <div>{userData.school}</div>\r\n            <div>{userData.class}</div>\r\n        </div>\r\n        // </Suspense>\r\n    );\r\n}\r\n\r\nexport default Test;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js", ["672"], [], "import React, { useEffect, useState } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Rate } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addReview, getAllReviews } from \"../../../apicalls/reviews\";\r\nimport image from '../../../assets/person.png';\r\n\r\nconst AboutUs = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [userRating, setUserRating] = useState('');\r\n    const [userText, setUserText] = useState('');\r\n    const [reviews, setReviews] = useState('');\r\n    const [userOldReview, setUserOldReview] = useState(null);\r\n    const dispatch = useDispatch();\r\n\r\n    const getReviews = async () => {\r\n        try {\r\n            const response = await getAllReviews();\r\n            if (response.success) {\r\n                setReviews(response.data.reverse());\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await getReviews();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const handleRatingChange = (value) => {\r\n        setUserRating(value);\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        if (userRating === '' || userRating === 0 || userText === '') {\r\n            return;\r\n        }\r\n        try {\r\n            const data = {\r\n                rating: userRating,\r\n                text: userText\r\n            }\r\n            const response = await addReview(data);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                getReviews();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n            dispatch(HideLoading());\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (reviews) {\r\n            const userInReview = reviews.find(review => review.user._id === userData._id);\r\n            setUserOldReview(userInReview);\r\n        }\r\n    }, [reviews, userData]);\r\n\r\n    return (\r\n        <div className=\"AboutUs\">\r\n            {!isAdmin &&\r\n                <>\r\n                    <PageTitle title=\"About Us\" />\r\n                    <div className=\"divider\"></div>\r\n                    <p className=\"info-para\">\r\n                        Welcome to our web application! Lorem ipsum dolor sit amet, consectetur adipiscing elit.\r\n                        Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,\r\n                        quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\n                        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                    </p>\r\n                    {!userOldReview ?\r\n                        <>\r\n                            <h1>Feedback</h1>\r\n                            <p>\r\n                                We strive to provide an exceptional user experience and value your feedback.<br />\r\n                                Please take a moment to rate our web app:\r\n                            </p>\r\n                            <div><b>Rate Your Experience:</b></div>\r\n                            <div className=\"rating\">\r\n                                <div>\r\n                                    <Rate defaultValue={0} onChange={handleRatingChange} />\r\n                                    <br />\r\n                                    <textarea\r\n                                        className=\"rating-text\"\r\n                                        placeholder=\"Share your thoughts...\"\r\n                                        rows={4}\r\n                                        value={userText}\r\n                                        onChange={(e) => setUserText(e.target.value)}\r\n                                    />\r\n                                </div>\r\n                                <button onClick={handleSubmit}>Submit</button>\r\n                            </div>\r\n                        </>\r\n                        :\r\n                        <>\r\n                            <h2>Your Feedback</h2>\r\n                            <div className=\"p-rating-div\">\r\n                                <div className=\"profile-row\">\r\n                                    <img className=\"profile\" src={userOldReview.user.profileImage ? userOldReview.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                    <p>{userOldReview.user.name}</p>\r\n                                </div>\r\n                                <Rate defaultValue={userOldReview.rating} className=\"rate\" disabled={true} />\r\n                                <br />\r\n                                <div className=\"text\">{userOldReview.text}</div>\r\n                            </div>\r\n                        </>\r\n                    }\r\n                    <h2>Previous Reviews</h2>\r\n                    {reviews ?\r\n                        <div className=\"p-ratings\">\r\n                            {reviews.map((review, index) => (\r\n                                <div key={index}>\r\n                                    {userOldReview?.user._id !== review.user?._id && review.user?._id &&\r\n                                        <div className=\"p-rating-div\">\r\n                                            <div className=\"profile-row\">\r\n                                                <img className=\"profile\" src={review.user.profileImage ? review.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                                <p>{review.user.name}</p>\r\n                                            </div>\r\n                                            <Rate defaultValue={review.rating} className=\"rate\" disabled={true} />\r\n                                            <br />\r\n                                            <div className=\"text\">{review.text}</div>\r\n                                        </div>\r\n                                    }\r\n                                </div>\r\n                            ))\r\n                            }\r\n                        </div>\r\n                        :\r\n                        <div>\r\n                            No reviews yet.    \r\n                        </div>\r\n                    }\r\n                </>\r\n            }\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default AboutUs;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js", ["673", "674", "675"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js", ["676", "677", "678", "679"], [], "import { message } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport \"./index.css\";\r\nimport {\r\n  getAllUsers,\r\n  blockUserById,\r\n  deleteUserById,\r\n} from \"../../../apicalls/users\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport AdminLayout from \"../../../components/AdminLayout\";\r\nimport AdminCard from \"../../../components/AdminCard\";\r\nimport {\r\n  TbUsers,\r\n  TbSearch,\r\n  TbFilter,\r\n  TbUserCheck,\r\n  TbUserX,\r\n  Tb<PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON>b<PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  TbX,\r\n  TbPlus,\r\n  TbDownload,\r\n  TbDashboard\r\n} from \"react-icons/tb\";\r\n\r\nfunction Users() {\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = useState([]);\r\n  const [filteredUsers, setFilteredUsers] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"all\");\r\n  const [filterSubscription, setFilterSubscription] = useState(\"all\");\r\n  const [loading, setLoading] = useState(false);\r\n  const dispatch = useDispatch();\r\n\r\n  // Function to determine subscription status for filtering based on subscription dates\r\n  const getSubscriptionFilterStatus = (user) => {\r\n    const now = new Date();\r\n    const paymentRequired = user.paymentRequired;\r\n    const subscriptionEndDate = user.subscriptionEndDate;\r\n    const subscriptionStartDate = user.subscriptionStartDate;\r\n\r\n    // Debug logging (can be removed in production)\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`User ${user.name}:`, {\r\n        paymentRequired,\r\n        subscriptionStartDate,\r\n        subscriptionEndDate,\r\n        isExpired: subscriptionEndDate ? new Date(subscriptionEndDate) < now : 'no end date'\r\n      });\r\n    }\r\n\r\n    // NO-PLAN: Users who never required payment or never had subscription\r\n    if (!paymentRequired) {\r\n      return 'no-plan';\r\n    }\r\n\r\n    // Users with paymentRequired = true (have or had a subscription)\r\n    if (paymentRequired) {\r\n      // Check if subscription has expired by date\r\n      if (subscriptionEndDate) {\r\n        const endDate = new Date(subscriptionEndDate);\r\n\r\n        if (endDate < now) {\r\n          // Subscription end date has passed - EXPIRED PLAN\r\n          return 'expired-plan';\r\n        } else {\r\n          // Subscription is still valid by date - ON PLAN\r\n          return 'on-plan';\r\n        }\r\n      } else {\r\n        // Has paymentRequired = true but no end date specified\r\n        // This could be a lifetime subscription or missing data\r\n        // Assume they are on plan if they have paymentRequired = true\r\n        return 'on-plan';\r\n      }\r\n    }\r\n\r\n    // Default fallback for edge cases\r\n    return 'no-plan';\r\n  };\r\n\r\n  const getUsersData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllUsers();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setUsers(response.users);\r\n        console.log(\"users loaded:\", response.users.length);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const blockUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await blockUserById({\r\n        studentId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteUserById({ studentId });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(\"User deleted successfully\");\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n\r\n  // Filter users based on search, status, and subscription\r\n  useEffect(() => {\r\n    let filtered = users;\r\n\r\n    // Filter by search query\r\n    if (searchQuery) {\r\n      filtered = filtered.filter(user =>\r\n        user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.school?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.class?.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Filter by status\r\n    if (filterStatus !== \"all\") {\r\n      filtered = filtered.filter(user => {\r\n        if (filterStatus === \"blocked\") return user.isBlocked;\r\n        if (filterStatus === \"active\") return !user.isBlocked;\r\n        return true;\r\n      });\r\n    }\r\n\r\n    // Filter by subscription plan\r\n    if (filterSubscription !== \"all\") {\r\n      filtered = filtered.filter(user => {\r\n        const subscriptionStatus = getSubscriptionFilterStatus(user);\r\n        return subscriptionStatus === filterSubscription;\r\n      });\r\n    }\r\n\r\n    setFilteredUsers(filtered);\r\n  }, [users, searchQuery, filterStatus, filterSubscription]);\r\n\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n\r\n  const UserCard = ({ user }) => {\r\n    const subscriptionStatus = getSubscriptionFilterStatus(user);\r\n\r\n    const getSubscriptionBadge = () => {\r\n      switch (subscriptionStatus) {\r\n        case 'on-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-green-100 text-green-800 flex items-center space-x-1\">\r\n              <TbCrown className=\"w-3 h-3\" />\r\n              <span>On Plan</span>\r\n            </span>\r\n          );\r\n        case 'expired-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-orange-100 text-orange-800 flex items-center space-x-1\">\r\n              <TbClock className=\"w-3 h-3\" />\r\n              <span>Expired</span>\r\n            </span>\r\n          );\r\n        case 'no-plan':\r\n          return (\r\n            <span className=\"badge-modern bg-gray-100 text-gray-800 flex items-center space-x-1\">\r\n              <TbX className=\"w-3 h-3\" />\r\n              <span>No Plan</span>\r\n            </span>\r\n          );\r\n        default:\r\n          return null;\r\n      }\r\n    };\r\n\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return 'N/A';\r\n      return new Date(dateString).toLocaleDateString();\r\n    };\r\n\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        whileHover={{ y: -2 }}\r\n        transition={{ duration: 0.2 }}\r\n      >\r\n        <Card className=\"p-6 hover:shadow-large\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex items-start space-x-4\">\r\n              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${\r\n                user.isBlocked ? 'bg-error-100' : 'bg-primary-100'\r\n              }`}>\r\n                <TbUser className={`w-6 h-6 ${user.isBlocked ? 'text-error-600' : 'text-primary-600'}`} />\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"flex items-center space-x-2 mb-2\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">{user.name}</h3>\r\n                  <span className={`badge-modern ${\r\n                    user.isBlocked ? 'bg-error-100 text-error-800' : 'bg-success-100 text-success-800'\r\n                  }`}>\r\n                    {user.isBlocked ? 'Blocked' : 'Active'}\r\n                  </span>\r\n                  {getSubscriptionBadge()}\r\n                </div>\r\n\r\n                <div className=\"space-y-1 text-sm text-gray-600\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbMail className=\"w-4 h-4\" />\r\n                    <span>{user.email}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbSchool className=\"w-4 h-4\" />\r\n                    <span>{user.school || 'No school specified'}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <TbUsers className=\"w-4 h-4\" />\r\n                    <span>Class: {user.class || 'Not assigned'}</span>\r\n                  </div>\r\n\r\n                  {/* Subscription Details */}\r\n                  {user.subscriptionPlan && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbCrown className=\"w-4 h-4\" />\r\n                      <span>Plan: {user.subscriptionPlan}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Subscription Period */}\r\n                  {user.subscriptionStartDate && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span>Started: {formatDate(user.subscriptionStartDate)}</span>\r\n                    </div>\r\n                  )}\r\n                  {user.subscriptionEndDate && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span className={new Date(user.subscriptionEndDate) < new Date() ? 'text-red-600 font-medium' : 'text-green-600'}>\r\n                        {new Date(user.subscriptionEndDate) < new Date() ? 'Expired: ' : 'Expires: '}\r\n                        {formatDate(user.subscriptionEndDate)}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Payment Status */}\r\n                  {user.paymentRequired !== undefined && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbCrown className=\"w-4 h-4\" />\r\n                      <span className={user.paymentRequired ? 'text-blue-600' : 'text-gray-600'}>\r\n                        {user.paymentRequired ? 'Paid Subscription' : 'Free Account'}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Activity Information */}\r\n                  {user.totalQuizzesTaken > 0 && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbUser className=\"w-4 h-4\" />\r\n                      <span>Quizzes: {user.totalQuizzesTaken}</span>\r\n                    </div>\r\n                  )}\r\n                  {user.lastActivity && (\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <TbClock className=\"w-4 h-4\" />\r\n                      <span>Last Active: {formatDate(user.lastActivity)}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Button\r\n                variant={user.isBlocked ? \"success\" : \"warning\"}\r\n                size=\"sm\"\r\n                onClick={() => blockUser(user.studentId)}\r\n                icon={user.isBlocked ? <TbUserCheck /> : <TbUserX />}\r\n              >\r\n                {user.isBlocked ? \"Unblock\" : \"Block\"}\r\n              </Button>\r\n\r\n              <Button\r\n                variant=\"error\"\r\n                size=\"sm\"\r\n                onClick={() => {\r\n                  if (window.confirm(\"Are you sure you want to delete this user?\")) {\r\n                    deleteUser(user.studentId);\r\n                  }\r\n                }}\r\n                icon={<TbTrash />}\r\n              >\r\n                Delete\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Card>\r\n      </motion.div>\r\n    );\r\n  };\r\n\r\n  const actionButtons = [\r\n    <motion.button\r\n      key=\"reports\"\r\n      whileHover={{ scale: 1.02 }}\r\n      whileTap={{ scale: 0.98 }}\r\n      onClick={() => navigate('/admin/reports')}\r\n      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2\"\r\n    >\r\n      <TbEye className=\"w-4 h-4\" />\r\n      <span className=\"hidden sm:inline\">View Reports</span>\r\n    </motion.button>,\r\n    <motion.button\r\n      key=\"export\"\r\n      whileHover={{ scale: 1.02 }}\r\n      whileTap={{ scale: 0.98 }}\r\n      onClick={() => {/* Add export functionality */}}\r\n      className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2\"\r\n    >\r\n      <TbDownload className=\"w-4 h-4\" />\r\n      <span className=\"hidden sm:inline\">Export</span>\r\n    </motion.button>\r\n  ];\r\n\r\n  return (\r\n    <AdminLayout showHeader={false}>\r\n      {/* Page Header */}\r\n      <div className=\"mb-6 sm:mb-8\">\r\n        <div className=\"bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-6 sm:p-8 text-white\">\r\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n            <div className=\"flex items-center gap-4\">\r\n              {/* Dashboard Shortcut */}\r\n              <motion.button\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n                onClick={() => navigate('/admin/dashboard')}\r\n                className=\"flex items-center gap-2 px-3 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200 text-white border border-white/30\"\r\n              >\r\n                <TbDashboard className=\"w-4 h-4\" />\r\n                <span className=\"hidden sm:inline text-sm font-medium\">Dashboard</span>\r\n              </motion.button>\r\n\r\n              <div>\r\n                <h1 className=\"text-2xl sm:text-3xl font-bold mb-2\">\r\n                  User Management\r\n                </h1>\r\n                <p className=\"text-green-100 text-sm sm:text-base\">\r\n                  Manage student accounts, permissions, and access controls\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {actionButtons}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8\">\r\n        <AdminCard className=\"bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-blue-100 text-sm font-medium mb-1\">Total Users</p>\r\n              <p className=\"text-2xl sm:text-3xl font-bold\">{users.length}</p>\r\n              <p className=\"text-blue-200 text-xs mt-1\">All registered</p>\r\n            </div>\r\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\r\n              <TbUsers className=\"w-6 h-6\" />\r\n            </div>\r\n          </div>\r\n        </AdminCard>\r\n\r\n        <AdminCard className=\"bg-gradient-to-br from-green-500 to-green-600 text-white border-0\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-green-100 text-sm font-medium mb-1\">Active Users</p>\r\n              <p className=\"text-2xl sm:text-3xl font-bold\">{users.filter(u => !u.isBlocked).length}</p>\r\n              <p className=\"text-green-200 text-xs mt-1\">Not blocked</p>\r\n            </div>\r\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\r\n              <TbUserCheck className=\"w-6 h-6\" />\r\n            </div>\r\n          </div>\r\n        </AdminCard>\r\n\r\n        <AdminCard className=\"bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-orange-100 text-sm font-medium mb-1\">Expired Plans</p>\r\n              <p className=\"text-2xl sm:text-3xl font-bold\">{users.filter(u => getSubscriptionFilterStatus(u) === 'expired-plan').length}</p>\r\n              <p className=\"text-orange-200 text-xs mt-1\">Need renewal</p>\r\n            </div>\r\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\r\n              <TbClock className=\"w-6 h-6\" />\r\n            </div>\r\n          </div>\r\n        </AdminCard>\r\n\r\n        <AdminCard className=\"bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-purple-100 text-sm font-medium mb-1\">No Plan</p>\r\n              <p className=\"text-2xl sm:text-3xl font-bold\">{users.filter(u => getSubscriptionFilterStatus(u) === 'no-plan').length}</p>\r\n              <p className=\"text-purple-200 text-xs mt-1\">Free users</p>\r\n            </div>\r\n            <div className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center\">\r\n              <TbX className=\"w-6 h-6\" />\r\n            </div>\r\n          </div>\r\n        </AdminCard>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <AdminCard\r\n        title=\"Search & Filter\"\r\n        subtitle=\"Find and filter users by various criteria\"\r\n        className=\"mb-6 sm:mb-8\"\r\n      >\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n          <div className=\"lg:col-span-2\">\r\n            <label className=\"block text-sm font-medium text-slate-700 mb-2\">\r\n              Search Users\r\n            </label>\r\n            <Input\r\n              placeholder=\"Search by name, email, school, or class...\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              icon={<TbSearch />}\r\n            />\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-slate-700 mb-2\">\r\n              Filter by Status\r\n            </label>\r\n            <select\r\n              value={filterStatus}\r\n              onChange={(e) => setFilterStatus(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n            >\r\n              <option value=\"all\">All Users</option>\r\n              <option value=\"active\">Active Only</option>\r\n              <option value=\"blocked\">Blocked Only</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-slate-700 mb-2\">\r\n              Filter by Plan\r\n            </label>\r\n            <select\r\n              value={filterSubscription}\r\n              onChange={(e) => setFilterSubscription(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n            >\r\n              <option value=\"all\">All Plans</option>\r\n              <option value=\"on-plan\">On Plan</option>\r\n              <option value=\"expired-plan\">Expired Plan</option>\r\n              <option value=\"no-plan\">No Plan</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-4\">\r\n          <div>\r\n            {(searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\") && (\r\n              <span className=\"text-sm text-slate-600\">\r\n                Showing {filteredUsers.length} of {users.length} users\r\n                {filterSubscription !== \"all\" && (\r\n                  <span className=\"ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs\">\r\n                    {filterSubscription === 'on-plan' && 'On Plan'}\r\n                    {filterSubscription === 'expired-plan' && 'Expired Plan'}\r\n                    {filterSubscription === 'no-plan' && 'No Plan'}\r\n                  </span>\r\n                )}\r\n              </span>\r\n            )}\r\n          </div>\r\n\r\n          <motion.button\r\n            whileHover={{ scale: 1.02 }}\r\n            whileTap={{ scale: 0.98 }}\r\n            onClick={() => {\r\n              setSearchQuery(\"\");\r\n              setFilterStatus(\"all\");\r\n              setFilterSubscription(\"all\");\r\n            }}\r\n            className=\"px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors duration-200 flex items-center gap-2\"\r\n          >\r\n            <TbFilter className=\"w-4 h-4\" />\r\n            Clear Filters\r\n          </motion.button>\r\n        </div>\r\n      </AdminCard>\r\n\r\n      {/* Users Grid */}\r\n      <AdminCard\r\n        title={`Users (${filteredUsers.length})`}\r\n        subtitle=\"Manage individual user accounts and permissions\"\r\n        loading={loading}\r\n      >\r\n        {loading ? (\r\n          <div className=\"flex justify-center py-12\">\r\n            <Loading text=\"Loading users...\" />\r\n          </div>\r\n        ) : filteredUsers.length > 0 ? (\r\n          <div className=\"space-y-4\">\r\n            {filteredUsers.map((user, index) => (\r\n              <motion.div\r\n                key={user.studentId}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: index * 0.05 }}\r\n              >\r\n                <UserCard user={user} />\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-12\">\r\n            <TbUsers className=\"w-16 h-16 text-slate-400 mx-auto mb-4\" />\r\n            <h3 className=\"text-xl font-semibold text-slate-900 mb-2\">No Users Found</h3>\r\n            <p className=\"text-slate-600\">\r\n              {searchQuery || filterStatus !== \"all\" || filterSubscription !== \"all\"\r\n                ? \"Try adjusting your search or filter criteria\"\r\n                : \"No users have been registered yet\"}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </AdminCard>\r\n    </AdminLayout>\r\n  );\r\n}\r\n\r\nexport default Users;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js", ["680", "681", "682", "683", "684", "685", "686", "687", "688"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js", ["689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706"], [], "import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n  sendOTP,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal, Input, Button } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\n\r\nconst Profile = () => {\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [userRankingStats, setUserRankingStats] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\",\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  // Fetch user ranking data from the ranking system\r\n  const fetchUserRankingData = async () => {\r\n    if (!userDetails?._id) return;\r\n\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Get user's ranking position and nearby users\r\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\r\n\r\n      if (rankingResponse.success) {\r\n        setUserRankingStats(rankingResponse.data);\r\n      }\r\n\r\n      // Also get the full leaderboard to find user's position\r\n      const leaderboardResponse = await getXPLeaderboard({\r\n        limit: 1000,\r\n        levelFilter: userDetails?.level || 'all'\r\n      });\r\n\r\n      if (leaderboardResponse.success) {\r\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\r\n        if (userIndex >= 0) {\r\n          const userWithRank = {\r\n            ...leaderboardResponse.data[userIndex],\r\n            rank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length\r\n          };\r\n          setUserRankingStats(prev => ({\r\n            ...prev,\r\n            userRank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length,\r\n            user: userWithRank\r\n          }));\r\n        }\r\n      }\r\n\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Error fetching ranking data:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData && userDetails) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData, userDetails]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          name: response.data.name || \"\",\r\n          email: response.data.email || \"\",\r\n          school: response.data.school || \"\",\r\n          class_: response.data.class || \"\",\r\n          level: response.data.level || \"\",\r\n          phoneNumber: response.data.phoneNumber || \"\",\r\n        });\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        fetchReports();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === \"phoneNumber\" && value.length > 10) return;\r\n    if (name === \"level\" && value !== userDetails?.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return;\r\n    }\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n      phoneNumber: userDetails.phoneNumber,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n  const sendOTPRequest = async (email) => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await sendOTP({ email });\r\n      if (response.success) {\r\n        message.success(\"Please verify new email!\");\r\n        setEdit(false);\r\n        setServerGeneratedOTP(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n        discardChanges();\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      discardChanges();\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleUpdate = async ({ skipOTP } = {}) => {\r\n    if (!formData.name) return message.error(\"Please enter your name.\");\r\n    if (!formData.class_) return message.error(\"Please select a class.\");\r\n\r\n    if (\r\n      !skipOTP &&\r\n      formData.email !== userDetails.email\r\n    ) {\r\n      return sendOTPRequest(formData.email);\r\n    }\r\n\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserInfo({\r\n        ...formData,\r\n        userId: userDetails._id,\r\n      });\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEdit(false);\r\n        setServerGeneratedOTP(null);\r\n        getUserData();\r\n        if (response.levelChanged) {\r\n          setTimeout(() => window.location.reload(), 2000);\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      level: pendingLevelChange,\r\n      class_: \"\",\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith('image/')) {\r\n        message.error('Please select a valid image file');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        message.error('Image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      setProfileImage(file);\r\n\r\n      // Show preview\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => setImagePreview(reader.result);\r\n      reader.readAsDataURL(file);\r\n\r\n      // Auto-upload the image\r\n      const data = new FormData();\r\n      data.append(\"profileImage\", file);\r\n      dispatch(ShowLoading());\r\n\r\n      try {\r\n        const response = await updateUserPhoto(data);\r\n        dispatch(HideLoading());\r\n        if (response.success) {\r\n          message.success(\"Profile picture updated successfully!\");\r\n          getUserData(); // Refresh user data to show new image\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message || \"Failed to update profile picture\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const data = new FormData();\r\n    data.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(data);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === serverGeneratedOTP) {\r\n      handleUpdate({ skipOTP: true });\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  // Load user data on component mount\r\n  useEffect(() => {\r\n    getUserData();\r\n  }, []);\r\n\r\n  // Load ranking data when user details are available\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      fetchUserRankingData();\r\n    }\r\n  }, [userDetails]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Header */}\r\n          <div className=\"text-center mb-8\">\r\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Profile</h1>\r\n            <p className=\"text-gray-600\">Manage your account settings and preferences</p>\r\n\r\n            {/* Profile Picture with Online Status - Centered Below Header */}\r\n            <div className=\"relative mt-8 flex justify-center\">\r\n              <div className=\"relative\">\r\n                <ProfilePicture\r\n                  user={userDetails}\r\n                  size=\"3xl\"\r\n                  showOnlineStatus={true}\r\n                  onClick={() => document.getElementById('profileImageInput').click()}\r\n                  className=\"hover:scale-105 transition-transform duration-200\"\r\n                  style={{\r\n                    width: '120px',\r\n                    height: '120px',\r\n                    border: '4px solid #BFDBFE',\r\n                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\r\n                  }}\r\n                />\r\n\r\n                {/* Camera Icon Overlay */}\r\n                <div className=\"absolute bottom-2 right-2 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\"\r\n                     onClick={() => document.getElementById('profileImageInput').click()}>\r\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" />\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 13a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                  </svg>\r\n                </div>\r\n\r\n                {/* Hidden File Input */}\r\n                <input\r\n                  id=\"profileImageInput\"\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleImageChange}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Profile Content */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\r\n            <div className=\"p-8\">\r\n              <div className=\"flex flex-col items-center mb-8\">\r\n                {/* User Info - Horizontal Layout */}\r\n                <div className=\"flex flex-wrap justify-center gap-4 text-center mb-6\">\r\n                  <div className=\"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-blue-600 font-medium\">Name</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.name || 'User'}</p>\r\n                  </div>\r\n                  <div className=\"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-green-600 font-medium\">Email</p>\r\n                    <p className=\"text-lg font-bold text-gray-900 truncate max-w-[150px]\">{userDetails?.email || '<EMAIL>'}</p>\r\n                  </div>\r\n                  <div className=\"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-purple-600 font-medium\">Class</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.class || 'N/A'}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Ranking Stats - Horizontal Layout */}\r\n                {userRankingStats && (\r\n                  <div className=\"flex flex-wrap justify-center gap-4 text-center\">\r\n                    <div className=\"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-yellow-600 font-medium\">Rank</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        #{userRankingStats.userRank || 'N/A'}\r\n                        {userRankingStats.totalUsers && (\r\n                          <span className=\"text-sm text-gray-500\">/{userRankingStats.totalUsers}</span>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-orange-600 font-medium\">Total XP</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalXP?.toLocaleString() || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-indigo-600 font-medium\">Avg Score</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.averageScore || '0'}%\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-pink-600 font-medium\">Quizzes</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalQuizzesTaken || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-teal-600 font-medium\">Streak</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.currentStreak || '0'}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Profile Details */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.name || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.email || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.school || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.level || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.class || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                    <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                      {userDetails?.phoneNumber || 'Not provided'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"mt-8 flex justify-center\">\r\n                <button className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\">\r\n                  Edit Profile\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js", ["707", "708", "709"], [], "import React from 'react';\r\nimport { InlineMath, BlockMath } from 'react-katex';\r\nimport 'katex/dist/katex.min.css';\r\n\r\nconst ContentRenderer = ({ text }) => {\r\n    // Handle undefined, null, or empty text\r\n    if (!text || typeof text !== 'string') {\r\n        return <div></div>;\r\n    }\r\n\r\n    const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\r\n    const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\r\n    // const boldTextRegex = /(?:\\*\\*.*?\\*\\*)/g;\r\n    const boldTextRegex = /\\*\\*.*?\\*\\*/g;\r\n    // console.log('Text: ', text);\r\n    let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\r\n    const lines = modifiedText.split('\\n');\r\n    // console.log('Lines with symbol: ', lines);\r\n    const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n    // console.log('Lines: ', restoredLines);\r\n\r\n\r\n\r\n\r\n    const inlineMathSymbol = \"~~INLINEMATH~~\";\r\n    const blockMathSymbol = \"~~BLOCKMATH~~\";\r\n    const boldSymbol = \"~~BOLD~~\";\r\n\r\n    let newModifiedText = text.replace(blockMathRegex, match => {\r\n        return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\r\n        return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(boldTextRegex, match => {\r\n        // console.log('Bold Part: ', match);\r\n        return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\r\n    });\r\n\r\n    const newLines = newModifiedText.split('\\n');\r\n\r\n    const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n\r\n    // console.log('New Modified Text: ', newModifiedText);\r\n\r\n    const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\r\n\r\n    // Debug logging removed to prevent React rendering issues\r\n\r\n    return (\r\n        <div>\r\n            {newRestoredLines.map((line, lineIndex) => (\r\n                <div key={lineIndex}>\r\n                    {line.trim() === '' ?\r\n                        <br key={`br-${lineIndex}`} />\r\n                        :\r\n                        line.split(newRegex).map((part, index) => {\r\n                            if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\r\n                                return (\r\n                                    <React.Fragment key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\r\n                                            if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\r\n                                                return (\r\n                                                    <InlineMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                                    </InlineMath>\r\n                                                );\r\n                                            } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\r\n                                                return (\r\n                                                    <BlockMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                                    </BlockMath>\r\n                                                );\r\n                                            } else {\r\n                                                return (\r\n                                                    <span key={`${lineIndex}-${index}-${n_index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                                        <strong>{nestedPart}</strong>\r\n                                                    </span>\r\n                                                );\r\n                                            }\r\n                                        })}\r\n                                    </React.Fragment>\r\n                                );\r\n                            } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\r\n                                return (\r\n                                    <InlineMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                    </InlineMath>\r\n                                );\r\n                            } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\r\n                                return (\r\n                                    <BlockMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                    </BlockMath>\r\n                                );\r\n                            } else {\r\n                                return (\r\n                                    <span key={`${lineIndex}-${index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                        {part}\r\n                                    </span>\r\n                                );\r\n                            }\r\n                        })}\r\n                </div>\r\n            ))}\r\n        </div>\r\n\r\n    )\r\n};\r\n\r\nexport default ContentRenderer;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js", ["710"], [], "import React from \"react\";\r\nimport { useNavigate } from \"react-router-dom\";\r\n\r\nfunction Instructions({ examData, setView, startTimer, questions = [] }) {\r\n  const navigate = useNavigate();\r\n\r\n  const formatTime = (seconds) => {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n      {/* Header */}\r\n      <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 shadow-lg\">\r\n        <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n          <div className=\"text-center\">\r\n            <h1 className=\"text-3xl font-bold text-white\">Instructions</h1>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-2xl mx-auto px-4 py-12\">\r\n        {/* Simplified Content Card */}\r\n        <div className=\"bg-white rounded-2xl shadow-xl border-0 p-8 mb-8\">\r\n          <div className=\"text-center mb-8\">\r\n            <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4\">\r\n              {examData.name}\r\n            </h1>\r\n            <p className=\"text-lg text-gray-600 font-medium\">Challenge your brain, Beat the rest</p>\r\n\r\n            {/* Debug Info */}\r\n            <div className=\"mt-4 p-3 bg-gray-100 rounded-lg text-sm text-gray-600\">\r\n              <p><strong>Questions Available:</strong> {questions.length}</p>\r\n              <p><strong>Exam ID:</strong> {examData._id}</p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Quick Stats */}\r\n          <div className=\"grid grid-cols-2 gap-6 mb-8\">\r\n            <div className=\"text-center p-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg\">\r\n              <div className=\"text-3xl font-bold text-white mb-2\">{Math.round(examData.duration / 60)} min</div>\r\n              <div className=\"text-sm font-semibold text-blue-100\">Duration</div>\r\n            </div>\r\n            <div className=\"text-center p-6 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg\">\r\n              <div className=\"text-3xl font-bold text-white mb-2\">{examData.totalMarks}</div>\r\n              <div className=\"text-sm font-semibold text-green-100\">Total Marks</div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex justify-center gap-4\">\r\n            <button\r\n              className=\"px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n              onClick={() => navigate('/user/quiz')}\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              className={`px-8 py-3 rounded-xl font-bold transform transition-all duration-300 shadow-lg ${\r\n                questions.length > 0\r\n                  ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 hover:scale-105'\r\n                  : 'bg-gray-400 text-gray-600 cursor-not-allowed'\r\n              }`}\r\n              onClick={() => {\r\n                if (questions.length > 0) {\r\n                  console.log(\"Starting quiz with\", questions.length, \"questions\");\r\n                  startTimer();\r\n                  setView(\"questions\");\r\n                } else {\r\n                  alert(\"Cannot start quiz: No questions available!\");\r\n                }\r\n              }}\r\n              disabled={questions.length === 0}\r\n            >\r\n              {questions.length > 0 ? 'Start Quiz' : 'No Questions Available'}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Instructions;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js", [], ["711"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx", ["712", "713"], [], "import React, { useEffect, useState } from \"react\";\r\nimport Mo<PERSON> from \"react-modal\";\r\nimport \"./WaitingModal.css\";\r\n\r\nModal.setAppElement(\"#root\"); // Ensure accessibility for screen readers\r\n\r\nconst WaitingModal = ({ isOpen, onClose }) => {\r\n    return (\r\n        <Modal\r\n            isOpen={isOpen}\r\n            onRequestClose={onClose}\r\n            className=\"waiting-modal-content\"\r\n            overlayClassName=\"waiting-modal-overlay\"\r\n        >\r\n            <div className=\"waiting-modal-header\">\r\n                <h2>Please confirm the payment</h2>\r\n            </div>\r\n            <div className=\"waiting-modal-timer\">\r\n                <svg\r\n                    fill=\"#253864\"\r\n                    version=\"1.1\"\r\n                    id=\"Layer_1\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    viewBox=\"0 0 512 512\"\r\n                    width=\"64px\"\r\n                    height=\"64px\"\r\n                    stroke=\"#253864\"\r\n                >\r\n                    <g>\r\n                        <path d=\"M437.019,74.981C388.668,26.629,324.38,0,256,0S123.332,26.629,74.981,74.981C26.629,123.332,0,187.62,0,256 s26.629,132.668,74.981,181.019C123.332,485.371,187.62,512,256,512c64.518,0,126.15-24.077,173.541-67.796l-10.312-11.178 c-44.574,41.12-102.544,63.766-163.229,63.766c-64.317,0-124.786-25.046-170.266-70.527 C40.254,380.786,15.208,320.317,15.208,256S40.254,131.214,85.734,85.735C131.214,40.254,191.683,15.208,256,15.208 s124.786,25.046,170.266,70.527c45.48,45.479,70.526,105.948,70.526,170.265c0,60.594-22.587,118.498-63.599,163.045 l11.188,10.301C487.986,381.983,512,320.421,512,256C512,187.62,485.371,123.332,437.019,74.981z\"></path>\r\n                        <path d=\"M282.819,263.604h63.415v-15.208h-63.415c-1.619-5.701-5.007-10.662-9.536-14.25l35.913-86.701l-14.049-5.82 l-35.908,86.688c-1.064-0.124-2.142-0.194-3.238-0.194c-15.374,0-27.881,12.508-27.881,27.881s12.507,27.881,27.881,27.881 C268.737,283.881,279.499,275.292,282.819,263.604z M243.327,256c0-6.989,5.685-12.673,12.673-12.673 c6.989,0,12.673,5.685,12.673,12.673c0,6.989-5.685,12.673-12.673,12.673C249.011,268.673,243.327,262.989,243.327,256z\"></path>\r\n                        <path d=\"M451.168,256c0-107.616-87.552-195.168-195.168-195.168S60.832,148.384,60.832,256S148.384,451.168,256,451.168 S451.168,363.616,451.168,256z M76.04,256c0-99.231,80.73-179.96,179.96-179.96S435.96,156.769,435.96,256 S355.231,435.96,256,435.96S76.04,355.231,76.04,256z\"></path>\r\n                    </g>\r\n                </svg>\r\n            </div>\r\n\r\n            <p className=\"waiting-modal-footer\">\r\n                Ensure that your payment is confirmed before the timer runs out.\r\n            </p>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nexport default WaitingModal;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["714", "715", "716", "717"], [], "import React, { useState, useEffect } from \"react\";\nimport { Table, Button, Space, Select, Input, message, Modal, Tag, Tooltip } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { \n  getAllStudyMaterials, \n  deleteVideo, \n  deleteNote, \n  deletePastPaper, \n  deleteBook \n} from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaEdit,\n  FaTrash,\n  FaEye,\n  FaFilter,\n  FaSearch\n} from \"react-icons/fa\";\nimport \"./StudyMaterialManager.css\";\n\nconst { Option } = Select;\nconst { Search } = Input;\n\nfunction StudyMaterialManager({ onEdit }) {\n  const dispatch = useDispatch();\n  const [materials, setMaterials] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filters, setFilters] = useState({\n    materialType: \"\",\n    level: \"\",\n    className: \"\",\n    subject: \"\"\n  });\n  const [searchText, setSearchText] = useState(\"\");\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\"];\n      case \"secondary\":\n        return [\"6\", \"7\", \"8\", \"9\", \"10\", \"11\"];\n      case \"advance\":\n        return [\"12\", \"13\"];\n      default:\n        return [];\n    }\n  };\n\n  // Fetch materials\n  const fetchMaterials = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      \n      const response = await getAllStudyMaterials(filters);\n      \n      if (response.status === 200 && response.data.success) {\n        setMaterials(response.data.data || []);\n      } else {\n        message.error(\"Failed to fetch study materials\");\n        setMaterials([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching materials:\", error);\n      message.error(\"Failed to fetch study materials\");\n      setMaterials([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  useEffect(() => {\n    fetchMaterials();\n  }, [filters]);\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => {\n      const newFilters = { ...prev, [key]: value };\n      \n      // Reset dependent filters\n      if (key === \"level\") {\n        newFilters.className = \"\";\n        newFilters.subject = \"\";\n      }\n      \n      return newFilters;\n    });\n  };\n\n  // Handle delete\n  const handleDelete = async (material) => {\n    Modal.confirm({\n      title: `Delete ${material.type.replace(\"-\", \" \")}`,\n      content: `Are you sure you want to delete \"${material.title}\"? This action cannot be undone.`,\n      okText: \"Delete\",\n      okType: \"danger\",\n      cancelText: \"Cancel\",\n      onOk: async () => {\n        try {\n          dispatch(ShowLoading());\n          \n          let response;\n          switch (material.type) {\n            case \"videos\":\n              response = await deleteVideo(material._id);\n              break;\n            case \"study-notes\":\n              response = await deleteNote(material._id);\n              break;\n            case \"past-papers\":\n              response = await deletePastPaper(material._id);\n              break;\n            case \"books\":\n              response = await deleteBook(material._id);\n              break;\n            default:\n              throw new Error(\"Invalid material type\");\n          }\n\n          if (response.status === 200 && response.data.success) {\n            message.success(response.data.message);\n            fetchMaterials(); // Refresh the list\n          } else {\n            message.error(response.data?.message || \"Failed to delete material\");\n          }\n        } catch (error) {\n          console.error(\"Error deleting material:\", error);\n          message.error(\"Failed to delete material\");\n        } finally {\n          dispatch(HideLoading());\n        }\n      }\n    });\n  };\n\n  // Get material type icon\n  const getMaterialIcon = (type) => {\n    switch (type) {\n      case \"videos\":\n        return <FaVideo className=\"material-icon video\" />;\n      case \"study-notes\":\n        return <FaFileAlt className=\"material-icon note\" />;\n      case \"past-papers\":\n        return <FaGraduationCap className=\"material-icon paper\" />;\n      case \"books\":\n        return <FaBook className=\"material-icon book\" />;\n      default:\n        return <FaFileAlt className=\"material-icon\" />;\n    }\n  };\n\n  // Get material type label\n  const getMaterialTypeLabel = (type) => {\n    switch (type) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return type;\n    }\n  };\n\n  // Filter materials based on search text\n  const filteredMaterials = materials.filter(material =>\n    material.title.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.subject.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.className.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // Table columns\n  const columns = [\n    {\n      title: \"Material\",\n      key: \"material\",\n      width: \"30%\",\n      render: (_, record) => (\n        <div className=\"material-info\">\n          <div className=\"material-header\">\n            {getMaterialIcon(record.type)}\n            <div className=\"material-details\">\n              <div className=\"material-title\">{record.title}</div>\n              <div className=\"material-meta\">\n                <Tag color=\"blue\">{getMaterialTypeLabel(record.type)}</Tag>\n                <span className=\"meta-text\">{record.subject} • Class {record.className}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: \"Level\",\n      dataIndex: \"level\",\n      key: \"level\",\n      width: \"10%\",\n      render: (level) => (\n        <Tag color={level === \"primary\" ? \"green\" : level === \"secondary\" ? \"orange\" : \"purple\"}>\n          {level.charAt(0).toUpperCase() + level.slice(1)}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Class\",\n      dataIndex: \"className\",\n      key: \"className\",\n      width: \"10%\",\n      render: (className) => <span className=\"class-badge\">Class {className}</span>,\n    },\n    {\n      title: \"Subject\",\n      dataIndex: \"subject\",\n      key: \"subject\",\n      width: \"15%\",\n    },\n    {\n      title: \"Year\",\n      dataIndex: \"year\",\n      key: \"year\",\n      width: \"10%\",\n      render: (year) => year || \"-\",\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      width: \"25%\",\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"Edit material\">\n            <Button\n              type=\"primary\"\n              icon={<FaEdit />}\n              size=\"small\"\n              onClick={() => onEdit(record)}\n            >\n              Edit\n            </Button>\n          </Tooltip>\n          \n          <Tooltip title=\"Delete material\">\n            <Button\n              danger\n              icon={<FaTrash />}\n              size=\"small\"\n              onClick={() => handleDelete(record)}\n            >\n              Delete\n            </Button>\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"study-material-manager\">\n      <div className=\"manager-header\">\n        <h2>Study Materials Management</h2>\n        <p>Manage all uploaded study materials - edit, delete, and organize content</p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"filters-section\">\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label>Material Type:</label>\n            <Select\n              placeholder=\"All Types\"\n              value={filters.materialType || undefined}\n              onChange={(value) => handleFilterChange(\"materialType\", value)}\n              allowClear\n              style={{ width: 150 }}\n            >\n              <Option value=\"videos\">Videos</Option>\n              <Option value=\"study-notes\">Study Notes</Option>\n              <Option value=\"past-papers\">Past Papers</Option>\n              <Option value=\"books\">Books</Option>\n            </Select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label>Level:</label>\n            <Select\n              placeholder=\"All Levels\"\n              value={filters.level || undefined}\n              onChange={(value) => handleFilterChange(\"level\", value)}\n              allowClear\n              style={{ width: 120 }}\n            >\n              <Option value=\"primary\">Primary</Option>\n              <Option value=\"secondary\">Secondary</Option>\n              <Option value=\"advance\">Advance</Option>\n            </Select>\n          </div>\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Class:</label>\n              <Select\n                placeholder=\"All Classes\"\n                value={filters.className || undefined}\n                onChange={(value) => handleFilterChange(\"className\", value)}\n                allowClear\n                style={{ width: 120 }}\n              >\n                {getClassesForLevel(filters.level).map(cls => (\n                  <Option key={cls} value={cls}>Class {cls}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Subject:</label>\n              <Select\n                placeholder=\"All Subjects\"\n                value={filters.subject || undefined}\n                onChange={(value) => handleFilterChange(\"subject\", value)}\n                allowClear\n                style={{ width: 150 }}\n              >\n                {getSubjectsForLevel(filters.level).map(subject => (\n                  <Option key={subject} value={subject}>{subject}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          <div className=\"filter-group\">\n            <label>Search:</label>\n            <Search\n              placeholder=\"Search materials...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              style={{ width: 200 }}\n              allowClear\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Materials Table */}\n      <div className=\"materials-table\">\n        <Table\n          columns={columns}\n          dataSource={filteredMaterials}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `Total ${total} materials`,\n          }}\n          scroll={{ x: 1000 }}\n        />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterialManager;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js", ["718", "719"], [], "import React, { useState, useEffect } from 'react';\nimport { Modal, Form, Input, Button, Checkbox, Alert, Typography, Space, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, RobotOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { quickLogin, autoRefreshToken } from '../apicalls/auth';\nimport { getTokenExpiryInfo } from '../utils/authUtils';\n\nconst { Title, Text } = Typography;\n\nconst AILoginModal = ({ \n  visible, \n  onCancel, \n  onSuccess, \n  title = \"Login Required for AI Features\",\n  description = \"Please login to access AI question generation features.\"\n}) => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [autoRefreshing, setAutoRefreshing] = useState(false);\n\n  useEffect(() => {\n    if (visible) {\n      // Check current token status when modal opens\n      const info = getTokenExpiryInfo();\n      setTokenInfo(info);\n      \n      // Try auto-refresh if token is expiring soon\n      if (info.needsRefresh && !info.expired) {\n        handleAutoRefresh();\n      }\n    }\n  }, [visible]);\n\n  const handleAutoRefresh = async () => {\n    try {\n      setAutoRefreshing(true);\n      const success = await autoRefreshToken();\n      if (success) {\n        const newInfo = getTokenExpiryInfo();\n        setTokenInfo(newInfo);\n        \n        if (!newInfo.expired) {\n          onSuccess?.();\n          return;\n        }\n      }\n    } catch (error) {\n      console.error('Auto-refresh failed:', error);\n    } finally {\n      setAutoRefreshing(false);\n    }\n  };\n\n  const handleLogin = async (values) => {\n    try {\n      setLoading(true);\n      \n      const response = await quickLogin({\n        email: values.email,\n        password: values.password,\n        rememberMe: values.rememberMe || false\n      });\n\n      if (response.success) {\n        // Check AI access\n        const { aiAccess } = response.data;\n        \n        if (!aiAccess.enabled) {\n          Modal.warning({\n            title: 'AI Features Not Available',\n            content: aiAccess.requiresUpgrade \n              ? 'AI question generation requires a premium subscription. Please upgrade your account.'\n              : 'AI features are not available for your account. Please contact support.',\n          });\n          return;\n        }\n\n        form.resetFields();\n        onSuccess?.(response.data);\n      }\n    } catch (error) {\n      console.error('Login failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderTokenStatus = () => {\n    if (!tokenInfo) return null;\n\n    if (tokenInfo.expired) {\n      return (\n        <Alert\n          type=\"warning\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expired\"\n          description=\"Your session has expired. Please login again to continue using AI features.\"\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    if (tokenInfo.needsRefresh) {\n      return (\n        <Alert\n          type=\"info\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expiring Soon\"\n          description={`Your session will expire in ${tokenInfo.formattedTimeLeft}. Login to extend your session.`}\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Modal\n      title={\n        <Space>\n          <RobotOutlined style={{ color: '#1890ff' }} />\n          <span>{title}</span>\n        </Space>\n      }\n      open={visible}\n      onCancel={onCancel}\n      footer={null}\n      width={450}\n      destroyOnClose\n      maskClosable={false}\n    >\n      <div style={{ padding: '20px 0' }}>\n        <Text type=\"secondary\" style={{ display: 'block', marginBottom: 24, textAlign: 'center' }}>\n          {description}\n        </Text>\n\n        {renderTokenStatus()}\n\n        {autoRefreshing && (\n          <Alert\n            type=\"info\"\n            message=\"Refreshing Session...\"\n            description=\"Attempting to refresh your authentication automatically.\"\n            style={{ marginBottom: 16 }}\n            showIcon\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleLogin}\n          autoComplete=\"off\"\n          size=\"large\"\n        >\n          <Form.Item\n            name=\"email\"\n            label=\"Email\"\n            rules={[\n              { required: true, message: 'Please enter your email' },\n              { type: 'email', message: 'Please enter a valid email' }\n            ]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"Enter your email\"\n              autoComplete=\"email\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            label=\"Password\"\n            rules={[{ required: true, message: 'Please enter your password' }]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"Enter your password\"\n              autoComplete=\"current-password\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"rememberMe\" valuePropName=\"checked\">\n            <Checkbox>\n              Keep me logged in for 30 days\n            </Checkbox>\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0 }}>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading || autoRefreshing}\n              block\n              size=\"large\"\n              icon={<RobotOutlined />}\n            >\n              {loading ? 'Logging in...' : autoRefreshing ? 'Refreshing...' : 'Login for AI Features'}\n            </Button>\n          </Form.Item>\n        </Form>\n\n        <Divider />\n\n        <div style={{ textAlign: 'center' }}>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            Secure authentication for AI-powered question generation\n          </Text>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport default AILoginModal;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js", ["720"], [], "import { useState, useEffect, useCallback } from 'react';\nimport { message } from 'antd';\nimport { validateSession, autoRefreshToken, checkAIAccess } from '../apicalls/auth';\nimport { getTokenExpiryInfo, isSessionValid } from '../utils/authUtils';\n\n/**\n * Enhanced authentication hook specifically for AI features\n * Provides automatic token refresh, session validation, and AI access checking\n */\nexport const useAIAuth = () => {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [hasAIAccess, setHasAIAccess] = useState(false);\n  const [user, setUser] = useState(null);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [requiresUpgrade, setRequiresUpgrade] = useState(false);\n\n  // Check authentication status\n  const checkAuth = useCallback(async () => {\n    try {\n      setLoading(true);\n      \n      // Quick check if session is valid\n      if (!isSessionValid()) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        setTokenInfo(null);\n        return false;\n      }\n\n      // Get token expiry info\n      const expiry = getTokenExpiryInfo();\n      setTokenInfo(expiry);\n\n      // If token is expired, clear everything\n      if (expiry.expired) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        return false;\n      }\n\n      // Try to auto-refresh if needed\n      if (expiry.needsRefresh) {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n        } catch (error) {\n          console.warn('Auto-refresh failed:', error);\n        }\n      }\n\n      // Validate session and check AI access\n      const accessCheck = await checkAIAccess();\n      \n      if (accessCheck.hasAccess) {\n        setIsAuthenticated(true);\n        setHasAIAccess(true);\n        setUser(accessCheck.user);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return true;\n      } else {\n        setIsAuthenticated(!!accessCheck.user);\n        setHasAIAccess(false);\n        setUser(accessCheck.user || null);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return false;\n      }\n\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setIsAuthenticated(false);\n      setHasAIAccess(false);\n      setUser(null);\n      setTokenInfo(null);\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  // Refresh authentication\n  const refreshAuth = useCallback(async () => {\n    return await checkAuth();\n  }, [checkAuth]);\n\n  // Logout\n  const logout = useCallback(() => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setIsAuthenticated(false);\n    setHasAIAccess(false);\n    setUser(null);\n    setTokenInfo(null);\n    message.info('Logged out successfully');\n  }, []);\n\n  // Login success handler\n  const handleLoginSuccess = useCallback((userData) => {\n    setIsAuthenticated(true);\n    setUser(userData.user);\n    \n    // Check AI access from login response\n    const aiEnabled = userData.aiAccess?.enabled !== false;\n    setHasAIAccess(aiEnabled);\n    setRequiresUpgrade(userData.aiAccess?.requiresUpgrade || false);\n    \n    // Update token info\n    const expiry = getTokenExpiryInfo();\n    setTokenInfo(expiry);\n    \n    message.success('Successfully logged in for AI features!');\n  }, []);\n\n  // Require authentication for AI operations\n  const requireAIAuth = useCallback(async () => {\n    if (loading) {\n      return { success: false, reason: 'loading' };\n    }\n\n    if (!isAuthenticated) {\n      return { success: false, reason: 'not_authenticated' };\n    }\n\n    if (!hasAIAccess) {\n      if (requiresUpgrade) {\n        return { success: false, reason: 'requires_upgrade' };\n      }\n      return { success: false, reason: 'no_ai_access' };\n    }\n\n    // Check if token is about to expire\n    if (tokenInfo?.needsRefresh) {\n      try {\n        await autoRefreshToken();\n        const newExpiry = getTokenExpiryInfo();\n        setTokenInfo(newExpiry);\n      } catch (error) {\n        return { success: false, reason: 'refresh_failed' };\n      }\n    }\n\n    return { success: true };\n  }, [isAuthenticated, hasAIAccess, requiresUpgrade, tokenInfo, loading]);\n\n  // Auto-refresh timer\n  useEffect(() => {\n    let refreshTimer;\n\n    if (isAuthenticated && tokenInfo && !tokenInfo.expired) {\n      // Set timer to refresh token 5 minutes before expiry\n      const refreshTime = Math.max(0, (tokenInfo.timeLeft - 300) * 1000);\n      \n      refreshTimer = setTimeout(async () => {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n          console.log('🔄 Token auto-refreshed');\n        } catch (error) {\n          console.warn('Auto-refresh timer failed:', error);\n        }\n      }, refreshTime);\n    }\n\n    return () => {\n      if (refreshTimer) {\n        clearTimeout(refreshTimer);\n      }\n    };\n  }, [isAuthenticated, tokenInfo]);\n\n  // Initial auth check\n  useEffect(() => {\n    checkAuth();\n  }, [checkAuth]);\n\n  return {\n    // State\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    tokenInfo,\n    loading,\n    requiresUpgrade,\n    \n    // Actions\n    checkAuth,\n    refreshAuth,\n    logout,\n    handleLoginSuccess,\n    requireAIAuth,\n    \n    // Computed values\n    needsLogin: !isAuthenticated,\n    needsUpgrade: isAuthenticated && !hasAIAccess && requiresUpgrade,\n    sessionExpiringSoon: tokenInfo?.needsRefresh || false,\n    timeUntilExpiry: tokenInfo?.formattedTimeLeft || 'Unknown'\n  };\n};\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js", ["721", "722", "723", "724", "725"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js", ["726", "727", "728"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js", ["729"], [], "import React, { useState, useEffect, Fragment } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbCheck, TbX, TbClock, TbBulb } from 'react-icons/tb';\nimport { Card, Button } from './index';\n\nconst QuizQuestion = ({\n  question,\n  questionNumber,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerSelect,\n  onNext,\n  onPrevious,\n  showResult = false,\n  correctAnswer = null,\n  timeRemaining = null,\n  isLastQuestion = false,\n  className = '',\n}) => {\n  const [selectedOption, setSelectedOption] = useState(selectedAnswer);\n\n  useEffect(() => {\n    setSelectedOption(selectedAnswer);\n  }, [selectedAnswer]);\n\n  const handleOptionSelect = (optionIndex) => {\n    if (showResult) return; // Prevent selection when showing results\n    \n    setSelectedOption(optionIndex);\n    onAnswerSelect(optionIndex);\n  };\n\n  const getOptionClassName = (optionIndex) => {\n    const baseClasses = 'quiz-option group cursor-pointer';\n    \n    if (showResult) {\n      if (optionIndex === correctAnswer) {\n        return `${baseClasses} quiz-option-correct cursor-default`;\n      }\n      if (optionIndex === selectedOption && optionIndex !== correctAnswer) {\n        return `${baseClasses} quiz-option-incorrect cursor-default`;\n      }\n      return `${baseClasses} opacity-60 cursor-default`;\n    }\n    \n    if (optionIndex === selectedOption) {\n      return `${baseClasses} quiz-option-selected`;\n    }\n    \n    return baseClasses;\n  };\n\n  const getOptionIcon = (optionIndex) => {\n    if (!showResult) return null;\n    \n    if (optionIndex === correctAnswer) {\n      return <TbCheck className=\"w-5 h-5 text-success-600\" />;\n    }\n    if (optionIndex === selectedOption && optionIndex !== correctAnswer) {\n      return <TbX className=\"w-5 h-5 text-error-600\" />;\n    }\n    return null;\n  };\n\n  const renderQuestionContent = () => {\n    // Check for image in multiple possible properties\n    const questionImage = question.image || question.imageUrl;\n\n    // Debug logging for image detection removed to prevent React rendering issues\n\n    switch (question.type) {\n      case 'image':\n        return (\n          <div className=\"space-y-6\">\n            {questionImage && (\n              <div className=\"quiz-image-container-modern\">\n                <div className=\"quiz-image-wrapper\">\n                  <img\n                    src={questionImage}\n                    alt=\"Question diagram\"\n                    className=\"quiz-image-modern\"\n                    onError={(e) => {\n                      console.error('Image failed to load:', questionImage);\n                      e.target.style.display = 'none';\n                      // Show error message\n                      const errorDiv = document.createElement('div');\n                      errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                      errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                      e.target.parentNode.appendChild(errorDiv);\n                    }}\n                    onLoad={() => {\n                      console.log('Image loaded successfully:', questionImage);\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n            <div className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {question.name}\n            </div>\n          </div>\n        );\n      \n      case 'fill':\n        return (\n          <div className=\"space-y-4\">\n            {/* Show image if available */}\n            {questionImage && (\n              <div className=\"quiz-image-container-modern\">\n                <div className=\"quiz-image-wrapper\">\n                  <img\n                    src={questionImage}\n                    alt=\"Question diagram\"\n                    className=\"quiz-image-modern\"\n                    onError={(e) => {\n                      console.error('Image failed to load:', questionImage);\n                      e.target.style.display = 'none';\n                      // Show error message\n                      const errorDiv = document.createElement('div');\n                      errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                      errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                      e.target.parentNode.appendChild(errorDiv);\n                    }}\n                    onLoad={() => {\n                      console.log('Image loaded successfully:', questionImage);\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n            <div className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {String(question.name || '')}\n            </div>\n            {!showResult ? (\n              <input\n                type=\"text\"\n                value={selectedOption || ''}\n                onChange={(e) => handleOptionSelect(e.target.value)}\n                className=\"input-modern\"\n                placeholder=\"Type your answer here...\"\n              />\n            ) : (\n              <div className=\"space-y-2\">\n                <div className=\"p-4 bg-gray-50 rounded-lg\">\n                  <span className=\"text-sm text-gray-600\">Your answer: </span>\n                  <span className={selectedOption === correctAnswer ? 'text-success-600 font-medium' : 'text-error-600 font-medium'}>\n                    {selectedOption || 'No answer provided'}\n                  </span>\n                </div>\n                <div className=\"p-4 bg-success-50 rounded-lg\">\n                  <span className=\"text-sm text-success-600\">Correct answer: </span>\n                  <span className=\"text-success-700 font-medium\">{correctAnswer}</span>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n      \n      default: // MCQ\n        return (\n          <div className=\"space-y-6\">\n            {/* Show image if available for any question type */}\n            {questionImage && (\n              <div className=\"quiz-image-container-modern\">\n                <div className=\"quiz-image-wrapper\">\n                  <img\n                    src={questionImage}\n                    alt=\"Question diagram\"\n                    className=\"quiz-image-modern\"\n                    onError={(e) => {\n                      console.error('Image failed to load:', questionImage);\n                      e.target.style.display = 'none';\n                      // Show error message\n                      const errorDiv = document.createElement('div');\n                      errorDiv.className = 'text-red-500 text-sm text-center p-4 bg-red-50 rounded-lg border border-red-200';\n                      errorDiv.textContent = 'Image could not be loaded. Please check the image URL.';\n                      e.target.parentNode.appendChild(errorDiv);\n                    }}\n                    onLoad={() => {\n                      console.log('Image loaded successfully:', questionImage);\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n            <div className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {String(question.name || '')}\n            </div>\n            <div className=\"space-y-3\">\n              {question.options ? (() => {\n                try {\n                  // Handle both object and array formats\n                  let optionsArray = [];\n                  if (Array.isArray(question.options)) {\n                    optionsArray = question.options\n                      .filter(option => option && typeof option === 'string')\n                      .map(option => String(option).trim())\n                      .filter(option => option.length > 0);\n                  } else if (typeof question.options === 'object' && question.options !== null) {\n                    optionsArray = Object.values(question.options)\n                      .filter(option => option && typeof option === 'string')\n                      .map(option => String(option).trim())\n                      .filter(option => option.length > 0);\n                  }\n\n                  // Ensure we always return valid JSX\n                  if (optionsArray.length === 0) {\n                    return (\n                      <div className=\"text-gray-500 text-center py-4\">\n                        No valid options available\n                      </div>\n                    );\n                  }\n\n                  return (\n                    <React.Fragment>\n                      {optionsArray.map((option, index) => (\n                        <motion.div\n                          key={`option-${index}`}\n                          whileHover={!showResult ? { scale: 1.02 } : {}}\n                          whileTap={!showResult ? { scale: 0.98 } : {}}\n                          className={getOptionClassName(index)}\n                          onClick={() => handleOptionSelect(index)}\n                        >\n                          <div className=\"flex items-center justify-between\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-medium text-gray-600\">\n                                {String.fromCharCode(65 + index)}\n                              </div>\n                              <span className=\"text-gray-900 dark:text-white\">{String(option)}</span>\n                            </div>\n                            {getOptionIcon(index)}\n                          </div>\n                        </motion.div>\n                      ))}\n                    </React.Fragment>\n                  );\n                } catch (error) {\n                  console.error('Error rendering options:', error);\n                  return (\n                    <div className=\"text-red-500 text-center py-4\">\n                      Error loading options\n                    </div>\n                  );\n                }\n              })() : (\n                <div className=\"text-gray-500 text-center py-4\">\n                  No options available\n                </div>\n              )}\n            </div>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Question Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"text-sm font-medium text-gray-500\">\n            {questionNumber} of {totalQuestions}\n          </div>\n          <div className=\"progress-bar w-32\">\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: `${(questionNumber / totalQuestions) * 100}%` }}\n              className=\"progress-fill\"\n            />\n          </div>\n        </div>\n        \n        {timeRemaining !== null && (\n          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${\n            timeRemaining <= 60 ? 'bg-error-100 text-error-700' : 'bg-primary-100 text-primary-700'\n          }`}>\n            <TbClock className=\"w-4 h-4\" />\n            <span className=\"text-sm font-medium\">\n              {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}\n            </span>\n          </div>\n        )}\n      </div>\n\n      {/* Question Card */}\n      <Card className=\"p-8\">\n        {renderQuestionContent()}\n      </Card>\n\n      {/* Explanation (shown after answer) */}\n      <AnimatePresence>\n        {showResult && question.explanation && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n          >\n            <Card className=\"p-6 bg-blue-50 border-blue-200\">\n              <div className=\"flex items-start space-x-3\">\n                <TbBulb className=\"w-6 h-6 text-blue-600 flex-shrink-0 mt-0.5\" />\n                <div>\n                  <h4 className=\"font-medium text-blue-900 mb-2\">Explanation</h4>\n                  <p className=\"text-blue-800 text-sm leading-relaxed\">\n                    {String(question.explanation || '')}\n                  </p>\n                </div>\n              </div>\n            </Card>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Navigation */}\n      <div className=\"flex items-center justify-between pt-6\">\n        <Button\n          variant=\"secondary\"\n          onClick={onPrevious}\n          disabled={questionNumber === 1}\n        >\n          Previous\n        </Button>\n        \n        <div className=\"text-sm text-gray-500\">\n          {selectedOption !== null && selectedOption !== undefined ? (\n            <span className=\"text-primary-600 font-medium\">Answer selected</span>\n          ) : (\n            <span>Select an answer to continue</span>\n          )}\n        </div>\n        \n        <Button\n          variant=\"primary\"\n          onClick={onNext}\n          disabled={!showResult && (selectedOption === null || selectedOption === undefined)}\n        >\n          {isLastQuestion ? 'Finish Quiz' : 'Next'}\n        </Button>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizQuestion;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js", ["730"], [], "import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { TbClock, TbAlertTriangle } from 'react-icons/tb';\n\nconst QuizTimer = ({\n  duration, // in seconds\n  onTimeUp,\n  isActive = true,\n  showWarning = true,\n  warningThreshold = 300, // 5 minutes\n  className = '',\n}) => {\n  const [timeRemaining, setTimeRemaining] = useState(duration);\n  const [isWarning, setIsWarning] = useState(false);\n\n  useEffect(() => {\n    setTimeRemaining(duration);\n  }, [duration]);\n\n  useEffect(() => {\n    if (!isActive) return;\n\n    const interval = setInterval(() => {\n      setTimeRemaining((prev) => {\n        if (prev <= 1) {\n          onTimeUp?.();\n          return 0;\n        }\n        \n        const newTime = prev - 1;\n        \n        // Check if we should show warning\n        if (showWarning && newTime <= warningThreshold && !isWarning) {\n          setIsWarning(true);\n        }\n        \n        return newTime;\n      });\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, [isActive, onTimeUp, showWarning, warningThreshold, isWarning]);\n\n  const formatTime = (seconds) => {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getProgressPercentage = () => {\n    return ((duration - timeRemaining) / duration) * 100;\n  };\n\n  const getTimerColor = () => {\n    if (timeRemaining <= 60) return 'text-white'; // Last minute\n    if (timeRemaining <= warningThreshold) return 'text-white'; // Warning\n    return 'text-white'; // Normal\n  };\n\n  const getProgressColor = () => {\n    if (timeRemaining <= 60) return 'from-red-500 to-red-600';\n    if (timeRemaining <= warningThreshold) return 'from-yellow-500 to-yellow-600';\n    return 'from-primary-500 to-blue-500';\n  };\n\n  return (\n    <div className={`${className}`}>\n      {/* Compact Timer Display */}\n      <motion.div\n        animate={isWarning ? { scale: [1, 1.05, 1] } : {}}\n        transition={{ duration: 1, repeat: isWarning ? Infinity : 0 }}\n        className={`inline-flex items-center space-x-3 px-6 py-3 rounded-xl shadow-lg border-2 ${\n          timeRemaining <= 60\n            ? 'bg-gradient-to-r from-red-600 to-red-700 border-red-300 text-red-50'\n            : timeRemaining <= warningThreshold\n            ? 'bg-gradient-to-r from-yellow-500 to-orange-500 border-yellow-300 text-yellow-50'\n            : 'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-300 text-blue-50'\n        }`}\n        style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}\n      >\n        {timeRemaining <= warningThreshold && (\n          <motion.div\n            animate={{ rotate: [0, 10, -10, 0] }}\n            transition={{ duration: 0.5, repeat: Infinity }}\n          >\n            <TbAlertTriangle className={`w-5 h-5 drop-shadow-md ${\n              timeRemaining <= 60 ? 'text-red-100' : 'text-yellow-100'\n            }`} />\n          </motion.div>\n        )}\n\n        <TbClock className={`w-5 h-5 drop-shadow-md ${\n          timeRemaining <= 60\n            ? 'text-red-100'\n            : timeRemaining <= warningThreshold\n            ? 'text-yellow-100'\n            : 'text-blue-100'\n        }`} />\n\n        <div className=\"text-center\">\n          <div className={`text-xs font-semibold opacity-90 mb-1 ${\n            timeRemaining <= 60\n              ? 'text-red-100'\n              : timeRemaining <= warningThreshold\n              ? 'text-yellow-100'\n              : 'text-blue-100'\n          }`}>TIME</div>\n          <span className={`font-mono font-black text-lg ${\n            timeRemaining <= 60\n              ? 'text-red-50'\n              : timeRemaining <= warningThreshold\n              ? 'text-yellow-50'\n              : 'text-blue-50'\n          }`} style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.7)' }}>\n            {formatTime(timeRemaining)}\n          </span>\n        </div>\n      </motion.div>\n\n      {/* Progress Bar */}\n      <div className=\"mt-3 w-full bg-gray-300 rounded-full h-2 overflow-hidden shadow-inner\">\n        <motion.div\n          initial={{ width: 0 }}\n          animate={{ width: `${getProgressPercentage()}%` }}\n          transition={{ duration: 0.5 }}\n          className={`h-full bg-gradient-to-r ${getProgressColor()} rounded-full shadow-sm`}\n        />\n      </div>\n\n      {/* Warning Message */}\n      {isWarning && timeRemaining > 60 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-3 text-sm font-semibold bg-yellow-100 text-yellow-800 px-3 py-2 rounded-lg border border-yellow-300\"\n        >\n          ⚠️ {Math.floor(timeRemaining / 60)} minutes remaining\n        </motion.div>\n      )}\n\n      {/* Critical Warning */}\n      {timeRemaining <= 60 && timeRemaining > 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-3 text-sm font-bold bg-red-100 text-red-800 px-3 py-2 rounded-lg border border-red-300\"\n        >\n          🚨 Less than 1 minute left!\n        </motion.div>\n      )}\n\n      {/* Time's Up */}\n      {timeRemaining === 0 && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          className=\"mt-3 text-sm font-black bg-red-200 text-red-900 px-3 py-2 rounded-lg border border-red-400\"\n        >\n          ⏰ Time's up!\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\n// Full-screen timer overlay for critical moments\nexport const QuizTimerOverlay = ({ timeRemaining, onClose }) => {\n  if (timeRemaining > 10) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center\"\n    >\n      <motion.div\n        initial={{ scale: 0.8, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        className=\"bg-white rounded-2xl p-8 text-center shadow-2xl max-w-sm mx-4\"\n      >\n        <motion.div\n          animate={{ scale: [1, 1.2, 1] }}\n          transition={{ duration: 1, repeat: Infinity }}\n          className=\"text-6xl mb-4\"\n        >\n          ⏰\n        </motion.div>\n        \n        <h3 className=\"text-2xl font-bold text-red-600 mb-2\">\n          Time Almost Up!\n        </h3>\n        \n        <motion.div\n          animate={{ scale: [1, 1.1, 1] }}\n          transition={{ duration: 0.5, repeat: Infinity }}\n          className=\"text-4xl font-mono font-bold text-red-600 mb-4\"\n        >\n          {timeRemaining}\n        </motion.div>\n        \n        <p className=\"text-gray-600 mb-4\">\n          Submit your answers now!\n        </p>\n        \n        <button\n          onClick={onClose}\n          className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n        >\n          Continue Quiz\n        </button>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default QuizTimer;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js", ["731"], [], "import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbSun, TbMoon } from 'react-icons/tb';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst ThemeToggle = ({ className = '', size = 'md' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  const sizes = {\n    sm: 'w-8 h-8',\n    md: 'w-10 h-10',\n    lg: 'w-12 h-12',\n  };\n\n  const iconSizes = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6',\n  };\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      onClick={toggleTheme}\n      className={`\n        ${sizes[size]} \n        relative rounded-full p-2 \n        bg-gray-200 dark:bg-gray-700 \n        hover:bg-gray-300 dark:hover:bg-gray-600 \n        transition-all duration-300 \n        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <div className=\"relative w-full h-full flex items-center justify-center\">\n        <AnimatePresence mode=\"wait\" initial={false}>\n          {isDarkMode ? (\n            <motion.div\n              key=\"sun\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbSun className={`${iconSizes[size]} text-yellow-500`} />\n            </motion.div>\n          ) : (\n            <motion.div\n              key=\"moon\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbMoon className={`${iconSizes[size]} text-blue-600`} />\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.button>\n  );\n};\n\n// Advanced Theme Toggle with Switch Design\nexport const ThemeSwitch = ({ className = '' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={toggleTheme}\n      className={`\n        relative inline-flex h-6 w-11 items-center rounded-full \n        transition-colors duration-300 focus:outline-none focus:ring-2 \n        focus:ring-primary-500 focus:ring-offset-2\n        ${isDarkMode ? 'bg-primary-600' : 'bg-gray-200'}\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <motion.span\n        layout\n        className={`\n          inline-block h-4 w-4 transform rounded-full bg-white shadow-lg \n          transition-transform duration-300 flex items-center justify-center\n          ${isDarkMode ? 'translate-x-6' : 'translate-x-1'}\n        `}\n      >\n        <motion.div\n          initial={false}\n          animate={{ rotate: isDarkMode ? 0 : 180 }}\n          transition={{ duration: 0.3 }}\n        >\n          {isDarkMode ? (\n            <TbMoon className=\"w-2.5 h-2.5 text-primary-600\" />\n          ) : (\n            <TbSun className=\"w-2.5 h-2.5 text-yellow-500\" />\n          )}\n        </motion.div>\n      </motion.span>\n    </motion.button>\n  );\n};\n\n// Theme Toggle with Label\nexport const ThemeToggleWithLabel = ({ className = '', showLabel = true }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <div className={`flex items-center space-x-3 ${className}`}>\n      {showLabel && (\n        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n          {isDarkMode ? 'Dark Mode' : 'Light Mode'}\n        </span>\n      )}\n      <ThemeSwitch />\n    </div>\n  );\n};\n\nexport default ThemeToggle;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js", ["732"], [], "import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst ResponsiveContainer = ({ \n  children, \n  className = '', \n  maxWidth = '7xl',\n  padding = 'responsive',\n  ...props \n}) => {\n  const maxWidths = {\n    'sm': 'max-w-sm',\n    'md': 'max-w-md',\n    'lg': 'max-w-lg',\n    'xl': 'max-w-xl',\n    '2xl': 'max-w-2xl',\n    '3xl': 'max-w-3xl',\n    '4xl': 'max-w-4xl',\n    '5xl': 'max-w-5xl',\n    '6xl': 'max-w-6xl',\n    '7xl': 'max-w-7xl',\n    'full': 'max-w-full',\n  };\n\n  const paddings = {\n    'none': '',\n    'sm': 'px-4',\n    'md': 'px-6',\n    'lg': 'px-8',\n    'responsive': 'px-4 sm:px-6 lg:px-8',\n  };\n\n  return (\n    <div \n      className={`${maxWidths[maxWidth]} mx-auto ${paddings[padding]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Grid Component\nexport const ResponsiveGrid = ({ \n  children, \n  cols = { xs: 1, sm: 2, md: 3, lg: 4 },\n  gap = 6,\n  className = '',\n  ...props \n}) => {\n  const gridCols = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-2',\n    3: 'grid-cols-3',\n    4: 'grid-cols-4',\n    5: 'grid-cols-5',\n    6: 'grid-cols-6',\n  };\n\n  const gaps = {\n    2: 'gap-2',\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  const responsiveClasses = [\n    cols.xs && gridCols[cols.xs],\n    cols.sm && `sm:${gridCols[cols.sm]}`,\n    cols.md && `md:${gridCols[cols.md]}`,\n    cols.lg && `lg:${gridCols[cols.lg]}`,\n    cols.xl && `xl:${gridCols[cols.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div \n      className={`grid ${responsiveClasses} ${gaps[gap]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Text Component\nexport const ResponsiveText = ({ \n  children, \n  size = { xs: 'sm', sm: 'base', md: 'lg' },\n  weight = 'normal',\n  className = '',\n  ...props \n}) => {\n  const textSizes = {\n    'xs': 'text-xs',\n    'sm': 'text-sm',\n    'base': 'text-base',\n    'lg': 'text-lg',\n    'xl': 'text-xl',\n    '2xl': 'text-2xl',\n    '3xl': 'text-3xl',\n    '4xl': 'text-4xl',\n  };\n\n  const fontWeights = {\n    'light': 'font-light',\n    'normal': 'font-normal',\n    'medium': 'font-medium',\n    'semibold': 'font-semibold',\n    'bold': 'font-bold',\n  };\n\n  const responsiveClasses = [\n    size.xs && textSizes[size.xs],\n    size.sm && `sm:${textSizes[size.sm]}`,\n    size.md && `md:${textSizes[size.md]}`,\n    size.lg && `lg:${textSizes[size.lg]}`,\n    size.xl && `xl:${textSizes[size.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <span \n      className={`${responsiveClasses} ${fontWeights[weight]} ${className}`}\n      {...props}\n    >\n      {children}\n    </span>\n  );\n};\n\n// Mobile-First Responsive Component\nexport const MobileFirst = ({ children, className = '' }) => {\n  return (\n    <div className={`block lg:hidden ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Desktop-First Responsive Component\nexport const DesktopFirst = ({ children, className = '' }) => {\n  return (\n    <div className={`hidden lg:block ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Responsive Stack Component\nexport const ResponsiveStack = ({ \n  children, \n  direction = { xs: 'col', md: 'row' },\n  spacing = 4,\n  align = 'start',\n  justify = 'start',\n  className = '',\n  ...props \n}) => {\n  const directions = {\n    'row': 'flex-row',\n    'col': 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse',\n  };\n\n  const spacings = {\n    2: 'gap-2',\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  const alignments = {\n    'start': 'items-start',\n    'center': 'items-center',\n    'end': 'items-end',\n    'stretch': 'items-stretch',\n  };\n\n  const justifications = {\n    'start': 'justify-start',\n    'center': 'justify-center',\n    'end': 'justify-end',\n    'between': 'justify-between',\n    'around': 'justify-around',\n    'evenly': 'justify-evenly',\n  };\n\n  const responsiveClasses = [\n    direction.xs && directions[direction.xs],\n    direction.sm && `sm:${directions[direction.sm]}`,\n    direction.md && `md:${directions[direction.md]}`,\n    direction.lg && `lg:${directions[direction.lg]}`,\n    direction.xl && `xl:${directions[direction.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div \n      className={`flex ${responsiveClasses} ${spacings[spacing]} ${alignments[align]} ${justifications[justify]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Show/Hide Component\nexport const ResponsiveShow = ({ \n  children, \n  breakpoint = 'md',\n  direction = 'up',\n  className = '' \n}) => {\n  const breakpoints = {\n    'sm': direction === 'up' ? 'sm:block' : 'sm:hidden',\n    'md': direction === 'up' ? 'md:block' : 'md:hidden',\n    'lg': direction === 'up' ? 'lg:block' : 'lg:hidden',\n    'xl': direction === 'up' ? 'xl:block' : 'xl:hidden',\n  };\n\n  const baseClass = direction === 'up' ? 'hidden' : 'block';\n\n  return (\n    <div className={`${baseClass} ${breakpoints[breakpoint]} ${className}`}>\n      {children}\n    </div>\n  );\n};\n\nexport default ResponsiveContainer;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js", ["733", "734"], [], "import React, { useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// Performance monitoring hook\nexport const usePerformanceMonitor = () => {\n  const [metrics, setMetrics] = useState({\n    loadTime: 0,\n    renderTime: 0,\n    memoryUsage: 0,\n    fps: 0,\n  });\n\n  useEffect(() => {\n    // Measure page load time\n    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;\n    \n    // Measure memory usage (if available)\n    const memoryUsage = performance.memory ? performance.memory.usedJSHeapSize / 1048576 : 0; // MB\n\n    setMetrics(prev => ({\n      ...prev,\n      loadTime,\n      memoryUsage,\n    }));\n\n    // FPS monitoring\n    let frameCount = 0;\n    let lastTime = performance.now();\n    \n    const measureFPS = () => {\n      frameCount++;\n      const currentTime = performance.now();\n      \n      if (currentTime >= lastTime + 1000) {\n        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));\n        setMetrics(prev => ({ ...prev, fps }));\n        frameCount = 0;\n        lastTime = currentTime;\n      }\n      \n      requestAnimationFrame(measureFPS);\n    };\n    \n    requestAnimationFrame(measureFPS);\n  }, []);\n\n  return metrics;\n};\n\n// Performance indicator component\nconst PerformanceIndicator = ({ show = false }) => {\n  const metrics = usePerformanceMonitor();\n  const [isVisible, setIsVisible] = useState(show);\n\n  useEffect(() => {\n    const handleKeyPress = (e) => {\n      if (e.ctrlKey && e.shiftKey && e.key === 'P') {\n        setIsVisible(!isVisible);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [isVisible]);\n\n  // Completely disable the performance indicator\n  return null;\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: 20 }}\n          className=\"fixed bottom-4 right-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono backdrop-blur-sm\"\n        >\n          <div className=\"space-y-1\">\n            <div className=\"font-bold text-green-400 mb-2\">Performance Metrics</div>\n            <div>Load Time: {metrics.loadTime}ms</div>\n            <div>Memory: {metrics.memoryUsage.toFixed(1)}MB</div>\n            <div>FPS: {metrics.fps}</div>\n            <div className=\"text-gray-400 mt-2 text-xs\">\n              Press Ctrl+Shift+P to toggle\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\n// Lazy loading wrapper with intersection observer\nexport const LazyWrapper = ({ children, threshold = 0.1, rootMargin = '50px' }) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [ref, setRef] = useState(null);\n\n  useEffect(() => {\n    if (!ref) return;\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n          observer.disconnect();\n        }\n      },\n      { threshold, rootMargin }\n    );\n\n    observer.observe(ref);\n    return () => observer.disconnect();\n  }, [ref, threshold, rootMargin]);\n\n  return (\n    <div ref={setRef}>\n      {isVisible ? children : <div className=\"h-32 bg-gray-100 animate-pulse rounded\" />}\n    </div>\n  );\n};\n\n// Optimized image component with WebP support\nexport const OptimizedImage = ({ \n  src, \n  webpSrc, \n  alt, \n  className = '',\n  loading = 'lazy',\n  ...props \n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  const handleError = () => {\n    setImageError(true);\n  };\n\n  const handleLoad = () => {\n    setIsLoaded(true);\n  };\n\n  return (\n    <div className={`relative overflow-hidden ${className}`}>\n      {!isLoaded && (\n        <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n      )}\n      \n      {!imageError ? (\n        <picture>\n          {webpSrc && <source srcSet={webpSrc} type=\"image/webp\" />}\n          <motion.img\n            src={src}\n            alt={alt}\n            loading={loading}\n            onError={handleError}\n            onLoad={handleLoad}\n            className={`w-full h-full object-cover transition-opacity duration-300 ${\n              isLoaded ? 'opacity-100' : 'opacity-0'\n            }`}\n            {...props}\n          />\n        </picture>\n      ) : (\n        <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n          <span className=\"text-gray-400 text-sm\">Image not available</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Debounced search hook\nexport const useDebouncedSearch = (searchTerm, delay = 300) => {\n  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedTerm(searchTerm);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [searchTerm, delay]);\n\n  return debouncedTerm;\n};\n\n// Virtual scrolling component for large lists\nexport const VirtualList = ({ \n  items, \n  itemHeight = 60, \n  containerHeight = 400,\n  renderItem,\n  className = '' \n}) => {\n  const [scrollTop, setScrollTop] = useState(0);\n  const [containerRef, setContainerRef] = useState(null);\n\n  const visibleStart = Math.floor(scrollTop / itemHeight);\n  const visibleEnd = Math.min(\n    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,\n    items.length\n  );\n\n  const visibleItems = items.slice(visibleStart, visibleEnd);\n  const totalHeight = items.length * itemHeight;\n  const offsetY = visibleStart * itemHeight;\n\n  const handleScroll = (e) => {\n    setScrollTop(e.target.scrollTop);\n  };\n\n  return (\n    <div\n      ref={setContainerRef}\n      className={`overflow-auto ${className}`}\n      style={{ height: containerHeight }}\n      onScroll={handleScroll}\n    >\n      <div style={{ height: totalHeight, position: 'relative' }}>\n        <div style={{ transform: `translateY(${offsetY}px)` }}>\n          {visibleItems.map((item, index) => (\n            <div key={visibleStart + index} style={{ height: itemHeight }}>\n              {renderItem(item, visibleStart + index)}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PerformanceIndicator;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js", ["735", "736", "737"], [], "import React from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  TbTrophy, \n  TbMedal, \n  TbCrown, \n  TbStar, \n  TbFlame, \n  TbTarget, \n  TbTrendingUp, \n  TbBolt,\n  TbAward,\n  TbDiamond\n} from 'react-icons/tb';\n\nconst AchievementBadge = ({ \n  achievement, \n  size = 'medium', \n  showDetails = true, \n  className = '',\n  onClick = null \n}) => {\n  // Achievement type configurations\n  const achievementConfig = {\n    first_quiz: {\n      icon: TbStar,\n      title: 'First Steps',\n      description: 'Completed your first quiz',\n      color: 'from-blue-400 to-blue-600',\n      bgColor: 'bg-blue-50',\n      textColor: 'text-blue-700'\n    },\n    perfect_score: {\n      icon: TbTrophy,\n      title: 'Perfect Score',\n      description: 'Achieved 100% on a quiz',\n      color: 'from-yellow-400 to-yellow-600',\n      bgColor: 'bg-yellow-50',\n      textColor: 'text-yellow-700'\n    },\n    streak_5: {\n      icon: TbFlame,\n      title: 'Hot Streak',\n      description: '5 correct answers in a row',\n      color: 'from-orange-400 to-red-500',\n      bgColor: 'bg-orange-50',\n      textColor: 'text-orange-700'\n    },\n    streak_10: {\n      icon: TbFlame,\n      title: 'Fire Streak',\n      description: '10 correct answers in a row',\n      color: 'from-red-400 to-red-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    streak_20: {\n      icon: TbFlame,\n      title: 'Blazing Streak',\n      description: '20 correct answers in a row',\n      color: 'from-red-500 to-purple-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    subject_master: {\n      icon: TbCrown,\n      title: 'Subject Master',\n      description: 'Mastered a subject',\n      color: 'from-purple-400 to-purple-600',\n      bgColor: 'bg-purple-50',\n      textColor: 'text-purple-700'\n    },\n    speed_demon: {\n      icon: TbBolt,\n      title: 'Speed Demon',\n      description: 'Completed quiz in record time',\n      color: 'from-cyan-400 to-blue-500',\n      bgColor: 'bg-cyan-50',\n      textColor: 'text-cyan-700'\n    },\n    consistent_learner: {\n      icon: TbTarget,\n      title: 'Consistent Learner',\n      description: 'Maintained consistent performance',\n      color: 'from-green-400 to-green-600',\n      bgColor: 'bg-green-50',\n      textColor: 'text-green-700'\n    },\n    improvement_star: {\n      icon: TbTrendingUp,\n      title: 'Improvement Star',\n      description: 'Showed remarkable improvement',\n      color: 'from-indigo-400 to-indigo-600',\n      bgColor: 'bg-indigo-50',\n      textColor: 'text-indigo-700'\n    }\n  };\n\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      container: 'w-12 h-12',\n      icon: 'w-6 h-6',\n      text: 'text-xs',\n      padding: 'p-2'\n    },\n    medium: {\n      container: 'w-16 h-16',\n      icon: 'w-8 h-8',\n      text: 'text-sm',\n      padding: 'p-3'\n    },\n    large: {\n      container: 'w-20 h-20',\n      icon: 'w-10 h-10',\n      text: 'text-base',\n      padding: 'p-4'\n    }\n  };\n\n  const config = achievementConfig[achievement.type] || achievementConfig.first_quiz;\n  const sizes = sizeConfig[size];\n  const IconComponent = config.icon;\n\n  const badgeVariants = {\n    hidden: { opacity: 0, scale: 0.8, rotate: -10 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      rotate: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 20\n      }\n    },\n    hover: { \n      scale: 1.1, \n      rotate: 5,\n      transition: {\n        type: \"spring\",\n        stiffness: 400,\n        damping: 10\n      }\n    }\n  };\n\n  const glowVariants = {\n    hidden: { opacity: 0 },\n    visible: { \n      opacity: [0.5, 1, 0.5],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      variants={badgeVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      whileHover=\"hover\"\n      onClick={onClick}\n      className={`\n        relative cursor-pointer group\n        ${className}\n      `}\n    >\n      {/* Glow effect */}\n      <motion.div\n        variants={glowVariants}\n        className={`\n          absolute inset-0 rounded-full blur-md opacity-75\n          bg-gradient-to-r ${config.color}\n          ${sizes.container}\n        `}\n      />\n      \n      {/* Main badge */}\n      <div className={`\n        relative flex items-center justify-center rounded-full\n        bg-gradient-to-r ${config.color}\n        ${sizes.container} ${sizes.padding}\n        shadow-lg border-2 border-white\n        group-hover:shadow-xl transition-shadow duration-200\n      `}>\n        <IconComponent className={`${sizes.icon} text-white drop-shadow-sm`} />\n      </div>\n\n      {/* Achievement details tooltip */}\n      {showDetails && (\n        <motion.div\n          initial={{ opacity: 0, y: 10, scale: 0.9 }}\n          whileHover={{ opacity: 1, y: 0, scale: 1 }}\n          className={`\n            absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2\n            ${config.bgColor} ${config.textColor}\n            px-3 py-2 rounded-lg shadow-lg border\n            whitespace-nowrap z-10\n            pointer-events-none\n            ${sizes.text}\n          `}\n        >\n          <div className=\"font-semibold\">{config.title}</div>\n          <div className=\"text-xs opacity-75\">{config.description}</div>\n          {achievement.subject && (\n            <div className=\"text-xs font-medium mt-1\">\n              Subject: {achievement.subject}\n            </div>\n          )}\n          {achievement.earnedAt && (\n            <div className=\"text-xs opacity-60 mt-1\">\n              {new Date(achievement.earnedAt).toLocaleDateString()}\n            </div>\n          )}\n          \n          {/* Tooltip arrow */}\n          <div className={`\n            absolute top-full left-1/2 transform -translate-x-1/2\n            w-0 h-0 border-l-4 border-r-4 border-t-4\n            border-l-transparent border-r-transparent\n            ${config.bgColor.replace('bg-', 'border-t-')}\n          `} />\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\n// Achievement list component\nexport const AchievementList = ({\n  achievements = [],\n  maxDisplay = 5,\n  size = 'medium',\n  layout = 'horizontal', // 'horizontal' or 'grid'\n  className = ''\n}) => {\n  const displayAchievements = achievements.slice(0, maxDisplay);\n  const remainingCount = Math.max(0, achievements.length - maxDisplay);\n\n  // Size configurations for the list\n  const sizeConfig = {\n    small: {\n      container: 'w-12 h-12',\n      text: 'text-xs',\n      padding: 'p-2'\n    },\n    medium: {\n      container: 'w-16 h-16',\n      text: 'text-sm',\n      padding: 'p-3'\n    },\n    large: {\n      container: 'w-20 h-20',\n      text: 'text-base',\n      padding: 'p-4'\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const layoutClasses = layout === 'grid'\n    ? 'grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-4'\n    : 'flex flex-wrap gap-2';\n\n  return (\n    <motion.div\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      className={`${layoutClasses} ${className}`}\n    >\n      {displayAchievements.map((achievement, index) => (\n        <AchievementBadge\n          key={`${achievement.type}-${index}`}\n          achievement={achievement}\n          size={size}\n          showDetails={true}\n        />\n      ))}\n\n      {remainingCount > 0 && (\n        <motion.div\n          variants={{\n            hidden: { opacity: 0, scale: 0.8 },\n            visible: { opacity: 1, scale: 1 }\n          }}\n          className={`\n            flex items-center justify-center rounded-full\n            bg-gray-100 border-2 border-gray-200\n            ${sizeConfig[size]?.container} ${sizeConfig[size]?.padding}\n            text-gray-600 font-semibold\n            ${sizeConfig[size]?.text}\n          `}\n        >\n          +{remainingCount}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\n// Achievement notification component\nexport const AchievementNotification = ({\n  achievement,\n  onClose,\n  autoClose = true,\n  duration = 4000\n}) => {\n  // Achievement type configurations for notifications\n  const achievementConfig = {\n    first_quiz: {\n      icon: TbStar,\n      title: 'First Steps',\n      description: 'Completed your first quiz',\n      color: 'from-blue-400 to-blue-600',\n      bgColor: 'bg-blue-50',\n      textColor: 'text-blue-700'\n    },\n    perfect_score: {\n      icon: TbTrophy,\n      title: 'Perfect Score',\n      description: 'Achieved 100% on a quiz',\n      color: 'from-yellow-400 to-yellow-600',\n      bgColor: 'bg-yellow-50',\n      textColor: 'text-yellow-700'\n    },\n    streak_5: {\n      icon: TbFlame,\n      title: 'Hot Streak',\n      description: '5 correct answers in a row',\n      color: 'from-orange-400 to-red-500',\n      bgColor: 'bg-orange-50',\n      textColor: 'text-orange-700'\n    },\n    streak_10: {\n      icon: TbFlame,\n      title: 'Fire Streak',\n      description: '10 correct answers in a row',\n      color: 'from-red-400 to-red-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    streak_20: {\n      icon: TbFlame,\n      title: 'Blazing Streak',\n      description: '20 correct answers in a row',\n      color: 'from-red-500 to-purple-600',\n      bgColor: 'bg-red-50',\n      textColor: 'text-red-700'\n    },\n    subject_master: {\n      icon: TbCrown,\n      title: 'Subject Master',\n      description: 'Mastered a subject',\n      color: 'from-purple-400 to-purple-600',\n      bgColor: 'bg-purple-50',\n      textColor: 'text-purple-700'\n    },\n    speed_demon: {\n      icon: TbBolt,\n      title: 'Speed Demon',\n      description: 'Completed quiz in record time',\n      color: 'from-cyan-400 to-blue-500',\n      bgColor: 'bg-cyan-50',\n      textColor: 'text-cyan-700'\n    },\n    consistent_learner: {\n      icon: TbTarget,\n      title: 'Consistent Learner',\n      description: 'Maintained consistent performance',\n      color: 'from-green-400 to-green-600',\n      bgColor: 'bg-green-50',\n      textColor: 'text-green-700'\n    },\n    improvement_star: {\n      icon: TbTrendingUp,\n      title: 'Improvement Star',\n      description: 'Showed remarkable improvement',\n      color: 'from-indigo-400 to-indigo-600',\n      bgColor: 'bg-indigo-50',\n      textColor: 'text-indigo-700'\n    }\n  };\n\n  React.useEffect(() => {\n    if (autoClose && onClose) {\n      const timer = setTimeout(onClose, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [autoClose, duration, onClose]);\n\n  const config = achievementConfig[achievement.type] || achievementConfig.first_quiz;\n  const IconComponent = config.icon;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: -50, scale: 0.9 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      exit={{ opacity: 0, y: -50, scale: 0.9 }}\n      className={`\n        fixed top-4 right-4 z-50\n        ${config.bgColor} border border-gray-200\n        rounded-lg shadow-lg p-4 max-w-sm\n      `}\n    >\n      <div className=\"flex items-center space-x-3\">\n        <div className={`\n          flex items-center justify-center w-12 h-12 rounded-full\n          bg-gradient-to-r ${config.color}\n        `}>\n          <IconComponent className=\"w-6 h-6 text-white\" />\n        </div>\n        \n        <div className=\"flex-1\">\n          <div className={`font-semibold ${config.textColor}`}>\n            Achievement Unlocked!\n          </div>\n          <div className=\"text-sm text-gray-600\">\n            {config.title}\n          </div>\n          <div className=\"text-xs text-gray-500\">\n            {config.description}\n          </div>\n        </div>\n        \n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            ×\n          </button>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nexport default AchievementBadge;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js", ["738"], [], "import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Tb<PERSON>lam<PERSON>, TbTrophy, TbBolt } from 'react-icons/tb';\n\nconst XPProgressBar = ({\n  currentXP = 0,\n  totalXP = 0,\n  currentLevel = 1,\n  xpToNextLevel = 100,\n  showAnimation = true,\n  size = 'medium', // 'small', 'medium', 'large'\n  showLevel = true,\n  showXPNumbers = true,\n  className = ''\n}) => {\n  const [animatedXP, setAnimatedXP] = useState(0);\n  const [isLevelingUp, setIsLevelingUp] = useState(false);\n\n  // Calculate progress percentage\n  const xpForCurrentLevel = totalXP - xpToNextLevel;\n  const xpProgressInLevel = currentXP - xpForCurrentLevel;\n  const xpNeededForLevel = totalXP - xpForCurrentLevel;\n  const progressPercentage = Math.min(100, Math.max(0, (xpProgressInLevel / xpNeededForLevel) * 100));\n\n  // Size configurations\n  const sizeConfig = {\n    small: {\n      height: 'h-2',\n      levelSize: 'w-6 h-6 text-xs',\n      textSize: 'text-xs',\n      padding: 'px-2 py-1'\n    },\n    medium: {\n      height: 'h-3',\n      levelSize: 'w-8 h-8 text-sm',\n      textSize: 'text-sm',\n      padding: 'px-3 py-2'\n    },\n    large: {\n      height: 'h-4',\n      levelSize: 'w-10 h-10 text-base',\n      textSize: 'text-base',\n      padding: 'px-4 py-3'\n    }\n  };\n\n  const config = sizeConfig[size];\n\n  // Animate XP changes\n  useEffect(() => {\n    if (showAnimation) {\n      const timer = setTimeout(() => {\n        setAnimatedXP(currentXP);\n      }, 100);\n      return () => clearTimeout(timer);\n    } else {\n      setAnimatedXP(currentXP);\n    }\n  }, [currentXP, showAnimation]);\n\n  // Level up animation\n  const triggerLevelUpAnimation = () => {\n    setIsLevelingUp(true);\n    setTimeout(() => setIsLevelingUp(false), 2000);\n  };\n\n  // Get level color based on level\n  const getLevelColor = (level) => {\n    if (level >= 10) return 'from-purple-600 to-pink-600';\n    if (level >= 8) return 'from-yellow-500 to-orange-600';\n    if (level >= 6) return 'from-green-500 to-blue-600';\n    if (level >= 4) return 'from-blue-500 to-purple-600';\n    if (level >= 2) return 'from-indigo-500 to-blue-600';\n    return 'from-gray-500 to-gray-600';\n  };\n\n  // Get XP bar gradient based on progress\n  const getXPBarGradient = () => {\n    if (progressPercentage >= 90) return 'from-yellow-400 via-orange-500 to-red-500';\n    if (progressPercentage >= 70) return 'from-green-400 via-blue-500 to-purple-500';\n    if (progressPercentage >= 50) return 'from-blue-400 via-purple-500 to-pink-500';\n    if (progressPercentage >= 25) return 'from-indigo-400 via-blue-500 to-cyan-500';\n    return 'from-gray-400 via-gray-500 to-gray-600';\n  };\n\n  return (\n    <div className={`xp-progress-container ${className}`}>\n      {/* Level Up Animation Overlay */}\n      <AnimatePresence>\n        {isLevelingUp && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.5 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.5 }}\n            className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm\"\n          >\n            <motion.div\n              initial={{ y: -50, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              exit={{ y: 50, opacity: 0 }}\n              className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-8 py-6 rounded-2xl shadow-2xl text-center\"\n            >\n              <motion.div\n                animate={{ rotate: 360 }}\n                transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                className=\"text-4xl mb-2\"\n              >\n                🎉\n              </motion.div>\n              <h2 className=\"text-2xl font-bold mb-1\">LEVEL UP!</h2>\n              <p className=\"text-lg\">You reached Level {currentLevel}!</p>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      <div className=\"flex items-center space-x-3\">\n        {/* Level Badge */}\n        {showLevel && (\n          <motion.div\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.95 }}\n            className={`\n              ${config.levelSize} rounded-full flex items-center justify-center\n              bg-gradient-to-r ${getLevelColor(currentLevel)}\n              text-white font-bold shadow-lg border-2 border-white\n              relative overflow-hidden\n            `}\n          >\n            {/* Level glow effect */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\" />\n            \n            {/* Level number */}\n            <span className={`relative z-10 ${config.textSize}`}>\n              {currentLevel}\n            </span>\n\n            {/* Level icon for high levels */}\n            {currentLevel >= 10 && (\n              <TbTrophy className=\"absolute top-0 right-0 w-3 h-3 text-yellow-300\" />\n            )}\n          </motion.div>\n        )}\n\n        {/* XP Progress Bar Container */}\n        <div className=\"flex-1\">\n          {/* XP Numbers */}\n          {showXPNumbers && (\n            <div className={`flex justify-between items-center mb-1 ${config.textSize} text-gray-600`}>\n              <span className=\"font-medium\">\n                {animatedXP.toLocaleString()} XP\n              </span>\n              <span className=\"text-gray-500\">\n                {xpToNextLevel > 0 ? `${xpToNextLevel} to next level` : 'Max Level'}\n              </span>\n            </div>\n          )}\n\n          {/* Progress Bar */}\n          <div className={`\n            relative ${config.height} bg-gray-200 rounded-full overflow-hidden\n            shadow-inner border border-gray-300\n          `}>\n            {/* Background gradient */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-gray-100 to-gray-200\" />\n            \n            {/* Progress fill */}\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: `${progressPercentage}%` }}\n              transition={{ duration: 1, ease: \"easeOut\" }}\n              className={`\n                absolute inset-y-0 left-0 rounded-full\n                bg-gradient-to-r ${getXPBarGradient()}\n                shadow-lg relative overflow-hidden\n              `}\n            >\n              {/* Animated shine effect */}\n              <motion.div\n                animate={{ x: ['0%', '100%'] }}\n                transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform skew-x-12\"\n              />\n              \n              {/* Progress glow */}\n              <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\" />\n            </motion.div>\n\n            {/* XP gain animation particles */}\n            <AnimatePresence>\n              {showAnimation && (\n                <motion.div\n                  initial={{ opacity: 0, y: 0 }}\n                  animate={{ opacity: [0, 1, 0], y: -20 }}\n                  exit={{ opacity: 0 }}\n                  transition={{ duration: 1 }}\n                  className=\"absolute right-2 top-1/2 transform -translate-y-1/2\"\n                >\n                  <TbBolt className=\"w-4 h-4 text-yellow-400\" />\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Level progress indicators */}\n          {xpToNextLevel > 0 && (\n            <div className=\"flex justify-between mt-1\">\n              <span className={`${config.textSize} text-gray-500 font-medium`}>\n                Level {currentLevel}\n              </span>\n              <span className={`${config.textSize} text-gray-500 font-medium`}>\n                Level {currentLevel + 1}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* XP Boost Indicator */}\n        {currentLevel > 1 && (\n          <motion.div\n            whileHover={{ scale: 1.1 }}\n            className=\"flex items-center space-x-1 bg-gradient-to-r from-yellow-100 to-orange-100 px-2 py-1 rounded-full border border-yellow-300\"\n          >\n            <TbFlame className=\"w-3 h-3 text-orange-500\" />\n            <span className=\"text-xs font-medium text-orange-700\">\n              +{((currentLevel - 1) * 10)}% XP\n            </span>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default XPProgressBar;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js", ["739"], [], "import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  TbB<PERSON>, \n  TbTrophy, \n  Tb<PERSON><PERSON>e, \n  TbTarget, \n  TbClock, \n  TbTrendingUp,\n  TbStar,\n  TbMedal,\n  TbChevronDown,\n  TbChevronUp\n} from 'react-icons/tb';\n\nconst XPResultDisplay = ({ xpData, className = '' }) => {\n  const [showBreakdown, setShowBreakdown] = useState(false);\n  const [animatedXP, setAnimatedXP] = useState(0);\n\n  // Debug XP data (safely)\n  if (process.env.NODE_ENV === 'development') {\n    console.log('🎨 XPResultDisplay - XP Data received:', xpData);\n  }\n\n  useEffect(() => {\n    if (xpData?.xpAwarded) {\n      // Animate XP counter\n      const duration = 2000;\n      const steps = 60;\n      const increment = xpData.xpAwarded / steps;\n      let current = 0;\n\n      const timer = setInterval(() => {\n        current += increment;\n        if (current >= xpData.xpAwarded) {\n          setAnimatedXP(xpData.xpAwarded);\n          clearInterval(timer);\n        } else {\n          setAnimatedXP(Math.floor(current));\n        }\n      }, duration / steps);\n\n      return () => clearInterval(timer);\n    }\n  }, [xpData]);\n\n  // Play level-up sound effect\n  useEffect(() => {\n    if (xpData?.levelUp) {\n      const playLevelUpSound = () => {\n        try {\n          const audioContext = new window.AudioContext();\n\n          // Create epic level-up sound sequence\n          const createLevelUpSound = () => {\n            // Triumphant ascending chord progression\n            const chords = [\n              [262, 330, 392], // C4, E4, G4\n              [294, 370, 440], // D4, F#4, A4\n              [330, 415, 494], // E4, G#4, B4\n              [349, 440, 523]  // F4, A4, C5\n            ];\n\n            chords.forEach((chord, index) => {\n              setTimeout(() => {\n                chord.forEach((frequency) => {\n                  const oscillator = audioContext.createOscillator();\n                  const gainNode = audioContext.createGain();\n\n                  oscillator.connect(gainNode);\n                  gainNode.connect(audioContext.destination);\n\n                  oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n                  oscillator.type = 'triangle';\n\n                  gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n                  gainNode.gain.linearRampToValueAtTime(0.15, audioContext.currentTime + 0.02);\n                  gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.6);\n\n                  oscillator.start(audioContext.currentTime);\n                  oscillator.stop(audioContext.currentTime + 0.6);\n                });\n              }, index * 200);\n            });\n\n            // Add sparkle effect at the end\n            setTimeout(() => {\n              [659, 784, 988, 1175].forEach((freq, i) => {\n                setTimeout(() => {\n                  const osc = audioContext.createOscillator();\n                  const gain = audioContext.createGain();\n\n                  osc.connect(gain);\n                  gain.connect(audioContext.destination);\n\n                  osc.frequency.setValueAtTime(freq, audioContext.currentTime);\n                  osc.type = 'sine';\n\n                  gain.gain.setValueAtTime(0, audioContext.currentTime);\n                  gain.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.01);\n                  gain.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3);\n\n                  osc.start(audioContext.currentTime);\n                  osc.stop(audioContext.currentTime + 0.3);\n                }, i * 100);\n              });\n            }, 800);\n          };\n\n          createLevelUpSound();\n          console.log('🎵 Level Up sound played!');\n\n        } catch (error) {\n          console.log('Level-up audio not supported:', error);\n        }\n      };\n\n      // Delay sound to sync with animation\n      setTimeout(playLevelUpSound, 600);\n    }\n  }, [xpData?.levelUp]);\n\n  if (!xpData) {\n    return null;\n  }\n\n  const {\n    xpAwarded = 0,\n    xpBreakdown = {},\n    levelUp = false,\n    newLevel = 1,\n    newTotalXP = 0,\n    currentStreak = 0,\n    achievements = []\n  } = xpData;\n\n  return (\n    <div className={`bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-6 border-2 border-purple-200 shadow-lg ${className}`}>\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes levelUpGlow {\n          0% { box-shadow: 0 25px 50px -12px rgba(251, 191, 36, 0.5), 0 0 20px rgba(251, 191, 36, 0.3); }\n          100% { box-shadow: 0 25px 50px -12px rgba(251, 191, 36, 0.8), 0 0 40px rgba(251, 191, 36, 0.6); }\n        }\n      `}</style>\n      {/* Enhanced Level Up Notification */}\n      <AnimatePresence>\n        {levelUp && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.3, y: -50, rotateY: -180 }}\n            animate={{\n              opacity: 1,\n              scale: [0.3, 1.2, 1],\n              y: 0,\n              rotateY: 0,\n              transition: {\n                duration: 1.2,\n                ease: \"easeOut\",\n                scale: {\n                  times: [0, 0.6, 1],\n                  duration: 1.2\n                }\n              }\n            }}\n            exit={{ opacity: 0, scale: 0.8, y: -20 }}\n            className=\"mb-6 p-6 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-2xl text-white text-center shadow-2xl relative overflow-hidden\"\n            style={{\n              background: 'linear-gradient(45deg, #fbbf24, #f59e0b, #ea580c, #dc2626)',\n              boxShadow: '0 25px 50px -12px rgba(251, 191, 36, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1)',\n              animation: 'levelUpGlow 2s infinite alternate'\n            }}\n          >\n            {/* Animated Background Effects */}\n            <motion.div\n              className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20\"\n              animate={{\n                x: ['-100%', '100%'],\n                transition: {\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"linear\"\n                }\n              }}\n            />\n\n            {/* Floating Particles */}\n            {[...Array(8)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-2 h-2 bg-white rounded-full opacity-80\"\n                style={{\n                  left: `${20 + i * 10}%`,\n                  top: `${20 + (i % 3) * 20}%`\n                }}\n                animate={{\n                  y: [-10, -30, -10],\n                  opacity: [0.8, 0.3, 0.8],\n                  scale: [1, 1.5, 1],\n                  transition: {\n                    duration: 2 + i * 0.2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }\n                }}\n              />\n            ))}\n\n            <motion.div\n              className=\"relative z-10\"\n              animate={{\n                scale: [1, 1.05, 1],\n                transition: {\n                  duration: 1.5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }}\n            >\n              <motion.div\n                className=\"flex items-center justify-center mb-3\"\n                animate={{\n                  rotateY: [0, 360],\n                  transition: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                  }\n                }}\n              >\n                <TbTrophy className=\"w-12 h-12 mr-3 drop-shadow-lg\" />\n                <motion.span\n                  className=\"text-3xl font-black tracking-wider\"\n                  style={{\n                    textShadow: '2px 2px 4px rgba(0,0,0,0.5)',\n                    background: 'linear-gradient(45deg, #fff, #fbbf24)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent'\n                  }}\n                  animate={{\n                    scale: [1, 1.1, 1],\n                    transition: {\n                      duration: 1,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  }}\n                >\n                  LEVEL UP!\n                </motion.span>\n              </motion.div>\n\n              <motion.p\n                className=\"text-xl font-bold\"\n                style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{\n                  opacity: 1,\n                  y: 0,\n                  transition: { delay: 0.5, duration: 0.8 }\n                }}\n              >\n                🎉 You reached Level {newLevel}! 🎉\n              </motion.p>\n\n              <motion.div\n                className=\"mt-3 text-sm font-semibold opacity-90\"\n                initial={{ opacity: 0 }}\n                animate={{\n                  opacity: 1,\n                  transition: { delay: 1, duration: 0.8 }\n                }}\n              >\n                Keep up the amazing work! 🚀\n              </motion.div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Main XP Display */}\n      <div className=\"text-center mb-6\">\n        <div className=\"flex items-center justify-center mb-3\">\n          <TbBolt className=\"w-8 h-8 text-purple-600 mr-2\" />\n          <h3 className=\"text-2xl font-bold text-gray-800\">XP Earned</h3>\n        </div>\n        \n        <motion.div\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          transition={{ type: \"spring\", stiffness: 200, damping: 10 }}\n          className=\"text-6xl font-bold text-purple-600 mb-2\"\n        >\n          +{animatedXP}\n        </motion.div>\n        \n        <p className=\"text-gray-600\">\n          Total XP: {newTotalXP.toLocaleString()} | Level {newLevel}\n        </p>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-2 gap-4 mb-6\">\n        {currentStreak > 0 && (\n          <div className=\"bg-white rounded-lg p-3 text-center shadow-sm\">\n            <TbFlame className=\"w-6 h-6 text-orange-500 mx-auto mb-1\" />\n            <div className=\"text-lg font-bold text-gray-800\">{currentStreak}</div>\n            <div className=\"text-sm text-gray-600\">Streak</div>\n          </div>\n        )}\n        \n        {achievements.length > 0 && (\n          <div className=\"bg-white rounded-lg p-3 text-center shadow-sm\">\n            <TbMedal className=\"w-6 h-6 text-yellow-500 mx-auto mb-1\" />\n            <div className=\"text-lg font-bold text-gray-800\">{achievements.length}</div>\n            <div className=\"text-sm text-gray-600\">New Badges</div>\n          </div>\n        )}\n      </div>\n\n      {/* XP Breakdown Toggle */}\n      {Object.keys(xpBreakdown).length > 0 && (\n        <div>\n          <button\n            onClick={() => setShowBreakdown(!showBreakdown)}\n            className=\"w-full flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200\"\n          >\n            <span className=\"font-medium text-gray-800\">XP Breakdown</span>\n            {showBreakdown ? (\n              <TbChevronUp className=\"w-5 h-5 text-gray-600\" />\n            ) : (\n              <TbChevronDown className=\"w-5 h-5 text-gray-600\" />\n            )}\n          </button>\n\n          <AnimatePresence>\n            {showBreakdown && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: \"auto\" }}\n                exit={{ opacity: 0, height: 0 }}\n                className=\"mt-3 bg-white rounded-lg p-4 shadow-sm\"\n              >\n                <div className=\"space-y-3\">\n                  {xpBreakdown.baseXP && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbTarget className=\"w-4 h-4 text-blue-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Base XP</span>\n                      </div>\n                      <span className=\"font-medium text-gray-800\">+{xpBreakdown.baseXP}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.difficultyBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbTrendingUp className=\"w-4 h-4 text-green-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Difficulty Bonus</span>\n                      </div>\n                      <span className=\"font-medium text-green-600\">+{xpBreakdown.difficultyBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.speedBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbClock className=\"w-4 h-4 text-yellow-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Speed Bonus</span>\n                      </div>\n                      <span className=\"font-medium text-yellow-600\">+{xpBreakdown.speedBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.perfectScoreBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbStar className=\"w-4 h-4 text-purple-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Perfect Score</span>\n                      </div>\n                      <span className=\"font-medium text-purple-600\">+{xpBreakdown.perfectScoreBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.streakBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbFlame className=\"w-4 h-4 text-orange-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">Streak Bonus</span>\n                      </div>\n                      <span className=\"font-medium text-orange-600\">+{xpBreakdown.streakBonus}</span>\n                    </div>\n                  )}\n                  \n                  {xpBreakdown.firstAttemptBonus > 0 && (\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center\">\n                        <TbMedal className=\"w-4 h-4 text-indigo-500 mr-2\" />\n                        <span className=\"text-sm text-gray-700\">First Attempt</span>\n                      </div>\n                      <span className=\"font-medium text-indigo-600\">+{xpBreakdown.firstAttemptBonus}</span>\n                    </div>\n                  )}\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      )}\n\n      {/* Achievement Notifications */}\n      {achievements.length > 0 && (\n        <div className=\"mt-6\">\n          <h4 className=\"text-lg font-bold text-gray-800 mb-3 flex items-center\">\n            <TbMedal className=\"w-5 h-5 mr-2 text-yellow-500\" />\n            New Achievements\n          </h4>\n          <div className=\"space-y-2\">\n            {achievements.map((achievement, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n                className=\"flex items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200\"\n              >\n                <TbTrophy className=\"w-6 h-6 text-yellow-500 mr-3\" />\n                <div>\n                  <div className=\"font-medium text-gray-800\">{String(achievement.name || 'Achievement')}</div>\n                  <div className=\"text-sm text-gray-600\">{String(achievement.description || 'Achievement unlocked!')}</div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default XPResultDisplay;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js", ["740", "741", "742"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js", ["743", "744"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizDashboard.js", ["745", "746", "747", "748"], [], "import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport ModernQuizCard from './ModernQuizCard';\nimport {\n  TbSearch,\n  TbFilter,\n  TbGridDots,\n  Tb<PERSON>ist,\n  TbSortAscending,\n  TbBook,\n  TbClock,\n  TbStar,\n} from 'react-icons/tb';\n\nconst QuizDashboard = ({\n  quizzes = [],\n  userResults = {},\n  onQuizStart,\n  loading = false,\n  className = ''\n}) => {\n  const { user } = useSelector((state) => state.user);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [viewMode, setViewMode] = useState('grid');\n\n  // Set default class filter to user's class\n  useEffect(() => {\n    if (user?.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Get unique subjects and classes from quizzes\n  const subjects = [...new Set(quizzes.map(quiz => quiz.subject).filter(Boolean))];\n  const classes = [...new Set(quizzes.map(quiz => quiz.class).filter(Boolean))].sort();\n\n  // Filter and sort quizzes\n  const filteredQuizzes = quizzes\n    .filter(quiz => {\n      const matchesSearch = quiz.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           quiz.subject?.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesSubject = selectedSubject === 'all' || quiz.subject === selectedSubject;\n      const matchesClass = selectedClass === 'all' || quiz.class === selectedClass;\n      return matchesSearch && matchesSubject && matchesClass;\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return (a.name || '').localeCompare(b.name || '');\n        case 'duration':\n          return (a.duration || 0) - (b.duration || 0);\n        case 'questions':\n          return (a.questions?.length || 0) - (b.questions?.length || 0);\n        case 'xp':\n          return (b.xpPoints || 0) - (a.xpPoints || 0);\n        default:\n          return 0;\n      }\n    });\n\n  // Stats - calculate based on filtered quizzes and actual user results\n  const stats = {\n    total: filteredQuizzes.length,\n    completed: filteredQuizzes.filter(quiz => userResults[quiz._id]).length,\n    passed: filteredQuizzes.filter(quiz => {\n      const result = userResults[quiz._id];\n      if (!result) return false;\n      const passingMarks = quiz.passingMarks || 60;\n      return result.percentage >= passingMarks;\n    }).length,\n    totalXP: filteredQuizzes.reduce((sum, quiz) => {\n      const result = userResults[quiz._id];\n      return sum + (result?.xpEarned || 0);\n    }, 0)\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${className}`}>\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Quiz Dashboard</h1>\n              <p className=\"text-gray-600 mt-1\">Challenge yourself and track your progress</p>\n            </div>\n            \n            {/* Stats */}\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div className=\"bg-blue-50 rounded-lg p-3 text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">{stats.total}</div>\n                <div className=\"text-xs text-blue-500 font-medium\">Total Quizzes</div>\n              </div>\n              <div className=\"bg-green-50 rounded-lg p-3 text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">{stats.passed}</div>\n                <div className=\"text-xs text-green-500 font-medium\">Passed</div>\n              </div>\n              <div className=\"bg-purple-50 rounded-lg p-3 text-center\">\n                <div className=\"text-2xl font-bold text-purple-600\">{stats.completed}</div>\n                <div className=\"text-xs text-purple-500 font-medium\">Attempted</div>\n              </div>\n              <div className=\"bg-yellow-50 rounded-lg p-3 text-center\">\n                <div className=\"text-2xl font-bold text-yellow-600\">{stats.totalXP}</div>\n                <div className=\"text-xs text-yellow-500 font-medium\">Total XP</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters and Controls */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border p-4 mb-6\">\n          <div className=\"flex flex-col lg:flex-row gap-4\">\n            {/* Search */}\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <TbSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search quizzes...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"lg:w-48\">\n              <select\n                value={selectedSubject}\n                onChange={(e) => setSelectedSubject(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {subjects.map(subject => (\n                  <option key={subject} value={subject}>{subject}</option>\n                ))}\n              </select>\n            </div>\n\n            {/* Class Filter */}\n            <div className=\"lg:w-48\">\n              <select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"all\">All Classes</option>\n                {classes.map(cls => (\n                  <option key={cls} value={cls}>Class {cls}</option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"lg:w-48\">\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"name\">Sort by Name</option>\n                <option value=\"duration\">Sort by Duration</option>\n                <option value=\"questions\">Sort by Questions</option>\n                <option value=\"xp\">Sort by XP</option>\n              </select>\n            </div>\n\n            {/* View Mode */}\n            <div className=\"flex border border-gray-300 rounded-lg overflow-hidden\">\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`px-3 py-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600'}`}\n              >\n                <TbGridDots className=\"w-5 h-5\" />\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`px-3 py-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600'}`}\n              >\n                <TbList className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Quiz Grid */}\n        {loading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n          </div>\n        ) : filteredQuizzes.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <TbBook className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No quizzes found</h3>\n            <p className=\"text-gray-500\">Try adjusting your search or filter criteria.</p>\n          </div>\n        ) : (\n          <div className={`\n            ${viewMode === 'grid' \n              ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' \n              : 'space-y-4'\n            }\n          `}>\n            {filteredQuizzes.map((quiz, index) => (\n              <motion.div\n                key={quiz._id || index}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3, delay: Math.min(index * 0.1, 0.8) }}\n                className={viewMode === 'list' ? 'w-full' : ''}\n              >\n                <ModernQuizCard\n                  quiz={quiz}\n                  userResult={userResults[quiz._id]}\n                  onStart={onQuizStart}\n                  className=\"h-full\"\n                />\n              </motion.div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default QuizDashboard;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ModernQuizCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\quiz.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js", ["749", "750", "751", "752", "753"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx", [], ["754"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js", ["755"], [], "import React from 'react';\nimport { motion } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport AdminTopNavigation from './AdminTopNavigation';\n\nconst AdminLayout = ({ children, title, subtitle, actions, showHeader = true }) => {\n  const { user } = useSelector((state) => state.user);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\">\n      {/* Top Navigation */}\n      <AdminTopNavigation />\n\n      {/* Admin Content Container */}\n      <div className=\"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto\">\n        {/* Page Header - Optional */}\n        {showHeader && (title || subtitle || actions) && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"mb-6 sm:mb-8\"\n          >\n            <div className=\"bg-white rounded-2xl shadow-lg border border-slate-200/50 p-6 sm:p-8\">\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n                <div className=\"flex-1\">\n                  {title && (\n                    <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-slate-800 to-blue-600 bg-clip-text text-transparent mb-2\">\n                      {title}\n                    </h1>\n                  )}\n                  {subtitle && (\n                    <p className=\"text-slate-600 text-sm sm:text-base lg:text-lg\">\n                      {subtitle}\n                    </p>\n                  )}\n                </div>\n\n                {/* Actions */}\n                {actions && (\n                  <div className=\"flex flex-wrap gap-2 sm:gap-3\">\n                    {actions}\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Main Content */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.1 }}\n          className=\"space-y-6\"\n        >\n          {children}\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js", ["756", "757", "758", "759", "760"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js", ["761", "762"], [], "import React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { useSelector } from 'react-redux';\nimport '../styles/admin-navigation.css';\nimport {\n  TbArrowLeft,\n  TbUsers,\n  TbBook,\n  TbFileText,\n  TbChartBar,\n  TbRobot,\n  TbBell,\n  TbSettings,\n  TbDashboard,\n  TbLogout,\n  TbHome,\n  TbUser\n} from 'react-icons/tb';\n\nconst AdminTopNavigation = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user } = useSelector((state) => state.user);\n\n  const adminMenuItems = [\n    {\n      title: 'Dashboard',\n      icon: TbDashboard,\n      path: '/admin/dashboard',\n      color: 'text-blue-500'\n    },\n    {\n      title: 'Users',\n      icon: TbUsers,\n      path: '/admin/users',\n      color: 'text-green-500'\n    },\n    {\n      title: 'Exams',\n      icon: TbFileText,\n      path: '/admin/exams',\n      color: 'text-purple-500'\n    },\n    {\n      title: 'Study Materials',\n      icon: TbBook,\n      path: '/admin/study-materials',\n      color: 'text-orange-500'\n    },\n    {\n      title: 'Reports',\n      icon: TbChartBar,\n      path: '/admin/reports',\n      color: 'text-indigo-500'\n    },\n    {\n      title: 'Notifications',\n      icon: TbBell,\n      path: '/admin/notifications',\n      color: 'text-yellow-500'\n    }\n  ];\n\n  const getCurrentPageInfo = () => {\n    const currentPath = location.pathname;\n    const currentItem = adminMenuItems.find(item => currentPath.startsWith(item.path));\n    return currentItem || { title: 'Admin Panel', icon: TbDashboard };\n  };\n\n  const isActivePath = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  const handleLogout = () => {\n    try {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      navigate('/login');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  const currentPage = getCurrentPageInfo();\n  const isDashboard = location.pathname === '/admin/dashboard';\n\n  return (\n    <div className=\"bg-white border-b border-slate-200 sticky top-0 z-50\">\n      <div className=\"px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Left side - Back button and current page */}\n          <div className=\"flex items-center space-x-4\">\n            {!isDashboard && (\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => navigate('/admin/dashboard')}\n                className=\"p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors duration-200\"\n              >\n                <TbArrowLeft className=\"w-5 h-5 text-slate-600\" />\n              </motion.button>\n            )}\n            \n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <currentPage.icon className=\"w-5 h-5 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-lg font-semibold text-slate-900\">{currentPage.title}</h1>\n                <p className=\"text-xs text-slate-500\">BrainWave Admin</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Right side - User info and logout */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"hidden sm:flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                <TbUser className=\"w-4 h-4 text-white\" />\n              </div>\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-slate-900\">{user?.name}</p>\n                <p className=\"text-xs text-slate-500\">Administrator</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-2\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => navigate('/')}\n                className=\"p-2 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors duration-200\"\n                title=\"View Site\"\n              >\n                <TbHome className=\"w-4 h-4 text-slate-600\" />\n              </motion.button>\n              \n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={handleLogout}\n                className=\"p-2 rounded-lg bg-red-100 hover:bg-red-200 transition-colors duration-200\"\n                title=\"Logout\"\n              >\n                <TbLogout className=\"w-4 h-4 text-red-600\" />\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation options below title */}\n        <div className=\"pb-4\">\n          <div className=\"flex items-center space-x-1 overflow-x-auto scrollbar-hide\">\n            {adminMenuItems.map((item) => {\n              const IconComponent = item.icon;\n              const isActive = isActivePath(item.path);\n\n              return (\n                <motion.button\n                  key={item.path}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  onClick={() => navigate(item.path)}\n                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap flex-shrink-0 ${\n                    isActive\n                      ? 'bg-blue-100 text-blue-700 shadow-sm border border-blue-200'\n                      : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900'\n                  }`}\n                >\n                  <IconComponent className={`w-4 h-4 ${isActive ? 'text-blue-600' : item.color}`} />\n                  <span className=\"hidden sm:inline\">{item.title}</span>\n                  <span className=\"sm:hidden text-xs\">{item.title.split(' ')[0]}</span>\n                </motion.button>\n              );\n            })}\n          </div>\n\n          {/* Mobile scroll indicator */}\n          <div className=\"sm:hidden mt-2 flex justify-center\">\n            <div className=\"text-xs text-slate-400\">← Swipe to see more options →</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminTopNavigation;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js", ["763", "764", "765", "766", "767", "768"], [], "import React, { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbX, Tb<PERSON><PERSON>, Tb<PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, TbArrowRight } from \"react-icons/tb\";\nimport { message } from \"antd\";\n\nconst TryForFreeModal = ({ isOpen, onClose, onSubmit }) => {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    level: \"\",\n    class: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n\n  // Level and class configurations\n  const levelOptions = [\n    { value: \"primary\", label: \"Primary\", description: \"Classes 1-7\", icon: \"🌱\" },\n    { value: \"secondary\", label: \"Secondary\", description: \"Form 1-4\", icon: \"📚\" },\n    { value: \"advance\", label: \"Advance\", description: \"Form 5-6\", icon: \"🎓\" }\n  ];\n\n  const classOptions = {\n    primary: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"],\n    secondary: [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"],\n    advance: [\"Form-5\", \"Form-6\"]\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n      // Reset class when level changes\n      ...(field === \"level\" && { class: \"\" })\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.name.trim()) {\n      message.error(\"Please enter your name\");\n      return;\n    }\n    \n    if (!formData.level) {\n      message.error(\"Please select your level\");\n      return;\n    }\n    \n    if (!formData.class) {\n      message.error(\"Please select your class\");\n      return;\n    }\n\n    setLoading(true);\n    try {\n      await onSubmit(formData);\n    } catch (error) {\n      message.error(\"Something went wrong. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const modalVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 50 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      y: 0,\n      transition: { \n        type: \"spring\", \n        damping: 25, \n        stiffness: 300 \n      }\n    },\n    exit: { \n      opacity: 0, \n      scale: 0.8, \n      y: 50,\n      transition: { duration: 0.2 }\n    }\n  };\n\n  const overlayVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n    exit: { opacity: 0 }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div\n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 p-4 overflow-y-auto\"\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 9999,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        overflowY: 'auto'\n      }}\n    >\n      <div\n        className=\"bg-white rounded-2xl p-6 max-w-md w-full mx-4 shadow-2xl border border-gray-100 my-8\"\n        style={{\n          backgroundColor: 'white',\n          borderRadius: '16px',\n          padding: '24px',\n          maxWidth: '28rem',\n          width: '90%',\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n          border: '1px solid #f3f4f6',\n          maxHeight: '90vh',\n          overflowY: 'auto'\n        }}\n      >\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 -m-6 mb-6 px-6 py-4 text-white rounded-t-2xl\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"p-2 bg-white/20 rounded-lg\">\n                <TbBrain className=\"w-6 h-6\" />\n              </div>\n              <div>\n                <h2 className=\"text-xl font-bold\">Try BrainWave Free</h2>\n                <p className=\"text-blue-100 text-sm\">Experience our platform with a sample quiz</p>\n              </div>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 hover:bg-white/20 rounded-lg transition-colors\"\n            >\n              <TbX className=\"w-5 h-5\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Form */}\n        <div className=\"space-y-6\">\n          {/* Name Input */}\n          <div className=\"space-y-2\">\n            <label className=\"flex items-center space-x-2 text-sm font-medium text-gray-700\">\n              <TbUser className=\"w-4 h-4\" />\n              <span>Your Name</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.name}\n              onChange={(e) => handleInputChange(\"name\", e.target.value)}\n              placeholder=\"Enter your full name\"\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\n              maxLength={50}\n            />\n          </div>\n\n          {/* Level Selection */}\n          <div className=\"space-y-3\">\n            <label className=\"flex items-center space-x-2 text-sm font-medium text-gray-700\">\n              <TbSchool className=\"w-4 h-4\" />\n              <span>Education Level</span>\n            </label>\n            <div className=\"grid gap-3\">\n              {[\n                { value: \"primary\", label: \"Primary\", description: \"Classes 1-7\", icon: \"🌱\" },\n                { value: \"secondary\", label: \"Secondary\", description: \"Form 1-4\", icon: \"📚\" },\n                { value: \"advance\", label: \"Advance\", description: \"Form 5-6\", icon: \"🎓\" }\n              ].map((level) => (\n                <button\n                  key={level.value}\n                  type=\"button\"\n                  onClick={() => handleInputChange(\"level\", level.value)}\n                  className={`p-4 border-2 rounded-lg text-left transition-all ${\n                    formData.level === level.value\n                      ? \"border-blue-500 bg-blue-50 text-blue-700\"\n                      : \"border-gray-200 hover:border-blue-300 hover:bg-blue-50\"\n                  }`}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-2xl\">{level.icon}</span>\n                    <div>\n                      <div className=\"font-medium\">{level.label}</div>\n                      <div className=\"text-sm text-gray-500\">{level.description}</div>\n                    </div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Class Selection */}\n          {formData.level && (\n            <div className=\"space-y-3\">\n              <label className=\"text-sm font-medium text-gray-700\">\n                Select Your Class\n              </label>\n              <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-2\">\n                {(formData.level === \"primary\" ? [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"] :\n                  formData.level === \"secondary\" ? [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"] :\n                  [\"Form-5\", \"Form-6\"]).map((classOption) => (\n                  <button\n                    key={classOption}\n                    type=\"button\"\n                    onClick={() => handleInputChange(\"class\", classOption)}\n                    className={`p-2 sm:p-3 border-2 rounded-lg text-center font-medium transition-all text-sm sm:text-base ${\n                      formData.class === classOption\n                        ? \"border-blue-500 bg-blue-500 text-white\"\n                        : \"border-gray-200 hover:border-blue-300 hover:bg-blue-50\"\n                    }`}\n                  >\n                    {classOption}\n                  </button>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Submit Button */}\n          <button\n            onClick={handleSubmit}\n            disabled={loading || !formData.name || !formData.level || !formData.class}\n            className={`w-full py-4 px-6 rounded-lg font-medium flex items-center justify-center space-x-2 transition-all ${\n              loading || !formData.name || !formData.level || !formData.class\n                ? \"bg-gray-300 text-gray-500 cursor-not-allowed\"\n                : \"bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl\"\n            }`}\n          >\n            {loading ? (\n              <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n            ) : (\n              <>\n                <span>Start Free Trial</span>\n                <TbArrowRight className=\"w-5 h-5\" />\n              </>\n            )}\n          </button>\n\n          {/* Info Text */}\n          <div className=\"text-center text-sm text-gray-500\">\n            <p>No registration required for trial</p>\n            <p className=\"mt-1\">Experience one quiz to see what BrainWave offers</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TryForFreeModal;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js", ["769"], [], "import React, { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport {\n  TbTrophy,\n  TbCheck,\n  TbX,\n  TbClock,\n  TbBrain,\n  TbArrowRight,\n  TbStar,\n  TbUsers,\n  TbBook,\n  TbMessageCircle,\n  TbChartBar,\n  TbSettings\n} from \"react-icons/tb\";\n\nconst TrialQuizResult = ({ result, onTryAnother, onRegister }) => {\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  const getPerformanceMessage = (percentage) => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n\n  const premiumFeatures = [\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Access comprehensive study materials, notes, and resources\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\"\n    },\n    {\n      icon: TbChartBar,\n      title: \"Ranking System\",\n      description: \"Compete with other students and track your progress\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\"\n    },\n    {\n      icon: TbUsers,\n      title: \"Unlimited Quizzes\",\n      description: \"Take as many quizzes as you want across all subjects\"\n    },\n    {\n      icon: TbStar,\n      title: \"Progress Tracking\",\n      description: \"Detailed analytics and performance insights\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-6 sm:py-12 px-4\">\n      <div className=\"max-w-6xl mx-auto\">\n        {/* Animated Result Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n          className=\"text-center mb-8 sm:mb-12\"\n        >\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.3, type: \"spring\", stiffness: 200 }}\n            className={`inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg`}\n            onAnimationComplete={() => setAnimationComplete(true)}\n          >\n            <TbTrophy className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-white\" />\n          </motion.div>\n\n          <motion.h1\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\"\n          >\n            Quiz Complete! 🎉\n          </motion.h1>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.8 }}\n            className={`inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`}\n          >\n            <p className={`text-xl sm:text-2xl font-bold ${performance.color}`}>\n              {performance.message}\n            </p>\n          </motion.div>\n        </motion.div>\n\n        {/* Animated Score Card */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.6, delay: 1.0 }}\n          className=\"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\"\n        >\n          {/* Score Header */}\n          <div className={`bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`}>\n            <div className=\"text-center\">\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ duration: 0.8, delay: 1.2, type: \"spring\" }}\n                className=\"text-6xl sm:text-7xl lg:text-8xl font-bold text-white mb-2\"\n              >\n                {result.percentage}%\n              </motion.div>\n              <div className=\"text-white/90 text-lg sm:text-xl\">\n                Your Score\n              </div>\n            </div>\n          </div>\n\n          {/* Score Details */}\n          <div className=\"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\"\n        >\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\">\n              {[\n                {\n                  label: \"Total Questions\",\n                  value: result.totalQuestions,\n                  icon: TbBook,\n                  color: \"blue\",\n                  delay: 1.4\n                },\n                {\n                  label: \"Correct Answers\",\n                  value: result.correctAnswers,\n                  icon: TbCheck,\n                  color: \"green\",\n                  delay: 1.6\n                },\n                {\n                  label: \"Wrong Answers\",\n                  value: result.wrongAnswers,\n                  icon: TbX,\n                  color: \"red\",\n                  delay: 1.8\n                },\n                {\n                  label: \"Time Taken\",\n                  value: formatTime(result.timeSpent),\n                  icon: TbClock,\n                  color: \"purple\",\n                  delay: 2.0\n                }\n              ].map((stat, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: stat.delay }}\n                  className={`p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`}\n                >\n                  <div className={`w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`}>\n                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />\n                  </div>\n                  <div className={`text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`}>\n                    {stat.value}\n                  </div>\n                  <div className=\"text-sm text-gray-600 font-medium\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Pass/Fail Status */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 2.2 }}\n              className=\"text-center mb-8\"\n            >\n              <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${\n                isPassed\n                  ? 'bg-green-100 text-green-700 border-2 border-green-200'\n                  : 'bg-red-100 text-red-700 border-2 border-red-200'\n              }`}>\n                {isPassed ? (\n                  <TbCheck className=\"w-6 h-6\" />\n                ) : (\n                  <TbX className=\"w-6 h-6\" />\n                )}\n                <span>\n                  {isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'}\n                </span>\n              </div>\n            </motion.div>\n\n            {/* Show Details Button */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 2.4 }}\n              className=\"text-center\"\n            >\n              <button\n                onClick={() => setShowDetails(!showDetails)}\n                className=\"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\"\n              >\n                <TbChartBar className=\"w-5 h-5\" />\n                <span>{showDetails ? 'Hide Question Summary' : 'View Question Summary'}</span>\n              </button>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Register Now Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 2.6 }}\n          className=\"text-center mb-8\"\n        >\n          <Link to=\"/register\">\n            <motion.button\n              className=\"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <span>Register Now</span>\n              <TbArrowRight className=\"w-6 h-6\" />\n            </motion.button>\n          </Link>\n        </motion.div>\n\n        {/* Unlock Features Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 2.8 }}\n          className=\"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\">\n            <h3 className=\"text-2xl sm:text-3xl font-bold mb-2\">\n              🔓 Unlock These Amazing Features\n            </h3>\n            <p className=\"text-blue-100 text-lg\">\n              Join thousands of students already excelling with BrainWave\n            </p>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"p-6 sm:p-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 3.0 + (0.1 * index) }}\n                  className=\"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\"\n                >\n                  <div className=\"flex items-center space-x-4 mb-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <feature.icon className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <h4 className=\"text-lg font-bold text-gray-800\">{feature.title}</h4>\n                  </div>\n                  <p className=\"text-gray-600 leading-relaxed\">{feature.description}</p>\n                  <div className=\"mt-4 flex items-center text-blue-600 font-medium\">\n                    <TbStar className=\"w-5 h-5 mr-2\" />\n                    <span className=\"text-sm\">Premium Feature</span>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Better Quiz Features */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.4 }}\n              className=\"mt-8 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center\">\n                <TbBrain className=\"w-6 h-6 mr-2 text-purple-600\" />\n                Advanced Quiz Features & Maximum Control\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Multiple Subject Selection */}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.6 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-purple-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbBook className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Multiple Subject Selection</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Choose from <strong>15+ subjects</strong> across all levels</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Mix and match subjects in custom quizzes</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Subject-specific performance tracking</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-purple-500 mr-2\">•</span>\n                      <span>Cross-subject comparison analytics</span>\n                    </li>\n                  </ul>\n                </motion.div>\n\n                {/* Maximum Control */}\n                <motion.div\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 3.8 }}\n                  className=\"bg-white rounded-xl p-5 shadow-sm border border-blue-100\"\n                >\n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center mr-3\">\n                      <TbSettings className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <h5 className=\"font-bold text-gray-800\">Maximum Quiz Control</h5>\n                  </div>\n                  <ul className=\"text-sm text-gray-600 space-y-2\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Set custom <strong>time limits</strong> (5-180 minutes)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Choose question count (5-100 questions)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Select difficulty levels (Easy, Medium, Hard)</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      <span>Pause and resume quiz sessions</span>\n                    </li>\n                  </ul>\n                </motion.div>\n              </div>\n\n              {/* Advanced Features */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: 4.0 }}\n                className=\"mt-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl p-5\"\n              >\n                <h5 className=\"font-bold text-gray-800 mb-4 text-center\">🚀 Advanced Quiz Features</h5>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  {[\n                    { icon: \"⏱️\", title: \"Smart Timer\", desc: \"Adaptive timing\" },\n                    { icon: \"🎯\", title: \"Targeted Practice\", desc: \"Weak area focus\" },\n                    { icon: \"📊\", title: \"Live Analytics\", desc: \"Real-time insights\" },\n                    { icon: \"🏆\", title: \"Achievement System\", desc: \"Unlock rewards\" }\n                  ].map((feature, index) => (\n                    <motion.div\n                      key={index}\n                      initial={{ opacity: 0, scale: 0.9 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ duration: 0.3, delay: 4.2 + (0.1 * index) }}\n                      className=\"text-center p-3 bg-white rounded-lg shadow-sm\"\n                    >\n                      <div className=\"text-xl mb-1\">{feature.icon}</div>\n                      <div className=\"font-semibold text-xs text-gray-800\">{feature.title}</div>\n                      <div className=\"text-xs text-gray-600\">{feature.desc}</div>\n                    </motion.div>\n                  ))}\n                </div>\n              </motion.div>\n            </motion.div>\n\n            {/* Additional Benefits */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.6 }}\n              className=\"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4 text-center\">\n                🎯 Why Students Choose BrainWave\n              </h4>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n                {[\n                  { icon: \"🚀\", title: \"Instant Access\", desc: \"Start learning immediately\" },\n                  { icon: \"📱\", title: \"Mobile Friendly\", desc: \"Study anywhere, anytime\" },\n                  { icon: \"🎓\", title: \"Expert Content\", desc: \"Created by top educators\" },\n                  { icon: \"🏆\", title: \"Proven Results\", desc: \"98% success rate\" }\n                ].map((benefit, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.4, delay: 3.8 + (0.1 * index) }}\n                    className=\"text-center p-4 bg-white rounded-xl shadow-sm\"\n                  >\n                    <div className=\"text-2xl mb-2\">{benefit.icon}</div>\n                    <div className=\"font-semibold text-gray-800 mb-1\">{benefit.title}</div>\n                    <div className=\"text-sm text-gray-600\">{benefit.desc}</div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Call to Action */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 4.2 }}\n              className=\"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\"\n            >\n              <h4 className=\"text-xl font-bold mb-2\">Ready to Excel? 🌟</h4>\n              <p className=\"text-blue-100 mb-4\">Join BrainWave today and unlock your full potential!</p>\n              <div className=\"flex flex-col sm:flex-row gap-3 justify-center items-center\">\n                <Link to=\"/register\">\n                  <motion.button\n                    className=\"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <span>Create Free Account</span>\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n                </Link>\n                <div className=\"text-blue-200 text-sm\">\n                  ✨ No credit card required • Start immediately\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Question Details */}\n        {showDetails && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl p-6 mb-8\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-800 mb-4\">Question Review</h3>\n            <div className=\"space-y-4\">\n              {result.questionResults?.map((q, index) => (\n                <div key={index} className={`p-4 rounded-lg border-2 ${\n                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'\n                }`}>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'\n                    }`}>\n                      {q.isCorrect ? <TbCheck className=\"w-4 h-4\" /> : <TbX className=\"w-4 h-4\" />}\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium text-gray-800 mb-2\">{q.question}</p>\n                      <div className=\"text-sm space-y-1\">\n                        <p>\n                          <span className=\"text-gray-600\">Your answer:</span>\n                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>\n                            {q.userAnswer || 'Not answered'}\n                          </span>\n                        </p>\n                        {!q.isCorrect && (\n                          <p>\n                            <span className=\"text-gray-600\">Correct answer:</span>\n                            <span className=\"ml-2 font-medium text-green-600\">{q.correctAnswer}</span>\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n        )}\n\n\n\n        {/* Action Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n        >\n          <button\n            onClick={onTryAnother}\n            className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n          >\n            Try Another Quiz\n          </button>\n          \n          <Link to=\"/\">\n            <button className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\">\n              Back to Home\n            </button>\n          </Link>\n        </motion.div>\n\n        {/* Trial Badge */}\n        <div className=\"text-center mt-8\">\n          <span className=\"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\">\n            🎯 Trial Mode - Register for unlimited access\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizResult;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js", ["770"], [], "import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbBrain, TbClock, TbQuestionMark, TbArrowRight, TbLoader } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { getTrialQuiz } from \"../../apicalls/trial\";\n\nconst TrialQuizSelection = ({ trialUserInfo, onQuizSelected, onBack }) => {\n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchTrialQuiz();\n  }, [trialUserInfo]);\n\n  const fetchTrialQuiz = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await getTrialQuiz({\n        level: trialUserInfo.level,\n        class: trialUserInfo.class\n      });\n\n      if (response.success) {\n        setQuiz(response.data);\n        console.log(\"✅ Trial quiz loaded:\", response.data);\n      } else {\n        setError(response.message || \"Failed to load trial quiz\");\n        message.error(response.message || \"Failed to load trial quiz\");\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching trial quiz:\", error);\n      setError(\"Something went wrong. Please try again.\");\n      message.error(\"Something went wrong. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleStartQuiz = () => {\n    if (quiz && quiz.exam) {\n      onQuizSelected({\n        ...quiz,\n        trialUserInfo\n      });\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { duration: 0.6, ease: \"easeOut\" }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, scale: 0.9 },\n    visible: { \n      opacity: 1, \n      scale: 1,\n      transition: { duration: 0.5, delay: 0.2 }\n    }\n  };\n\n  if (loading) {\n    return (\n      <motion.div\n        className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\">\n            <TbLoader className=\"w-8 h-8 text-blue-600 animate-spin\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">\n            Finding Your Perfect Trial Quiz\n          </h2>\n          <p className=\"text-gray-600\">\n            We're selecting a quiz that matches your level and class...\n          </p>\n        </div>\n      </motion.div>\n    );\n  }\n\n  if (error) {\n    return (\n      <motion.div\n        className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center\">\n            <TbQuestionMark className=\"w-8 h-8 text-red-600\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">\n            Quiz Not Available\n          </h2>\n          <p className=\"text-gray-600 mb-6\">\n            {error}\n          </p>\n          <div className=\"space-y-3\">\n            <button\n              onClick={fetchTrialQuiz}\n              className=\"w-full py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n            >\n              Try Again\n            </button>\n            <button\n              onClick={onBack}\n              className=\"w-full py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n            >\n              Go Back\n            </button>\n          </div>\n        </div>\n      </motion.div>\n    );\n  }\n\n  return (\n    <motion.div\n      className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      <div className=\"max-w-2xl w-full mx-auto\">\n        {/* Welcome Header */}\n        <motion.div\n          className=\"text-center mb-6 sm:mb-8\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <h1 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-2\">\n            Welcome, {trialUserInfo.name}! 👋\n          </h1>\n          <p className=\"text-base sm:text-lg text-gray-600 px-4\">\n            We've found the perfect quiz for your <span className=\"font-semibold text-blue-600\">{trialUserInfo.level} {trialUserInfo.class}</span> level\n          </p>\n        </motion.div>\n\n        {/* Quiz Card */}\n        <motion.div\n          className=\"bg-white rounded-2xl shadow-xl overflow-hidden\"\n          variants={cardVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {/* Quiz Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-4 sm:px-6 py-6 sm:py-8 text-white\">\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\n              <div className=\"p-2 sm:p-3 bg-white/20 rounded-xl\">\n                <TbBrain className=\"w-6 h-6 sm:w-8 sm:h-8\" />\n              </div>\n              <div className=\"min-w-0 flex-1\">\n                <h2 className=\"text-lg sm:text-2xl font-bold truncate\">{quiz?.exam?.name}</h2>\n                <p className=\"text-blue-100 text-sm sm:text-base\">\n                  {quiz?.exam?.subject} • {trialUserInfo.level.charAt(0).toUpperCase() + trialUserInfo.level.slice(1)} Level\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Quiz Details */}\n          <div className=\"p-4 sm:p-6\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8\">\n              <div className=\"text-center p-4 bg-blue-50 rounded-xl\">\n                <TbQuestionMark className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />\n                <div className=\"text-2xl font-bold text-gray-800\">\n                  {quiz?.trialInfo?.trialQuestionCount || quiz?.exam?.questions?.length || 0}\n                </div>\n                <div className=\"text-sm text-gray-600\">Questions</div>\n              </div>\n              \n              <div className=\"text-center p-4 bg-green-50 rounded-xl\">\n                <TbClock className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n                <div className=\"text-2xl font-bold text-gray-800\">\n                  {quiz?.trialInfo?.trialDuration || quiz?.exam?.duration || 0}\n                </div>\n                <div className=\"text-sm text-gray-600\">Minutes</div>\n              </div>\n              \n              <div className=\"text-center p-4 bg-purple-50 rounded-xl\">\n                <TbBrain className=\"w-8 h-8 text-purple-600 mx-auto mb-2\" />\n                <div className=\"text-2xl font-bold text-gray-800\">\n                  {quiz?.exam?.passingMarks || Math.ceil((quiz?.exam?.questions?.length || 0) * 0.6)}\n                </div>\n                <div className=\"text-sm text-gray-600\">Pass Mark</div>\n              </div>\n            </div>\n\n            {/* Simple Instructions */}\n            <div className=\"bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6\">\n              <div className=\"flex flex-wrap items-center justify-center gap-4 text-sm text-gray-700\">\n                <div className=\"flex items-center\">\n                  <TbClock className=\"w-4 h-4 mr-2 text-blue-600\" />\n                  <span><strong>{quiz?.trialInfo?.trialDuration || quiz?.exam?.duration || 0}</strong> minutes</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <TbQuestionMark className=\"w-4 h-4 mr-2 text-green-600\" />\n                  <span><strong>{quiz?.trialInfo?.trialQuestionCount || 5}</strong> questions</span>\n                </div>\n                <div className=\"flex items-center\">\n                  <TbBrain className=\"w-4 h-4 mr-2 text-purple-600\" />\n                  <span>Pass mark: <strong>{quiz?.exam?.passingMarks || Math.ceil((quiz?.exam?.questions?.length || 0) * 0.6)}</strong></span>\n                </div>\n                <div className=\"flex items-center\">\n                  <TbArrowRight className=\"w-4 h-4 mr-2 text-orange-600\" />\n                  <span>Click answers to select</span>\n                </div>\n              </div>\n            </div>\n\n\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-3\">\n              <button\n                onClick={onBack}\n                className=\"flex-1 py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n              >\n                Go Back\n              </button>\n              <motion.button\n                onClick={handleStartQuiz}\n                className=\"flex-1 py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span>Start Trial Quiz</span>\n                <TbArrowRight className=\"w-5 h-5\" />\n              </motion.button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Footer Note */}\n        <motion.div\n          className=\"text-center mt-6 text-sm text-gray-500\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.8 }}\n        >\n          After completing this trial, you'll be invited to register for full access to our platform\n        </motion.div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default TrialQuizSelection;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js", ["771"], [], "import React from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { \n  TbX, \n  TbBrain, \n  TbBook, \n  TbChartBar, \n  TbMessageCircle, \n  TbUsers,\n  TbStar,\n  TbArrowRight,\n  TbCheck,\n  TbInfinity,\n  TbTrophy,\n  TbBulb\n} from \"react-icons/tb\";\n\nconst TrialRegistrationPrompt = ({ isOpen, onClose, trialResult }) => {\n  const premiumFeatures = [\n    {\n      icon: TbInfinity,\n      title: \"Unlimited Quizzes\",\n      description: \"Access thousands of quizzes across all subjects and levels\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\"\n    },\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Comprehensive notes, videos, and resources for all subjects\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\"\n    },\n    {\n      icon: Tb<PERSON>hartBar,\n      title: \"Ranking System\",\n      description: \"Compete with students nationwide and track your progress\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\",\n      color: \"text-indigo-600\",\n      bg: \"bg-indigo-50\"\n    },\n    {\n      icon: TbTrophy,\n      title: \"Achievements\",\n      description: \"Earn badges and rewards for your learning milestones\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\"\n    }\n  ];\n\n  const benefits = [\n    \"Track your progress across all subjects\",\n    \"Get detailed performance analytics\",\n    \"Access past papers and exam preparation\",\n    \"Join study groups and discussions\",\n    \"Receive personalized study plans\",\n    \"Get instant feedback and explanations\"\n  ];\n\n  const modalVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 50 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      y: 0,\n      transition: { \n        type: \"spring\", \n        damping: 25, \n        stiffness: 300 \n      }\n    },\n    exit: { \n      opacity: 0, \n      scale: 0.8, \n      y: 50,\n      transition: { duration: 0.2 }\n    }\n  };\n\n  const overlayVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n    exit: { opacity: 0 }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n        variants={overlayVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        exit=\"exit\"\n      >\n        {/* Backdrop */}\n        <motion.div\n          className=\"absolute inset-0 bg-black/60 backdrop-blur-sm\"\n          onClick={onClose}\n        />\n        \n        {/* Modal */}\n        <motion.div\n          className=\"relative w-full max-w-4xl max-h-[90vh] mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden\"\n          variants={modalVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"exit\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-4 sm:px-6 py-4 sm:py-6 text-white relative overflow-hidden\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90\"></div>\n            <div className=\"relative z-10\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"p-2 bg-white/20 rounded-lg\">\n                    <TbStar className=\"w-6 h-6\" />\n                  </div>\n                  <div>\n                    <h2 className=\"text-xl sm:text-2xl font-bold\">Congratulations! 🎉</h2>\n                    <p className=\"text-blue-100 text-sm sm:text-base\">You've completed your trial quiz</p>\n                  </div>\n                </div>\n                <button\n                  onClick={onClose}\n                  className=\"p-2 hover:bg-white/20 rounded-lg transition-colors\"\n                >\n                  <TbX className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              {/* Trial Result Summary */}\n              {trialResult && (\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-4\">\n                  <div className=\"grid grid-cols-3 gap-4 text-center\">\n                    <div>\n                      <div className=\"text-2xl font-bold\">{trialResult.percentage}%</div>\n                      <div className=\"text-sm text-blue-100\">Score</div>\n                    </div>\n                    <div>\n                      <div className=\"text-2xl font-bold\">{trialResult.correctAnswers}/{trialResult.totalQuestions}</div>\n                      <div className=\"text-sm text-blue-100\">Correct</div>\n                    </div>\n                    <div>\n                      <div className=\"text-2xl font-bold\">{trialResult.passed ? 'PASSED' : 'FAILED'}</div>\n                      <div className=\"text-sm text-blue-100\">Result</div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-4 sm:p-6 overflow-y-auto max-h-[60vh]\">\n            {/* Unlock Message */}\n            <div className=\"text-center mb-8\">\n              <div className=\"w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\">\n                <TbBulb className=\"w-8 h-8 text-white\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-800 mb-2\">\n                Ready to Unlock Your Full Potential?\n              </h3>\n              <p className=\"text-gray-600 text-lg\">\n                Join thousands of students who are already excelling with BrainWave\n              </p>\n            </div>\n\n            {/* Premium Features Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8\">\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: 0.1 * index }}\n                  className={`p-4 rounded-xl border-2 border-gray-100 hover:border-blue-200 transition-all ${feature.bg}`}\n                >\n                  <feature.icon className={`w-8 h-8 ${feature.color} mb-3`} />\n                  <h4 className=\"font-semibold text-gray-800 mb-2\">{feature.title}</h4>\n                  <p className=\"text-sm text-gray-600\">{feature.description}</p>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Benefits List */}\n            <div className=\"bg-gray-50 rounded-xl p-6 mb-8\">\n              <h4 className=\"font-bold text-gray-800 mb-4 flex items-center\">\n                <TbCheck className=\"w-5 h-5 text-green-600 mr-2\" />\n                What You'll Get With Full Access:\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                {benefits.map((benefit, index) => (\n                  <div key={index} className=\"flex items-center space-x-2\">\n                    <TbCheck className=\"w-4 h-4 text-green-600 flex-shrink-0\" />\n                    <span className=\"text-sm text-gray-700\">{benefit}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Social Proof */}\n            <div className=\"bg-blue-50 rounded-xl p-6 mb-8\">\n              <div className=\"flex items-center justify-center space-x-8 text-center\">\n                <div>\n                  <div className=\"text-2xl font-bold text-blue-600\">10,000+</div>\n                  <div className=\"text-sm text-gray-600\">Active Students</div>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-blue-600\">50,000+</div>\n                  <div className=\"text-sm text-gray-600\">Quizzes Completed</div>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-blue-600\">95%</div>\n                  <div className=\"text-sm text-gray-600\">Success Rate</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"space-y-4\">\n              <Link to=\"/register\" className=\"block\">\n                <motion.button\n                  className=\"w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-bold text-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl flex items-center justify-center space-x-2\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <span>Create Free Account</span>\n                  <TbArrowRight className=\"w-5 h-5\" />\n                </motion.button>\n              </Link>\n\n              <Link to=\"/login\" className=\"block\">\n                <button className=\"w-full py-3 px-6 border-2 border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all\">\n                  Already have an account? Sign In\n                </button>\n              </Link>\n\n              <div className=\"text-center\">\n                <button\n                  onClick={onClose}\n                  className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n                >\n                  Maybe later\n                </button>\n              </div>\n            </div>\n\n            {/* Trust Indicators */}\n            <div className=\"mt-6 text-center text-xs text-gray-500\">\n              <p>✅ Free to join • ✅ No credit card required • ✅ Cancel anytime</p>\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default TrialRegistrationPrompt;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js", ["772", "773"], [], "import React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, TbClock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\n\nconst TrialQuizPlay = ({ quizData, onComplete, onBack }) => {\n  const { exam, trialUserInfo } = quizData;\n  const questions = exam.questions || [];\n  \n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n      \n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n\n  const goToQuestion = (index) => {\n    setCurrentQuestionIndex(index);\n  };\n\n  if (questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">No Questions Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz doesn't have any questions.</p>\n          <button\n            onClick={onBack}\n            className=\"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Go Back\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n\n  // Determine question type and prepare options\n  const questionType = currentQuestion?.type || currentQuestion?.answerType || 'mcq';\n  const isMCQ = questionType === 'mcq' || questionType === 'Options' || questionType === 'multiple-choice';\n  const isFillBlank = questionType === 'fill' || questionType === 'Fill in the Blank' || questionType === 'Free Text';\n\n  // Prepare options for MCQ questions\n  let questionOptions = [];\n  if (isMCQ) {\n    if (Array.isArray(currentQuestion?.options)) {\n      questionOptions = currentQuestion.options;\n    } else if (typeof currentQuestion?.options === 'object' && currentQuestion?.options !== null) {\n      questionOptions = Object.values(currentQuestion.options);\n    } else {\n      questionOptions = [currentQuestion?.optionA, currentQuestion?.optionB, currentQuestion?.optionC, currentQuestion?.optionD].filter(Boolean);\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50\">\n      {/* Professional Header */}\n      <div className=\"bg-white shadow-lg border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1\">\n              <button\n                onClick={onBack}\n                className=\"p-2 sm:p-3 hover:bg-gray-100 rounded-xl transition-all duration-200 flex-shrink-0 group\"\n              >\n                <TbArrowLeft className=\"w-5 h-5 sm:w-6 sm:h-6 text-gray-600 group-hover:text-gray-800\" />\n              </button>\n              <div className=\"min-w-0 flex-1\">\n                <h1 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 truncate\">{exam.name}</h1>\n                <div className=\"flex items-center space-x-2 mt-1\">\n                  <span className=\"text-sm sm:text-base text-gray-600\">{exam.subject}</span>\n                  <span className=\"text-gray-400\">•</span>\n                  <span className=\"text-sm sm:text-base font-medium text-blue-600\">\n                    Question {currentQuestionIndex + 1} of {questions.length}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-3 sm:space-x-4 flex-shrink-0\">\n              <div className={`flex items-center space-x-2 px-3 sm:px-4 py-2 sm:py-3 rounded-xl font-medium transition-all ${\n                timeLeft <= 60\n                  ? 'bg-red-100 text-red-700 border border-red-200'\n                  : 'bg-blue-100 text-blue-700 border border-blue-200'\n              }`}>\n                <TbClock className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n                <span className=\"text-sm sm:text-base\">{formatTime(timeLeft)}</span>\n              </div>\n\n              <div className=\"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-xl\">\n                <span className=\"text-sm font-medium text-gray-700\">\n                  {answeredQuestions}/{questions.length} answered\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Mobile progress indicator */}\n          <div className=\"sm:hidden mt-3 flex items-center justify-center\">\n            <div className=\"flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg\">\n              <span className=\"text-sm font-medium text-gray-700\">\n                {answeredQuestions}/{questions.length} answered\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex-1\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700\">Progress</span>\n                <span className=\"text-sm text-gray-500\">\n                  {Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}% Complete\n                </span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                <motion.div\n                  className=\"bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500\"\n                  initial={{ width: 0 }}\n                  animate={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}\n                />\n              </div>\n            </div>\n\n            {/* Quick navigation dots */}\n            <div className=\"hidden md:flex items-center space-x-1\">\n              {questions.map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => goToQuestion(index)}\n                  className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                    index === currentQuestionIndex\n                      ? 'bg-blue-600 scale-125'\n                      : selectedAnswers[questions[index]._id]\n                      ? 'bg-green-500'\n                      : 'bg-gray-300 hover:bg-gray-400'\n                  }`}\n                  title={`Question ${index + 1}`}\n                />\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Question Content */}\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12\">\n        <motion.div\n          key={currentQuestionIndex}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -20 }}\n          transition={{ duration: 0.4, ease: \"easeOut\" }}\n          className=\"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden\"\n        >\n          {/* Question Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-6 sm:px-8 lg:px-10 py-6 sm:py-8\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-xl flex items-center justify-center\">\n                  <span className=\"text-lg sm:text-xl font-bold text-white\">\n                    {currentQuestionIndex + 1}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"text-white/90 text-sm sm:text-base\">Question</div>\n                  <div className=\"text-white font-medium text-lg sm:text-xl\">\n                    {currentQuestionIndex + 1} of {questions.length}\n                  </div>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-white/90 text-sm\">Status</div>\n                <div className={`text-sm sm:text-base font-medium px-3 py-1 rounded-full ${\n                  selectedAnswers[currentQuestion._id]\n                    ? 'bg-green-500/20 text-green-100 border border-green-400/30'\n                    : 'bg-yellow-500/20 text-yellow-100 border border-yellow-400/30'\n                }`}>\n                  {selectedAnswers[currentQuestion._id] ? '✓ Answered' : '○ Pending'}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Question Content */}\n          <div className=\"px-6 sm:px-8 lg:px-10 py-8 sm:py-10 lg:py-12\">\n            <div className=\"mb-8 sm:mb-10\">\n              <h2 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-relaxed mb-6\">\n                {currentQuestion.name}\n              </h2>\n\n              {/* Question Image (if exists) */}\n              {currentQuestion.image && (\n                <div className=\"mb-8\">\n                  <div className=\"bg-gray-50 rounded-2xl p-4 sm:p-6 border border-gray-200\">\n                    <img\n                      src={currentQuestion.image}\n                      alt=\"Question\"\n                      className=\"max-w-full h-auto rounded-xl shadow-sm mx-auto\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Answer Section */}\n            <div className=\"mb-10 sm:mb-12\">\n              {isMCQ && questionOptions.length > 0 ? (\n                // Multiple Choice Questions\n                <div className=\"space-y-4 sm:space-y-5\">\n                  <h3 className=\"text-lg sm:text-xl font-semibold text-gray-800 mb-6\">\n                    Choose your answer:\n                  </h3>\n                  {questionOptions.map((option, index) => {\n                    const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n                    const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n\n                    return (\n                      <motion.button\n                        key={index}\n                        onClick={() => handleAnswerSelect(currentQuestion._id, optionLetter)}\n                        className={`w-full p-5 sm:p-6 text-left rounded-2xl border-2 transition-all duration-300 ${\n                          isSelected\n                            ? 'border-blue-500 bg-blue-50 text-blue-900 shadow-lg scale-[1.02]'\n                            : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md'\n                        }`}\n                        whileHover={{ scale: isSelected ? 1.02 : 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <div className=\"flex items-center space-x-4 sm:space-x-5\">\n                          <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 flex items-center justify-center font-bold text-lg ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-500 text-white'\n                              : 'border-gray-300 text-gray-600'\n                          }`}>\n                            {isSelected ? <TbCheck className=\"w-5 h-5 sm:w-6 sm:h-6\" /> : optionLetter}\n                          </div>\n                          <span className=\"flex-1 text-base sm:text-lg font-medium leading-relaxed\">\n                            {option}\n                          </span>\n                        </div>\n                      </motion.button>\n                    );\n                  })}\n                </div>\n              ) : (\n                // Fill-in-the-blank / Free Text Questions\n                <div className=\"space-y-6\">\n                  <h3 className=\"text-lg sm:text-xl font-semibold text-gray-800\">\n                    Type your answer:\n                  </h3>\n                  <div className=\"relative\">\n                    <input\n                      type=\"text\"\n                      value={selectedAnswers[currentQuestion._id] || ''}\n                      onChange={(e) => handleAnswerSelect(currentQuestion._id, e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"w-full p-5 sm:p-6 border-2 border-gray-300 rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-base sm:text-lg font-medium bg-gray-50 focus:bg-white\"\n                    />\n                    <div className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400\">\n                      ✏️\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Navigation Buttons */}\n            <div className=\"flex items-center justify-between pt-8 border-t border-gray-200\">\n              <motion.button\n                onClick={goToPrevious}\n                disabled={currentQuestionIndex === 0}\n                className={`flex items-center space-x-3 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 ${\n                  currentQuestionIndex === 0\n                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300 hover:shadow-md'\n                }`}\n                whileHover={currentQuestionIndex > 0 ? { scale: 1.02 } : {}}\n                whileTap={currentQuestionIndex > 0 ? { scale: 0.98 } : {}}\n              >\n                <TbArrowLeft className=\"w-5 h-5\" />\n                <span className=\"text-base sm:text-lg\">Previous</span>\n              </motion.button>\n\n              {isLastQuestion ? (\n                <motion.button\n                  onClick={handleSubmitQuiz}\n                  disabled={isSubmitting}\n                  className={`flex items-center space-x-3 px-8 sm:px-10 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-300 ${\n                    isSubmitting\n                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                      : 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl'\n                  }`}\n                  whileHover={!isSubmitting ? { scale: 1.02 } : {}}\n                  whileTap={!isSubmitting ? { scale: 0.98 } : {}}\n                >\n                  {isSubmitting ? (\n                    <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n                  ) : (\n                    <TbCheck className=\"w-5 h-5\" />\n                  )}\n                  <span className=\"text-base sm:text-lg\">\n                    {isSubmitting ? 'Submitting...' : 'Submit Quiz'}\n                  </span>\n                </motion.button>\n              ) : (\n                <motion.button\n                  onClick={goToNext}\n                  className=\"flex items-center space-x-3 px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <span className=\"text-base sm:text-lg\">Next Question</span>\n                  <TbArrowRight className=\"w-5 h-5\" />\n                </motion.button>\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Trial Watermark */}\n      <div className=\"fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg\">\n        Trial Mode\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizPlay;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js", ["774"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js", ["775"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js", ["776"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js", ["777", "778", "779"], [], {"ruleId": "780", "severity": 1, "message": "781", "line": 8, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 8, "endColumn": 21}, {"ruleId": "780", "severity": 1, "message": "784", "line": 8, "column": 23, "nodeType": "782", "messageId": "783", "endLine": 8, "endColumn": 34}, {"ruleId": "780", "severity": 1, "message": "785", "line": 13, "column": 8, "nodeType": "782", "messageId": "783", "endLine": 13, "endColumn": 23}, {"ruleId": "780", "severity": 1, "message": "786", "line": 15, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 16}, {"ruleId": "780", "severity": 1, "message": "787", "line": 15, "column": 18, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 33}, {"ruleId": "780", "severity": 1, "message": "788", "line": 15, "column": 35, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 42}, {"ruleId": "780", "severity": 1, "message": "789", "line": 15, "column": 44, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 47}, {"ruleId": "780", "severity": 1, "message": "790", "line": 15, "column": 49, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 62}, {"ruleId": "780", "severity": 1, "message": "791", "line": 15, "column": 64, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 72}, {"ruleId": "780", "severity": 1, "message": "792", "line": 15, "column": 74, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 80}, {"ruleId": "780", "severity": 1, "message": "793", "line": 15, "column": 82, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 92}, {"ruleId": "780", "severity": 1, "message": "794", "line": 15, "column": 94, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 100}, {"ruleId": "780", "severity": 1, "message": "795", "line": 15, "column": 102, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 108}, {"ruleId": "780", "severity": 1, "message": "796", "line": 16, "column": 8, "nodeType": "782", "messageId": "783", "endLine": 16, "endColumn": 29}, {"ruleId": "797", "severity": 1, "message": "798", "line": 110, "column": 6, "nodeType": "799", "endLine": 110, "endColumn": 8, "suggestions": "800"}, {"ruleId": "797", "severity": 1, "message": "801", "line": 157, "column": 6, "nodeType": "799", "endLine": 157, "endColumn": 33, "suggestions": "802"}, {"ruleId": "797", "severity": 1, "message": "803", "line": 164, "column": 6, "nodeType": "799", "endLine": 164, "endColumn": 25, "suggestions": "804"}, {"ruleId": "780", "severity": 1, "message": "805", "line": 195, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 195, "endColumn": 23}, {"ruleId": "780", "severity": 1, "message": "806", "line": 1, "column": 35, "nodeType": "782", "messageId": "783", "endLine": 1, "endColumn": 41}, {"ruleId": "797", "severity": 1, "message": "807", "line": 110, "column": 6, "nodeType": "799", "endLine": 110, "endColumn": 8, "suggestions": "808"}, {"ruleId": "797", "severity": 1, "message": "809", "line": 96, "column": 6, "nodeType": "799", "endLine": 96, "endColumn": 8, "suggestions": "810"}, {"ruleId": "797", "severity": 1, "message": "811", "line": 65, "column": 8, "nodeType": "799", "endLine": 65, "endColumn": 32, "suggestions": "812"}, {"ruleId": "780", "severity": 1, "message": "813", "line": 17, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 17, "endColumn": 31}, {"ruleId": "780", "severity": 1, "message": "814", "line": 17, "column": 33, "nodeType": "782", "messageId": "783", "endLine": 17, "endColumn": 43}, {"ruleId": "797", "severity": 1, "message": "815", "line": 777, "column": 6, "nodeType": "799", "endLine": 777, "endColumn": 81, "suggestions": "816"}, {"ruleId": "780", "severity": 1, "message": "817", "line": 19, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 19, "endColumn": 16}, {"ruleId": "780", "severity": 1, "message": "818", "line": 20, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 20, "endColumn": 11}, {"ruleId": "780", "severity": 1, "message": "819", "line": 29, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 29, "endColumn": 11}, {"ruleId": "780", "severity": 1, "message": "820", "line": 30, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 30, "endColumn": 11}, {"ruleId": "780", "severity": 1, "message": "821", "line": 31, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 31, "endColumn": 18}, {"ruleId": "780", "severity": 1, "message": "822", "line": 32, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 32, "endColumn": 9}, {"ruleId": "780", "severity": 1, "message": "823", "line": 33, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 33, "endColumn": 13}, {"ruleId": "780", "severity": 1, "message": "824", "line": 34, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 34, "endColumn": 8}, {"ruleId": "780", "severity": 1, "message": "825", "line": 35, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 35, "endColumn": 13}, {"ruleId": "780", "severity": 1, "message": "792", "line": 36, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 36, "endColumn": 9}, {"ruleId": "780", "severity": 1, "message": "826", "line": 38, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 38, "endColumn": 14}, {"ruleId": "780", "severity": 1, "message": "789", "line": 39, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 39, "endColumn": 6}, {"ruleId": "780", "severity": 1, "message": "827", "line": 40, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 40, "endColumn": 18}, {"ruleId": "780", "severity": 1, "message": "828", "line": 41, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 41, "endColumn": 15}, {"ruleId": "780", "severity": 1, "message": "829", "line": 42, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 42, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "830", "line": 43, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 43, "endColumn": 14}, {"ruleId": "797", "severity": 1, "message": "831", "line": 71, "column": 9, "nodeType": "832", "endLine": 75, "endColumn": 29}, {"ruleId": "780", "severity": 1, "message": "825", "line": 13, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 13, "endColumn": 13}, {"ruleId": "780", "severity": 1, "message": "833", "line": 14, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 14, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "834", "line": 15, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "823", "line": 17, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 17, "endColumn": 13}, {"ruleId": "780", "severity": 1, "message": "835", "line": 35, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 35, "endColumn": 18}, {"ruleId": "780", "severity": 1, "message": "836", "line": 35, "column": 20, "nodeType": "782", "messageId": "783", "endLine": 35, "endColumn": 31}, {"ruleId": "780", "severity": 1, "message": "837", "line": 43, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 43, "endColumn": 18}, {"ruleId": "780", "severity": 1, "message": "838", "line": 44, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 44, "endColumn": 18}, {"ruleId": "797", "severity": 1, "message": "839", "line": 135, "column": 6, "nodeType": "799", "endLine": 135, "endColumn": 8, "suggestions": "840"}, {"ruleId": "797", "severity": 1, "message": "841", "line": 139, "column": 6, "nodeType": "799", "endLine": 139, "endColumn": 60, "suggestions": "842"}, {"ruleId": "780", "severity": 1, "message": "795", "line": 16, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 16, "endColumn": 9}, {"ruleId": "780", "severity": 1, "message": "786", "line": 17, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 17, "endColumn": 9}, {"ruleId": "780", "severity": 1, "message": "843", "line": 18, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 18, "endColumn": 9}, {"ruleId": "797", "severity": 1, "message": "844", "line": 200, "column": 6, "nodeType": "799", "endLine": 200, "endColumn": 8, "suggestions": "845"}, {"ruleId": "797", "severity": 1, "message": "846", "line": 212, "column": 6, "nodeType": "799", "endLine": 212, "endColumn": 33, "suggestions": "847"}, {"ruleId": "780", "severity": 1, "message": "848", "line": 418, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 418, "endColumn": 19}, {"ruleId": "780", "severity": 1, "message": "849", "line": 2, "column": 18, "nodeType": "782", "messageId": "783", "endLine": 2, "endColumn": 33}, {"ruleId": "780", "severity": 1, "message": "850", "line": 21, "column": 53, "nodeType": "782", "messageId": "783", "endLine": 21, "endColumn": 67}, {"ruleId": "780", "severity": 1, "message": "796", "line": 24, "column": 8, "nodeType": "782", "messageId": "783", "endLine": 24, "endColumn": 29}, {"ruleId": "780", "severity": 1, "message": "835", "line": 75, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 75, "endColumn": 18}, {"ruleId": "780", "severity": 1, "message": "836", "line": 75, "column": 20, "nodeType": "782", "messageId": "783", "endLine": 75, "endColumn": 31}, {"ruleId": "780", "severity": 1, "message": "851", "line": 76, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 76, "endColumn": 19}, {"ruleId": "780", "severity": 1, "message": "852", "line": 76, "column": 21, "nodeType": "782", "messageId": "783", "endLine": 76, "endColumn": 33}, {"ruleId": "780", "severity": 1, "message": "853", "line": 77, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 77, "endColumn": 24}, {"ruleId": "780", "severity": 1, "message": "854", "line": 80, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 80, "endColumn": 27}, {"ruleId": "780", "severity": 1, "message": "855", "line": 82, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 82, "endColumn": 24}, {"ruleId": "780", "severity": 1, "message": "856", "line": 90, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 90, "endColumn": 18}, {"ruleId": "780", "severity": 1, "message": "857", "line": 91, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 91, "endColumn": 23}, {"ruleId": "797", "severity": 1, "message": "858", "line": 843, "column": 6, "nodeType": "799", "endLine": 843, "endColumn": 8, "suggestions": "859"}, {"ruleId": "780", "severity": 1, "message": "860", "line": 864, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 864, "endColumn": 24}, {"ruleId": "780", "severity": 1, "message": "861", "line": 1019, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 1019, "endColumn": 29}, {"ruleId": "780", "severity": 1, "message": "862", "line": 1507, "column": 39, "nodeType": "782", "messageId": "783", "endLine": 1507, "endColumn": 48}, {"ruleId": "863", "severity": 1, "message": "864", "line": 2102, "column": 27, "nodeType": "865", "messageId": "866", "endLine": 2102, "endColumn": 28}, {"ruleId": "780", "severity": 1, "message": "867", "line": 1, "column": 38, "nodeType": "782", "messageId": "783", "endLine": 1, "endColumn": 46}, {"ruleId": "780", "severity": 1, "message": "868", "line": 8, "column": 12, "nodeType": "782", "messageId": "783", "endLine": 8, "endColumn": 19}, {"ruleId": "797", "severity": 1, "message": "869", "line": 58, "column": 8, "nodeType": "799", "endLine": 58, "endColumn": 10, "suggestions": "870"}, {"ruleId": "780", "severity": 1, "message": "871", "line": 5, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 5, "endColumn": 14}, {"ruleId": "780", "severity": 1, "message": "872", "line": 31, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 31, "endColumn": 17}, {"ruleId": "797", "severity": 1, "message": "873", "line": 241, "column": 6, "nodeType": "799", "endLine": 241, "endColumn": 35, "suggestions": "874"}, {"ruleId": "780", "severity": 1, "message": "875", "line": 12, "column": 8, "nodeType": "782", "messageId": "783", "endLine": 12, "endColumn": 17}, {"ruleId": "780", "severity": 1, "message": "876", "line": 31, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 31, "endColumn": 9}, {"ruleId": "780", "severity": 1, "message": "877", "line": 43, "column": 19, "nodeType": "782", "messageId": "783", "endLine": 43, "endColumn": 29}, {"ruleId": "797", "severity": 1, "message": "878", "line": 182, "column": 6, "nodeType": "799", "endLine": 182, "endColumn": 8, "suggestions": "879"}, {"ruleId": "780", "severity": 1, "message": "880", "line": 1, "column": 38, "nodeType": "782", "messageId": "783", "endLine": 1, "endColumn": 44}, {"ruleId": "780", "severity": 1, "message": "881", "line": 4, "column": 40, "nodeType": "782", "messageId": "783", "endLine": 4, "endColumn": 46}, {"ruleId": "780", "severity": 1, "message": "875", "line": 5, "column": 8, "nodeType": "782", "messageId": "783", "endLine": 5, "endColumn": 17}, {"ruleId": "780", "severity": 1, "message": "796", "line": 9, "column": 8, "nodeType": "782", "messageId": "783", "endLine": 9, "endColumn": 29}, {"ruleId": "780", "severity": 1, "message": "882", "line": 18, "column": 8, "nodeType": "782", "messageId": "783", "endLine": 18, "endColumn": 13}, {"ruleId": "780", "severity": 1, "message": "883", "line": 63, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 63, "endColumn": 20}, {"ruleId": "797", "severity": 1, "message": "884", "line": 113, "column": 6, "nodeType": "799", "endLine": 113, "endColumn": 26, "suggestions": "885"}, {"ruleId": "797", "severity": 1, "message": "886", "line": 145, "column": 6, "nodeType": "799", "endLine": 145, "endColumn": 8, "suggestions": "887"}, {"ruleId": "797", "severity": 1, "message": "888", "line": 265, "column": 6, "nodeType": "799", "endLine": 265, "endColumn": 20, "suggestions": "889"}, {"ruleId": "780", "severity": 1, "message": "875", "line": 3, "column": 8, "nodeType": "782", "messageId": "783", "endLine": 3, "endColumn": 17}, {"ruleId": "780", "severity": 1, "message": "890", "line": 10, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 10, "endColumn": 14}, {"ruleId": "780", "severity": 1, "message": "891", "line": 10, "column": 25, "nodeType": "782", "messageId": "783", "endLine": 10, "endColumn": 30}, {"ruleId": "780", "severity": 1, "message": "892", "line": 10, "column": 32, "nodeType": "782", "messageId": "783", "endLine": 10, "endColumn": 37}, {"ruleId": "780", "severity": 1, "message": "893", "line": 10, "column": 39, "nodeType": "782", "messageId": "783", "endLine": 10, "endColumn": 45}, {"ruleId": "780", "severity": 1, "message": "894", "line": 19, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 19, "endColumn": 21}, {"ruleId": "780", "severity": 1, "message": "895", "line": 21, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 21, "endColumn": 14}, {"ruleId": "780", "severity": 1, "message": "896", "line": 22, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 22, "endColumn": 22}, {"ruleId": "780", "severity": 1, "message": "897", "line": 33, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 33, "endColumn": 30}, {"ruleId": "797", "severity": 1, "message": "898", "line": 110, "column": 6, "nodeType": "799", "endLine": 110, "endColumn": 32, "suggestions": "899"}, {"ruleId": "797", "severity": 1, "message": "886", "line": 144, "column": 6, "nodeType": "799", "endLine": 144, "endColumn": 8, "suggestions": "900"}, {"ruleId": "780", "severity": 1, "message": "901", "line": 146, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 146, "endColumn": 21}, {"ruleId": "780", "severity": 1, "message": "902", "line": 228, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 228, "endColumn": 33}, {"ruleId": "780", "severity": 1, "message": "903", "line": 238, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 238, "endColumn": 32}, {"ruleId": "780", "severity": 1, "message": "904", "line": 286, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 286, "endColumn": 26}, {"ruleId": "780", "severity": 1, "message": "905", "line": 305, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 305, "endColumn": 19}, {"ruleId": "797", "severity": 1, "message": "886", "line": 316, "column": 6, "nodeType": "799", "endLine": 316, "endColumn": 8, "suggestions": "906"}, {"ruleId": "797", "severity": 1, "message": "907", "line": 323, "column": 6, "nodeType": "799", "endLine": 323, "endColumn": 19, "suggestions": "908"}, {"ruleId": "780", "severity": 1, "message": "909", "line": 19, "column": 11, "nodeType": "782", "messageId": "783", "endLine": 19, "endColumn": 24}, {"ruleId": "910", "severity": 1, "message": "911", "line": 73, "column": 111, "nodeType": "912", "messageId": "913", "endLine": 73, "endColumn": 112, "suggestions": "914"}, {"ruleId": "910", "severity": 1, "message": "911", "line": 95, "column": 89, "nodeType": "912", "messageId": "913", "endLine": 95, "endColumn": 90, "suggestions": "915"}, {"ruleId": "780", "severity": 1, "message": "848", "line": 7, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 7, "endColumn": 19}, {"ruleId": "797", "severity": 1, "message": "916", "line": 126, "column": 6, "nodeType": "799", "endLine": 126, "endColumn": 32, "suggestions": "917", "suppressions": "918"}, {"ruleId": "780", "severity": 1, "message": "919", "line": 1, "column": 17, "nodeType": "782", "messageId": "783", "endLine": 1, "endColumn": 26}, {"ruleId": "780", "severity": 1, "message": "920", "line": 1, "column": 28, "nodeType": "782", "messageId": "783", "endLine": 1, "endColumn": 36}, {"ruleId": "780", "severity": 1, "message": "921", "line": 20, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 20, "endColumn": 8}, {"ruleId": "780", "severity": 1, "message": "922", "line": 21, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 21, "endColumn": 11}, {"ruleId": "780", "severity": 1, "message": "818", "line": 22, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 22, "endColumn": 11}, {"ruleId": "797", "severity": 1, "message": "923", "line": 95, "column": 6, "nodeType": "799", "endLine": 95, "endColumn": 15, "suggestions": "924"}, {"ruleId": "780", "severity": 1, "message": "925", "line": 7, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 7, "endColumn": 14}, {"ruleId": "797", "severity": 1, "message": "926", "line": 32, "column": 6, "nodeType": "799", "endLine": 32, "endColumn": 15, "suggestions": "927"}, {"ruleId": "780", "severity": 1, "message": "928", "line": 3, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 3, "endColumn": 25}, {"ruleId": "780", "severity": 1, "message": "929", "line": 8, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 8, "endColumn": 9}, {"ruleId": "780", "severity": 1, "message": "930", "line": 20, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 20, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "931", "line": 21, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 21, "endColumn": 15}, {"ruleId": "780", "severity": 1, "message": "932", "line": 30, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 30, "endColumn": 21}, {"ruleId": "797", "severity": 1, "message": "933", "line": 56, "column": 6, "nodeType": "799", "endLine": 56, "endColumn": 8, "suggestions": "934"}, {"ruleId": "780", "severity": 1, "message": "824", "line": 11, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 11, "endColumn": 8}, {"ruleId": "780", "severity": 1, "message": "935", "line": 12, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 12, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "936", "line": 13, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 13, "endColumn": 9}, {"ruleId": "780", "severity": 1, "message": "937", "line": 1, "column": 38, "nodeType": "782", "messageId": "783", "endLine": 1, "endColumn": 46}, {"ruleId": "780", "severity": 1, "message": "938", "line": 59, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 59, "endColumn": 22}, {"ruleId": "780", "severity": 1, "message": "939", "line": 112, "column": 23, "nodeType": "782", "messageId": "783", "endLine": 112, "endColumn": 34}, {"ruleId": "780", "severity": 1, "message": "940", "line": 2, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 2, "endColumn": 16}, {"ruleId": "941", "severity": 1, "message": "942", "line": 69, "column": 3, "nodeType": "943", "messageId": "944", "endLine": 90, "endColumn": 5}, {"ruleId": "780", "severity": 1, "message": "945", "line": 198, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 198, "endColumn": 22}, {"ruleId": "780", "severity": 1, "message": "946", "line": 5, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 5, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "834", "line": 12, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 12, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "947", "line": 13, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 13, "endColumn": 12}, {"ruleId": "780", "severity": 1, "message": "948", "line": 62, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 62, "endColumn": 32}, {"ruleId": "780", "severity": 1, "message": "949", "line": 128, "column": 5, "nodeType": "782", "messageId": "783", "endLine": 128, "endColumn": 14}, {"ruleId": "780", "severity": 1, "message": "950", "line": 11, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 11, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "793", "line": 18, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 18, "endColumn": 13}, {"ruleId": "780", "severity": 1, "message": "951", "line": 28, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 28, "endColumn": 17}, {"ruleId": "780", "severity": 1, "message": "950", "line": 11, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 11, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "952", "line": 25, "column": 11, "nodeType": "782", "messageId": "783", "endLine": 25, "endColumn": 15}, {"ruleId": "780", "severity": 1, "message": "820", "line": 7, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 7, "endColumn": 11}, {"ruleId": "780", "severity": 1, "message": "821", "line": 10, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 10, "endColumn": 18}, {"ruleId": "780", "severity": 1, "message": "833", "line": 12, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 12, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "795", "line": 13, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 13, "endColumn": 9}, {"ruleId": "780", "severity": 1, "message": "829", "line": 7, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 7, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "789", "line": 8, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 8, "endColumn": 6}, {"ruleId": "780", "severity": 1, "message": "793", "line": 9, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 9, "endColumn": 13}, {"ruleId": "780", "severity": 1, "message": "953", "line": 10, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 10, "endColumn": 10}, {"ruleId": "797", "severity": 1, "message": "954", "line": 78, "column": 6, "nodeType": "799", "endLine": 78, "endColumn": 14, "suggestions": "955"}, {"ruleId": "797", "severity": 1, "message": "956", "line": 46, "column": 6, "nodeType": "799", "endLine": 46, "endColumn": 8, "suggestions": "957", "suppressions": "958"}, {"ruleId": "780", "severity": 1, "message": "952", "line": 7, "column": 11, "nodeType": "782", "messageId": "783", "endLine": 7, "endColumn": 15}, {"ruleId": "780", "severity": 1, "message": "959", "line": 12, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 12, "endColumn": 11}, {"ruleId": "780", "severity": 1, "message": "833", "line": 14, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 14, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "824", "line": 16, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 16, "endColumn": 8}, {"ruleId": "780", "severity": 1, "message": "960", "line": 40, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 40, "endColumn": 17}, {"ruleId": "797", "severity": 1, "message": "961", "line": 44, "column": 6, "nodeType": "799", "endLine": 44, "endColumn": 8, "suggestions": "962"}, {"ruleId": "780", "severity": 1, "message": "950", "line": 12, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 12, "endColumn": 10}, {"ruleId": "780", "severity": 1, "message": "793", "line": 14, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 14, "endColumn": 13}, {"ruleId": "780", "severity": 1, "message": "940", "line": 2, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 2, "endColumn": 16}, {"ruleId": "780", "severity": 1, "message": "849", "line": 2, "column": 18, "nodeType": "782", "messageId": "783", "endLine": 2, "endColumn": 33}, {"ruleId": "780", "severity": 1, "message": "963", "line": 15, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 15, "endColumn": 21}, {"ruleId": "780", "severity": 1, "message": "964", "line": 21, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 21, "endColumn": 21}, {"ruleId": "780", "severity": 1, "message": "965", "line": 64, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 64, "endColumn": 22}, {"ruleId": "780", "severity": 1, "message": "966", "line": 84, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 84, "endColumn": 24}, {"ruleId": "780", "severity": 1, "message": "967", "line": 21, "column": 10, "nodeType": "782", "messageId": "783", "endLine": 21, "endColumn": 27}, {"ruleId": "797", "severity": 1, "message": "968", "line": 14, "column": 6, "nodeType": "799", "endLine": 14, "endColumn": 21, "suggestions": "969"}, {"ruleId": "780", "severity": 1, "message": "970", "line": 10, "column": 3, "nodeType": "782", "messageId": "783", "endLine": 10, "endColumn": 10}, {"ruleId": "797", "severity": 1, "message": "971", "line": 29, "column": 6, "nodeType": "799", "endLine": 29, "endColumn": 16, "suggestions": "972"}, {"ruleId": "780", "severity": 1, "message": "973", "line": 115, "column": 9, "nodeType": "782", "messageId": "783", "endLine": 115, "endColumn": 20}, {"ruleId": "797", "severity": 1, "message": "974", "line": 318, "column": 6, "nodeType": "799", "endLine": 318, "endColumn": 57, "suggestions": "975"}, {"ruleId": "976", "severity": 1, "message": "977", "line": 1070, "column": 29, "nodeType": "978", "endLine": 1079, "endColumn": 31}, {"ruleId": "797", "severity": 1, "message": "979", "line": 36, "column": 6, "nodeType": "799", "endLine": 36, "endColumn": 8, "suggestions": "980"}, {"ruleId": "780", "severity": 1, "message": "892", "line": 2, "column": 27, "nodeType": "782", "messageId": "783", "endLine": 2, "endColumn": 32}, {"ruleId": "780", "severity": 1, "message": "890", "line": 2, "column": 34, "nodeType": "782", "messageId": "783", "endLine": 2, "endColumn": 38}, {"ruleId": "797", "severity": 1, "message": "884", "line": 113, "column": 6, "nodeType": "799", "endLine": 113, "endColumn": 8, "suggestions": "981"}, "no-unused-vars", "'HideLoading' is defined but never used.", "Identifier", "unusedVar", "'ShowLoading' is defined but never used.", "'AdminNavigation' is defined but never used.", "'TbHome' is defined but never used.", "'TbBrandTanzania' is defined but never used.", "'TbMenu2' is defined but never used.", "'TbX' is defined but never used.", "'TbChevronDown' is defined but never used.", "'TbLogout' is defined but never used.", "'TbUser' is defined but never used.", "'TbSettings' is defined but never used.", "'TbBell' is defined but never used.", "'TbStar' is defined but never used.", "'OnlineStatusIndicator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch', 'getUserData', 'navigate', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["982"], "React Hook useEffect has missing dependencies: 'dispatch', 'user?.isAdmin', 'user?.paymentRequired', and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["983"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["984"], "'getButtonClass' is assigned a value but never used.", "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["985"], "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["986"], "React Hook useEffect has a missing dependency: 'paymentInProgress'. Either include it or remove the dependency array.", ["987"], "'extractUserResultData' is defined but never used.", "'safeNumber' is defined but never used.", "React Hook useCallback has a missing dependency: 'startTime'. Either include it or remove the dependency array.", ["988"], "'FaChevronDown' is defined but never used.", "'FaSearch' is defined but never used.", "'TbSearch' is defined but never used.", "'TbFilter' is defined but never used.", "'TbSortAscending' is defined but never used.", "'TbPlay' is defined but never used.", "'TbDownload' is defined but never used.", "'TbEye' is defined but never used.", "'TbCalendar' is defined but never used.", "'TbChevronUp' is defined but never used.", "'TbAlertTriangle' is defined but never used.", "'TbInfoCircle' is defined but never used.", "'TbCheck' is defined but never used.", "'TbSubtitles' is defined but never used.", "The 'allPossibleClasses' conditional could make the dependencies of useCallback Hook (at line 127) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'allPossibleClasses' in its own useMemo() Hook.", "VariableDeclarator", "'TbClock' is defined but never used.", "'TbAward' is defined but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'isMobile' is assigned a value but never used.", "'isTablet' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["989"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["990"], "'TbBolt' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExams', 'getUserResults', and 'user'. Either include them or remove the dependency array.", ["991"], "React Hook useEffect has a missing dependency: 'getExams'. Either include it or remove the dependency array.", ["992"], "'formatTime' is assigned a value but never used.", "'AnimatePresence' is defined but never used.", "'getUserRanking' is defined but never used.", "'showStats' is assigned a value but never used.", "'setShowStats' is assigned a value but never used.", "'animationPhase' is assigned a value but never used.", "'currentUserLeague' is assigned a value but never used.", "'showLeagueView' is assigned a value but never used.", "'headerRef' is assigned a value but never used.", "'currentUserRef' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFullUserData', 'fetchRankingData', 'motivationalQuotes', 'rankingData', and 'user'. Either include them or remove the dependency array.", ["993"], "'otherPerformers' is assigned a value but never used.", "'getSubscriptionBadge' is assigned a value but never used.", "'leagueKey' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'y'.", "ObjectExpression", "unexpected", "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserData'. Either include them or remove the dependency array.", ["994"], "'TbDashboard' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["995"], "'PageTitle' is defined but never used.", "'TbPlus' is defined but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["996"], "'useRef' is defined but never used.", "'Avatar' is defined but never used.", "'image' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["997"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["998"], "React Hook useEffect has a missing dependency: 'form2'. Either include it or remove the dependency array.", ["999"], "'Form' is defined but never used.", "'Modal' is defined but never used.", "'Input' is defined but never used.", "'Button' is defined but never used.", "'userRanking' is assigned a value but never used.", "'edit' is assigned a value but never used.", "'imagePreview' is assigned a value but never used.", "'showLevelChangeModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["1000"], ["1001"], "'handleChange' is assigned a value but never used.", "'handleLevelChangeConfirm' is assigned a value but never used.", "'handleLevelChangeCancel' is assigned a value but never used.", "'handleImageUpload' is assigned a value but never used.", "'verifyUser' is assigned a value but never used.", ["1002"], "React Hook useEffect has a missing dependency: 'fetchUserRankingData'. Either include it or remove the dependency array.", ["1003"], "'restoredLines' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["1004", "1005"], ["1006", "1007"], "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["1008"], ["1009"], "'useEffect' is defined but never used.", "'useState' is defined but never used.", "'FaEye' is defined but never used.", "'FaFilter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["1010"], "'Title' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleAutoRefresh'. Either include it or remove the dependency array.", ["1011"], "'validateSession' is defined but never used.", "'FaHome' is defined but never used.", "'FaRobot' is defined but never used.", "'FaSignOutAlt' is defined but never used.", "'handleLogout' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'inspiringQuotes.length'. Either include it or remove the dependency array.", ["1012"], "'TbPhoto' is defined but never used.", "'TbEdit' is defined but never used.", "'Fragment' is defined but never used.", "'getTimerColor' is assigned a value but never used.", "'toggleTheme' is assigned a value but never used.", "'motion' is defined but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'containerRef' is assigned a value but never used.", "'TbMedal' is defined but never used.", "'TbDiamond' is defined but never used.", "'triggerLevelUpAnimation' is assigned a value but never used.", "'xpAwarded' is assigned a value but never used.", "'TbRobot' is defined but never used.", "'dispatch' is assigned a value but never used.", "'user' is assigned a value but never used.", "'TbTrash' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchNotifications' and 'notifications.length'. Either include them or remove the dependency array.", ["1013"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1014"], ["1015"], "'TbTarget' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["1016"], "'levelOptions' is assigned a value but never used.", "'classOptions' is assigned a value but never used.", "'modalVariants' is assigned a value but never used.", "'overlayVariants' is assigned a value but never used.", "'animationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTrialQuiz'. Either include it or remove the dependency array.", ["1017"], "'TbUsers' is defined but never used.", "React Hook useEffect has a missing dependency: 'handleSubmitQuiz'. Either include it or remove the dependency array.", ["1018"], "'isFillBlank' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'quiz.category', 'quiz.name', 'quiz.passingMarks', 'quiz.passingPercentage', 'quiz.subject', and 'submitting'. Either include them or remove the dependency array.", ["1019"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'clearChat'. Either include it or remove the dependency array.", ["1020"], ["1021"], {"desc": "1022", "fix": "1023"}, {"desc": "1024", "fix": "1025"}, {"desc": "1026", "fix": "1027"}, {"desc": "1028", "fix": "1029"}, {"desc": "1030", "fix": "1031"}, {"desc": "1032", "fix": "1033"}, {"desc": "1034", "fix": "1035"}, {"desc": "1036", "fix": "1037"}, {"desc": "1038", "fix": "1039"}, {"desc": "1040", "fix": "1041"}, {"desc": "1042", "fix": "1043"}, {"desc": "1044", "fix": "1045"}, {"desc": "1046", "fix": "1047"}, {"desc": "1048", "fix": "1049"}, {"desc": "1050", "fix": "1051"}, {"desc": "1052", "fix": "1053"}, {"desc": "1054", "fix": "1055"}, {"desc": "1056", "fix": "1057"}, {"desc": "1058", "fix": "1059"}, {"desc": "1054", "fix": "1060"}, {"desc": "1054", "fix": "1061"}, {"desc": "1062", "fix": "1063"}, {"messageId": "1064", "fix": "1065", "desc": "1066"}, {"messageId": "1067", "fix": "1068", "desc": "1069"}, {"messageId": "1064", "fix": "1070", "desc": "1066"}, {"messageId": "1067", "fix": "1071", "desc": "1069"}, {"desc": "1072", "fix": "1073"}, {"kind": "1074", "justification": "1075"}, {"desc": "1076", "fix": "1077"}, {"desc": "1078", "fix": "1079"}, {"desc": "1080", "fix": "1081"}, {"desc": "1082", "fix": "1083"}, {"desc": "1084", "fix": "1085"}, {"kind": "1074", "justification": "1075"}, {"desc": "1086", "fix": "1087"}, {"desc": "1088", "fix": "1089"}, {"desc": "1090", "fix": "1091"}, {"desc": "1092", "fix": "1093"}, {"desc": "1094", "fix": "1095"}, {"desc": "1096", "fix": "1097"}, "Update the dependencies array to be: [dispatch, getUserData, navigate, user]", {"range": "1098", "text": "1099"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", {"range": "1100", "text": "1101"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "1102", "text": "1103"}, "Update the dependencies array to be: [getExamData, params.id]", {"range": "1104", "text": "1105"}, "Update the dependencies array to be: [getExamsData]", {"range": "1106", "text": "1107"}, "Update the dependencies array to be: [user, subscriptionData, paymentInProgress]", {"range": "1108", "text": "1109"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", {"range": "1110", "text": "1111"}, "Update the dependencies array to be: [getData]", {"range": "1112", "text": "1113"}, "Update the dependencies array to be: [filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", {"range": "1114", "text": "1115"}, "Update the dependencies array to be: [getExams, getUserResults, user]", {"range": "1116", "text": "1117"}, "Update the dependencies array to be: [user, loading, refreshing, getExams]", {"range": "1118", "text": "1119"}, "Update the dependencies array to be: [fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", {"range": "1120", "text": "1121"}, "Update the dependencies array to be: [dispatch, getUserData]", {"range": "1122", "text": "1123"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "1124", "text": "1125"}, "Update the dependencies array to be: [getUsersData]", {"range": "1126", "text": "1127"}, "Update the dependencies array to be: [currentPage, fetchQuestions, limit]", {"range": "1128", "text": "1129"}, "Update the dependencies array to be: [getUserData]", {"range": "1130", "text": "1131"}, "Update the dependencies array to be: [editQuestion, form2]", {"range": "1132", "text": "1133"}, "Update the dependencies array to be: [getUserStats, rankingData, userDetails]", {"range": "1134", "text": "1135"}, {"range": "1136", "text": "1131"}, {"range": "1137", "text": "1131"}, "Update the dependencies array to be: [fetchUserRankingData, userDetails]", {"range": "1138", "text": "1139"}, "removeEscape", {"range": "1140", "text": "1075"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1141", "text": "1142"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1143", "text": "1075"}, {"range": "1144", "text": "1142"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "1145", "text": "1146"}, "directive", "", "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "1147", "text": "1148"}, "Update the dependencies array to be: [handleAutoRefresh, visible]", {"range": "1149", "text": "1150"}, "Update the dependencies array to be: [inspiringQuotes.length]", {"range": "1151", "text": "1152"}, "Update the dependencies array to be: [fetchNotifications, isOpen, notifications.length]", {"range": "1153", "text": "1154"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1155", "text": "1156"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1157", "text": "1158"}, "Update the dependencies array to be: [fetchTrialQuiz, trialUserInfo]", {"range": "1159", "text": "1160"}, "Update the dependencies array to be: [handleSubmitQuiz, timeLeft]", {"range": "1161", "text": "1162"}, "Update the dependencies array to be: [submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", {"range": "1163", "text": "1164"}, "Update the dependencies array to be: [clearChat]", {"range": "1165", "text": "1166"}, "Update the dependencies array to be: [fetchQuestions]", {"range": "1167", "text": "1168"}, [4199, 4201], "[dispatch, getUserData, navigate, user]", [5566, 5593], "[dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", [5754, 5773], "[user, activeRoute, verifyPaymentStatus]", [3399, 3401], "[getExamData, params.id]", [2533, 2535], "[getExamsData]", [2446, 2470], "[user, subscriptionData, paymentInProgress]", [28756, 28831], "[user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", [3981, 3983], "[getData]", [4036, 4090], "[filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", [6992, 6994], "[getExams, getUserResults, user]", [7399, 7426], "[user, loading, refreshing, getExams]", [32174, 32176], "[fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", [1990, 1992], "[dispatch, getUserData]", [7040, 7069], "[filters, getData, pagination]", [5587, 5589], "[getUsersData]", [4494, 4514], "[currentPage, fetchQuestions, limit]", [5262, 5264], "[getUserData]", [8373, 8387], "[editQuestion, form2]", [3556, 3582], "[getUserStats, rankingData, userDetails]", [4524, 4526], [9206, 9208], [9358, 9371], "[fetchUserRankingData, userDetails]", [3500, 3501], [3500, 3500], "\\", [5011, 5012], [5011, 5011], [3978, 4004], "[modalIsOpen, documentUrl, renderPDF]", [2411, 2420], "[fetchMaterials, filters]", [1110, 1119], "[handleAutoRefresh, visible]", [1591, 1593], "[inspiringQuotes.length]", [2363, 2371], "[fetchNotifications, isOpen, notifications.length]", [1327, 1329], "[fetchUsers]", [1196, 1198], "[fetchDashboardData]", [527, 542], "[fetchTrialQuiz, trialUserInfo]", [1008, 1018], "[handleSubmitQuiz, timeLeft]", [11349, 11400], "[submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", [1381, 1383], "[clearChat]", [3074, 3076], "[fetchQuestions]"]