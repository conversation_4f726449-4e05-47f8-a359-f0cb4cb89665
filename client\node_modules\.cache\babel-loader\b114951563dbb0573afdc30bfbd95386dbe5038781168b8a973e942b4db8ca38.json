{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\UserReports\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './index.css';\nimport PageTitle from \"../../../components/PageTitle\";\nimport { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty, Table, Tag } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\nimport { motion } from \"framer-motion\";\nimport { TbTrophy, TbTarget, TbTrendingUp, TbCalendar, TbClock, TbAward, TbChartBar, TbDownload, Tb<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>, <PERSON>b<PERSON><PERSON>e } from \"react-icons/tb\";\nimport moment from \"moment\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nfunction UserReports() {\n  _s();\n  const [reportsData, setReportsData] = useState([]);\n  const [filteredData, setFilteredData] = useState([]);\n  const [filterSubject, setFilterSubject] = useState('all');\n  const [filterVerdict, setFilterVerdict] = useState('all');\n  const [dateRange, setDateRange] = useState(null);\n  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'\n  const [stats, setStats] = useState({\n    totalExams: 0,\n    passedExams: 0,\n    averageScore: 0,\n    streak: 0,\n    bestScore: 0\n  });\n  const dispatch = useDispatch();\n  const calculateStats = data => {\n    if (!data || data.length === 0) {\n      setStats({\n        totalExams: 0,\n        passedExams: 0,\n        averageScore: 0,\n        streak: 0,\n        bestScore: 0\n      });\n      return;\n    }\n    const totalExams = data.length;\n    const passedExams = data.filter(report => {\n      var _report$result;\n      return ((_report$result = report.result) === null || _report$result === void 0 ? void 0 : _report$result.verdict) === 'Pass';\n    }).length;\n    const scores = data.map(report => {\n      var _report$result2, _report$result2$corre, _report$exam;\n      const obtained = ((_report$result2 = report.result) === null || _report$result2 === void 0 ? void 0 : (_report$result2$corre = _report$result2.correctAnswers) === null || _report$result2$corre === void 0 ? void 0 : _report$result2$corre.length) || 0;\n      const total = ((_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam.totalMarks) || 1;\n      return obtained / total * 100;\n    });\n    const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalExams;\n    const bestScore = Math.max(...scores);\n\n    // Calculate streak (consecutive passes)\n    let currentStreak = 0;\n    let maxStreak = 0;\n    for (let i = data.length - 1; i >= 0; i--) {\n      var _data$i$result;\n      if (((_data$i$result = data[i].result) === null || _data$i$result === void 0 ? void 0 : _data$i$result.verdict) === 'Pass') {\n        currentStreak++;\n        maxStreak = Math.max(maxStreak, currentStreak);\n      } else {\n        currentStreak = 0;\n      }\n    }\n    setStats({\n      totalExams,\n      passedExams,\n      averageScore: Math.round(averageScore),\n      streak: maxStreak,\n      bestScore: Math.round(bestScore)\n    });\n  };\n  const getData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllReportsByUser();\n      if (response.success) {\n        setReportsData(response.data);\n        setFilteredData(response.data);\n        calculateStats(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const applyFilters = () => {\n    let filtered = [...reportsData];\n    if (filterSubject !== 'all') {\n      filtered = filtered.filter(report => {\n        var _report$exam2, _report$exam2$subject;\n        return (_report$exam2 = report.exam) === null || _report$exam2 === void 0 ? void 0 : (_report$exam2$subject = _report$exam2.subject) === null || _report$exam2$subject === void 0 ? void 0 : _report$exam2$subject.toLowerCase().includes(filterSubject.toLowerCase());\n      });\n    }\n    if (filterVerdict !== 'all') {\n      filtered = filtered.filter(report => {\n        var _report$result3;\n        return ((_report$result3 = report.result) === null || _report$result3 === void 0 ? void 0 : _report$result3.verdict) === filterVerdict;\n      });\n    }\n    if (dateRange && dateRange.length === 2) {\n      filtered = filtered.filter(report => {\n        const reportDate = moment(report.createdAt);\n        return reportDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');\n      });\n    }\n    setFilteredData(filtered);\n    calculateStats(filtered);\n  };\n  useEffect(() => {\n    getData();\n  }, []);\n  useEffect(() => {\n    applyFilters();\n  }, [filterSubject, filterVerdict, dateRange, reportsData]);\n  const getScoreColor = score => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-blue-600';\n    if (score >= 40) return 'text-orange-600';\n    return 'text-red-600';\n  };\n  const getVerdictIcon = verdict => {\n    return verdict === 'Pass' ? /*#__PURE__*/_jsxDEV(TbCheck, {\n      className: \"w-5 h-5 text-green-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n      className: \"w-5 h-5 text-red-600\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this);\n  };\n  const getUniqueSubjects = () => {\n    const subjects = reportsData.map(report => {\n      var _report$exam3;\n      return (_report$exam3 = report.exam) === null || _report$exam3 === void 0 ? void 0 : _report$exam3.subject;\n    }).filter(Boolean);\n    return [...new Set(subjects)];\n  };\n  const getResponsiveColumns = () => {\n    const isMobile = window.innerWidth < 768;\n    const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;\n    const baseColumns = [{\n      title: 'Exam',\n      dataIndex: 'examName',\n      key: 'examName',\n      render: (text, record) => {\n        var _record$exam, _record$exam2;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold text-gray-900 text-sm sm:text-base truncate\",\n            children: ((_record$exam = record.exam) === null || _record$exam === void 0 ? void 0 : _record$exam.name) || 'Unnamed Exam'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs sm:text-sm text-gray-500 truncate\",\n            children: ((_record$exam2 = record.exam) === null || _record$exam2 === void 0 ? void 0 : _record$exam2.subject) || 'General'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400 mt-1\",\n            children: moment(record.createdAt).format(\"MMM DD, YYYY\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this);\n      },\n      width: isMobile ? 180 : isTablet ? 200 : 250,\n      ellipsis: true\n    }, {\n      title: 'Score',\n      dataIndex: 'score',\n      key: 'score',\n      render: (text, record) => {\n        var _record$result, _record$result$correc, _record$exam3;\n        const obtained = ((_record$result = record.result) === null || _record$result === void 0 ? void 0 : (_record$result$correc = _record$result.correctAnswers) === null || _record$result$correc === void 0 ? void 0 : _record$result$correc.length) || 0;\n        const total = ((_record$exam3 = record.exam) === null || _record$exam3 === void 0 ? void 0 : _record$exam3.totalMarks) || 1;\n        const percentage = Math.round(obtained / total * 100);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm sm:text-base font-bold text-gray-900\",\n            children: [obtained, \"/\", total]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: percentage,\n            size: \"small\",\n            strokeColor: percentage >= 60 ? '#10b981' : '#ef4444',\n            showInfo: false,\n            className: \"mb-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-xs sm:text-sm font-medium ${getScoreColor(percentage)}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this);\n      },\n      width: isMobile ? 80 : 120,\n      sorter: (a, b) => {\n        var _a$result, _a$result$correctAnsw, _a$exam, _b$result, _b$result$correctAnsw, _b$exam;\n        const scoreA = Math.round((((_a$result = a.result) === null || _a$result === void 0 ? void 0 : (_a$result$correctAnsw = _a$result.correctAnswers) === null || _a$result$correctAnsw === void 0 ? void 0 : _a$result$correctAnsw.length) || 0) / (((_a$exam = a.exam) === null || _a$exam === void 0 ? void 0 : _a$exam.totalMarks) || 1) * 100);\n        const scoreB = Math.round((((_b$result = b.result) === null || _b$result === void 0 ? void 0 : (_b$result$correctAnsw = _b$result.correctAnswers) === null || _b$result$correctAnsw === void 0 ? void 0 : _b$result$correctAnsw.length) || 0) / (((_b$exam = b.exam) === null || _b$exam === void 0 ? void 0 : _b$exam.totalMarks) || 1) * 100);\n        return scoreA - scoreB;\n      }\n    }, {\n      title: 'Result',\n      dataIndex: 'verdict',\n      key: 'verdict',\n      render: (text, record) => {\n        var _record$result2;\n        const verdict = (_record$result2 = record.result) === null || _record$result2 === void 0 ? void 0 : _record$result2.verdict;\n        const isPassed = verdict === 'Pass';\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          icon: !isMobile ? getVerdictIcon(verdict) : null,\n          color: isPassed ? 'success' : 'error',\n          className: \"font-medium text-xs sm:text-sm\",\n          children: isMobile ? isPassed ? 'P' : 'F' : verdict || 'N/A'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this);\n      },\n      width: isMobile ? 50 : 100,\n      filters: !isMobile ? [{\n        text: 'Pass',\n        value: 'Pass'\n      }, {\n        text: 'Fail',\n        value: 'Fail'\n      }] : undefined,\n      onFilter: !isMobile ? (value, record) => {\n        var _record$result3;\n        return ((_record$result3 = record.result) === null || _record$result3 === void 0 ? void 0 : _record$result3.verdict) === value;\n      } : undefined\n    }];\n\n    // Add date column for tablet and desktop\n    if (!isMobile) {\n      baseColumns.splice(1, 0, {\n        title: 'Date',\n        dataIndex: 'createdAt',\n        key: 'date',\n        render: date => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium\",\n            children: moment(date).format(\"MMM DD, YYYY\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-500\",\n            children: moment(date).format(\"HH:mm\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this),\n        width: isTablet ? 100 : 120\n      });\n    }\n\n    // Add actions column for desktop\n    if (!isMobile) {\n      baseColumns.push({\n        title: 'Actions',\n        key: 'actions',\n        render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(TbEye, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 19\n          }, this),\n          className: \"bg-blue-500 hover:bg-blue-600\",\n          children: isTablet ? '' : 'View'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this),\n        width: isTablet ? 60 : 80\n      });\n    }\n    return baseColumns;\n  };\n  const columns = getResponsiveColumns();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n      title: \"Performance Reports\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8 sm:mb-10 lg:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n            className: \"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\",\n          children: [\"Your \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 18\n          }, this), \" Journey\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto px-4\",\n          children: \"Track your progress, analyze your performance, and celebrate your achievements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center p-2 sm:p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-blue-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Total Exams\",\n              value: stats.totalExams,\n              valueStyle: {\n                color: '#1e40af',\n                fontSize: window.innerWidth < 640 ? '18px' : '24px',\n                fontWeight: 'bold'\n              },\n              className: \"text-xs sm:text-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center p-2 sm:p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-green-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Passed\",\n              value: stats.passedExams,\n              valueStyle: {\n                color: '#059669',\n                fontSize: window.innerWidth < 640 ? '18px' : '24px',\n                fontWeight: 'bold'\n              },\n              className: \"text-xs sm:text-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center p-2 sm:p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-purple-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbTrendingUp, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Average Score\",\n              value: stats.averageScore,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#7c3aed',\n                fontSize: window.innerWidth < 640 ? '18px' : '24px',\n                fontWeight: 'bold'\n              },\n              className: \"text-xs sm:text-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center p-2 sm:p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-orange-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Best Score\",\n              value: stats.bestScore,\n              suffix: \"%\",\n              valueStyle: {\n                color: '#ea580c',\n                fontSize: window.innerWidth < 640 ? '18px' : '24px',\n                fontWeight: 'bold'\n              },\n              className: \"text-xs sm:text-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100 sm:col-span-3 lg:col-span-1\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center p-2 sm:p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-pink-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\",\n              children: /*#__PURE__*/_jsxDEV(TbFlame, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"Best Streak\",\n              value: stats.streak,\n              valueStyle: {\n                color: '#db2777',\n                fontSize: window.innerWidth < 640 ? '18px' : '24px',\n                fontWeight: 'bold'\n              },\n              className: \"text-xs sm:text-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        className: \"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbFilter, {\n              className: \"w-5 h-5 text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Filter Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Subject\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"All Subjects\",\n                value: filterSubject,\n                onChange: setFilterSubject,\n                className: \"w-full\",\n                size: \"large\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"all\",\n                  children: \"All Subjects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this), getUniqueSubjects().map(subject => /*#__PURE__*/_jsxDEV(Option, {\n                  value: subject,\n                  children: subject\n                }, subject, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Result\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"All Results\",\n                value: filterVerdict,\n                onChange: setFilterVerdict,\n                className: \"w-full\",\n                size: \"large\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"all\",\n                  children: \"All Results\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"Pass\",\n                  children: \"Passed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"Fail\",\n                  children: \"Failed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2 sm:col-span-2 lg:col-span-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: \"Date Range\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n                value: dateRange,\n                onChange: setDateRange,\n                className: \"w-full\",\n                size: \"large\",\n                placeholder: ['Start Date', 'End Date'],\n                format: \"MMM DD, YYYY\",\n                allowClear: true,\n                presets: [{\n                  label: 'Last 7 Days',\n                  value: [moment().subtract(7, 'days'), moment()]\n                }, {\n                  label: 'Last 30 Days',\n                  value: [moment().subtract(30, 'days'), moment()]\n                }, {\n                  label: 'Last 3 Months',\n                  value: [moment().subtract(3, 'months'), moment()]\n                }, {\n                  label: 'This Year',\n                  value: [moment().startOf('year'), moment()]\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"text-sm font-medium text-gray-700 opacity-0\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => {\n                  setFilterSubject('all');\n                  setFilterVerdict('all');\n                  setDateRange(null);\n                },\n                size: \"large\",\n                className: \"w-full\",\n                icon: /*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 25\n                }, this),\n                children: \"Clear All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), (filterSubject !== 'all' || filterVerdict !== 'all' || dateRange) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2 pt-2 border-t border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Active filters:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), filterSubject !== 'all' && /*#__PURE__*/_jsxDEV(Tag, {\n              closable: true,\n              onClose: () => setFilterSubject('all'),\n              color: \"blue\",\n              children: [\"Subject: \", filterSubject]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 19\n            }, this), filterVerdict !== 'all' && /*#__PURE__*/_jsxDEV(Tag, {\n              closable: true,\n              onClose: () => setFilterVerdict('all'),\n              color: filterVerdict === 'Pass' ? 'green' : 'red',\n              children: [\"Result: \", filterVerdict]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 19\n            }, this), dateRange && /*#__PURE__*/_jsxDEV(Tag, {\n              closable: true,\n              onClose: () => setDateRange(null),\n              color: \"purple\",\n              children: [dateRange[0].format('MMM DD'), \" - \", dateRange[1].format('MMM DD, YYYY')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        className: \"bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          columns: columns,\n          dataSource: filteredData,\n          rowKey: record => record._id,\n          pagination: {\n            pageSize: window.innerWidth < 768 ? 5 : 10,\n            showSizeChanger: window.innerWidth >= 768,\n            showQuickJumper: window.innerWidth >= 768,\n            showTotal: (total, range) => window.innerWidth >= 640 ? `${range[0]}-${range[1]} of ${total} results` : `${range[0]}-${range[1]} / ${total}`,\n            className: \"px-3 sm:px-6 py-2 sm:py-4\",\n            simple: window.innerWidth < 640\n          },\n          scroll: {\n            x: window.innerWidth < 768 ? 600 : 800\n          },\n          className: \"modern-table\",\n          size: window.innerWidth < 768 ? \"middle\" : \"large\",\n          locale: {\n            emptyText: /*#__PURE__*/_jsxDEV(Empty, {\n              image: Empty.PRESENTED_IMAGE_SIMPLE,\n              description: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-base sm:text-lg font-medium text-gray-900 mb-2\",\n                  children: \"No exam results found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm sm:text-base text-gray-500 px-4\",\n                  children: \"Try adjusting your filters or take some exams to see your results here.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n}\n_s(UserReports, \"LAvY+fW8DCnRnM0Lc/pXJyt5XxQ=\", false, function () {\n  return [useDispatch];\n});\n_c = UserReports;\nexport default UserReports;\nvar _c;\n$RefreshReg$(_c, \"UserReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Page<PERSON><PERSON>le", "message", "Card", "Progress", "Statistic", "Select", "DatePicker", "<PERSON><PERSON>", "Empty", "Table", "Tag", "useDispatch", "HideLoading", "ShowLoading", "getAllReportsByUser", "motion", "TbTrophy", "TbTarget", "TbTrendingUp", "TbCalendar", "TbClock", "TbAward", "TbChartBar", "TbDownload", "Tb<PERSON><PERSON>er", "TbEye", "TbCheck", "TbX", "TbFlame", "moment", "jsxDEV", "_jsxDEV", "Option", "RangePicker", "UserReports", "_s", "reportsData", "setReportsData", "filteredData", "setFilteredData", "filterSubject", "setFilterSubject", "filterVerdict", "setFilterVerdict", "date<PERSON><PERSON><PERSON>", "setDateRange", "viewMode", "setViewMode", "stats", "setStats", "totalExams", "passedExams", "averageScore", "streak", "bestScore", "dispatch", "calculateStats", "data", "length", "filter", "report", "_report$result", "result", "verdict", "scores", "map", "_report$result2", "_report$result2$corre", "_report$exam", "obtained", "correctAnswers", "total", "exam", "totalMarks", "reduce", "sum", "score", "Math", "max", "currentStreak", "maxStreak", "i", "_data$i$result", "round", "getData", "response", "success", "error", "applyFilters", "filtered", "_report$exam2", "_report$exam2$subject", "subject", "toLowerCase", "includes", "_report$result3", "reportDate", "createdAt", "isBetween", "getScoreColor", "getVerdictIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getUniqueSubjects", "subjects", "_report$exam3", "Boolean", "Set", "getResponsiveColumns", "isMobile", "window", "innerWidth", "isTablet", "baseColumns", "title", "dataIndex", "key", "render", "text", "record", "_record$exam", "_record$exam2", "children", "name", "format", "width", "ellipsis", "_record$result", "_record$result$correc", "_record$exam3", "percentage", "percent", "size", "strokeColor", "showInfo", "sorter", "a", "b", "_a$result", "_a$result$correctAnsw", "_a$exam", "_b$result", "_b$result$correctAnsw", "_b$exam", "scoreA", "scoreB", "_record$result2", "isPassed", "icon", "color", "filters", "value", "undefined", "onFilter", "_record$result3", "splice", "date", "push", "type", "columns", "div", "initial", "opacity", "y", "animate", "transition", "delay", "valueStyle", "fontSize", "fontWeight", "suffix", "placeholder", "onChange", "allowClear", "presets", "label", "subtract", "startOf", "onClick", "closable", "onClose", "dataSource", "<PERSON><PERSON><PERSON>", "_id", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "simple", "scroll", "x", "locale", "emptyText", "image", "PRESENTED_IMAGE_SIMPLE", "description", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/UserReports/index.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport './index.css';\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Card, Progress, Statistic, Select, DatePicker, Button, Empty, Table, Tag } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbTrophy,\r\n  TbTarget,\r\n  TbTrendingUp,\r\n  TbCalendar,\r\n  TbClock,\r\n  TbAward,\r\n  TbChartBar,\r\n  TbDownload,\r\n  TbFilter,\r\n  TbEye,\r\n  TbCheck,\r\n  TbX,\r\n  TbFlame\r\n} from \"react-icons/tb\";\r\nimport moment from \"moment\";\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\n\r\nfunction UserReports() {\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [filteredData, setFilteredData] = useState([]);\r\n  const [filterSubject, setFilterSubject] = useState('all');\r\n  const [filterVerdict, setFilterVerdict] = useState('all');\r\n  const [dateRange, setDateRange] = useState(null);\r\n  const [viewMode, setViewMode] = useState('cards'); // 'cards' or 'table'\r\n  const [stats, setStats] = useState({\r\n    totalExams: 0,\r\n    passedExams: 0,\r\n    averageScore: 0,\r\n    streak: 0,\r\n    bestScore: 0\r\n  });\r\n  const dispatch = useDispatch();\r\n\r\n  const calculateStats = (data) => {\r\n    if (!data || data.length === 0) {\r\n      setStats({\r\n        totalExams: 0,\r\n        passedExams: 0,\r\n        averageScore: 0,\r\n        streak: 0,\r\n        bestScore: 0\r\n      });\r\n      return;\r\n    }\r\n\r\n    const totalExams = data.length;\r\n    const passedExams = data.filter(report => report.result?.verdict === 'Pass').length;\r\n    const scores = data.map(report => {\r\n      const obtained = report.result?.correctAnswers?.length || 0;\r\n      const total = report.exam?.totalMarks || 1;\r\n      return (obtained / total) * 100;\r\n    });\r\n\r\n    const averageScore = scores.reduce((sum, score) => sum + score, 0) / totalExams;\r\n    const bestScore = Math.max(...scores);\r\n\r\n    // Calculate streak (consecutive passes)\r\n    let currentStreak = 0;\r\n    let maxStreak = 0;\r\n    for (let i = data.length - 1; i >= 0; i--) {\r\n      if (data[i].result?.verdict === 'Pass') {\r\n        currentStreak++;\r\n        maxStreak = Math.max(maxStreak, currentStreak);\r\n      } else {\r\n        currentStreak = 0;\r\n      }\r\n    }\r\n\r\n    setStats({\r\n      totalExams,\r\n      passedExams,\r\n      averageScore: Math.round(averageScore),\r\n      streak: maxStreak,\r\n      bestScore: Math.round(bestScore)\r\n    });\r\n  };\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        setFilteredData(response.data);\r\n        calculateStats(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const applyFilters = () => {\r\n    let filtered = [...reportsData];\r\n\r\n    if (filterSubject !== 'all') {\r\n      filtered = filtered.filter(report =>\r\n        report.exam?.subject?.toLowerCase().includes(filterSubject.toLowerCase())\r\n      );\r\n    }\r\n\r\n    if (filterVerdict !== 'all') {\r\n      filtered = filtered.filter(report => report.result?.verdict === filterVerdict);\r\n    }\r\n\r\n    if (dateRange && dateRange.length === 2) {\r\n      filtered = filtered.filter(report => {\r\n        const reportDate = moment(report.createdAt);\r\n        return reportDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');\r\n      });\r\n    }\r\n\r\n    setFilteredData(filtered);\r\n    calculateStats(filtered);\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    applyFilters();\r\n  }, [filterSubject, filterVerdict, dateRange, reportsData]);\r\n\r\n  const getScoreColor = (score) => {\r\n    if (score >= 80) return 'text-green-600';\r\n    if (score >= 60) return 'text-blue-600';\r\n    if (score >= 40) return 'text-orange-600';\r\n    return 'text-red-600';\r\n  };\r\n\r\n  const getVerdictIcon = (verdict) => {\r\n    return verdict === 'Pass' ?\r\n      <TbCheck className=\"w-5 h-5 text-green-600\" /> :\r\n      <TbX className=\"w-5 h-5 text-red-600\" />;\r\n  };\r\n\r\n  const getUniqueSubjects = () => {\r\n    const subjects = reportsData.map(report => report.exam?.subject).filter(Boolean);\r\n    return [...new Set(subjects)];\r\n  };\r\n\r\n  const getResponsiveColumns = () => {\r\n    const isMobile = window.innerWidth < 768;\r\n    const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;\r\n\r\n    const baseColumns = [\r\n      {\r\n        title: 'Exam',\r\n        dataIndex: 'examName',\r\n        key: 'examName',\r\n        render: (text, record) => (\r\n          <div className=\"min-w-0\">\r\n            <div className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">\r\n              {record.exam?.name || 'Unnamed Exam'}\r\n            </div>\r\n            <div className=\"text-xs sm:text-sm text-gray-500 truncate\">\r\n              {record.exam?.subject || 'General'}\r\n            </div>\r\n            {isMobile && (\r\n              <div className=\"text-xs text-gray-400 mt-1\">\r\n                {moment(record.createdAt).format(\"MMM DD, YYYY\")}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ),\r\n        width: isMobile ? 180 : isTablet ? 200 : 250,\r\n        ellipsis: true,\r\n      },\r\n      {\r\n        title: 'Score',\r\n        dataIndex: 'score',\r\n        key: 'score',\r\n        render: (text, record) => {\r\n          const obtained = record.result?.correctAnswers?.length || 0;\r\n          const total = record.exam?.totalMarks || 1;\r\n          const percentage = Math.round((obtained / total) * 100);\r\n\r\n          return (\r\n            <div className=\"text-center\">\r\n              <div className=\"text-sm sm:text-base font-bold text-gray-900\">\r\n                {obtained}/{total}\r\n              </div>\r\n              <Progress\r\n                percent={percentage}\r\n                size=\"small\"\r\n                strokeColor={percentage >= 60 ? '#10b981' : '#ef4444'}\r\n                showInfo={false}\r\n                className=\"mb-1\"\r\n              />\r\n              <div className={`text-xs sm:text-sm font-medium ${getScoreColor(percentage)}`}>\r\n                {percentage}%\r\n              </div>\r\n            </div>\r\n          );\r\n        },\r\n        width: isMobile ? 80 : 120,\r\n        sorter: (a, b) => {\r\n          const scoreA = Math.round(((a.result?.correctAnswers?.length || 0) / (a.exam?.totalMarks || 1)) * 100);\r\n          const scoreB = Math.round(((b.result?.correctAnswers?.length || 0) / (b.exam?.totalMarks || 1)) * 100);\r\n          return scoreA - scoreB;\r\n        },\r\n      },\r\n      {\r\n        title: 'Result',\r\n        dataIndex: 'verdict',\r\n        key: 'verdict',\r\n        render: (text, record) => {\r\n          const verdict = record.result?.verdict;\r\n          const isPassed = verdict === 'Pass';\r\n\r\n          return (\r\n            <Tag\r\n              icon={!isMobile ? getVerdictIcon(verdict) : null}\r\n              color={isPassed ? 'success' : 'error'}\r\n              className=\"font-medium text-xs sm:text-sm\"\r\n            >\r\n              {isMobile ? (isPassed ? 'P' : 'F') : (verdict || 'N/A')}\r\n            </Tag>\r\n          );\r\n        },\r\n        width: isMobile ? 50 : 100,\r\n        filters: !isMobile ? [\r\n          { text: 'Pass', value: 'Pass' },\r\n          { text: 'Fail', value: 'Fail' },\r\n        ] : undefined,\r\n        onFilter: !isMobile ? (value, record) => record.result?.verdict === value : undefined,\r\n      },\r\n    ];\r\n\r\n    // Add date column for tablet and desktop\r\n    if (!isMobile) {\r\n      baseColumns.splice(1, 0, {\r\n        title: 'Date',\r\n        dataIndex: 'createdAt',\r\n        key: 'date',\r\n        render: (date) => (\r\n          <div className=\"text-sm\">\r\n            <div className=\"font-medium\">{moment(date).format(\"MMM DD, YYYY\")}</div>\r\n            <div className=\"text-gray-500\">{moment(date).format(\"HH:mm\")}</div>\r\n          </div>\r\n        ),\r\n        width: isTablet ? 100 : 120,\r\n      });\r\n    }\r\n\r\n    // Add actions column for desktop\r\n    if (!isMobile) {\r\n      baseColumns.push({\r\n        title: 'Actions',\r\n        key: 'actions',\r\n        render: (text, record) => (\r\n          <Button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            icon={<TbEye />}\r\n            className=\"bg-blue-500 hover:bg-blue-600\"\r\n          >\r\n            {isTablet ? '' : 'View'}\r\n          </Button>\r\n        ),\r\n        width: isTablet ? 60 : 80,\r\n      });\r\n    }\r\n\r\n    return baseColumns;\r\n  };\r\n\r\n  const columns = getResponsiveColumns();\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <PageTitle title=\"Performance Reports\" />\r\n\r\n      <div className=\"max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Header Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"text-center mb-8 sm:mb-10 lg:mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg\">\r\n            <TbChartBar className=\"w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Your <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Performance</span> Journey\r\n          </h1>\r\n          <p className=\"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto px-4\">\r\n            Track your progress, analyze your performance, and celebrate your achievements\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Stats Cards */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8\"\r\n        >\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-blue-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbTarget className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Total Exams\"\r\n                value={stats.totalExams}\r\n                valueStyle={{\r\n                  color: '#1e40af',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : '24px',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"text-xs sm:text-sm\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-green-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Passed\"\r\n                value={stats.passedExams}\r\n                valueStyle={{\r\n                  color: '#059669',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : '24px',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"text-xs sm:text-sm\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-purple-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbTrendingUp className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Average Score\"\r\n                value={stats.averageScore}\r\n                suffix=\"%\"\r\n                valueStyle={{\r\n                  color: '#7c3aed',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : '24px',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"text-xs sm:text-sm\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-orange-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbTrophy className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Best Score\"\r\n                value={stats.bestScore}\r\n                suffix=\"%\"\r\n                valueStyle={{\r\n                  color: '#ea580c',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : '24px',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"text-xs sm:text-sm\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n          <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-pink-50 to-pink-100 sm:col-span-3 lg:col-span-1\">\r\n            <div className=\"flex flex-col items-center p-2 sm:p-4\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-pink-500 rounded-full flex items-center justify-center mb-2 sm:mb-3\">\r\n                <TbFlame className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white\" />\r\n              </div>\r\n              <Statistic\r\n                title=\"Best Streak\"\r\n                value={stats.streak}\r\n                valueStyle={{\r\n                  color: '#db2777',\r\n                  fontSize: window.innerWidth < 640 ? '18px' : '24px',\r\n                  fontWeight: 'bold'\r\n                }}\r\n                className=\"text-xs sm:text-sm\"\r\n              />\r\n            </div>\r\n          </Card>\r\n\r\n\r\n        </motion.div>\r\n\r\n        {/* Filters Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 mb-6 sm:mb-8 border border-gray-100\"\r\n        >\r\n          <div className=\"flex flex-col gap-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <TbFilter className=\"w-5 h-5 text-gray-600\" />\r\n              <h3 className=\"text-lg font-semibold text-gray-900\">Filter Results</h3>\r\n            </div>\r\n\r\n            {/* Filter Controls */}\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n              {/* Subject Filter */}\r\n              <div className=\"space-y-2\">\r\n                <label className=\"text-sm font-medium text-gray-700\">Subject</label>\r\n                <Select\r\n                  placeholder=\"All Subjects\"\r\n                  value={filterSubject}\r\n                  onChange={setFilterSubject}\r\n                  className=\"w-full\"\r\n                  size=\"large\"\r\n                >\r\n                  <Option value=\"all\">All Subjects</Option>\r\n                  {getUniqueSubjects().map(subject => (\r\n                    <Option key={subject} value={subject}>{subject}</Option>\r\n                  ))}\r\n                </Select>\r\n              </div>\r\n\r\n              {/* Result Filter */}\r\n              <div className=\"space-y-2\">\r\n                <label className=\"text-sm font-medium text-gray-700\">Result</label>\r\n                <Select\r\n                  placeholder=\"All Results\"\r\n                  value={filterVerdict}\r\n                  onChange={setFilterVerdict}\r\n                  className=\"w-full\"\r\n                  size=\"large\"\r\n                >\r\n                  <Option value=\"all\">All Results</Option>\r\n                  <Option value=\"Pass\">Passed</Option>\r\n                  <Option value=\"Fail\">Failed</Option>\r\n                </Select>\r\n              </div>\r\n\r\n              {/* Date Range Filter */}\r\n              <div className=\"space-y-2 sm:col-span-2 lg:col-span-1\">\r\n                <label className=\"text-sm font-medium text-gray-700\">Date Range</label>\r\n                <RangePicker\r\n                  value={dateRange}\r\n                  onChange={setDateRange}\r\n                  className=\"w-full\"\r\n                  size=\"large\"\r\n                  placeholder={['Start Date', 'End Date']}\r\n                  format=\"MMM DD, YYYY\"\r\n                  allowClear\r\n                  presets={[\r\n                    {\r\n                      label: 'Last 7 Days',\r\n                      value: [moment().subtract(7, 'days'), moment()],\r\n                    },\r\n                    {\r\n                      label: 'Last 30 Days',\r\n                      value: [moment().subtract(30, 'days'), moment()],\r\n                    },\r\n                    {\r\n                      label: 'Last 3 Months',\r\n                      value: [moment().subtract(3, 'months'), moment()],\r\n                    },\r\n                    {\r\n                      label: 'This Year',\r\n                      value: [moment().startOf('year'), moment()],\r\n                    },\r\n                  ]}\r\n                />\r\n              </div>\r\n\r\n              {/* Clear Button */}\r\n              <div className=\"space-y-2\">\r\n                <label className=\"text-sm font-medium text-gray-700 opacity-0\">Actions</label>\r\n                <Button\r\n                  onClick={() => {\r\n                    setFilterSubject('all');\r\n                    setFilterVerdict('all');\r\n                    setDateRange(null);\r\n                  }}\r\n                  size=\"large\"\r\n                  className=\"w-full\"\r\n                  icon={<TbX />}\r\n                >\r\n                  Clear All\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Active Filters Display */}\r\n            {(filterSubject !== 'all' || filterVerdict !== 'all' || dateRange) && (\r\n              <div className=\"flex flex-wrap gap-2 pt-2 border-t border-gray-100\">\r\n                <span className=\"text-sm text-gray-600\">Active filters:</span>\r\n                {filterSubject !== 'all' && (\r\n                  <Tag\r\n                    closable\r\n                    onClose={() => setFilterSubject('all')}\r\n                    color=\"blue\"\r\n                  >\r\n                    Subject: {filterSubject}\r\n                  </Tag>\r\n                )}\r\n                {filterVerdict !== 'all' && (\r\n                  <Tag\r\n                    closable\r\n                    onClose={() => setFilterVerdict('all')}\r\n                    color={filterVerdict === 'Pass' ? 'green' : 'red'}\r\n                  >\r\n                    Result: {filterVerdict}\r\n                  </Tag>\r\n                )}\r\n                {dateRange && (\r\n                  <Tag\r\n                    closable\r\n                    onClose={() => setDateRange(null)}\r\n                    color=\"purple\"\r\n                  >\r\n                    {dateRange[0].format('MMM DD')} - {dateRange[1].format('MMM DD, YYYY')}\r\n                  </Tag>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Exam Results Table */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden border border-gray-100\"\r\n        >\r\n          <Table\r\n            columns={columns}\r\n            dataSource={filteredData}\r\n            rowKey={(record) => record._id}\r\n            pagination={{\r\n              pageSize: window.innerWidth < 768 ? 5 : 10,\r\n              showSizeChanger: window.innerWidth >= 768,\r\n              showQuickJumper: window.innerWidth >= 768,\r\n              showTotal: (total, range) =>\r\n                window.innerWidth >= 640\r\n                  ? `${range[0]}-${range[1]} of ${total} results`\r\n                  : `${range[0]}-${range[1]} / ${total}`,\r\n              className: \"px-3 sm:px-6 py-2 sm:py-4\",\r\n              simple: window.innerWidth < 640\r\n            }}\r\n            scroll={{ x: window.innerWidth < 768 ? 600 : 800 }}\r\n            className=\"modern-table\"\r\n            size={window.innerWidth < 768 ? \"middle\" : \"large\"}\r\n            locale={{\r\n              emptyText: (\r\n                <Empty\r\n                  image={Empty.PRESENTED_IMAGE_SIMPLE}\r\n                  description={\r\n                    <div className=\"py-8\">\r\n                      <h3 className=\"text-base sm:text-lg font-medium text-gray-900 mb-2\">No exam results found</h3>\r\n                      <p className=\"text-sm sm:text-base text-gray-500 px-4\">Try adjusting your filters or take some exams to see your results here.</p>\r\n                    </div>\r\n                  }\r\n                />\r\n              )\r\n            }}\r\n          />\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserReports;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AACxG,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,GAAG,EACHC,OAAO,QACF,gBAAgB;AACvB,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAM;EAAEC;AAAO,CAAC,GAAG3B,MAAM;AACzB,MAAM;EAAE4B;AAAY,CAAC,GAAG3B,UAAU;AAElC,SAAS4B,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC;IACjCoD,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAE9B,MAAM6C,cAAc,GAAIC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9BT,QAAQ,CAAC;QACPC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE;MACb,CAAC,CAAC;MACF;IACF;IAEA,MAAMJ,UAAU,GAAGO,IAAI,CAACC,MAAM;IAC9B,MAAMP,WAAW,GAAGM,IAAI,CAACE,MAAM,CAACC,MAAM;MAAA,IAAAC,cAAA;MAAA,OAAI,EAAAA,cAAA,GAAAD,MAAM,CAACE,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,OAAO,MAAK,MAAM;IAAA,EAAC,CAACL,MAAM;IACnF,MAAMM,MAAM,GAAGP,IAAI,CAACQ,GAAG,CAACL,MAAM,IAAI;MAAA,IAAAM,eAAA,EAAAC,qBAAA,EAAAC,YAAA;MAChC,MAAMC,QAAQ,GAAG,EAAAH,eAAA,GAAAN,MAAM,CAACE,MAAM,cAAAI,eAAA,wBAAAC,qBAAA,GAAbD,eAAA,CAAeI,cAAc,cAAAH,qBAAA,uBAA7BA,qBAAA,CAA+BT,MAAM,KAAI,CAAC;MAC3D,MAAMa,KAAK,GAAG,EAAAH,YAAA,GAAAR,MAAM,CAACY,IAAI,cAAAJ,YAAA,uBAAXA,YAAA,CAAaK,UAAU,KAAI,CAAC;MAC1C,OAAQJ,QAAQ,GAAGE,KAAK,GAAI,GAAG;IACjC,CAAC,CAAC;IAEF,MAAMnB,YAAY,GAAGY,MAAM,CAACU,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAG1B,UAAU;IAC/E,MAAMI,SAAS,GAAGuB,IAAI,CAACC,GAAG,CAAC,GAAGd,MAAM,CAAC;;IAErC;IACA,IAAIe,aAAa,GAAG,CAAC;IACrB,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAGxB,IAAI,CAACC,MAAM,GAAG,CAAC,EAAEuB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAAA,IAAAC,cAAA;MACzC,IAAI,EAAAA,cAAA,GAAAzB,IAAI,CAACwB,CAAC,CAAC,CAACnB,MAAM,cAAAoB,cAAA,uBAAdA,cAAA,CAAgBnB,OAAO,MAAK,MAAM,EAAE;QACtCgB,aAAa,EAAE;QACfC,SAAS,GAAGH,IAAI,CAACC,GAAG,CAACE,SAAS,EAAED,aAAa,CAAC;MAChD,CAAC,MAAM;QACLA,aAAa,GAAG,CAAC;MACnB;IACF;IAEA9B,QAAQ,CAAC;MACPC,UAAU;MACVC,WAAW;MACXC,YAAY,EAAEyB,IAAI,CAACM,KAAK,CAAC/B,YAAY,CAAC;MACtCC,MAAM,EAAE2B,SAAS;MACjB1B,SAAS,EAAEuB,IAAI,CAACM,KAAK,CAAC7B,SAAS;IACjC,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8B,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF7B,QAAQ,CAAC1C,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMwE,QAAQ,GAAG,MAAMvE,mBAAmB,CAAC,CAAC;MAC5C,IAAIuE,QAAQ,CAACC,OAAO,EAAE;QACpBjD,cAAc,CAACgD,QAAQ,CAAC5B,IAAI,CAAC;QAC7BlB,eAAe,CAAC8C,QAAQ,CAAC5B,IAAI,CAAC;QAC9BD,cAAc,CAAC6B,QAAQ,CAAC5B,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLxD,OAAO,CAACsF,KAAK,CAACF,QAAQ,CAACpF,OAAO,CAAC;MACjC;MACAsD,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO2E,KAAK,EAAE;MACdhC,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACsF,KAAK,CAACA,KAAK,CAACtF,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMuF,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,QAAQ,GAAG,CAAC,GAAGrD,WAAW,CAAC;IAE/B,IAAII,aAAa,KAAK,KAAK,EAAE;MAC3BiD,QAAQ,GAAGA,QAAQ,CAAC9B,MAAM,CAACC,MAAM;QAAA,IAAA8B,aAAA,EAAAC,qBAAA;QAAA,QAAAD,aAAA,GAC/B9B,MAAM,CAACY,IAAI,cAAAkB,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAaE,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtD,aAAa,CAACqD,WAAW,CAAC,CAAC,CAAC;MAAA,CAC3E,CAAC;IACH;IAEA,IAAInD,aAAa,KAAK,KAAK,EAAE;MAC3B+C,QAAQ,GAAGA,QAAQ,CAAC9B,MAAM,CAACC,MAAM;QAAA,IAAAmC,eAAA;QAAA,OAAI,EAAAA,eAAA,GAAAnC,MAAM,CAACE,MAAM,cAAAiC,eAAA,uBAAbA,eAAA,CAAehC,OAAO,MAAKrB,aAAa;MAAA,EAAC;IAChF;IAEA,IAAIE,SAAS,IAAIA,SAAS,CAACc,MAAM,KAAK,CAAC,EAAE;MACvC+B,QAAQ,GAAGA,QAAQ,CAAC9B,MAAM,CAACC,MAAM,IAAI;QACnC,MAAMoC,UAAU,GAAGnE,MAAM,CAAC+B,MAAM,CAACqC,SAAS,CAAC;QAC3C,OAAOD,UAAU,CAACE,SAAS,CAACtD,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACtE,CAAC,CAAC;IACJ;IAEAL,eAAe,CAACkD,QAAQ,CAAC;IACzBjC,cAAc,CAACiC,QAAQ,CAAC;EAC1B,CAAC;EAED1F,SAAS,CAAC,MAAM;IACdqF,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EAENrF,SAAS,CAAC,MAAM;IACdyF,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAChD,aAAa,EAAEE,aAAa,EAAEE,SAAS,EAAER,WAAW,CAAC,CAAC;EAE1D,MAAM+D,aAAa,GAAIvB,KAAK,IAAK;IAC/B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,eAAe;IACvC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,iBAAiB;IACzC,OAAO,cAAc;EACvB,CAAC;EAED,MAAMwB,cAAc,GAAIrC,OAAO,IAAK;IAClC,OAAOA,OAAO,KAAK,MAAM,gBACvBhC,OAAA,CAACL,OAAO;MAAC2E,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAC9C1E,OAAA,CAACJ,GAAG;MAAC0E,SAAS,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC5C,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,QAAQ,GAAGvE,WAAW,CAAC6B,GAAG,CAACL,MAAM;MAAA,IAAAgD,aAAA;MAAA,QAAAA,aAAA,GAAIhD,MAAM,CAACY,IAAI,cAAAoC,aAAA,uBAAXA,aAAA,CAAahB,OAAO;IAAA,EAAC,CAACjC,MAAM,CAACkD,OAAO,CAAC;IAChF,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,QAAQ,GAAGC,MAAM,CAACC,UAAU,GAAG,GAAG;IACxC,MAAMC,QAAQ,GAAGF,MAAM,CAACC,UAAU,IAAI,GAAG,IAAID,MAAM,CAACC,UAAU,GAAG,IAAI;IAErE,MAAME,WAAW,GAAG,CAClB;MACEC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,UAAU;MACrBC,GAAG,EAAE,UAAU;MACfC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;QAAA,IAAAC,YAAA,EAAAC,aAAA;QAAA,oBACnB7F,OAAA;UAAKsE,SAAS,EAAC,SAAS;UAAAwB,QAAA,gBACtB9F,OAAA;YAAKsE,SAAS,EAAC,2DAA2D;YAAAwB,QAAA,EACvE,EAAAF,YAAA,GAAAD,MAAM,CAAClD,IAAI,cAAAmD,YAAA,uBAAXA,YAAA,CAAaG,IAAI,KAAI;UAAc;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACN1E,OAAA;YAAKsE,SAAS,EAAC,2CAA2C;YAAAwB,QAAA,EACvD,EAAAD,aAAA,GAAAF,MAAM,CAAClD,IAAI,cAAAoD,aAAA,uBAAXA,aAAA,CAAahC,OAAO,KAAI;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,EACLO,QAAQ,iBACPjF,OAAA;YAAKsE,SAAS,EAAC,4BAA4B;YAAAwB,QAAA,EACxChG,MAAM,CAAC6F,MAAM,CAACzB,SAAS,CAAC,CAAC8B,MAAM,CAAC,cAAc;UAAC;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,CACP;MACDuB,KAAK,EAAEhB,QAAQ,GAAG,GAAG,GAAGG,QAAQ,GAAG,GAAG,GAAG,GAAG;MAC5Cc,QAAQ,EAAE;IACZ,CAAC,EACD;MACEZ,KAAK,EAAE,OAAO;MACdC,SAAS,EAAE,OAAO;MAClBC,GAAG,EAAE,OAAO;MACZC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;QAAA,IAAAQ,cAAA,EAAAC,qBAAA,EAAAC,aAAA;QACxB,MAAM/D,QAAQ,GAAG,EAAA6D,cAAA,GAAAR,MAAM,CAAC5D,MAAM,cAAAoE,cAAA,wBAAAC,qBAAA,GAAbD,cAAA,CAAe5D,cAAc,cAAA6D,qBAAA,uBAA7BA,qBAAA,CAA+BzE,MAAM,KAAI,CAAC;QAC3D,MAAMa,KAAK,GAAG,EAAA6D,aAAA,GAAAV,MAAM,CAAClD,IAAI,cAAA4D,aAAA,uBAAXA,aAAA,CAAa3D,UAAU,KAAI,CAAC;QAC1C,MAAM4D,UAAU,GAAGxD,IAAI,CAACM,KAAK,CAAEd,QAAQ,GAAGE,KAAK,GAAI,GAAG,CAAC;QAEvD,oBACExC,OAAA;UAAKsE,SAAS,EAAC,aAAa;UAAAwB,QAAA,gBAC1B9F,OAAA;YAAKsE,SAAS,EAAC,8CAA8C;YAAAwB,QAAA,GAC1DxD,QAAQ,EAAC,GAAC,EAACE,KAAK;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACN1E,OAAA,CAAC5B,QAAQ;YACPmI,OAAO,EAAED,UAAW;YACpBE,IAAI,EAAC,OAAO;YACZC,WAAW,EAAEH,UAAU,IAAI,EAAE,GAAG,SAAS,GAAG,SAAU;YACtDI,QAAQ,EAAE,KAAM;YAChBpC,SAAS,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACF1E,OAAA;YAAKsE,SAAS,EAAG,kCAAiCF,aAAa,CAACkC,UAAU,CAAE,EAAE;YAAAR,QAAA,GAC3EQ,UAAU,EAAC,GACd;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,CAAC;MACDuB,KAAK,EAAEhB,QAAQ,GAAG,EAAE,GAAG,GAAG;MAC1B0B,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK;QAAA,IAAAC,SAAA,EAAAC,qBAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,qBAAA,EAAAC,OAAA;QAChB,MAAMC,MAAM,GAAGtE,IAAI,CAACM,KAAK,CAAE,CAAC,EAAA0D,SAAA,GAAAF,CAAC,CAAC7E,MAAM,cAAA+E,SAAA,wBAAAC,qBAAA,GAARD,SAAA,CAAUvE,cAAc,cAAAwE,qBAAA,uBAAxBA,qBAAA,CAA0BpF,MAAM,KAAI,CAAC,KAAK,EAAAqF,OAAA,GAAAJ,CAAC,CAACnE,IAAI,cAAAuE,OAAA,uBAANA,OAAA,CAAQtE,UAAU,KAAI,CAAC,CAAC,GAAI,GAAG,CAAC;QACtG,MAAM2E,MAAM,GAAGvE,IAAI,CAACM,KAAK,CAAE,CAAC,EAAA6D,SAAA,GAAAJ,CAAC,CAAC9E,MAAM,cAAAkF,SAAA,wBAAAC,qBAAA,GAARD,SAAA,CAAU1E,cAAc,cAAA2E,qBAAA,uBAAxBA,qBAAA,CAA0BvF,MAAM,KAAI,CAAC,KAAK,EAAAwF,OAAA,GAAAN,CAAC,CAACpE,IAAI,cAAA0E,OAAA,uBAANA,OAAA,CAAQzE,UAAU,KAAI,CAAC,CAAC,GAAI,GAAG,CAAC;QACtG,OAAO0E,MAAM,GAAGC,MAAM;MACxB;IACF,CAAC,EACD;MACE/B,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE,SAAS;MACpBC,GAAG,EAAE,SAAS;MACdC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;QAAA,IAAA2B,eAAA;QACxB,MAAMtF,OAAO,IAAAsF,eAAA,GAAG3B,MAAM,CAAC5D,MAAM,cAAAuF,eAAA,uBAAbA,eAAA,CAAetF,OAAO;QACtC,MAAMuF,QAAQ,GAAGvF,OAAO,KAAK,MAAM;QAEnC,oBACEhC,OAAA,CAACrB,GAAG;UACF6I,IAAI,EAAE,CAACvC,QAAQ,GAAGZ,cAAc,CAACrC,OAAO,CAAC,GAAG,IAAK;UACjDyF,KAAK,EAAEF,QAAQ,GAAG,SAAS,GAAG,OAAQ;UACtCjD,SAAS,EAAC,gCAAgC;UAAAwB,QAAA,EAEzCb,QAAQ,GAAIsC,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAKvF,OAAO,IAAI;QAAM;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAEV,CAAC;MACDuB,KAAK,EAAEhB,QAAQ,GAAG,EAAE,GAAG,GAAG;MAC1ByC,OAAO,EAAE,CAACzC,QAAQ,GAAG,CACnB;QAAES,IAAI,EAAE,MAAM;QAAEiC,KAAK,EAAE;MAAO,CAAC,EAC/B;QAAEjC,IAAI,EAAE,MAAM;QAAEiC,KAAK,EAAE;MAAO,CAAC,CAChC,GAAGC,SAAS;MACbC,QAAQ,EAAE,CAAC5C,QAAQ,GAAG,CAAC0C,KAAK,EAAEhC,MAAM;QAAA,IAAAmC,eAAA;QAAA,OAAK,EAAAA,eAAA,GAAAnC,MAAM,CAAC5D,MAAM,cAAA+F,eAAA,uBAAbA,eAAA,CAAe9F,OAAO,MAAK2F,KAAK;MAAA,IAAGC;IAC9E,CAAC,CACF;;IAED;IACA,IAAI,CAAC3C,QAAQ,EAAE;MACbI,WAAW,CAAC0C,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE;QACvBzC,KAAK,EAAE,MAAM;QACbC,SAAS,EAAE,WAAW;QACtBC,GAAG,EAAE,MAAM;QACXC,MAAM,EAAGuC,IAAI,iBACXhI,OAAA;UAAKsE,SAAS,EAAC,SAAS;UAAAwB,QAAA,gBACtB9F,OAAA;YAAKsE,SAAS,EAAC,aAAa;YAAAwB,QAAA,EAAEhG,MAAM,CAACkI,IAAI,CAAC,CAAChC,MAAM,CAAC,cAAc;UAAC;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxE1E,OAAA;YAAKsE,SAAS,EAAC,eAAe;YAAAwB,QAAA,EAAEhG,MAAM,CAACkI,IAAI,CAAC,CAAChC,MAAM,CAAC,OAAO;UAAC;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN;QACDuB,KAAK,EAAEb,QAAQ,GAAG,GAAG,GAAG;MAC1B,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACH,QAAQ,EAAE;MACbI,WAAW,CAAC4C,IAAI,CAAC;QACf3C,KAAK,EAAE,SAAS;QAChBE,GAAG,EAAE,SAAS;QACdC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB3F,OAAA,CAACxB,MAAM;UACL0J,IAAI,EAAC,SAAS;UACd1B,IAAI,EAAC,OAAO;UACZgB,IAAI,eAAExH,OAAA,CAACN,KAAK;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChBJ,SAAS,EAAC,+BAA+B;UAAAwB,QAAA,EAExCV,QAAQ,GAAG,EAAE,GAAG;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACT;QACDuB,KAAK,EAAEb,QAAQ,GAAG,EAAE,GAAG;MACzB,CAAC,CAAC;IACJ;IAEA,OAAOC,WAAW;EACpB,CAAC;EAED,MAAM8C,OAAO,GAAGnD,oBAAoB,CAAC,CAAC;EAEtC,oBACEhF,OAAA;IAAKsE,SAAS,EAAC,oEAAoE;IAAAwB,QAAA,gBACjF9F,OAAA,CAAC/B,SAAS;MAACqH,KAAK,EAAC;IAAqB;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEzC1E,OAAA;MAAKsE,SAAS,EAAC,qEAAqE;MAAAwB,QAAA,gBAElF9F,OAAA,CAAChB,MAAM,CAACoJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BjE,SAAS,EAAC,oCAAoC;QAAAwB,QAAA,gBAE9C9F,OAAA;UAAKsE,SAAS,EAAC,iLAAiL;UAAAwB,QAAA,eAC9L9F,OAAA,CAACT,UAAU;YAAC+E,SAAS,EAAC;UAAgD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACN1E,OAAA;UAAIsE,SAAS,EAAC,4EAA4E;UAAAwB,QAAA,GAAC,OACpF,eAAA9F,OAAA;YAAMsE,SAAS,EAAC,4EAA4E;YAAAwB,QAAA,EAAC;UAAW;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,YACtH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1E,OAAA;UAAGsE,SAAS,EAAC,kFAAkF;UAAAwB,QAAA,EAAC;QAEhG;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb1E,OAAA,CAAChB,MAAM,CAACoJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BpE,SAAS,EAAC,qFAAqF;QAAAwB,QAAA,gBAE/F9F,OAAA,CAAC7B,IAAI;UAACmG,SAAS,EAAC,6GAA6G;UAAAwB,QAAA,eAC3H9F,OAAA;YAAKsE,SAAS,EAAC,uCAAuC;YAAAwB,QAAA,gBACpD9F,OAAA;cAAKsE,SAAS,EAAC,gHAAgH;cAAAwB,QAAA,eAC7H9F,OAAA,CAACd,QAAQ;gBAACoF,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACN1E,OAAA,CAAC3B,SAAS;cACRiH,KAAK,EAAC,aAAa;cACnBqC,KAAK,EAAE1G,KAAK,CAACE,UAAW;cACxBwH,UAAU,EAAE;gBACVlB,KAAK,EAAE,SAAS;gBAChBmB,QAAQ,EAAE1D,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnD0D,UAAU,EAAE;cACd,CAAE;cACFvE,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP1E,OAAA,CAAC7B,IAAI;UAACmG,SAAS,EAAC,+GAA+G;UAAAwB,QAAA,eAC7H9F,OAAA;YAAKsE,SAAS,EAAC,uCAAuC;YAAAwB,QAAA,gBACpD9F,OAAA;cAAKsE,SAAS,EAAC,iHAAiH;cAAAwB,QAAA,eAC9H9F,OAAA,CAACL,OAAO;gBAAC2E,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACN1E,OAAA,CAAC3B,SAAS;cACRiH,KAAK,EAAC,QAAQ;cACdqC,KAAK,EAAE1G,KAAK,CAACG,WAAY;cACzBuH,UAAU,EAAE;gBACVlB,KAAK,EAAE,SAAS;gBAChBmB,QAAQ,EAAE1D,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnD0D,UAAU,EAAE;cACd,CAAE;cACFvE,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP1E,OAAA,CAAC7B,IAAI;UAACmG,SAAS,EAAC,iHAAiH;UAAAwB,QAAA,eAC/H9F,OAAA;YAAKsE,SAAS,EAAC,uCAAuC;YAAAwB,QAAA,gBACpD9F,OAAA;cAAKsE,SAAS,EAAC,kHAAkH;cAAAwB,QAAA,eAC/H9F,OAAA,CAACb,YAAY;gBAACmF,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACN1E,OAAA,CAAC3B,SAAS;cACRiH,KAAK,EAAC,eAAe;cACrBqC,KAAK,EAAE1G,KAAK,CAACI,YAAa;cAC1ByH,MAAM,EAAC,GAAG;cACVH,UAAU,EAAE;gBACVlB,KAAK,EAAE,SAAS;gBAChBmB,QAAQ,EAAE1D,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnD0D,UAAU,EAAE;cACd,CAAE;cACFvE,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP1E,OAAA,CAAC7B,IAAI;UAACmG,SAAS,EAAC,iHAAiH;UAAAwB,QAAA,eAC/H9F,OAAA;YAAKsE,SAAS,EAAC,uCAAuC;YAAAwB,QAAA,gBACpD9F,OAAA;cAAKsE,SAAS,EAAC,kHAAkH;cAAAwB,QAAA,eAC/H9F,OAAA,CAACf,QAAQ;gBAACqF,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACN1E,OAAA,CAAC3B,SAAS;cACRiH,KAAK,EAAC,YAAY;cAClBqC,KAAK,EAAE1G,KAAK,CAACM,SAAU;cACvBuH,MAAM,EAAC,GAAG;cACVH,UAAU,EAAE;gBACVlB,KAAK,EAAE,SAAS;gBAChBmB,QAAQ,EAAE1D,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnD0D,UAAU,EAAE;cACd,CAAE;cACFvE,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP1E,OAAA,CAAC7B,IAAI;UAACmG,SAAS,EAAC,yIAAyI;UAAAwB,QAAA,eACvJ9F,OAAA;YAAKsE,SAAS,EAAC,uCAAuC;YAAAwB,QAAA,gBACpD9F,OAAA;cAAKsE,SAAS,EAAC,gHAAgH;cAAAwB,QAAA,eAC7H9F,OAAA,CAACH,OAAO;gBAACyE,SAAS,EAAC;cAAgD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACN1E,OAAA,CAAC3B,SAAS;cACRiH,KAAK,EAAC,aAAa;cACnBqC,KAAK,EAAE1G,KAAK,CAACK,MAAO;cACpBqH,UAAU,EAAE;gBACVlB,KAAK,EAAE,SAAS;gBAChBmB,QAAQ,EAAE1D,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnD0D,UAAU,EAAE;cACd,CAAE;cACFvE,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGG,CAAC,eAGb1E,OAAA,CAAChB,MAAM,CAACoJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BpE,SAAS,EAAC,6FAA6F;QAAAwB,QAAA,eAEvG9F,OAAA;UAAKsE,SAAS,EAAC,qBAAqB;UAAAwB,QAAA,gBAClC9F,OAAA;YAAKsE,SAAS,EAAC,yBAAyB;YAAAwB,QAAA,gBACtC9F,OAAA,CAACP,QAAQ;cAAC6E,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C1E,OAAA;cAAIsE,SAAS,EAAC,qCAAqC;cAAAwB,QAAA,EAAC;YAAc;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAGN1E,OAAA;YAAKsE,SAAS,EAAC,sDAAsD;YAAAwB,QAAA,gBAEnE9F,OAAA;cAAKsE,SAAS,EAAC,WAAW;cAAAwB,QAAA,gBACxB9F,OAAA;gBAAOsE,SAAS,EAAC,mCAAmC;gBAAAwB,QAAA,EAAC;cAAO;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpE1E,OAAA,CAAC1B,MAAM;gBACLyK,WAAW,EAAC,cAAc;gBAC1BpB,KAAK,EAAElH,aAAc;gBACrBuI,QAAQ,EAAEtI,gBAAiB;gBAC3B4D,SAAS,EAAC,QAAQ;gBAClBkC,IAAI,EAAC,OAAO;gBAAAV,QAAA,gBAEZ9F,OAAA,CAACC,MAAM;kBAAC0H,KAAK,EAAC,KAAK;kBAAA7B,QAAA,EAAC;gBAAY;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxCC,iBAAiB,CAAC,CAAC,CAACzC,GAAG,CAAC2B,OAAO,iBAC9B7D,OAAA,CAACC,MAAM;kBAAe0H,KAAK,EAAE9D,OAAQ;kBAAAiC,QAAA,EAAEjC;gBAAO,GAAjCA,OAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmC,CACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN1E,OAAA;cAAKsE,SAAS,EAAC,WAAW;cAAAwB,QAAA,gBACxB9F,OAAA;gBAAOsE,SAAS,EAAC,mCAAmC;gBAAAwB,QAAA,EAAC;cAAM;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnE1E,OAAA,CAAC1B,MAAM;gBACLyK,WAAW,EAAC,aAAa;gBACzBpB,KAAK,EAAEhH,aAAc;gBACrBqI,QAAQ,EAAEpI,gBAAiB;gBAC3B0D,SAAS,EAAC,QAAQ;gBAClBkC,IAAI,EAAC,OAAO;gBAAAV,QAAA,gBAEZ9F,OAAA,CAACC,MAAM;kBAAC0H,KAAK,EAAC,KAAK;kBAAA7B,QAAA,EAAC;gBAAW;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC1E,OAAA,CAACC,MAAM;kBAAC0H,KAAK,EAAC,MAAM;kBAAA7B,QAAA,EAAC;gBAAM;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC1E,OAAA,CAACC,MAAM;kBAAC0H,KAAK,EAAC,MAAM;kBAAA7B,QAAA,EAAC;gBAAM;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN1E,OAAA;cAAKsE,SAAS,EAAC,uCAAuC;cAAAwB,QAAA,gBACpD9F,OAAA;gBAAOsE,SAAS,EAAC,mCAAmC;gBAAAwB,QAAA,EAAC;cAAU;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvE1E,OAAA,CAACE,WAAW;gBACVyH,KAAK,EAAE9G,SAAU;gBACjBmI,QAAQ,EAAElI,YAAa;gBACvBwD,SAAS,EAAC,QAAQ;gBAClBkC,IAAI,EAAC,OAAO;gBACZuC,WAAW,EAAE,CAAC,YAAY,EAAE,UAAU,CAAE;gBACxC/C,MAAM,EAAC,cAAc;gBACrBiD,UAAU;gBACVC,OAAO,EAAE,CACP;kBACEC,KAAK,EAAE,aAAa;kBACpBxB,KAAK,EAAE,CAAC7H,MAAM,CAAC,CAAC,CAACsJ,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,EAAEtJ,MAAM,CAAC,CAAC;gBAChD,CAAC,EACD;kBACEqJ,KAAK,EAAE,cAAc;kBACrBxB,KAAK,EAAE,CAAC7H,MAAM,CAAC,CAAC,CAACsJ,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAEtJ,MAAM,CAAC,CAAC;gBACjD,CAAC,EACD;kBACEqJ,KAAK,EAAE,eAAe;kBACtBxB,KAAK,EAAE,CAAC7H,MAAM,CAAC,CAAC,CAACsJ,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAEtJ,MAAM,CAAC,CAAC;gBAClD,CAAC,EACD;kBACEqJ,KAAK,EAAE,WAAW;kBAClBxB,KAAK,EAAE,CAAC7H,MAAM,CAAC,CAAC,CAACuJ,OAAO,CAAC,MAAM,CAAC,EAAEvJ,MAAM,CAAC,CAAC;gBAC5C,CAAC;cACD;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN1E,OAAA;cAAKsE,SAAS,EAAC,WAAW;cAAAwB,QAAA,gBACxB9F,OAAA;gBAAOsE,SAAS,EAAC,6CAA6C;gBAAAwB,QAAA,EAAC;cAAO;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9E1E,OAAA,CAACxB,MAAM;gBACL8K,OAAO,EAAEA,CAAA,KAAM;kBACb5I,gBAAgB,CAAC,KAAK,CAAC;kBACvBE,gBAAgB,CAAC,KAAK,CAAC;kBACvBE,YAAY,CAAC,IAAI,CAAC;gBACpB,CAAE;gBACF0F,IAAI,EAAC,OAAO;gBACZlC,SAAS,EAAC,QAAQ;gBAClBkD,IAAI,eAAExH,OAAA,CAACJ,GAAG;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAoB,QAAA,EACf;cAED;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,CAACjE,aAAa,KAAK,KAAK,IAAIE,aAAa,KAAK,KAAK,IAAIE,SAAS,kBAC/Db,OAAA;YAAKsE,SAAS,EAAC,oDAAoD;YAAAwB,QAAA,gBACjE9F,OAAA;cAAMsE,SAAS,EAAC,uBAAuB;cAAAwB,QAAA,EAAC;YAAe;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7DjE,aAAa,KAAK,KAAK,iBACtBT,OAAA,CAACrB,GAAG;cACF4K,QAAQ;cACRC,OAAO,EAAEA,CAAA,KAAM9I,gBAAgB,CAAC,KAAK,CAAE;cACvC+G,KAAK,EAAC,MAAM;cAAA3B,QAAA,GACb,WACU,EAACrF,aAAa;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CACN,EACA/D,aAAa,KAAK,KAAK,iBACtBX,OAAA,CAACrB,GAAG;cACF4K,QAAQ;cACRC,OAAO,EAAEA,CAAA,KAAM5I,gBAAgB,CAAC,KAAK,CAAE;cACvC6G,KAAK,EAAE9G,aAAa,KAAK,MAAM,GAAG,OAAO,GAAG,KAAM;cAAAmF,QAAA,GACnD,UACS,EAACnF,aAAa;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACN,EACA7D,SAAS,iBACRb,OAAA,CAACrB,GAAG;cACF4K,QAAQ;cACRC,OAAO,EAAEA,CAAA,KAAM1I,YAAY,CAAC,IAAI,CAAE;cAClC2G,KAAK,EAAC,QAAQ;cAAA3B,QAAA,GAEbjF,SAAS,CAAC,CAAC,CAAC,CAACmF,MAAM,CAAC,QAAQ,CAAC,EAAC,KAAG,EAACnF,SAAS,CAAC,CAAC,CAAC,CAACmF,MAAM,CAAC,cAAc,CAAC;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb1E,OAAA,CAAChB,MAAM,CAACoJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BpE,SAAS,EAAC,qFAAqF;QAAAwB,QAAA,eAE/F9F,OAAA,CAACtB,KAAK;UACJyJ,OAAO,EAAEA,OAAQ;UACjBsB,UAAU,EAAElJ,YAAa;UACzBmJ,MAAM,EAAG/D,MAAM,IAAKA,MAAM,CAACgE,GAAI;UAC/BC,UAAU,EAAE;YACVC,QAAQ,EAAE3E,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,CAAC,GAAG,EAAE;YAC1C2E,eAAe,EAAE5E,MAAM,CAACC,UAAU,IAAI,GAAG;YACzC4E,eAAe,EAAE7E,MAAM,CAACC,UAAU,IAAI,GAAG;YACzC6E,SAAS,EAAEA,CAACxH,KAAK,EAAEyH,KAAK,KACtB/E,MAAM,CAACC,UAAU,IAAI,GAAG,GACnB,GAAE8E,KAAK,CAAC,CAAC,CAAE,IAAGA,KAAK,CAAC,CAAC,CAAE,OAAMzH,KAAM,UAAS,GAC5C,GAAEyH,KAAK,CAAC,CAAC,CAAE,IAAGA,KAAK,CAAC,CAAC,CAAE,MAAKzH,KAAM,EAAC;YAC1C8B,SAAS,EAAE,2BAA2B;YACtC4F,MAAM,EAAEhF,MAAM,CAACC,UAAU,GAAG;UAC9B,CAAE;UACFgF,MAAM,EAAE;YAAEC,CAAC,EAAElF,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG;UAAI,CAAE;UACnDb,SAAS,EAAC,cAAc;UACxBkC,IAAI,EAAEtB,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG,OAAQ;UACnDkF,MAAM,EAAE;YACNC,SAAS,eACPtK,OAAA,CAACvB,KAAK;cACJ8L,KAAK,EAAE9L,KAAK,CAAC+L,sBAAuB;cACpCC,WAAW,eACTzK,OAAA;gBAAKsE,SAAS,EAAC,MAAM;gBAAAwB,QAAA,gBACnB9F,OAAA;kBAAIsE,SAAS,EAAC,qDAAqD;kBAAAwB,QAAA,EAAC;gBAAqB;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9F1E,OAAA;kBAAGsE,SAAS,EAAC,yCAAyC;kBAAAwB,QAAA,EAAC;gBAAuE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/H;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAEL;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtE,EAAA,CA7iBQD,WAAW;EAAA,QAcDvB,WAAW;AAAA;AAAA8L,EAAA,GAdrBvK,WAAW;AA+iBpB,eAAeA,WAAW;AAAC,IAAAuK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}