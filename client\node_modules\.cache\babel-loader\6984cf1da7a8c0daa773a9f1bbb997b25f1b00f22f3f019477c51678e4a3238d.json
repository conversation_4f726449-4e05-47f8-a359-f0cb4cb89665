{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { getUserInfo } from \"../apicalls/users\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { SetUser } from \"../redux/usersSlice.js\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\nimport \"./ProtectedRoute.css\";\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\nimport AdminNavigation from \"./AdminNavigation\";\nimport ModernSidebar from \"./ModernSidebar\";\nimport { TbHome, TbBrandTanzania, TbMenu2, TbX, TbChevronDown, TbLogout, TbUser, TbSettings, TbBell, TbStar } from \"react-icons/tb\";\nimport OnlineStatusIndicator from './common/OnlineStatusIndicator';\nimport NotificationBell from './common/NotificationBell';\nimport ProfilePicture from './common/ProfilePicture';\nimport { setUserOnline, setUserOffline, sendHeartbeat } from '../apicalls/notifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProtectedRoute({\n  children\n}) {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\n  const intervalRef = useRef(null);\n  const heartbeatRef = useRef(null);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const {\n    paymentVerificationNeeded\n  } = useSelector(state => state.payment);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const activeRoute = location.pathname;\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        dispatch(SetUser(response.data));\n\n        // Store user data in localStorage for consistency\n        localStorage.setItem(\"user\", JSON.stringify(response.data));\n\n        // Debug log to help identify admin login issues\n        console.log(\"User data loaded:\", {\n          name: response.data.name,\n          isAdmin: response.data.isAdmin,\n          email: response.data.email\n        });\n      } else {\n        message.error(response.message);\n        navigate(\"/login\");\n      }\n    } catch (error) {\n      navigate(\"/login\");\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      // Check if user data already exists in Redux (from login)\n      if (!user) {\n        // Try to load user from localStorage first\n        const storedUser = localStorage.getItem(\"user\");\n        if (storedUser) {\n          try {\n            const userData = JSON.parse(storedUser);\n            console.log(\"ProtectedRoute: Loading user from localStorage\", {\n              name: userData.name,\n              isAdmin: userData.isAdmin\n            });\n            dispatch(SetUser(userData));\n          } catch (error) {\n            console.log(\"ProtectedRoute: Error parsing stored user data, fetching from server\");\n            getUserData();\n          }\n        } else {\n          console.log(\"ProtectedRoute: No user in Redux or localStorage, fetching from server\");\n          getUserData();\n        }\n      } else {\n        console.log(\"ProtectedRoute: User already in Redux\", {\n          name: user.name,\n          isAdmin: user.isAdmin\n        });\n      }\n    } else {\n      navigate(\"/login\");\n    }\n  }, []);\n  useEffect(() => {\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\n      navigate('/user/plans');\n    }\n  }, [isPaymentPending, activeRoute, navigate]);\n  const verifyPaymentStatus = async () => {\n    try {\n      const data = await checkPaymentStatus();\n      console.log(\"Payment Status:\", data);\n      if (data !== null && data !== void 0 && data.error || (data === null || data === void 0 ? void 0 : data.paymentStatus) !== 'paid') {\n        if (subscriptionData !== null) {\n          dispatch(SetSubscription(null));\n        }\n        setIsPaymentPending(true);\n      } else {\n        setIsPaymentPending(false);\n        dispatch(SetSubscription(data));\n        if (intervalRef.current) {\n          clearInterval(intervalRef.current);\n        }\n      }\n    } catch (error) {\n      console.log(\"Error checking payment status:\", error);\n      dispatch(SetSubscription(null));\n      setIsPaymentPending(true);\n    }\n  };\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing 2222222...\");\n      if (paymentVerificationNeeded) {\n        console.log('Inside timer in effect 2....');\n        intervalRef.current = setInterval(() => {\n          console.log('Timer in action...');\n          verifyPaymentStatus();\n        }, 15000);\n        dispatch(setPaymentVerificationNeeded(false));\n      }\n    }\n  }, [paymentVerificationNeeded]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.paymentRequired && !(user !== null && user !== void 0 && user.isAdmin)) {\n      console.log(\"Effect Runing...\");\n      verifyPaymentStatus();\n    }\n  }, [user, activeRoute]);\n\n  // Online status management\n  useEffect(() => {\n    if (user && !user.isAdmin) {\n      // Set user as online when component mounts\n      setUserOnline().catch(console.error);\n\n      // Send heartbeat every 2 minutes\n      heartbeatRef.current = setInterval(() => {\n        sendHeartbeat().catch(console.error);\n      }, 120000); // 2 minutes\n\n      // Set user as offline when component unmounts or page unloads\n      const handleBeforeUnload = () => {\n        setUserOffline().catch(console.error);\n      };\n      window.addEventListener('beforeunload', handleBeforeUnload);\n      return () => {\n        if (heartbeatRef.current) {\n          clearInterval(heartbeatRef.current);\n        }\n        window.removeEventListener('beforeunload', handleBeforeUnload);\n        setUserOffline().catch(console.error);\n      };\n    }\n  }, [user]);\n  const getButtonClass = title => {\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\n      return \"\"; // No class applied\n    }\n\n    return (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.paymentStatus) !== \"paid\" && user !== null && user !== void 0 && user.paymentRequired ? \"button-disabled\" : \"\";\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout-modern min-h-screen flex flex-col\",\n    children: [!(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(ModernSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 26\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col min-h-screen\",\n      children: [/*#__PURE__*/_jsxDEV(motion.header, {\n        initial: {\n          y: -20,\n          opacity: 0\n        },\n        animate: {\n          y: 0,\n          opacity: 1\n        },\n        className: `nav-modern ${location.pathname.includes('/quiz') || location.pathname.includes('/write-exam') ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98' : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'} backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 flex justify-center\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.6,\n                  delay: 0.2\n                },\n                className: \"relative group flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                  style: {\n                    width: '32px',\n                    height: '24px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"https://flagcdn.com/w40/tz.png\",\n                    alt: \"Tanzania Flag\",\n                    className: \"w-full h-full object-cover\",\n                    style: {\n                      objectFit: 'cover'\n                    },\n                    onError: e => {\n                      // Fallback to another flag source if first fails\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                      e.target.onerror = () => {\n                        // Final fallback - hide image and show text\n                        e.target.style.display = 'none';\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                      };\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative brainwave-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                    style: {\n                      fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                      letterSpacing: '-0.02em'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                      className: \"relative inline-block\",\n                      initial: {\n                        opacity: 0,\n                        x: -30,\n                        scale: 0.8\n                      },\n                      animate: {\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                      },\n                      transition: {\n                        duration: 1,\n                        delay: 0.3,\n                        textShadow: {\n                          duration: 2,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      },\n                      whileHover: {\n                        scale: 1.1,\n                        rotate: [0, -2, 2, 0],\n                        transition: {\n                          duration: 0.3\n                        }\n                      },\n                      style: {\n                        color: '#1f2937',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                      },\n                      children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                        animate: {\n                          opacity: [0, 1, 0],\n                          scale: [0.5, 1.2, 0.5],\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                        },\n                        transition: {\n                          duration: 1.5,\n                          repeat: Infinity,\n                          delay: 2\n                        },\n                        style: {\n                          backgroundColor: '#3b82f6',\n                          boxShadow: '0 0 10px #3b82f6'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 290,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                      className: \"relative inline-block\",\n                      initial: {\n                        opacity: 0,\n                        x: 30,\n                        scale: 0.8\n                      },\n                      animate: {\n                        opacity: 1,\n                        x: 0,\n                        scale: 1,\n                        y: [0, -2, 0, 2, 0],\n                        textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                      },\n                      transition: {\n                        duration: 1,\n                        delay: 0.5,\n                        y: {\n                          duration: 3,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        },\n                        textShadow: {\n                          duration: 2.5,\n                          repeat: Infinity,\n                          ease: \"easeInOut\"\n                        }\n                      },\n                      whileHover: {\n                        scale: 1.1,\n                        rotate: [0, 2, -2, 0],\n                        transition: {\n                          duration: 0.3\n                        }\n                      },\n                      style: {\n                        color: '#059669',\n                        fontWeight: '900',\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                      },\n                      children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                        className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                        animate: {\n                          opacity: [0, 1, 0],\n                          x: [0, 40, 80],\n                          y: [0, -5, 0, 5, 0],\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\n                        },\n                        transition: {\n                          duration: 3,\n                          repeat: Infinity,\n                          delay: 1\n                        },\n                        style: {\n                          backgroundColor: '#10b981',\n                          boxShadow: '0 0 8px #10b981'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                    initial: {\n                      width: 0,\n                      opacity: 0\n                    },\n                    animate: {\n                      width: '100%',\n                      opacity: 1,\n                      boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                    },\n                    transition: {\n                      duration: 1.5,\n                      delay: 1.2,\n                      boxShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    style: {\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                  style: {\n                    background: '#f0f0f0',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                    width: '32px',\n                    height: '32px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/favicon.png\",\n                    alt: \"Brainwave Logo\",\n                    className: \"w-full h-full object-cover\",\n                    style: {\n                      objectFit: 'cover'\n                    },\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                    style: {\n                      display: 'none',\n                      fontSize: '12px'\n                    },\n                    children: \"\\uD83E\\uDDE0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end space-x-2 sm:space-x-3\",\n              children: [!(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.8\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.2\n                },\n                children: /*#__PURE__*/_jsxDEV(NotificationBell, {\n                  unreadCount: 2\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.8\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.3\n                },\n                className: \"flex items-center space-x-2 group\",\n                children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                  user: user,\n                  size: \"sm\",\n                  showOnlineStatus: true,\n                  style: {\n                    width: '32px',\n                    height: '32px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hidden sm:block text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\",\n                    children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\",\n                    children: [\"Class \", user === null || user === void 0 ? void 0 : user.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: `flex-1 overflow-auto ${user !== null && user !== void 0 && user.isAdmin ? 'bg-gray-100' : 'bg-gradient-to-br from-gray-50 to-blue-50'} ${user !== null && user !== void 0 && user.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`,\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3,\n            delay: 0.1\n          },\n          className: \"h-full\",\n          children: children\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n}\n_s(ProtectedRoute, \"NYNgiZh2QMxHh24W/N9oO4sdwys=\", false, function () {\n  return [useSelector, useSelector, useSelector, useDispatch, useNavigate, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["message", "React", "useEffect", "useState", "useRef", "motion", "getUserInfo", "useDispatch", "useSelector", "SetUser", "useNavigate", "useLocation", "HideLoading", "ShowLoading", "checkPaymentStatus", "SetSubscription", "setPaymentVerificationNeeded", "AdminNavigation", "ModernSidebar", "TbHome", "TbBrandTanzania", "TbMenu2", "TbX", "TbChevronDown", "TbLogout", "TbUser", "TbSettings", "TbBell", "TbStar", "OnlineStatusIndicator", "NotificationBell", "ProfilePicture", "setUserOnline", "setUserOffline", "sendHeartbeat", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "state", "isPaymentPending", "setIsPaymentPending", "intervalRef", "heartbeatRef", "subscriptionData", "subscription", "paymentVerificationNeeded", "payment", "dispatch", "navigate", "location", "activeRoute", "pathname", "getUserData", "response", "success", "data", "localStorage", "setItem", "JSON", "stringify", "console", "log", "name", "isAdmin", "email", "error", "token", "getItem", "storedUser", "userData", "parse", "includes", "verifyPaymentStatus", "paymentStatus", "current", "clearInterval", "paymentRequired", "setInterval", "catch", "handleBeforeUnload", "window", "addEventListener", "removeEventListener", "getButtonClass", "title", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "header", "initial", "y", "opacity", "animate", "div", "scale", "transition", "duration", "delay", "style", "width", "height", "src", "alt", "objectFit", "onError", "e", "target", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "unreadCount", "size", "showOnlineStatus", "class", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/ProtectedRoute.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport AdminNavigation from \"./AdminNavigation\";\r\nimport ModernSidebar from \"./ModernSidebar\";\r\nimport { TbHome, TbBrandTanzania, TbMenu2, TbX, TbChevronDown, Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ell, TbStar } from \"react-icons/tb\";\r\nimport OnlineStatusIndicator from './common/OnlineStatusIndicator';\r\nimport NotificationBell from './common/NotificationBell';\r\nimport ProfilePicture from './common/ProfilePicture';\r\nimport { setUserOnline, setUserOffline, sendHeartbeat } from '../apicalls/notifications';\r\n\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const heartbeatRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const activeRoute = location.pathname;\r\n\r\n\r\n\r\n\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n\r\n        // Store user data in localStorage for consistency\r\n        localStorage.setItem(\"user\", JSON.stringify(response.data));\r\n\r\n        // Debug log to help identify admin login issues\r\n        console.log(\"User data loaded:\", {\r\n          name: response.data.name,\r\n          isAdmin: response.data.isAdmin,\r\n          email: response.data.email\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      // Check if user data already exists in Redux (from login)\r\n      if (!user) {\r\n        // Try to load user from localStorage first\r\n        const storedUser = localStorage.getItem(\"user\");\r\n        if (storedUser) {\r\n          try {\r\n            const userData = JSON.parse(storedUser);\r\n            console.log(\"ProtectedRoute: Loading user from localStorage\", { name: userData.name, isAdmin: userData.isAdmin });\r\n            dispatch(SetUser(userData));\r\n          } catch (error) {\r\n            console.log(\"ProtectedRoute: Error parsing stored user data, fetching from server\");\r\n            getUserData();\r\n          }\r\n        } else {\r\n          console.log(\"ProtectedRoute: No user in Redux or localStorage, fetching from server\");\r\n          getUserData();\r\n        }\r\n      } else {\r\n        console.log(\"ProtectedRoute: User already in Redux\", { name: user.name, isAdmin: user.isAdmin });\r\n      }\r\n    } else {\r\n      navigate(\"/login\");\r\n    }\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n  // Online status management\r\n  useEffect(() => {\r\n    if (user && !user.isAdmin) {\r\n      // Set user as online when component mounts\r\n      setUserOnline().catch(console.error);\r\n\r\n      // Send heartbeat every 2 minutes\r\n      heartbeatRef.current = setInterval(() => {\r\n        sendHeartbeat().catch(console.error);\r\n      }, 120000); // 2 minutes\r\n\r\n      // Set user as offline when component unmounts or page unloads\r\n      const handleBeforeUnload = () => {\r\n        setUserOffline().catch(console.error);\r\n      };\r\n\r\n      window.addEventListener('beforeunload', handleBeforeUnload);\r\n\r\n      return () => {\r\n        if (heartbeatRef.current) {\r\n          clearInterval(heartbeatRef.current);\r\n        }\r\n        window.removeEventListener('beforeunload', handleBeforeUnload);\r\n        setUserOffline().catch(console.error);\r\n      };\r\n    }\r\n  }, [user]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex flex-col\">\r\n      {/* Modern Sidebar for regular users */}\r\n      {!user?.isAdmin && <ModernSidebar />}\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex-1 flex flex-col min-h-screen\">\r\n        {/* Modern Responsive Header - Show for all users */}\r\n        {(\r\n          <motion.header\r\n            initial={{ y: -20, opacity: 0 }}\r\n            animate={{ y: 0, opacity: 1 }}\r\n            className={`nav-modern ${\r\n              location.pathname.includes('/quiz') || location.pathname.includes('/write-exam')\r\n                ? 'quiz-header bg-gradient-to-r from-blue-600/98 via-blue-700/95 to-blue-600/98'\r\n                : 'bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98'\r\n            } backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20`}\r\n          >\r\n          <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n            <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n              {/* Left section - Modern Sidebar */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                {/* Modern Sidebar Toggle Button */}\r\n              </div>\r\n\r\n              {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n              <div className=\"flex-1 flex justify-center\">\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.9 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.6, delay: 0.2 }}\r\n                  className=\"relative group flex items-center space-x-3\"\r\n                >\r\n                  {/* Tanzania Flag - Using actual flag image */}\r\n                  <div\r\n                    className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '24px'\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"https://flagcdn.com/w40/tz.png\"\r\n                      alt=\"Tanzania Flag\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        // Fallback to another flag source if first fails\r\n                        e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                        e.target.onerror = () => {\r\n                          // Final fallback - hide image and show text\r\n                          e.target.style.display = 'none';\r\n                          e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                        };\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Amazing Animated Brainwave Text */}\r\n                  <div className=\"relative brainwave-container\">\r\n                    <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                        style={{\r\n                          fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                          letterSpacing: '-0.02em'\r\n                        }}>\r\n                      {/* Brain - with amazing effects */}\r\n                      <motion.span\r\n                        className=\"relative inline-block\"\r\n                        initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                        animate={{\r\n                          opacity: 1,\r\n                          x: 0,\r\n                          scale: 1,\r\n                          textShadow: [\r\n                            \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                            \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                            \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                          ]\r\n                        }}\r\n                        transition={{\r\n                          duration: 1,\r\n                          delay: 0.3,\r\n                          textShadow: {\r\n                            duration: 2,\r\n                            repeat: Infinity,\r\n                            ease: \"easeInOut\"\r\n                          }\r\n                        }}\r\n                        whileHover={{\r\n                          scale: 1.1,\r\n                          rotate: [0, -2, 2, 0],\r\n                          transition: { duration: 0.3 }\r\n                        }}\r\n                        style={{\r\n                          color: '#1f2937',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                        }}\r\n                      >\r\n                        Brain\r\n\r\n                        {/* Electric spark */}\r\n                        <motion.div\r\n                          className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                          animate={{\r\n                            opacity: [0, 1, 0],\r\n                            scale: [0.5, 1.2, 0.5],\r\n                            backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                          }}\r\n                          transition={{\r\n                            duration: 1.5,\r\n                            repeat: Infinity,\r\n                            delay: 2\r\n                          }}\r\n                          style={{\r\n                            backgroundColor: '#3b82f6',\r\n                            boxShadow: '0 0 10px #3b82f6'\r\n                          }}\r\n                        />\r\n                      </motion.span>\r\n\r\n                      {/* Wave - with flowing effects (no space) */}\r\n                      <motion.span\r\n                        className=\"relative inline-block\"\r\n                        initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                        animate={{\r\n                          opacity: 1,\r\n                          x: 0,\r\n                          scale: 1,\r\n                          y: [0, -2, 0, 2, 0],\r\n                          textShadow: [\r\n                            \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                            \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                            \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                          ]\r\n                        }}\r\n                        transition={{\r\n                          duration: 1,\r\n                          delay: 0.5,\r\n                          y: {\r\n                            duration: 3,\r\n                            repeat: Infinity,\r\n                            ease: \"easeInOut\"\r\n                          },\r\n                          textShadow: {\r\n                            duration: 2.5,\r\n                            repeat: Infinity,\r\n                            ease: \"easeInOut\"\r\n                          }\r\n                        }}\r\n                        whileHover={{\r\n                          scale: 1.1,\r\n                          rotate: [0, 2, -2, 0],\r\n                          transition: { duration: 0.3 }\r\n                        }}\r\n                        style={{\r\n                          color: '#059669',\r\n                          fontWeight: '900',\r\n                          textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                        }}\r\n                      >\r\n                        wave\r\n\r\n                        {/* Wave particle */}\r\n                        <motion.div\r\n                          className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\r\n                          animate={{\r\n                            opacity: [0, 1, 0],\r\n                            x: [0, 40, 80],\r\n                            y: [0, -5, 0, 5, 0],\r\n                            backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                          }}\r\n                          transition={{\r\n                            duration: 3,\r\n                            repeat: Infinity,\r\n                            delay: 1\r\n                          }}\r\n                          style={{\r\n                            backgroundColor: '#10b981',\r\n                            boxShadow: '0 0 8px #10b981'\r\n                          }}\r\n                        />\r\n                      </motion.span>\r\n                    </h1>\r\n\r\n                    {/* Glowing underline effect */}\r\n                    <motion.div\r\n                      className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                      initial={{ width: 0, opacity: 0 }}\r\n                      animate={{\r\n                        width: '100%',\r\n                        opacity: 1,\r\n                        boxShadow: [\r\n                          '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                          '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                          '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1.5,\r\n                        delay: 1.2,\r\n                        boxShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      style={{\r\n                        background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                        boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Official Logo - Small like profile */}\r\n                  <div\r\n                    className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                    style={{\r\n                      background: '#f0f0f0',\r\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  >\r\n                    <img\r\n                      src=\"/favicon.png\"\r\n                      alt=\"Brainwave Logo\"\r\n                      className=\"w-full h-full object-cover\"\r\n                      style={{ objectFit: 'cover' }}\r\n                      onError={(e) => {\r\n                        e.target.style.display = 'none';\r\n                        e.target.nextSibling.style.display = 'flex';\r\n                      }}\r\n                    />\r\n                    <div\r\n                      className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                      style={{\r\n                        display: 'none',\r\n                        fontSize: '12px'\r\n                      }}\r\n                    >\r\n                      🧠\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Modern Glow Effect */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n                </motion.div>\r\n              </div>\r\n\r\n              {/* Right Section - Notifications + User Profile */}\r\n              <div className=\"flex items-center justify-end space-x-2 sm:space-x-3\">\r\n                {/* Notification Bell */}\r\n                {!user?.isAdmin && (\r\n                  <motion.div\r\n                    initial={{ opacity: 0, scale: 0.8 }}\r\n                    animate={{ opacity: 1, scale: 1 }}\r\n                    transition={{ duration: 0.5, delay: 0.2 }}\r\n                  >\r\n                    <NotificationBell unreadCount={2} />\r\n                  </motion.div>\r\n                )}\r\n\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 group\"\r\n                >\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={user}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  />\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\">\r\n                      Class {user?.class}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n        )}\r\n\r\n        {/* Page Content */}\r\n        <main className={`flex-1 overflow-auto ${\r\n          user?.isAdmin\r\n            ? 'bg-gray-100'\r\n            : 'bg-gradient-to-br from-gray-50 to-blue-50'\r\n        } ${user?.isAdmin ? 'p-6' : 'pb-20 sm:pb-0'}`}>\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,WAAW,EAAEC,WAAW,QAAQ,sBAAsB;AAC/D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAO,sBAAsB;AAC7B,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,4BAA4B,QAAQ,0BAA0B;AACvE,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,MAAM,EAAEC,eAAe,EAAEC,OAAO,EAAEC,GAAG,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AACnI,OAAOC,qBAAqB,MAAM,gCAAgC;AAClE,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,aAAa,EAAEC,cAAc,EAAEC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGzF,SAASC,cAAcA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAK,CAAC,GAAGhC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMyC,WAAW,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMyC,YAAY,GAAGzC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM;IAAE0C;EAAiB,CAAC,GAAGtC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACM,YAAY,CAAC;EACvE,MAAM;IAAEC;EAA0B,CAAC,GAAGxC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACQ,OAAO,CAAC;EAC3E,MAAMC,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM0C,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM0C,WAAW,GAAGD,QAAQ,CAACE,QAAQ;EAMrC,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlD,WAAW,CAAC,CAAC;MACpC,IAAIkD,QAAQ,CAACC,OAAO,EAAE;QACpBP,QAAQ,CAACzC,OAAO,CAAC+C,QAAQ,CAACE,IAAI,CAAC,CAAC;;QAEhC;QACAC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,QAAQ,CAACE,IAAI,CAAC,CAAC;;QAE3D;QACAK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;UAC/BC,IAAI,EAAET,QAAQ,CAACE,IAAI,CAACO,IAAI;UACxBC,OAAO,EAAEV,QAAQ,CAACE,IAAI,CAACQ,OAAO;UAC9BC,KAAK,EAAEX,QAAQ,CAACE,IAAI,CAACS;QACvB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLnE,OAAO,CAACoE,KAAK,CAACZ,QAAQ,CAACxD,OAAO,CAAC;QAC/BmD,QAAQ,CAAC,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdjB,QAAQ,CAAC,QAAQ,CAAC;MAClBnD,OAAO,CAACoE,KAAK,CAACA,KAAK,CAACpE,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDE,SAAS,CAAC,MAAM;IACd,MAAMmE,KAAK,GAAGV,YAAY,CAACW,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAID,KAAK,EAAE;MACT;MACA,IAAI,CAAC7B,IAAI,EAAE;QACT;QACA,MAAM+B,UAAU,GAAGZ,YAAY,CAACW,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIC,UAAU,EAAE;UACd,IAAI;YACF,MAAMC,QAAQ,GAAGX,IAAI,CAACY,KAAK,CAACF,UAAU,CAAC;YACvCR,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;cAAEC,IAAI,EAAEO,QAAQ,CAACP,IAAI;cAAEC,OAAO,EAAEM,QAAQ,CAACN;YAAQ,CAAC,CAAC;YACjHhB,QAAQ,CAACzC,OAAO,CAAC+D,QAAQ,CAAC,CAAC;UAC7B,CAAC,CAAC,OAAOJ,KAAK,EAAE;YACdL,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;YACnFT,WAAW,CAAC,CAAC;UACf;QACF,CAAC,MAAM;UACLQ,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;UACrFT,WAAW,CAAC,CAAC;QACf;MACF,CAAC,MAAM;QACLQ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;UAAEC,IAAI,EAAEzB,IAAI,CAACyB,IAAI;UAAEC,OAAO,EAAE1B,IAAI,CAAC0B;QAAQ,CAAC,CAAC;MAClG;IACF,CAAC,MAAM;MACLf,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,EAAE,CAAC;EAINjD,SAAS,CAAC,MAAM;IACd,IAAIwC,gBAAgB,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACgC,QAAQ,CAACrB,WAAW,CAAC,EAAE;MACrEF,QAAQ,CAAC,aAAa,CAAC;IACzB;EACF,CAAC,EAAE,CAACT,gBAAgB,EAAEW,WAAW,EAAEF,QAAQ,CAAC,CAAC;EAE7C,MAAMwB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMjB,IAAI,GAAG,MAAM5C,kBAAkB,CAAC,CAAC;MACvCiD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEN,IAAI,CAAC;MACpC,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEU,KAAK,IAAI,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,aAAa,MAAK,MAAM,EAAE;QACjD,IAAI9B,gBAAgB,KAAK,IAAI,EAAE;UAC7BI,QAAQ,CAACnC,eAAe,CAAC,IAAI,CAAC,CAAC;QACjC;QACA4B,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MACI;QACHA,mBAAmB,CAAC,KAAK,CAAC;QAC1BO,QAAQ,CAACnC,eAAe,CAAC2C,IAAI,CAAC,CAAC;QAC/B,IAAId,WAAW,CAACiC,OAAO,EAAE;UACvBC,aAAa,CAAClC,WAAW,CAACiC,OAAO,CAAC;QACpC;MACF;IACF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEI,KAAK,CAAC;MACpDlB,QAAQ,CAACnC,eAAe,CAAC,IAAI,CAAC,CAAC;MAC/B4B,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAEDzC,SAAS,CAAC,MAAM;IACd,IAAIsC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuC,eAAe,IAAI,EAACvC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,OAAO,GAAE;MAC3CH,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MAEvC,IAAIhB,yBAAyB,EAAE;QAC7Be,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3CpB,WAAW,CAACiC,OAAO,GAAGG,WAAW,CAAC,MAAM;UACtCjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCW,mBAAmB,CAAC,CAAC;QACvB,CAAC,EAAE,KAAK,CAAC;QACTzB,QAAQ,CAAClC,4BAA4B,CAAC,KAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACgC,yBAAyB,CAAC,CAAC;EAE/B9C,SAAS,CAAC,MAAM;IACd,IAAIsC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuC,eAAe,IAAI,EAACvC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,OAAO,GAAE;MAC3CH,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BW,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACnC,IAAI,EAAEa,WAAW,CAAC,CAAC;;EAEvB;EACAnD,SAAS,CAAC,MAAM;IACd,IAAIsC,IAAI,IAAI,CAACA,IAAI,CAAC0B,OAAO,EAAE;MACzB;MACAlC,aAAa,CAAC,CAAC,CAACiD,KAAK,CAAClB,OAAO,CAACK,KAAK,CAAC;;MAEpC;MACAvB,YAAY,CAACgC,OAAO,GAAGG,WAAW,CAAC,MAAM;QACvC9C,aAAa,CAAC,CAAC,CAAC+C,KAAK,CAAClB,OAAO,CAACK,KAAK,CAAC;MACtC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;MAEZ;MACA,MAAMc,kBAAkB,GAAGA,CAAA,KAAM;QAC/BjD,cAAc,CAAC,CAAC,CAACgD,KAAK,CAAClB,OAAO,CAACK,KAAK,CAAC;MACvC,CAAC;MAEDe,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEF,kBAAkB,CAAC;MAE3D,OAAO,MAAM;QACX,IAAIrC,YAAY,CAACgC,OAAO,EAAE;UACxBC,aAAa,CAACjC,YAAY,CAACgC,OAAO,CAAC;QACrC;QACAM,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEH,kBAAkB,CAAC;QAC9DjD,cAAc,CAAC,CAAC,CAACgD,KAAK,CAAClB,OAAO,CAACK,KAAK,CAAC;MACvC,CAAC;IACH;EACF,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;EAGV,MAAM8C,cAAc,GAAIC,KAAK,IAAK;IAChC;IACA,IAAI,CAAC/C,IAAI,CAACuC,eAAe,IAAIQ,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3F,OAAO,EAAE,CAAC,CAAC;IACb;;IAEA,OAAO,CAAAzC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE8B,aAAa,MAAK,MAAM,IAAIpC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuC,eAAe,GACtE,iBAAiB,GACjB,EAAE;EACR,CAAC;EAKD,oBACE3C,OAAA;IAAKoD,SAAS,EAAC,0CAA0C;IAAAlD,QAAA,GAEtD,EAACE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,OAAO,kBAAI9B,OAAA,CAAClB,aAAa;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGpCxD,OAAA;MAAKoD,SAAS,EAAC,mCAAmC;MAAAlD,QAAA,gBAG9CF,OAAA,CAAC/B,MAAM,CAACwF,MAAM;QACZC,OAAO,EAAE;UAAEC,CAAC,EAAE,CAAC,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QAChCC,OAAO,EAAE;UAAEF,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE;QAAE,CAAE;QAC9BR,SAAS,EAAG,cACVpC,QAAQ,CAACE,QAAQ,CAACoB,QAAQ,CAAC,OAAO,CAAC,IAAItB,QAAQ,CAACE,QAAQ,CAACoB,QAAQ,CAAC,aAAa,CAAC,GAC5E,8EAA8E,GAC9E,2DACL,8FAA8F;QAAApC,QAAA,eAEjGF,OAAA;UAAKoD,SAAS,EAAC,+CAA+C;UAAAlD,QAAA,eAC5DF,OAAA;YAAKoD,SAAS,EAAC,wEAAwE;YAAAlD,QAAA,gBAErFF,OAAA;cAAKoD,SAAS,EAAC;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEvC,CAAC,eAGNxD,OAAA;cAAKoD,SAAS,EAAC,4BAA4B;cAAAlD,QAAA,eACzCF,OAAA,CAAC/B,MAAM,CAAC6F,GAAG;gBACTJ,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBACpCF,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1Cd,SAAS,EAAC,4CAA4C;gBAAAlD,QAAA,gBAGtDF,OAAA;kBACEoD,SAAS,EAAC,wEAAwE;kBAClFe,KAAK,EAAE;oBACLC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV,CAAE;kBAAAnE,QAAA,eAEFF,OAAA;oBACEsE,GAAG,EAAC,gCAAgC;oBACpCC,GAAG,EAAC,eAAe;oBACnBnB,SAAS,EAAC,4BAA4B;oBACtCe,KAAK,EAAE;sBAAEK,SAAS,EAAE;oBAAQ,CAAE;oBAC9BC,OAAO,EAAGC,CAAC,IAAK;sBACd;sBACAA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,8GAA8G;sBAC7HI,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,MAAM;wBACvB;wBACAF,CAAC,CAACC,MAAM,CAACR,KAAK,CAACU,OAAO,GAAG,MAAM;wBAC/BH,CAAC,CAACC,MAAM,CAACG,aAAa,CAACC,SAAS,GAAG,+GAA+G;sBACpJ,CAAC;oBACH;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNxD,OAAA;kBAAKoD,SAAS,EAAC,8BAA8B;kBAAAlD,QAAA,gBAC3CF,OAAA;oBAAIoD,SAAS,EAAC,qFAAqF;oBAC/Fe,KAAK,EAAE;sBACLa,UAAU,EAAE,yDAAyD;sBACrEC,aAAa,EAAE;oBACjB,CAAE;oBAAA/E,QAAA,gBAEJF,OAAA,CAAC/B,MAAM,CAACiH,IAAI;sBACV9B,SAAS,EAAC,uBAAuB;sBACjCM,OAAO,EAAE;wBAAEE,OAAO,EAAE,CAAC;wBAAEuB,CAAC,EAAE,CAAC,EAAE;wBAAEpB,KAAK,EAAE;sBAAI,CAAE;sBAC5CF,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC;wBACVuB,CAAC,EAAE,CAAC;wBACJpB,KAAK,EAAE,CAAC;wBACRqB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;sBAEtC,CAAE;sBACFpB,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXC,KAAK,EAAE,GAAG;wBACVkB,UAAU,EAAE;0BACVnB,QAAQ,EAAE,CAAC;0BACXoB,MAAM,EAAEC,QAAQ;0BAChBC,IAAI,EAAE;wBACR;sBACF,CAAE;sBACFC,UAAU,EAAE;wBACVzB,KAAK,EAAE,GAAG;wBACV0B,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACrBzB,UAAU,EAAE;0BAAEC,QAAQ,EAAE;wBAAI;sBAC9B,CAAE;sBACFE,KAAK,EAAE;wBACLuB,KAAK,EAAE,SAAS;wBAChBC,UAAU,EAAE,KAAK;wBACjBP,UAAU,EAAE;sBACd,CAAE;sBAAAlF,QAAA,GACH,OAGC,eACAF,OAAA,CAAC/B,MAAM,CAAC6F,GAAG;wBACTV,SAAS,EAAC,+CAA+C;wBACzDS,OAAO,EAAE;0BACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;0BAClBG,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;0BACtB6B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;wBACnD,CAAE;wBACF5B,UAAU,EAAE;0BACVC,QAAQ,EAAE,GAAG;0BACboB,MAAM,EAAEC,QAAQ;0BAChBpB,KAAK,EAAE;wBACT,CAAE;wBACFC,KAAK,EAAE;0BACLyB,eAAe,EAAE,SAAS;0BAC1BC,SAAS,EAAE;wBACb;sBAAE;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC,eAGdxD,OAAA,CAAC/B,MAAM,CAACiH,IAAI;sBACV9B,SAAS,EAAC,uBAAuB;sBACjCM,OAAO,EAAE;wBAAEE,OAAO,EAAE,CAAC;wBAAEuB,CAAC,EAAE,EAAE;wBAAEpB,KAAK,EAAE;sBAAI,CAAE;sBAC3CF,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC;wBACVuB,CAAC,EAAE,CAAC;wBACJpB,KAAK,EAAE,CAAC;wBACRJ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnByB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;sBAEtC,CAAE;sBACFpB,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXC,KAAK,EAAE,GAAG;wBACVP,CAAC,EAAE;0BACDM,QAAQ,EAAE,CAAC;0BACXoB,MAAM,EAAEC,QAAQ;0BAChBC,IAAI,EAAE;wBACR,CAAC;wBACDH,UAAU,EAAE;0BACVnB,QAAQ,EAAE,GAAG;0BACboB,MAAM,EAAEC,QAAQ;0BAChBC,IAAI,EAAE;wBACR;sBACF,CAAE;sBACFC,UAAU,EAAE;wBACVzB,KAAK,EAAE,GAAG;wBACV0B,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;wBACrBzB,UAAU,EAAE;0BAAEC,QAAQ,EAAE;wBAAI;sBAC9B,CAAE;sBACFE,KAAK,EAAE;wBACLuB,KAAK,EAAE,SAAS;wBAChBC,UAAU,EAAE,KAAK;wBACjBP,UAAU,EAAE;sBACd,CAAE;sBAAAlF,QAAA,GACH,MAGC,eACAF,OAAA,CAAC/B,MAAM,CAAC6F,GAAG;wBACTV,SAAS,EAAC,gDAAgD;wBAC1DS,OAAO,EAAE;0BACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;0BAClBuB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;0BACdxB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;0BACnBiC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;wBACnD,CAAE;wBACF5B,UAAU,EAAE;0BACVC,QAAQ,EAAE,CAAC;0BACXoB,MAAM,EAAEC,QAAQ;0BAChBpB,KAAK,EAAE;wBACT,CAAE;wBACFC,KAAK,EAAE;0BACLyB,eAAe,EAAE,SAAS;0BAC1BC,SAAS,EAAE;wBACb;sBAAE;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eAGLxD,OAAA,CAAC/B,MAAM,CAAC6F,GAAG;oBACTV,SAAS,EAAC,4CAA4C;oBACtDM,OAAO,EAAE;sBAAEU,KAAK,EAAE,CAAC;sBAAER,OAAO,EAAE;oBAAE,CAAE;oBAClCC,OAAO,EAAE;sBACPO,KAAK,EAAE,MAAM;sBACbR,OAAO,EAAE,CAAC;sBACViC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACF7B,UAAU,EAAE;sBACVC,QAAQ,EAAE,GAAG;sBACbC,KAAK,EAAE,GAAG;sBACV2B,SAAS,EAAE;wBACT5B,QAAQ,EAAE,CAAC;wBACXoB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFpB,KAAK,EAAE;sBACL2B,UAAU,EAAE,mDAAmD;sBAC/DD,SAAS,EAAE;oBACb;kBAAE;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNxD,OAAA;kBACEoD,SAAS,EAAC,gEAAgE;kBAC1Ee,KAAK,EAAE;oBACL2B,UAAU,EAAE,SAAS;oBACrBD,SAAS,EAAE,4BAA4B;oBACvCzB,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV,CAAE;kBAAAnE,QAAA,gBAEFF,OAAA;oBACEsE,GAAG,EAAC,cAAc;oBAClBC,GAAG,EAAC,gBAAgB;oBACpBnB,SAAS,EAAC,4BAA4B;oBACtCe,KAAK,EAAE;sBAAEK,SAAS,EAAE;oBAAQ,CAAE;oBAC9BC,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAACR,KAAK,CAACU,OAAO,GAAG,MAAM;sBAC/BH,CAAC,CAACC,MAAM,CAACoB,WAAW,CAAC5B,KAAK,CAACU,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFxD,OAAA;oBACEoD,SAAS,EAAC,gHAAgH;oBAC1He,KAAK,EAAE;sBACLU,OAAO,EAAE,MAAM;sBACfmB,QAAQ,EAAE;oBACZ,CAAE;oBAAA9F,QAAA,EACH;kBAED;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNxD,OAAA;kBAAKoD,SAAS,EAAC;gBAAyK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNxD,OAAA;cAAKoD,SAAS,EAAC,sDAAsD;cAAAlD,QAAA,GAElE,EAACE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,OAAO,kBACb9B,OAAA,CAAC/B,MAAM,CAAC6F,GAAG;gBACTJ,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBACpCF,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAAAhE,QAAA,eAE1CF,OAAA,CAACN,gBAAgB;kBAACuG,WAAW,EAAE;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CACb,eAEDxD,OAAA,CAAC/B,MAAM,CAAC6F,GAAG;gBACTJ,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAI,CAAE;gBACpCF,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEG,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAI,CAAE;gBAC1Cd,SAAS,EAAC,mCAAmC;gBAAAlD,QAAA,gBAG7CF,OAAA,CAACL,cAAc;kBACbS,IAAI,EAAEA,IAAK;kBACX8F,IAAI,EAAC,IAAI;kBACTC,gBAAgB,EAAE,IAAK;kBACvBhC,KAAK,EAAE;oBACLC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE;kBACV;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGFxD,OAAA;kBAAKoD,SAAS,EAAC,4BAA4B;kBAAAlD,QAAA,gBACzCF,OAAA;oBAAKoD,SAAS,EAAC,wGAAwG;oBAAAlD,QAAA,EACpH,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,IAAI,KAAI;kBAAM;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACNxD,OAAA;oBAAKoD,SAAS,EAAC,iFAAiF;oBAAAlD,QAAA,GAAC,QACzF,EAACE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,KAAK;kBAAA;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAIhBxD,OAAA;QAAMoD,SAAS,EAAG,wBAChBhD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,OAAO,GACT,aAAa,GACb,2CACL,IAAG1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0B,OAAO,GAAG,KAAK,GAAG,eAAgB,EAAE;QAAA5B,QAAA,eAC5CF,OAAA,CAAC/B,MAAM,CAAC6F,GAAG;UACTJ,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9BK,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,QAAQ;UAAAlD,QAAA,EAEjBA;QAAQ;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACrD,EAAA,CAleQF,cAAc;EAAA,QACJ7B,WAAW,EAICA,WAAW,EACFA,WAAW,EAChCD,WAAW,EACXG,WAAW,EACXC,WAAW;AAAA;AAAA8H,EAAA,GATrBpG,cAAc;AAoevB,eAAeA,cAAc;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}