import React, { startTransition } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { TbTrophy, TbClock, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar, TbTarget, TbBulb, TbRocket } from 'react-icons/tb';

const QuizResult = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams();
  
  // Get result data from navigation state
  const resultData = location.state || {
    percentage: 0,
    correctAnswers: 0,
    totalQuestions: 0,
    timeTaken: 0,
    resultDetails: [],
    xpData: null,
    quizName: 'Quiz',
    quizSubject: 'General',
    passingPercentage: 60,
    verdict: 'Fail'
  };

  const {
    percentage,
    correctAnswers,
    totalQuestions,
    timeTaken,
    xpData,
    quizName,
    quizSubject,
    passingPercentage,
    verdict,
    resultDetails
  } = resultData;
  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleBackToQuizzes = () => {
    console.log('🏠 Navigating to quiz listing...');
    startTransition(() => {
      navigate('/user/quiz');
    });
  };

  const handleRetakeQuiz = () => {
    console.log('🔄 Retaking quiz with ID:', id);
    if (id) {
      startTransition(() => {
        navigate(`/quiz/${id}/play`);
      });
    } else {
      console.log('❌ No quiz ID available, going to quiz listing');
      startTransition(() => {
        navigate('/user/quiz');
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">


      <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-2xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full mb-4 ${
            isPassed ? 'bg-green-100' : 'bg-red-100'
          }`}>
            <TbTrophy className={`w-10 h-10 ${
              isPassed ? 'text-green-600' : 'text-red-600'
            }`} />
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Quiz Completed!
          </h1>

          <p className={`text-lg font-semibold ${
            isPassed ? 'text-green-600' : 'text-red-600'
          }`}>
            {isPassed ? 'Congratulations! You Passed!' : 'Keep Practicing!'}
          </p>

          <p className="text-gray-600 mt-2">
            {quizName} - {quizSubject}
          </p>
        </div>

        {/* Score Display */}
        <div className="text-center mb-8">
          <div className={`inline-block px-8 py-4 rounded-2xl ${
            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'
          }`}>
            <div className={`text-5xl font-bold mb-2 ${
              isPassed ? 'text-green-600' : 'text-red-600'
            }`}>
              {percentage}%
            </div>
            <div className="text-gray-600">
              Your Score
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-green-50 rounded-xl p-4 text-center border border-green-200">
            <div className="flex items-center justify-center mb-2">
              <TbCheck className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">{correctAnswers}</div>
            <div className="text-sm text-green-700 font-medium">Correct</div>
          </div>

          <div className="bg-red-50 rounded-xl p-4 text-center border border-red-200">
            <div className="flex items-center justify-center mb-2">
              <TbX className="w-6 h-6 text-red-600" />
            </div>
            <div className="text-2xl font-bold text-red-600">{totalQuestions - correctAnswers}</div>
            <div className="text-sm text-red-700 font-medium">Wrong</div>
          </div>

          <div className="bg-gray-50 rounded-xl p-4 text-center border border-gray-200">
            <div className="flex items-center justify-center mb-2">
              <TbTrophy className="w-6 h-6 text-gray-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{totalQuestions}</div>
            <div className="text-sm text-gray-600 font-medium">Total</div>
          </div>

          <div className="bg-blue-50 rounded-xl p-4 text-center border border-blue-200">
            <div className="flex items-center justify-center mb-2">
              <TbClock className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{formatTime(timeTaken)}</div>
            <div className="text-sm text-blue-700 font-medium">Time Taken</div>
          </div>
        </div>

        {/* Performance Message */}
        <div className={`rounded-xl p-6 mb-6 border-2 ${
          isPassed
            ? 'bg-green-50 border-green-200'
            : 'bg-orange-50 border-orange-200'
        }`}>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">📊 Quiz Results Breakdown</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Questions Answered:</span>
              <span className="font-semibold text-gray-900">{totalQuestions} / {totalQuestions}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Correct Answers:</span>
              <span className="font-semibold text-green-600">{correctAnswers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Wrong Answers:</span>
              <span className="font-semibold text-red-600">{totalQuestions - correctAnswers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Accuracy Rate:</span>
              <span className="font-semibold text-blue-600">{percentage}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Pass Mark:</span>
              <span className="font-semibold text-gray-600">{passingPercentage || 60}%</span>
            </div>
            <div className="flex justify-between items-center border-t pt-2 mt-3">
              <span className="text-gray-700 font-medium">Result:</span>
              <span className={`font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`}>
                {isPassed ? '✅ PASSED' : '❌ FAILED'}
              </span>
            </div>
          </div>

          <div className="mt-4 p-3 bg-white rounded-lg">
            <p className={`text-sm ${isPassed ? 'text-green-700' : 'text-orange-700'}`}>
              {isPassed
                ? `🎉 Congratulations! You passed with ${percentage}% (${correctAnswers}/${totalQuestions} correct). Great job!`
                : `📚 You scored ${percentage}% (${correctAnswers}/${totalQuestions} correct). You need ${passingPercentage || 60}% to pass. Keep studying and try again!`
              }
            </p>
          </div>
        </div>

        {/* XP Earned Section */}
        {xpData && (
          <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 mb-6 border border-purple-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                <TbStar className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">XP Earned!</h3>
                <p className="text-purple-600 font-bold text-2xl">+{xpData.xpAwarded || 0} XP</p>
              </div>
            </div>

            {xpData.breakdown && (
              <div className="grid grid-cols-2 gap-3 text-sm">
                {xpData.breakdown.baseCompletion && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Quiz Completion:</span>
                    <span className="font-medium">+{xpData.breakdown.baseCompletion} XP</span>
                  </div>
                )}
                {xpData.breakdown.correctAnswers && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Correct Answers:</span>
                    <span className="font-medium">+{xpData.breakdown.correctAnswers} XP</span>
                  </div>
                )}
                {xpData.breakdown.perfectScore && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Perfect Score:</span>
                    <span className="font-medium text-green-600">+{xpData.breakdown.perfectScore} XP</span>
                  </div>
                )}
                {xpData.breakdown.firstAttemptBonus && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">First Attempt:</span>
                    <span className="font-medium text-blue-600">+{xpData.breakdown.firstAttemptBonus} XP</span>
                  </div>
                )}
                {xpData.breakdown.speedBonus && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Speed Bonus:</span>
                    <span className="font-medium text-orange-600">+{xpData.breakdown.speedBonus} XP</span>
                  </div>
                )}
                {xpData.breakdown.difficultyBonus && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Difficulty Bonus:</span>
                    <span className="font-medium text-red-600">+{xpData.breakdown.difficultyBonus} XP</span>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Learning Summary Section */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <TbBrain className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">📚 Learning Summary</h3>
          </div>

          <div className="space-y-4">
            {/* Performance Analysis */}
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <h4 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                <TbChartBar className="w-5 h-5 text-blue-600" />
                Performance Analysis
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Accuracy Rate:</span>
                  <span className={`font-semibold ${percentage >= 80 ? 'text-green-600' : percentage >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {percentage}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Questions Attempted:</span>
                  <span className="font-semibold text-gray-800">{totalQuestions}/{totalQuestions}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Time Efficiency:</span>
                  <span className="font-semibold text-blue-600">
                    {timeTaken < 60 ? 'Excellent' : timeTaken < 120 ? 'Good' : 'Could Improve'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Subject:</span>
                  <span className="font-semibold text-gray-800">{quizSubject}</span>
                </div>
              </div>
            </div>

            {/* Areas for Improvement */}
            {!isPassed && (
              <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                <h4 className="font-semibold text-orange-800 mb-3 flex items-center gap-2">
                  <TbTarget className="w-5 h-5 text-orange-600" />
                  Areas for Improvement
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-start gap-2">
                    <span className="text-orange-600 mt-1">•</span>
                    <span className="text-orange-700">
                      You need {Math.ceil((passingPercentage || 60) - percentage)}% more to pass. Focus on understanding the concepts better.
                    </span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-orange-600 mt-1">•</span>
                    <span className="text-orange-700">
                      Review the {totalQuestions - correctAnswers} questions you got wrong and understand the correct answers.
                    </span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-orange-600 mt-1">•</span>
                    <span className="text-orange-700">
                      Practice more questions in {quizSubject} to strengthen your knowledge.
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Study Recommendations */}
            <div className="bg-green-50 rounded-lg p-4 border border-green-200">
              <h4 className="font-semibold text-green-800 mb-3 flex items-center gap-2">
                <TbBulb className="w-5 h-5 text-green-600" />
                Study Recommendations
              </h4>
              <div className="space-y-2 text-sm">
                {isPassed ? (
                  <>
                    <div className="flex items-start gap-2">
                      <span className="text-green-600 mt-1">✓</span>
                      <span className="text-green-700">
                        Great job! You've mastered this topic. Try more advanced quizzes to challenge yourself.
                      </span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-green-600 mt-1">✓</span>
                      <span className="text-green-700">
                        Keep practicing regularly to maintain your knowledge and improve further.
                      </span>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex items-start gap-2">
                      <span className="text-green-600 mt-1">•</span>
                      <span className="text-green-700">
                        Review study materials for {quizSubject} and focus on the fundamentals.
                      </span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-green-600 mt-1">•</span>
                      <span className="text-green-700">
                        Take practice quizzes to identify weak areas and improve gradually.
                      </span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="text-green-600 mt-1">•</span>
                      <span className="text-green-700">
                        Don't give up! Learning takes time. Retake this quiz after studying.
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Next Steps */}
            <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
              <h4 className="font-semibold text-purple-800 mb-3 flex items-center gap-2">
                <TbRocket className="w-5 h-5 text-purple-600" />
                Next Steps
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-start gap-2">
                  <span className="text-purple-600 mt-1">1.</span>
                  <span className="text-purple-700">
                    {isPassed ? 'Try more quizzes in different subjects to expand your knowledge' : 'Review the incorrect answers and understand why they were wrong'}
                  </span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-purple-600 mt-1">2.</span>
                  <span className="text-purple-700">
                    {isPassed ? 'Challenge yourself with harder difficulty levels' : 'Study the relevant topics and retake this quiz'}
                  </span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-purple-600 mt-1">3.</span>
                  <span className="text-purple-700">
                    Track your progress and aim for consistent improvement
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Question Breakdown */}
        {resultDetails && resultDetails.length > 0 && (
          <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                <TbChartBar className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">📋 Question by Question Review</h3>
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {resultDetails.map((detail, index) => (
                <div
                  key={detail.questionId || index}
                  className={`p-4 rounded-lg border-2 ${
                    detail.isCorrect
                      ? 'border-green-200 bg-green-50'
                      : 'border-red-200 bg-red-50'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                      detail.isCorrect
                        ? 'bg-green-500 text-white'
                        : 'bg-red-500 text-white'
                    }`}>
                      {detail.isCorrect ? <TbCheck className="w-4 h-4" /> : <TbX className="w-4 h-4" />}
                    </div>

                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2">
                        Question {index + 1}: {detail.questionText || detail.questionName}
                      </h4>

                      <div className="space-y-2 text-sm">
                        <div className="flex items-start gap-2">
                          <span className="font-medium text-gray-700 min-w-[80px]">Your Answer:</span>
                          <span className={`font-medium ${
                            detail.isCorrect ? 'text-green-700' : 'text-red-700'
                          }`}>
                            {detail.userAnswer || 'No answer provided'}
                          </span>
                        </div>

                        {!detail.isCorrect && (
                          <div className="flex items-start gap-2">
                            <span className="font-medium text-gray-700 min-w-[80px]">Correct Answer:</span>
                            <span className="font-medium text-green-700">
                              {detail.correctAnswer}
                            </span>
                          </div>
                        )}

                        <div className="flex items-center gap-2 mt-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            detail.isCorrect
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {detail.isCorrect ? '✅ Correct' : '❌ Incorrect'}
                          </span>
                          {detail.questionType && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                              {detail.questionType.toUpperCase()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Summary Stats */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="bg-green-100 rounded-lg p-3">
                  <div className="text-2xl font-bold text-green-600">{correctAnswers}</div>
                  <div className="text-sm text-green-700">Correct</div>
                </div>
                <div className="bg-red-100 rounded-lg p-3">
                  <div className="text-2xl font-bold text-red-600">{totalQuestions - correctAnswers}</div>
                  <div className="text-sm text-red-700">Wrong</div>
                </div>
                <div className="bg-blue-100 rounded-lg p-3">
                  <div className="text-2xl font-bold text-blue-600">{percentage}%</div>
                  <div className="text-sm text-blue-700">Score</div>
                </div>
                <div className="bg-purple-100 rounded-lg p-3">
                  <div className="text-2xl font-bold text-purple-600">{Math.round((correctAnswers / totalQuestions) * 100)}%</div>
                  <div className="text-sm text-purple-700">Accuracy</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={(e) => {
              e.preventDefault();
              console.log('🔥 More Quizzes button clicked!');
              handleBackToQuizzes();
            }}
            className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer"
            type="button"
          >
            <TbHome className="w-5 h-5" />
            More Quizzes
          </button>

          <button
            onClick={(e) => {
              e.preventDefault();
              console.log('🔥 Retake Quiz button clicked!');
              handleRetakeQuiz();
            }}
            className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer"
            type="button"
          >
            <TbTrophy className="w-5 h-5" />
            Retake Quiz
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuizResult;
