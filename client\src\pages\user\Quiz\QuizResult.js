import React, { startTransition, useEffect, useState } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Tb<PERSON>rophy, Tb<PERSON>lock, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';

const QuizResult = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams();
  const { user } = useSelector((state) => state.user);
  const [showAnimation, setShowAnimation] = useState(false);
  const [confetti, setConfetti] = useState([]);

  // Play sound and trigger animations when component loads
  useEffect(() => {
    // Trigger entrance animation
    setTimeout(() => setShowAnimation(true), 100);

    // Play sound effect based on pass/fail
    const playSound = () => {
      try {
        if (isPassed) {
          // Success sound (you can replace with actual audio file)
          const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
          audio.volume = 0.3;
          audio.play().catch(() => {}); // Ignore errors if audio fails
        } else {
          // Fail sound
          const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
          audio.volume = 0.2;
          audio.play().catch(() => {}); // Ignore errors if audio fails
        }
      } catch (error) {
        console.log('Audio not supported');
      }
    };

    // Generate confetti for pass
    if (isPassed) {
      const newConfetti = [];
      for (let i = 0; i < 50; i++) {
        newConfetti.push({
          id: i,
          left: Math.random() * 100,
          delay: Math.random() * 3,
          duration: 3 + Math.random() * 2,
          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'][Math.floor(Math.random() * 5)]
        });
      }
      setConfetti(newConfetti);

      // Remove confetti after animation
      setTimeout(() => setConfetti([]), 5000);
    }

    playSound();
  }, [isPassed]);

  // Get result data from navigation state
  const resultData = location.state || {
    percentage: 0,
    correctAnswers: 0,
    totalQuestions: 0,
    timeTaken: 0,
    resultDetails: [],
    xpData: null,
    quizName: 'Quiz',
    quizSubject: 'General',
    passingPercentage: 60,
    verdict: 'Fail'
  };

  const {
    percentage,
    correctAnswers,
    totalQuestions,
    timeTaken,
    xpData,
    quizName,
    quizSubject,
    passingPercentage,
    verdict,
    resultDetails
  } = resultData;
  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleBackToQuizzes = () => {
    console.log('🏠 Navigating to quiz listing...');
    startTransition(() => {
      navigate('/user/quiz');
    });
  };

  const handleRetakeQuiz = () => {
    console.log('🔄 Retaking quiz with ID:', id);
    if (id) {
      startTransition(() => {
        navigate(`/quiz/${id}/play`);
      });
    } else {
      console.log('❌ No quiz ID available, going to quiz listing');
      startTransition(() => {
        navigate('/user/quiz');
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4 relative overflow-hidden">

      {/* Confetti Animation */}
      {confetti.map((piece) => (
        <div
          key={piece.id}
          className="absolute w-2 h-2 opacity-80"
          style={{
            left: `${piece.left}%`,
            backgroundColor: piece.color,
            animation: `confetti-fall ${piece.duration}s linear ${piece.delay}s forwards`,
            top: '-10px'
          }}
        />
      ))}

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes confetti-fall {
          0% {
            transform: translateY(-10px) rotate(0deg);
            opacity: 1;
          }
          100% {
            transform: translateY(100vh) rotate(720deg);
            opacity: 0;
          }
        }

        @keyframes bounce-in {
          0% {
            transform: scale(0.3) rotate(-10deg);
            opacity: 0;
          }
          50% {
            transform: scale(1.1) rotate(5deg);
          }
          100% {
            transform: scale(1) rotate(0deg);
            opacity: 1;
          }
        }

        @keyframes pulse-glow {
          0%, 100% {
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
          }
          50% {
            box-shadow: 0 0 40px rgba(34, 197, 94, 0.8);
          }
        }

        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
          20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .result-card {
          animation: bounce-in 0.8s ease-out forwards;
        }

        .pass-glow {
          animation: pulse-glow 2s ease-in-out infinite;
        }

        .fail-shake {
          animation: shake 0.5s ease-in-out;
        }
      `}</style>

      <div className={`bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-2xl w-full result-card ${
        showAnimation ? (isPassed ? 'pass-glow' : 'fail-shake') : ''
      } ${isPassed ? 'border-green-300' : 'border-red-300'}`}>
        {/* Header */}
        <div className="text-center mb-8">
          <div className={`inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${
            isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'
          } ${showAnimation ? 'animate-bounce' : ''}`}>

            {/* Animated rings for pass */}
            {isPassed && showAnimation && (
              <>
                <div className="absolute inset-0 rounded-full border-4 border-green-300 animate-ping"></div>
                <div className="absolute inset-2 rounded-full border-2 border-green-400 animate-pulse"></div>
              </>
            )}

            <TbTrophy className={`w-12 h-12 ${
              isPassed ? 'text-green-600' : 'text-red-600'
            } ${showAnimation ? 'animate-pulse' : ''}`} />

            {/* Sparkles for pass */}
            {isPassed && showAnimation && (
              <>
                <div className="absolute -top-2 -right-2 w-3 h-3 bg-yellow-400 rounded-full animate-ping"></div>
                <div className="absolute -bottom-2 -left-2 w-2 h-2 bg-yellow-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                <div className="absolute top-0 left-0 w-2 h-2 bg-yellow-500 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
              </>
            )}
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Quiz Completed!
          </h1>

          <p className={`text-lg font-semibold ${
            isPassed ? 'text-green-600' : 'text-red-600'
          }`}>
            {isPassed ? 'Congratulations! You Passed!' : 'Keep Practicing!'}
          </p>

          <p className="text-gray-600 mt-2">
            {quizName} - {quizSubject}
          </p>
        </div>

        {/* Score Display */}
        <div className="text-center mb-8">
          <div className={`inline-block px-8 py-4 rounded-2xl ${
            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'
          }`}>
            <div className={`text-5xl font-bold mb-2 ${
              isPassed ? 'text-green-600' : 'text-red-600'
            }`}>
              {percentage}%
            </div>
            <div className="text-gray-600">
              Your Score
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-green-50 rounded-xl p-4 text-center border border-green-200">
            <div className="flex items-center justify-center mb-2">
              <TbCheck className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">{correctAnswers}</div>
            <div className="text-sm text-green-700 font-medium">Correct</div>
          </div>

          <div className="bg-red-50 rounded-xl p-4 text-center border border-red-200">
            <div className="flex items-center justify-center mb-2">
              <TbX className="w-6 h-6 text-red-600" />
            </div>
            <div className="text-2xl font-bold text-red-600">{totalQuestions - correctAnswers}</div>
            <div className="text-sm text-red-700 font-medium">Wrong</div>
          </div>

          <div className="bg-gray-50 rounded-xl p-4 text-center border border-gray-200">
            <div className="flex items-center justify-center mb-2">
              <TbTrophy className="w-6 h-6 text-gray-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{totalQuestions}</div>
            <div className="text-sm text-gray-600 font-medium">Total</div>
          </div>

          <div className="bg-blue-50 rounded-xl p-4 text-center border border-blue-200">
            <div className="flex items-center justify-center mb-2">
              <TbClock className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{formatTime(timeTaken)}</div>
            <div className="text-sm text-blue-700 font-medium">Time Taken</div>
          </div>
        </div>

        {/* Performance Message */}
        <div className={`rounded-xl p-6 mb-6 border-2 ${
          isPassed
            ? 'bg-green-50 border-green-200'
            : 'bg-orange-50 border-orange-200'
        }`}>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">📊 Quiz Results Breakdown</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Questions Answered:</span>
              <span className="font-semibold text-gray-900">{totalQuestions} / {totalQuestions}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Correct Answers:</span>
              <span className="font-semibold text-green-600">{correctAnswers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Wrong Answers:</span>
              <span className="font-semibold text-red-600">{totalQuestions - correctAnswers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Accuracy Rate:</span>
              <span className="font-semibold text-blue-600">{percentage}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700">Pass Mark:</span>
              <span className="font-semibold text-gray-600">{passingPercentage || 60}%</span>
            </div>
            <div className="flex justify-between items-center border-t pt-2 mt-3">
              <span className="text-gray-700 font-medium">Result:</span>
              <span className={`font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`}>
                {isPassed ? '✅ PASSED' : '❌ FAILED'}
              </span>
            </div>
          </div>

          <div className="mt-4 p-3 bg-white rounded-lg">
            <p className={`text-sm ${isPassed ? 'text-green-700' : 'text-orange-700'}`}>
              {isPassed
                ? `🎉 Congratulations! You passed with ${percentage}% (${correctAnswers}/${totalQuestions} correct). Great job!`
                : `📚 You scored ${percentage}% (${correctAnswers}/${totalQuestions} correct). You need ${passingPercentage || 60}% to pass. Keep studying and try again!`
              }
            </p>
          </div>
        </div>

        {/* XP Earned Section */}
        {xpData && (
          <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 mb-6 border border-purple-200">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                  <TbStar className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">XP Earned!</h3>
                  <p className="text-purple-600 font-bold text-2xl">+{xpData.xpAwarded || 0} XP</p>
                </div>
              </div>

              {/* Total XP Display */}
              <div className="text-right">
                <p className="text-sm text-gray-600">Total XP</p>
                <p className="text-xl font-bold text-indigo-600">
                  {((user?.totalXP || 0) + (xpData.xpAwarded || 0)).toLocaleString()} XP
                </p>
                <p className="text-xs text-gray-500">Level {user?.currentLevel || 1}</p>
              </div>
            </div>

            {xpData.breakdown && (
              <div className="grid grid-cols-2 gap-3 text-sm">
                {xpData.breakdown.baseCompletion && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Quiz Completion:</span>
                    <span className="font-medium">+{xpData.breakdown.baseCompletion} XP</span>
                  </div>
                )}
                {xpData.breakdown.correctAnswers && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Correct Answers:</span>
                    <span className="font-medium">+{xpData.breakdown.correctAnswers} XP</span>
                  </div>
                )}
                {xpData.breakdown.perfectScore && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Perfect Score:</span>
                    <span className="font-medium text-green-600">+{xpData.breakdown.perfectScore} XP</span>
                  </div>
                )}
                {xpData.breakdown.firstAttemptBonus && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">First Attempt:</span>
                    <span className="font-medium text-blue-600">+{xpData.breakdown.firstAttemptBonus} XP</span>
                  </div>
                )}
                {xpData.breakdown.speedBonus && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Speed Bonus:</span>
                    <span className="font-medium text-orange-600">+{xpData.breakdown.speedBonus} XP</span>
                  </div>
                )}
                {xpData.breakdown.difficultyBonus && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Difficulty Bonus:</span>
                    <span className="font-medium text-red-600">+{xpData.breakdown.difficultyBonus} XP</span>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Total XP Section (for users without XP data) */}
        {!xpData && user && (
          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 mb-6 border border-indigo-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center">
                  <TbStar className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Your XP Progress</h3>
                  <p className="text-sm text-gray-600">Keep taking quizzes to earn more XP!</p>
                </div>
              </div>

              <div className="text-right">
                <p className="text-sm text-gray-600">Total XP</p>
                <p className="text-2xl font-bold text-indigo-600">
                  {(user.totalXP || 0).toLocaleString()} XP
                </p>
                <p className="text-sm text-gray-500">Level {user.currentLevel || 1}</p>
              </div>
            </div>

            {/* XP Progress Bar */}
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Level {user.currentLevel || 1}</span>
                <span>Level {(user.currentLevel || 1) + 1}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 h-3 rounded-full transition-all duration-300"
                  style={{
                    width: `${Math.min(100, ((user.totalXP || 0) % 1000) / 10)}%`
                  }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 mt-1 text-center">
                {(user.xpToNextLevel || 100)} XP to next level
              </p>
            </div>
          </div>
        )}

        {/* Learning Summary Section */}
        <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <TbBrain className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">📚 Learning Summary</h3>
          </div>

          {/* Simplified Learning Summary - Only Question Review */}
        </div>

        {/* Detailed Question Breakdown */}
        {resultDetails && resultDetails.length > 0 && (
          <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                <TbChartBar className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">📋 Question by Question Review</h3>
            </div>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {resultDetails.map((detail, index) => (
                <div
                  key={detail.questionId || index}
                  className={`rounded-xl border-2 overflow-hidden transition-all duration-300 hover:shadow-lg ${
                    detail.isCorrect
                      ? 'border-green-300 bg-gradient-to-r from-green-50 to-emerald-50'
                      : 'border-red-300 bg-gradient-to-r from-red-50 to-pink-50'
                  }`}
                >
                  {/* Question Header */}
                  <div className={`p-4 ${
                    detail.isCorrect
                      ? 'bg-green-100 border-b border-green-200'
                      : 'bg-red-100 border-b border-red-200'
                  }`}>
                    <div className="flex items-center gap-3">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${
                        detail.isCorrect
                          ? 'bg-green-500 text-white shadow-lg'
                          : 'bg-red-500 text-white shadow-lg'
                      }`}>
                        {detail.isCorrect ? <TbCheck className="w-5 h-5" /> : <TbX className="w-5 h-5" />}
                      </div>

                      <div className="flex-1">
                        <h4 className="font-bold text-gray-900 text-lg">
                          Question {index + 1}
                        </h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                            detail.isCorrect
                              ? 'bg-green-500 text-white'
                              : 'bg-red-500 text-white'
                          }`}>
                            {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}
                          </span>
                          {detail.questionType && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700">
                              {detail.questionType.toUpperCase()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Question Content */}
                  <div className="p-4">
                    <div className="mb-4">
                      <h5 className="font-semibold text-gray-800 mb-2">Question:</h5>
                      <p className="text-gray-700 bg-white p-3 rounded-lg border">
                        {detail.questionText || detail.questionName}
                      </p>
                    </div>

                    {/* Show Image if it's an image question */}
                    {detail.questionType === 'image' && detail.questionImage && (
                      <div className="mb-4">
                        <h5 className="font-semibold text-gray-800 mb-2">Image:</h5>
                        <div className="bg-white p-2 rounded-lg border">
                          <img
                            src={detail.questionImage}
                            alt="Question"
                            className="max-w-full h-auto rounded-lg shadow-sm"
                            style={{ maxHeight: '200px' }}
                          />
                        </div>
                      </div>
                    )}

                    {/* Answer Section */}
                    <div className="space-y-3">
                      <div className="bg-white p-3 rounded-lg border">
                        <div className="flex items-start gap-2">
                          <span className="font-semibold text-gray-700 min-w-[100px]">Your Answer:</span>
                          <span className={`font-bold ${
                            detail.isCorrect ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {detail.userAnswer || 'No answer provided'}
                          </span>
                        </div>
                      </div>

                      {!detail.isCorrect && (
                        <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                          <div className="flex items-start gap-2">
                            <span className="font-semibold text-gray-700 min-w-[100px]">Correct Answer:</span>
                            <span className="font-bold text-green-600">
                              {detail.correctAnswer}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Explanation Button for Wrong Answers */}
                      {!detail.isCorrect && (
                        <div className="mt-3">
                          <button
                            className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
                            onClick={() => {
                              // Toggle explanation visibility
                              const explanationDiv = document.getElementById(`explanation-${index}`);
                              if (explanationDiv) {
                                explanationDiv.style.display = explanationDiv.style.display === 'none' ? 'block' : 'none';
                              }
                            }}
                          >
                            <TbBrain className="w-4 h-4" />
                            Show Explanation
                          </button>

                          <div
                            id={`explanation-${index}`}
                            className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg"
                            style={{ display: 'none' }}
                          >
                            <h6 className="font-semibold text-blue-800 mb-2">💡 Explanation:</h6>
                            <p className="text-blue-700">
                              {detail.explanation || `The correct answer is "${detail.correctAnswer}". Make sure to review this topic and understand why this is the right answer.`}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Summary Stats */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="bg-green-100 rounded-lg p-3">
                  <div className="text-2xl font-bold text-green-600">{correctAnswers}</div>
                  <div className="text-sm text-green-700">Correct</div>
                </div>
                <div className="bg-red-100 rounded-lg p-3">
                  <div className="text-2xl font-bold text-red-600">{totalQuestions - correctAnswers}</div>
                  <div className="text-sm text-red-700">Wrong</div>
                </div>
                <div className="bg-blue-100 rounded-lg p-3">
                  <div className="text-2xl font-bold text-blue-600">{percentage}%</div>
                  <div className="text-sm text-blue-700">Score</div>
                </div>
                <div className="bg-purple-100 rounded-lg p-3">
                  <div className="text-2xl font-bold text-purple-600">{Math.round((correctAnswers / totalQuestions) * 100)}%</div>
                  <div className="text-sm text-purple-700">Accuracy</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={(e) => {
              e.preventDefault();
              console.log('🔥 More Quizzes button clicked!');
              handleBackToQuizzes();
            }}
            className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer"
            type="button"
          >
            <TbHome className="w-5 h-5" />
            More Quizzes
          </button>

          <button
            onClick={(e) => {
              e.preventDefault();
              console.log('🔥 Retake Quiz button clicked!');
              handleRetakeQuiz();
            }}
            className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer"
            type="button"
          >
            <TbTrophy className="w-5 h-5" />
            Retake Quiz
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuizResult;
