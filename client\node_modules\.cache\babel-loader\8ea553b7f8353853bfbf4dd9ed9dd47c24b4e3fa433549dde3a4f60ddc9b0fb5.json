{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    id\n  } = useParams();\n  const {\n    user\n  } = useSelector(state => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n    // Play enhanced sound effects based on pass/fail\n    const playSound = () => {\n      try {\n        if (isPassed) {\n          // Success sound - create a pleasant success tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play success melody: C-E-G-C (major chord progression)\n          const now = audioContext.currentTime;\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate enhanced animations based on pass/fail\n    if (isPassed) {\n      // Enhanced confetti for pass\n      const newConfetti = [];\n      for (let i = 0; i < 100; i++) {\n        newConfetti.push({\n          id: i,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 3 + Math.random() * 3,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FF69B4', '#32CD32', '#FF4500'][Math.floor(Math.random() * 8)],\n          shape: Math.random() > 0.5 ? 'circle' : 'square',\n          size: 2 + Math.random() * 4\n        });\n      }\n      setConfetti(newConfetti);\n\n      // Add clapping hands animation\n      setTimeout(() => {\n        const clapElements = [];\n        for (let i = 0; i < 6; i++) {\n          clapElements.push({\n            id: `clap_${i}`,\n            left: 20 + Math.random() * 60,\n            top: 20 + Math.random() * 60,\n            delay: Math.random() * 2\n          });\n        }\n        setConfetti(prev => [...prev, ...clapElements.map(clap => ({\n          ...clap,\n          isClap: true,\n          duration: 2,\n          color: '#FFD700'\n        }))]);\n      }, 1000);\n\n      // Remove all animations after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    } else {\n      // Creative fail animations - floating motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡'];\n      for (let i = 0; i < 20; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 4 + Math.random() * 2,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 6 seconds\n      setTimeout(() => setConfetti([]), 6000);\n    }\n    playSound();\n  }, [isPassed]);\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n    try {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: true\n      }));\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: false\n      }));\n    }\n  };\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen flex items-center justify-center p-4 relative overflow-hidden ${isPassed ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100' : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'}`,\n    children: [confetti.map(piece => {\n      if (piece.isClap) {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute text-4xl opacity-90\",\n          style: {\n            left: `${piece.left}%`,\n            top: `${piece.top}%`,\n            animation: `clap ${piece.duration}s ease-in-out ${piece.delay}s infinite`\n          },\n          children: \"\\uD83D\\uDC4F\"\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this);\n      }\n      if (piece.isMotivational) {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute text-3xl opacity-80\",\n          style: {\n            left: `${piece.left}%`,\n            animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n            top: `${20 + Math.random() * 60}%`\n          },\n          children: piece.emoji\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this);\n      }\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute opacity-80\",\n        style: {\n          left: `${piece.left}%`,\n          width: `${piece.size || 2}px`,\n          height: `${piece.size || 2}px`,\n          backgroundColor: piece.color,\n          borderRadius: piece.shape === 'circle' ? '50%' : '0%',\n          animation: `confetti-fall ${piece.duration}s linear ${piece.delay}s forwards`,\n          top: '-10px'\n        }\n      }, piece.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-10px) rotate(0deg);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) rotate(720deg);\n            opacity: 0;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-2xl w-full ${isPassed ? 'border-green-300' : 'border-red-300'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'}`,\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: `w-12 h-12 ${isPassed ? 'text-green-600' : 'text-red-600'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `text-6xl font-bold mb-4 ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n          children: isPassed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-3\",\n            children: \"\\uD83C\\uDF89 Congratulations! \\uD83C\\uDF89\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center gap-3\",\n            children: \"\\uD83D\\uDCAA Keep Going! \\uD83D\\uDCAA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold mb-2 text-gray-700\",\n          children: isPassed ? 'You Passed!' : 'You Can Do It!'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2 text-lg\",\n          children: [\"\\uD83D\\uDCDA \", quizName, \" - \", quizSubject]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-block px-8 py-4 rounded-2xl ${isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-5xl font-bold mb-2 ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-600\",\n            children: \"Your Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-green-600\",\n            children: correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-green-600 font-medium\",\n            children: \"\\u2705 Correct\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-red-600\",\n            children: totalQuestions - correctAnswers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-600 font-medium\",\n            children: \"\\u274C Wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\uD83D\\uDCCA Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-blue-600\",\n            children: [Math.floor(timeTaken / 60), \":\", (timeTaken % 60).toString().padStart(2, '0')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600 font-medium\",\n            children: \"\\u23F1\\uFE0F Time\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-purple-600 font-bold text-xl\",\n                children: [\"+\", xpData.xpAwarded || 0, \" XP\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Earned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (((user === null || user === void 0 ? void 0 : user.totalXP) || 0) + (xpData.xpAwarded || 0)).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", (user === null || user === void 0 ? void 0 : user.currentLevel) || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 11\n      }, this), !xpData && user && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Your Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (user.totalXP || 0).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", user.currentLevel || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCDA Learning Summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), resultDetails && resultDetails.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCCB Question by Question Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 max-h-96 overflow-y-auto\",\n          children: resultDetails.map((detail, index) => {\n            // Debug: Log question data to see what's available\n            console.log(`Question ${index + 1} data:`, detail);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl ${detail.isCorrect ? 'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300' : 'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'}`,\n              style: {\n                boxShadow: detail.isCorrect ? '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)' : '0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 ${detail.isCorrect ? 'bg-green-300 border-b-4 border-green-500' : 'bg-red-300 border-b-4 border-red-500'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-10 h-10 rounded-full flex items-center justify-center font-bold ${detail.isCorrect ? 'bg-green-500 text-white shadow-lg' : 'bg-red-500 text-white shadow-lg'}`,\n                    children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 45\n                    }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 79\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-bold text-gray-900 text-lg\",\n                      children: [\"Question \", index + 1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 494,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-3 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 498,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 bg-white p-3 rounded-lg border\",\n                    children: detail.questionText || detail.questionName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 21\n                }, this), (detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-lg border-2 ${detail.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-semibold text-gray-700\",\n                        children: \"\\uD83D\\uDCF7 Question Image:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 527,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 528,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 526,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-2 rounded-lg border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: detail.questionImage || detail.image || detail.imageUrl,\n                        alt: \"Question Image\",\n                        className: \"max-w-full h-auto rounded-lg shadow-sm mx-auto block\",\n                        style: {\n                          maxHeight: '300px'\n                        },\n                        onError: e => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'block';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 537,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\",\n                        style: {\n                          display: 'none'\n                        },\n                        children: \"\\uD83D\\uDCF7 Image could not be loaded\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 547,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-4 rounded-lg border-4 ${detail.isCorrect ? 'bg-green-50 border-green-500' : 'bg-red-50 border-red-500'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-6 h-6 rounded-full flex items-center justify-center ${detail.isCorrect ? 'bg-green-500' : 'bg-red-500'}`,\n                        children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 570,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 572,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 566,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Your Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 575,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `p-3 rounded-lg font-bold text-lg ${detail.isCorrect ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'}`,\n                      children: detail.userAnswer || 'No answer provided'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 577,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 23\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-50 p-4 rounded-lg border-4 border-green-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 590,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 589,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Correct Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 592,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\",\n                      children: detail.correctAnswer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 25\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${loadingExplanations[`question_${index}`] ? 'bg-gray-400 cursor-not-allowed' : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'}`,\n                      onClick: () => fetchExplanation(index, detail),\n                      disabled: loadingExplanations[`question_${index}`],\n                      children: loadingExplanations[`question_${index}`] ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 614,\n                          columnNumber: 33\n                        }, this), \"Getting Explanation...\"]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 619,\n                          columnNumber: 33\n                        }, this), \"Get Explanation\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 603,\n                      columnNumber: 27\n                    }, this), explanations[`question_${index}`] && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-2 mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5 text-blue-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 629,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-bold text-blue-800\",\n                          children: \"\\uD83D\\uDCA1 Explanation:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 630,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 628,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-blue-700 leading-relaxed whitespace-pre-wrap\",\n                        children: explanations[`question_${index}`]\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 632,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this)]\n            }, detail.questionId || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 More Quizzes button clicked!');\n            handleBackToQuizzes();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbHome, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this), \"More Quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 Retake Quiz button clicked!');\n            handleRetakeQuiz();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 13\n          }, this), \"Retake Quiz\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"raoJxFi78kbAvVUKKJvoU0q+lLA=\", false, function () {\n  return [useNavigate, useLocation, useParams, useSelector];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "startTransition", "useEffect", "useState", "useNavigate", "useLocation", "useParams", "useSelector", "TbTrophy", "TbCheck", "TbX", "TbHome", "TbStar", "TbBrain", "TbChartBar", "chatWithChatGPTToExplainAns", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "navigate", "location", "id", "user", "state", "resultData", "percentage", "correctAnswers", "totalQuestions", "timeTaken", "resultDetails", "xpData", "quizName", "quizSubject", "passingPercentage", "verdict", "isPassed", "confetti", "set<PERSON>on<PERSON>tti", "explanations", "setExplanations", "loadingExplanations", "setLoadingExplanations", "playSound", "audioContext", "window", "AudioContext", "webkitAudioContext", "playTone", "frequency", "startTime", "duration", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "type", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "now", "currentTime", "error", "console", "log", "newConfetti", "i", "push", "left", "Math", "random", "delay", "color", "floor", "shape", "size", "setTimeout", "clapElements", "top", "prev", "map", "clap", "isClap", "motivationalElements", "motivationalEmojis", "emoji", "length", "isMotivational", "fetchExplanation", "questionIndex", "detail", "<PERSON><PERSON><PERSON>", "response", "question", "questionText", "questionName", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "userAnswer", "imageUrl", "questionImage", "image", "success", "explanation", "handleBackToQuizzes", "handleRetakeQuiz", "className", "children", "piece", "style", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "backgroundColor", "borderRadius", "jsx", "toString", "padStart", "xpAwarded", "totalXP", "toLocaleString", "currentLevel", "index", "isCorrect", "boxShadow", "questionType", "src", "alt", "maxHeight", "onError", "e", "target", "display", "nextS<PERSON>ling", "onClick", "disabled", "questionId", "preventDefault", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\n\nconst QuizResult = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { id } = useParams();\n  const { user } = useSelector((state) => state.user);\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n\n    // Play enhanced sound effects based on pass/fail\n    const playSound = () => {\n      try {\n        if (isPassed) {\n          // Success sound - create a pleasant success tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play success melody: C-E-G-C (major chord progression)\n          const now = audioContext.currentTime;\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate enhanced animations based on pass/fail\n    if (isPassed) {\n      // Enhanced confetti for pass\n      const newConfetti = [];\n      for (let i = 0; i < 100; i++) {\n        newConfetti.push({\n          id: i,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 3 + Math.random() * 3,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FF69B4', '#32CD32', '#FF4500'][Math.floor(Math.random() * 8)],\n          shape: Math.random() > 0.5 ? 'circle' : 'square',\n          size: 2 + Math.random() * 4\n        });\n      }\n      setConfetti(newConfetti);\n\n      // Add clapping hands animation\n      setTimeout(() => {\n        const clapElements = [];\n        for (let i = 0; i < 6; i++) {\n          clapElements.push({\n            id: `clap_${i}`,\n            left: 20 + Math.random() * 60,\n            top: 20 + Math.random() * 60,\n            delay: Math.random() * 2\n          });\n        }\n        setConfetti(prev => [...prev, ...clapElements.map(clap => ({\n          ...clap,\n          isClap: true,\n          duration: 2,\n          color: '#FFD700'\n        }))]);\n      }, 1000);\n\n      // Remove all animations after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    } else {\n      // Creative fail animations - floating motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡'];\n\n      for (let i = 0; i < 20; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 4 + Math.random() * 2,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 6 seconds\n      setTimeout(() => setConfetti([]), 6000);\n    }\n\n    playSound();\n  }, [isPassed]);\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n\n    try {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: true }));\n\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null\n      });\n\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: false }));\n    }\n  };\n\n\n\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center p-4 relative overflow-hidden ${\n      isPassed\n        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100'\n        : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'\n    }`}>\n\n      {/* Enhanced Animations */}\n      {confetti.map((piece) => {\n        if (piece.isClap) {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute text-4xl opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                top: `${piece.top}%`,\n                animation: `clap ${piece.duration}s ease-in-out ${piece.delay}s infinite`\n              }}\n            >\n              👏\n            </div>\n          );\n        }\n\n        if (piece.isMotivational) {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute text-3xl opacity-80\"\n              style={{\n                left: `${piece.left}%`,\n                animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                top: `${20 + Math.random() * 60}%`\n              }}\n            >\n              {piece.emoji}\n            </div>\n          );\n        }\n\n        return (\n          <div\n            key={piece.id}\n            className=\"absolute opacity-80\"\n            style={{\n              left: `${piece.left}%`,\n              width: `${piece.size || 2}px`,\n              height: `${piece.size || 2}px`,\n              backgroundColor: piece.color,\n              borderRadius: piece.shape === 'circle' ? '50%' : '0%',\n              animation: `confetti-fall ${piece.duration}s linear ${piece.delay}s forwards`,\n              top: '-10px'\n            }}\n          />\n        );\n      })}\n\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-10px) rotate(0deg);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) rotate(720deg);\n            opacity: 0;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n      `}</style>\n\n      <div className={`bg-white rounded-2xl shadow-xl border border-gray-200 p-8 max-w-2xl w-full ${isPassed ? 'border-green-300' : 'border-red-300'}`}>\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-flex items-center justify-center w-24 h-24 rounded-full mb-4 relative ${\n            isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'\n          }`}>\n\n            <TbTrophy className={`w-12 h-12 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`} />\n          </div>\n          \n          <h1 className={`text-6xl font-bold mb-4 ${isPassed ? 'text-green-600' : 'text-red-600'}`}>\n            {isPassed ? (\n              <span className=\"flex items-center justify-center gap-3\">\n                🎉 Congratulations! 🎉\n              </span>\n            ) : (\n              <span className=\"flex items-center justify-center gap-3\">\n                💪 Keep Going! 💪\n              </span>\n            )}\n          </h1>\n\n          <div className=\"text-2xl font-semibold mb-2 text-gray-700\">\n            {isPassed ? 'You Passed!' : 'You Can Do It!'}\n          </div>\n\n          <p className=\"text-gray-600 mt-2 text-lg\">\n            📚 {quizName} - {quizSubject}\n          </p>\n        </div>\n\n        {/* Score Display */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-block px-8 py-4 rounded-2xl ${\n            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'\n          }`}>\n            <div className={`text-5xl font-bold mb-2 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {percentage}%\n            </div>\n            <div className=\"text-gray-600\">\n              Your Score\n            </div>\n          </div>\n        </div>\n\n\n\n        {/* Streamlined Results Cards */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-green-600\">{correctAnswers}</div>\n            <div className=\"text-sm text-green-600 font-medium\">✅ Correct</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-red-600\">{totalQuestions - correctAnswers}</div>\n            <div className=\"text-sm text-red-600 font-medium\">❌ Wrong</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className={`text-3xl font-bold ${isPassed ? 'text-green-600' : 'text-red-600'}`}>{percentage}%</div>\n            <div className=\"text-sm text-gray-600 font-medium\">📊 Score</div>\n          </div>\n          <div className=\"bg-white rounded-xl p-4 border border-gray-200 text-center shadow-sm\">\n            <div className=\"text-3xl font-bold text-blue-600\">\n              {Math.floor(timeTaken / 60)}:{(timeTaken % 60).toString().padStart(2, '0')}\n            </div>\n            <div className=\"text-sm text-gray-600 font-medium\">⏱️ Time</div>\n          </div>\n        </div>\n\n        {/* Streamlined XP Section */}\n        {xpData && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-purple-600 font-bold text-xl\">+{xpData.xpAwarded || 0} XP</div>\n                  <div className=\"text-sm text-gray-600\">Earned</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {((user?.totalXP || 0) + (xpData.xpAwarded || 0)).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user?.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Streamlined XP Section (for users without XP data) */}\n        {!xpData && user && (\n          <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-sm text-gray-600\">Your Progress</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {(user.totalXP || 0).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Summary Section */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n              <TbBrain className=\"w-6 h-6 text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">📚 Learning Summary</h3>\n          </div>\n\n          {/* Simplified Learning Summary - Only Question Review */}\n        </div>\n\n        {/* Detailed Question Breakdown */}\n        {resultDetails && resultDetails.length > 0 && (\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\">\n                <TbChartBar className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">📋 Question by Question Review</h3>\n            </div>\n\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              {resultDetails.map((detail, index) => {\n                // Debug: Log question data to see what's available\n                console.log(`Question ${index + 1} data:`, detail);\n                return (\n                <div\n                  key={detail.questionId || index}\n                  className={`rounded-xl border-4 overflow-hidden transition-all duration-300 hover:shadow-xl ${\n                    detail.isCorrect\n                      ? 'border-green-600 bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300'\n                      : 'border-red-600 bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'\n                  }`}\n                  style={{\n                    boxShadow: detail.isCorrect\n                      ? '0 10px 25px rgba(34, 197, 94, 0.3), 0 0 0 1px rgba(34, 197, 94, 0.1)'\n                      : '0 10px 25px rgba(239, 68, 68, 0.3), 0 0 0 1px rgba(239, 68, 68, 0.1)'\n                  }}\n                >\n                  {/* Question Header */}\n                  <div className={`p-4 ${\n                    detail.isCorrect\n                      ? 'bg-green-300 border-b-4 border-green-500'\n                      : 'bg-red-300 border-b-4 border-red-500'\n                  }`}>\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${\n                        detail.isCorrect\n                          ? 'bg-green-500 text-white shadow-lg'\n                          : 'bg-red-500 text-white shadow-lg'\n                      }`}>\n                        {detail.isCorrect ? <TbCheck className=\"w-5 h-5\" /> : <TbX className=\"w-5 h-5\" />}\n                      </div>\n\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-bold text-gray-900 text-lg\">\n                          Question {index + 1}\n                        </h4>\n                        <div className=\"flex items-center gap-2 mt-1\">\n                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${\n                            detail.isCorrect\n                              ? 'bg-green-500 text-white'\n                              : 'bg-red-500 text-white'\n                          }`}>\n                            {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4\">\n                    <div className=\"mb-4\">\n                      <p className=\"text-gray-700 bg-white p-3 rounded-lg border\">\n                        {detail.questionText || detail.questionName}\n                      </p>\n                    </div>\n\n                    {/* Show Image for image questions or any question with an image */}\n                    {(detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && (\n                      <div className=\"mb-4\">\n                        <div className={`p-3 rounded-lg border-2 ${\n                          detail.isCorrect\n                            ? 'bg-green-50 border-green-200'\n                            : 'bg-red-50 border-red-200'\n                        }`}>\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <span className=\"text-sm font-semibold text-gray-700\">📷 Question Image:</span>\n                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${\n                              detail.isCorrect\n                                ? 'bg-green-500 text-white'\n                                : 'bg-red-500 text-white'\n                            }`}>\n                              {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                            </span>\n                          </div>\n                          <div className=\"bg-white p-2 rounded-lg border\">\n                            <img\n                              src={detail.questionImage || detail.image || detail.imageUrl}\n                              alt=\"Question Image\"\n                              className=\"max-w-full h-auto rounded-lg shadow-sm mx-auto block\"\n                              style={{ maxHeight: '300px' }}\n                              onError={(e) => {\n                                e.target.style.display = 'none';\n                                e.target.nextSibling.style.display = 'block';\n                              }}\n                            />\n                            <div\n                              className=\"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\"\n                              style={{ display: 'none' }}\n                            >\n                              📷 Image could not be loaded\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Enhanced Answer Section with Color Indicators */}\n                    <div className=\"space-y-3\">\n                      <div className={`p-4 rounded-lg border-4 ${\n                        detail.isCorrect\n                          ? 'bg-green-50 border-green-500'\n                          : 'bg-red-50 border-red-500'\n                      }`}>\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${\n                            detail.isCorrect ? 'bg-green-500' : 'bg-red-500'\n                          }`}>\n                            {detail.isCorrect ? (\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            ) : (\n                              <TbX className=\"w-4 h-4 text-white\" />\n                            )}\n                          </div>\n                          <span className=\"font-semibold text-gray-700\">Your Answer:</span>\n                        </div>\n                        <div className={`p-3 rounded-lg font-bold text-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-100 text-green-700 border border-green-200'\n                            : 'bg-red-100 text-red-700 border border-red-200'\n                        }`}>\n                          {detail.userAnswer || 'No answer provided'}\n                        </div>\n                      </div>\n\n                      {!detail.isCorrect && (\n                        <div className=\"bg-green-50 p-4 rounded-lg border-4 border-green-500\">\n                          <div className=\"flex items-center gap-3 mb-2\">\n                            <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\">\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            </div>\n                            <span className=\"font-semibold text-gray-700\">Correct Answer:</span>\n                          </div>\n                          <div className=\"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\">\n                            {detail.correctAnswer}\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Explanation Button for Wrong Answers */}\n                      {!detail.isCorrect && (\n                        <div className=\"mt-3\">\n                          <button\n                            className={`w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${\n                              loadingExplanations[`question_${index}`]\n                                ? 'bg-gray-400 cursor-not-allowed'\n                                : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'\n                            }`}\n                            onClick={() => fetchExplanation(index, detail)}\n                            disabled={loadingExplanations[`question_${index}`]}\n                          >\n                            {loadingExplanations[`question_${index}`] ? (\n                              <>\n                                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                                Getting Explanation...\n                              </>\n                            ) : (\n                              <>\n                                <TbBrain className=\"w-5 h-5\" />\n                                Get Explanation\n                              </>\n                            )}\n                          </button>\n\n                          {/* Explanation Display */}\n                          {explanations[`question_${index}`] && (\n                            <div className=\"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\">\n                              <div className=\"flex items-center gap-2 mb-3\">\n                                <TbBrain className=\"w-5 h-5 text-blue-600\" />\n                                <h6 className=\"font-bold text-blue-800\">💡 Explanation:</h6>\n                              </div>\n                              <div className=\"text-blue-700 leading-relaxed whitespace-pre-wrap\">\n                                {explanations[`question_${index}`]}\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                );\n              })}\n            </div>\n\n\n          </div>\n        )}\n\n        {/* Actions */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 More Quizzes button clicked!');\n              handleBackToQuizzes();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbHome className=\"w-5 h-5\" />\n            More Quizzes\n          </button>\n\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 Retake Quiz button clicked!');\n              handleRetakeQuiz();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            type=\"button\"\n          >\n            <TbTrophy className=\"w-5 h-5\" />\n            Retake Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACnE,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AAC5F,SAASC,2BAA2B,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB;EAAG,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEmB;EAAK,CAAC,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD;EACA,MAAME,UAAU,GAAGJ,QAAQ,CAACG,KAAK,IAAI;IACnCE,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,EAAE;IACjBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,SAAS;IACtBC,iBAAiB,EAAE,EAAE;IACrBC,OAAO,EAAE;EACX,CAAC;EAED,MAAM;IACJT,UAAU;IACVC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTE,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,iBAAiB;IACjBC,OAAO;IACPL;EACF,CAAC,GAAGL,UAAU;EACd,MAAMW,QAAQ,GAAGD,OAAO,KAAK,MAAM,IAAIT,UAAU,KAAKQ,iBAAiB,IAAI,EAAE,CAAC;EAE9E,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACwC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAElE;EACAD,SAAS,CAAC,MAAM;IAEd;IACA,MAAM2C,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAI;QACF,IAAIP,QAAQ,EAAE;UACZ;UACA,MAAMQ,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;;UAE7E;UACA,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,KAAK;YACnD,MAAMC,UAAU,GAAGR,YAAY,CAACS,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGV,YAAY,CAACW,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACZ,YAAY,CAACa,WAAW,CAAC;YAE1CL,UAAU,CAACH,SAAS,CAACS,cAAc,CAACT,SAAS,EAAEC,SAAS,CAAC;YACzDE,UAAU,CAACO,IAAI,GAAG,MAAM;YAExBL,QAAQ,CAACM,IAAI,CAACF,cAAc,CAAC,CAAC,EAAER,SAAS,CAAC;YAC1CI,QAAQ,CAACM,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC5DI,QAAQ,CAACM,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEC,UAAU,CAACW,KAAK,CAACb,SAAS,CAAC;YAC3BE,UAAU,CAACY,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAMc,GAAG,GAAGrB,YAAY,CAACsB,WAAW;UACpClB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAC5BjB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClCjB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClCjB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAEpC,CAAC,MAAM;UACL;UACA,MAAMrB,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;UAE7E,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,KAAK;YACnD,MAAMC,UAAU,GAAGR,YAAY,CAACS,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGV,YAAY,CAACW,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACZ,YAAY,CAACa,WAAW,CAAC;YAE1CL,UAAU,CAACH,SAAS,CAACS,cAAc,CAACT,SAAS,EAAEC,SAAS,CAAC;YACzDE,UAAU,CAACO,IAAI,GAAG,MAAM;YAExBL,QAAQ,CAACM,IAAI,CAACF,cAAc,CAAC,CAAC,EAAER,SAAS,CAAC;YAC1CI,QAAQ,CAACM,IAAI,CAACC,uBAAuB,CAAC,IAAI,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC7DI,QAAQ,CAACM,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEC,UAAU,CAACW,KAAK,CAACb,SAAS,CAAC;YAC3BE,UAAU,CAACY,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAMc,GAAG,GAAGrB,YAAY,CAACsB,WAAW;UACpClB,QAAQ,CAAC,GAAG,EAAEiB,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UACzBjB,QAAQ,CAAC,MAAM,EAAEiB,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACpC;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACpC;IACF,CAAC;;IAED;IACA,IAAIjC,QAAQ,EAAE;MACZ;MACA,MAAMkC,WAAW,GAAG,EAAE;MACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5BD,WAAW,CAACE,IAAI,CAAC;UACflD,EAAE,EAAEiD,CAAC;UACLE,IAAI,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBC,KAAK,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBxB,QAAQ,EAAE,CAAC,GAAGuB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BE,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACH,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAC9HI,KAAK,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;UAChDK,IAAI,EAAE,CAAC,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAC5B,CAAC,CAAC;MACJ;MACArC,WAAW,CAACgC,WAAW,CAAC;;MAExB;MACAW,UAAU,CAAC,MAAM;QACf,MAAMC,YAAY,GAAG,EAAE;QACvB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1BW,YAAY,CAACV,IAAI,CAAC;YAChBlD,EAAE,EAAG,QAAOiD,CAAE,EAAC;YACfE,IAAI,EAAE,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YAC7BQ,GAAG,EAAE,EAAE,GAAGT,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YAC5BC,KAAK,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;UACzB,CAAC,CAAC;QACJ;QACArC,WAAW,CAAC8C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGF,YAAY,CAACG,GAAG,CAACC,IAAI,KAAK;UACzD,GAAGA,IAAI;UACPC,MAAM,EAAE,IAAI;UACZpC,QAAQ,EAAE,CAAC;UACX0B,KAAK,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,EAAE,IAAI,CAAC;;MAER;MACAI,UAAU,CAAC,MAAM3C,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACzC,CAAC,MAAM;MACL;MACA,MAAMkD,oBAAoB,GAAG,EAAE;MAC/B,MAAMC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAE/D,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BiB,oBAAoB,CAAChB,IAAI,CAAC;UACxBlD,EAAE,EAAG,YAAWiD,CAAE,EAAC;UACnBE,IAAI,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBC,KAAK,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBxB,QAAQ,EAAE,CAAC,GAAGuB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Be,KAAK,EAAED,kBAAkB,CAACf,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGc,kBAAkB,CAACE,MAAM,CAAC,CAAC;UAChFC,cAAc,EAAE;QAClB,CAAC,CAAC;MACJ;MACAtD,WAAW,CAACkD,oBAAoB,CAAC;;MAEjC;MACAP,UAAU,CAAC,MAAM3C,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACzC;IAEAK,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMyD,gBAAgB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,MAAM,KAAK;IACxD,MAAMC,WAAW,GAAI,YAAWF,aAAc,EAAC;;IAE/C;IACA,IAAIrD,mBAAmB,CAACuD,WAAW,CAAC,IAAIzD,YAAY,CAACyD,WAAW,CAAC,EAAE;MACjE;IACF;IAEA,IAAI;MACFtD,sBAAsB,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACY,WAAW,GAAG;MAAK,CAAC,CAAC,CAAC;MAElE,MAAMC,QAAQ,GAAG,MAAMpF,2BAA2B,CAAC;QACjDqF,QAAQ,EAAEH,MAAM,CAACI,YAAY,IAAIJ,MAAM,CAACK,YAAY;QACpDC,cAAc,EAAEN,MAAM,CAACO,aAAa;QACpCC,UAAU,EAAER,MAAM,CAACQ,UAAU;QAC7BC,QAAQ,EAAET,MAAM,CAACU,aAAa,IAAIV,MAAM,CAACW,KAAK,IAAIX,MAAM,CAACS,QAAQ,IAAI;MACvE,CAAC,CAAC;MAEF,IAAIP,QAAQ,CAACU,OAAO,EAAE;QACpBnE,eAAe,CAAC4C,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACY,WAAW,GAAGC,QAAQ,CAACW;QAC1B,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLxC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAE8B,QAAQ,CAAC9B,KAAK,CAAC;QAC7D3B,eAAe,CAAC4C,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACY,WAAW,GAAG;QACjB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD3B,eAAe,CAAC4C,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACY,WAAW,GAAG;MACjB,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRtD,sBAAsB,CAAC0C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACY,WAAW,GAAG;MAAM,CAAC,CAAC,CAAC;IACrE;EACF,CAAC;EAID,MAAMa,mBAAmB,GAAGA,CAAA,KAAM;IAChCzC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/CtE,eAAe,CAAC,MAAM;MACpBqB,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM0F,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1C,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE/C,EAAE,CAAC;IAC5C,IAAIA,EAAE,EAAE;MACNvB,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAE,SAAQE,EAAG,OAAM,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL8C,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5DtE,eAAe,CAAC,MAAM;QACpBqB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEL,OAAA;IAAKgG,SAAS,EAAG,8EACf3E,QAAQ,GACJ,4DAA4D,GAC5D,yDACL,EAAE;IAAA4E,QAAA,GAGA3E,QAAQ,CAACgD,GAAG,CAAE4B,KAAK,IAAK;MACvB,IAAIA,KAAK,CAAC1B,MAAM,EAAE;QAChB,oBACExE,OAAA;UAEEgG,SAAS,EAAC,8BAA8B;UACxCG,KAAK,EAAE;YACLzC,IAAI,EAAG,GAAEwC,KAAK,CAACxC,IAAK,GAAE;YACtBU,GAAG,EAAG,GAAE8B,KAAK,CAAC9B,GAAI,GAAE;YACpBgC,SAAS,EAAG,QAAOF,KAAK,CAAC9D,QAAS,iBAAgB8D,KAAK,CAACrC,KAAM;UAChE,CAAE;UAAAoC,QAAA,EACH;QAED,GATOC,KAAK,CAAC3F,EAAE;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CAAC;MAEV;MAEA,IAAIN,KAAK,CAACrB,cAAc,EAAE;QACxB,oBACE7E,OAAA;UAEEgG,SAAS,EAAC,8BAA8B;UACxCG,KAAK,EAAE;YACLzC,IAAI,EAAG,GAAEwC,KAAK,CAACxC,IAAK,GAAE;YACtB0C,SAAS,EAAG,cAAaF,KAAK,CAAC9D,QAAS,iBAAgB8D,KAAK,CAACrC,KAAM,YAAW;YAC/EO,GAAG,EAAG,GAAE,EAAE,GAAGT,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAG;UAClC,CAAE;UAAAqC,QAAA,EAEDC,KAAK,CAACvB;QAAK,GARPuB,KAAK,CAAC3F,EAAE;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CAAC;MAEV;MAEA,oBACExG,OAAA;QAEEgG,SAAS,EAAC,qBAAqB;QAC/BG,KAAK,EAAE;UACLzC,IAAI,EAAG,GAAEwC,KAAK,CAACxC,IAAK,GAAE;UACtB+C,KAAK,EAAG,GAAEP,KAAK,CAACjC,IAAI,IAAI,CAAE,IAAG;UAC7ByC,MAAM,EAAG,GAAER,KAAK,CAACjC,IAAI,IAAI,CAAE,IAAG;UAC9B0C,eAAe,EAAET,KAAK,CAACpC,KAAK;UAC5B8C,YAAY,EAAEV,KAAK,CAAClC,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,IAAI;UACrDoC,SAAS,EAAG,iBAAgBF,KAAK,CAAC9D,QAAS,YAAW8D,KAAK,CAACrC,KAAM,YAAW;UAC7EO,GAAG,EAAE;QACP;MAAE,GAVG8B,KAAK,CAAC3F,EAAE;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWd,CAAC;IAEN,CAAC,CAAC,eAGFxG,OAAA;MAAO6G,GAAG;MAAAZ,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEVxG,OAAA;MAAKgG,SAAS,EAAG,8EAA6E3E,QAAQ,GAAG,kBAAkB,GAAG,gBAAiB,EAAE;MAAA4E,QAAA,gBAE/IjG,OAAA;QAAKgG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BjG,OAAA;UAAKgG,SAAS,EAAG,gFACf3E,QAAQ,GAAG,iDAAiD,GAAG,4CAChE,EAAE;UAAA4E,QAAA,eAEDjG,OAAA,CAACT,QAAQ;YAACyG,SAAS,EAAG,aACpB3E,QAAQ,GAAG,gBAAgB,GAAG,cAC/B;UAAE;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxG,OAAA;UAAIgG,SAAS,EAAG,2BAA0B3E,QAAQ,GAAG,gBAAgB,GAAG,cAAe,EAAE;UAAA4E,QAAA,EACtF5E,QAAQ,gBACPrB,OAAA;YAAMgG,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEzD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAEPxG,OAAA;YAAMgG,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEzD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAELxG,OAAA;UAAKgG,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACvD5E,QAAQ,GAAG,aAAa,GAAG;QAAgB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAENxG,OAAA;UAAGgG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,eACrC,EAAChF,QAAQ,EAAC,KAAG,EAACC,WAAW;QAAA;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNxG,OAAA;QAAKgG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BjG,OAAA;UAAKgG,SAAS,EAAG,sCACf3E,QAAQ,GAAG,uCAAuC,GAAG,mCACtD,EAAE;UAAA4E,QAAA,gBACDjG,OAAA;YAAKgG,SAAS,EAAG,2BACf3E,QAAQ,GAAG,gBAAgB,GAAG,cAC/B,EAAE;YAAA4E,QAAA,GACAtF,UAAU,EAAC,GACd;UAAA;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxG,OAAA;YAAKgG,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE/B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAKNxG,OAAA;QAAKgG,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDjG,OAAA;UAAKgG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFjG,OAAA;YAAKgG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAErF;UAAc;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzExG,OAAA;YAAKgG,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACNxG,OAAA;UAAKgG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFjG,OAAA;YAAKgG,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEpF,cAAc,GAAGD;UAAc;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxFxG,OAAA;YAAKgG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eACNxG,OAAA;UAAKgG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFjG,OAAA;YAAKgG,SAAS,EAAG,sBAAqB3E,QAAQ,GAAG,gBAAgB,GAAG,cAAe,EAAE;YAAA4E,QAAA,GAAEtF,UAAU,EAAC,GAAC;UAAA;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzGxG,OAAA;YAAKgG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNxG,OAAA;UAAKgG,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFjG,OAAA;YAAKgG,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAC9CtC,IAAI,CAACI,KAAK,CAACjD,SAAS,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC,CAACA,SAAS,GAAG,EAAE,EAAEgG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNxG,OAAA;YAAKgG,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxF,MAAM,iBACLhB,OAAA;QAAKgG,SAAS,EAAC,2FAA2F;QAAAC,QAAA,eACxGjG,OAAA;UAAKgG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDjG,OAAA;YAAKgG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCjG,OAAA;cAAKgG,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eACpFjG,OAAA,CAACL,MAAM;gBAACqG,SAAS,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNxG,OAAA;cAAAiG,QAAA,gBACEjG,OAAA;gBAAKgG,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,GAAC,EAACjF,MAAM,CAACgG,SAAS,IAAI,CAAC,EAAC,KAAG;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpFxG,OAAA;gBAAKgG,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxG,OAAA;YAAKgG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjG,OAAA;cAAKgG,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD,CAAC,CAAC,CAAAzF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyG,OAAO,KAAI,CAAC,KAAKjG,MAAM,CAACgG,SAAS,IAAI,CAAC,CAAC,EAAEE,cAAc,CAAC;YAAC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNxG,OAAA;cAAKgG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBAAiB,EAAC,CAAAzF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2G,YAAY,KAAI,CAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACxF,MAAM,IAAIR,IAAI,iBACdR,OAAA;QAAKgG,SAAS,EAAC,2FAA2F;QAAAC,QAAA,eACxGjG,OAAA;UAAKgG,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDjG,OAAA;YAAKgG,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCjG,OAAA;cAAKgG,SAAS,EAAC,uEAAuE;cAAAC,QAAA,eACpFjG,OAAA,CAACL,MAAM;gBAACqG,SAAS,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNxG,OAAA;cAAAiG,QAAA,eACEjG,OAAA;gBAAKgG,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxG,OAAA;YAAKgG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBjG,OAAA;cAAKgG,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChD,CAACzF,IAAI,CAACyG,OAAO,IAAI,CAAC,EAAEC,cAAc,CAAC;YAAC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNxG,OAAA;cAAKgG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,wBAAiB,EAACzF,IAAI,CAAC2G,YAAY,IAAI,CAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDxG,OAAA;QAAKgG,SAAS,EAAC,qFAAqF;QAAAC,QAAA,eAClGjG,OAAA;UAAKgG,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CjG,OAAA;YAAKgG,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClFjG,OAAA,CAACJ,OAAO;cAACoG,SAAS,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNxG,OAAA;YAAIgG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CAAC,EAGLzF,aAAa,IAAIA,aAAa,CAAC6D,MAAM,GAAG,CAAC,iBACxC5E,OAAA;QAAKgG,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBACnGjG,OAAA;UAAKgG,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CjG,OAAA;YAAKgG,SAAS,EAAC,qEAAqE;YAAAC,QAAA,eAClFjG,OAAA,CAACH,UAAU;cAACmG,SAAS,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNxG,OAAA;YAAIgG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAA8B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAENxG,OAAA;UAAKgG,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChDlF,aAAa,CAACuD,GAAG,CAAC,CAACU,MAAM,EAAEoC,KAAK,KAAK;YACpC;YACA/D,OAAO,CAACC,GAAG,CAAE,YAAW8D,KAAK,GAAG,CAAE,QAAO,EAAEpC,MAAM,CAAC;YAClD,oBACAhF,OAAA;cAEEgG,SAAS,EAAG,mFACVhB,MAAM,CAACqC,SAAS,GACZ,4FAA4F,GAC5F,mFACL,EAAE;cACHlB,KAAK,EAAE;gBACLmB,SAAS,EAAEtC,MAAM,CAACqC,SAAS,GACvB,sEAAsE,GACtE;cACN,CAAE;cAAApB,QAAA,gBAGFjG,OAAA;gBAAKgG,SAAS,EAAG,OACfhB,MAAM,CAACqC,SAAS,GACZ,0CAA0C,GAC1C,sCACL,EAAE;gBAAApB,QAAA,eACDjG,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCjG,OAAA;oBAAKgG,SAAS,EAAG,qEACfhB,MAAM,CAACqC,SAAS,GACZ,mCAAmC,GACnC,iCACL,EAAE;oBAAApB,QAAA,EACAjB,MAAM,CAACqC,SAAS,gBAAGrH,OAAA,CAACR,OAAO;sBAACwG,SAAS,EAAC;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGxG,OAAA,CAACP,GAAG;sBAACuG,SAAS,EAAC;oBAAS;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eAENxG,OAAA;oBAAKgG,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBjG,OAAA;sBAAIgG,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,GAAC,WACrC,EAACmB,KAAK,GAAG,CAAC;oBAAA;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACLxG,OAAA;sBAAKgG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,eAC3CjG,OAAA;wBAAMgG,SAAS,EAAG,4CAChBhB,MAAM,CAACqC,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAApB,QAAA,EACAjB,MAAM,CAACqC,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxG,OAAA;gBAAKgG,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBjG,OAAA;kBAAKgG,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBjG,OAAA;oBAAGgG,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EACxDjB,MAAM,CAACI,YAAY,IAAIJ,MAAM,CAACK;kBAAY;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EAGL,CAACxB,MAAM,CAACuC,YAAY,KAAK,OAAO,IAAIvC,MAAM,CAACU,aAAa,IAAIV,MAAM,CAACW,KAAK,IAAIX,MAAM,CAACS,QAAQ,MAAMT,MAAM,CAACU,aAAa,IAAIV,MAAM,CAACW,KAAK,IAAIX,MAAM,CAACS,QAAQ,CAAC,iBACxJzF,OAAA;kBAAKgG,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBjG,OAAA;oBAAKgG,SAAS,EAAG,2BACfhB,MAAM,CAACqC,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;oBAAApB,QAAA,gBACDjG,OAAA;sBAAKgG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CjG,OAAA;wBAAMgG,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAAkB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/ExG,OAAA;wBAAMgG,SAAS,EAAG,4CAChBhB,MAAM,CAACqC,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAApB,QAAA,EACAjB,MAAM,CAACqC,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAAhB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNxG,OAAA;sBAAKgG,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,gBAC7CjG,OAAA;wBACEwH,GAAG,EAAExC,MAAM,CAACU,aAAa,IAAIV,MAAM,CAACW,KAAK,IAAIX,MAAM,CAACS,QAAS;wBAC7DgC,GAAG,EAAC,gBAAgB;wBACpBzB,SAAS,EAAC,sDAAsD;wBAChEG,KAAK,EAAE;0BAAEuB,SAAS,EAAE;wBAAQ,CAAE;wBAC9BC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAC2B,OAAO,GAAG,MAAM;0BAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAAC5B,KAAK,CAAC2B,OAAO,GAAG,OAAO;wBAC9C;sBAAE;wBAAAzB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFxG,OAAA;wBACEgG,SAAS,EAAC,8DAA8D;wBACxEG,KAAK,EAAE;0BAAE2B,OAAO,EAAE;wBAAO,CAAE;wBAAA7B,QAAA,EAC5B;sBAED;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGDxG,OAAA;kBAAKgG,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBjG,OAAA;oBAAKgG,SAAS,EAAG,2BACfhB,MAAM,CAACqC,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;oBAAApB,QAAA,gBACDjG,OAAA;sBAAKgG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CjG,OAAA;wBAAKgG,SAAS,EAAG,yDACfhB,MAAM,CAACqC,SAAS,GAAG,cAAc,GAAG,YACrC,EAAE;wBAAApB,QAAA,EACAjB,MAAM,CAACqC,SAAS,gBACfrH,OAAA,CAACR,OAAO;0BAACwG,SAAS,EAAC;wBAAoB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAE1CxG,OAAA,CAACP,GAAG;0BAACuG,SAAS,EAAC;wBAAoB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBACtC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNxG,OAAA;wBAAMgG,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAY;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNxG,OAAA;sBAAKgG,SAAS,EAAG,oCACfhB,MAAM,CAACqC,SAAS,GACZ,qDAAqD,GACrD,+CACL,EAAE;sBAAApB,QAAA,EACAjB,MAAM,CAACQ,UAAU,IAAI;oBAAoB;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,CAACxB,MAAM,CAACqC,SAAS,iBAChBrH,OAAA;oBAAKgG,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,gBACnEjG,OAAA;sBAAKgG,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CjG,OAAA;wBAAKgG,SAAS,EAAC,oEAAoE;wBAAAC,QAAA,eACjFjG,OAAA,CAACR,OAAO;0BAACwG,SAAS,EAAC;wBAAoB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNxG,OAAA;wBAAMgG,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAe;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNxG,OAAA;sBAAKgG,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,EACpGjB,MAAM,CAACO;oBAAa;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA,CAACxB,MAAM,CAACqC,SAAS,iBAChBrH,OAAA;oBAAKgG,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBjG,OAAA;sBACEgG,SAAS,EAAG,gHACVtE,mBAAmB,CAAE,YAAW0F,KAAM,EAAC,CAAC,GACpC,gCAAgC,GAChC,2HACL,EAAE;sBACHY,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAACsC,KAAK,EAAEpC,MAAM,CAAE;sBAC/CiD,QAAQ,EAAEvG,mBAAmB,CAAE,YAAW0F,KAAM,EAAC,CAAE;sBAAAnB,QAAA,EAElDvE,mBAAmB,CAAE,YAAW0F,KAAM,EAAC,CAAC,gBACvCpH,OAAA,CAAAE,SAAA;wBAAA+F,QAAA,gBACEjG,OAAA;0BAAKgG,SAAS,EAAC;wBAA8E;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,0BAEtG;sBAAA,eAAE,CAAC,gBAEHxG,OAAA,CAAAE,SAAA;wBAAA+F,QAAA,gBACEjG,OAAA,CAACJ,OAAO;0BAACoG,SAAS,EAAC;wBAAS;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,mBAEjC;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC,EAGRhF,YAAY,CAAE,YAAW4F,KAAM,EAAC,CAAC,iBAChCpH,OAAA;sBAAKgG,SAAS,EAAC,yFAAyF;sBAAAC,QAAA,gBACtGjG,OAAA;wBAAKgG,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,gBAC3CjG,OAAA,CAACJ,OAAO;0BAACoG,SAAS,EAAC;wBAAuB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CxG,OAAA;0BAAIgG,SAAS,EAAC,yBAAyB;0BAAAC,QAAA,EAAC;wBAAe;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eACNxG,OAAA;wBAAKgG,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAC/DzE,YAAY,CAAE,YAAW4F,KAAM,EAAC;sBAAC;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA9KDxB,MAAM,CAACkD,UAAU,IAAId,KAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+K5B,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CACN,eAGDxG,OAAA;QAAKgG,SAAS,EAAC,iCAAiC;QAAAC,QAAA,gBAC9CjG,OAAA;UACEgI,OAAO,EAAGJ,CAAC,IAAK;YACdA,CAAC,CAACO,cAAc,CAAC,CAAC;YAClB9E,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9CwC,mBAAmB,CAAC,CAAC;UACvB,CAAE;UACFE,SAAS,EAAC,+NAA+N;UACzOpD,IAAI,EAAC,QAAQ;UAAAqD,QAAA,gBAEbjG,OAAA,CAACN,MAAM;YAACsG,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETxG,OAAA;UACEgI,OAAO,EAAGJ,CAAC,IAAK;YACdA,CAAC,CAACO,cAAc,CAAC,CAAC;YAClB9E,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7CyC,gBAAgB,CAAC,CAAC;UACpB,CAAE;UACFC,SAAS,EAAC,kOAAkO;UAC5OpD,IAAI,EAAC,QAAQ;UAAAqD,QAAA,gBAEbjG,OAAA,CAACT,QAAQ;YAACyG,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpG,EAAA,CAlqBID,UAAU;EAAA,QACGhB,WAAW,EACXC,WAAW,EACbC,SAAS,EACPC,WAAW;AAAA;AAAA8I,EAAA,GAJxBjI,UAAU;AAoqBhB,eAAeA,UAAU;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}